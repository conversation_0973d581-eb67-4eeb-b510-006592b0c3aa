// 全局样式文件

// 变量定义
:root {
  // 主色调
  --primary-color: #409eff;
  --success-color: #67c23a;
  --warning-color: #e6a23c;
  --danger-color: #f56c6c;
  --info-color: #909399;
  
  // 背景色
  --bg-color: #f5f7fa;
  --bg-color-light: #fafbfc;
  --bg-color-dark: #ebeef5;
  
  // 文字色
  --text-color-primary: #303133;
  --text-color-regular: #606266;
  --text-color-secondary: #909399;
  --text-color-placeholder: #c0c4cc;
  
  // 边框色
  --border-color: #dcdfe6;
  --border-color-light: #e4e7ed;
  --border-color-lighter: #ebeef5;
  
  // 阴影
  --box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  --box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  
  // 圆角
  --border-radius-base: 4px;
  --border-radius-small: 2px;
  --border-radius-large: 8px;
  
  // 间距
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
}

// 全局重置
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: var(--text-color-primary);
  background-color: var(--bg-color);
}

// 通用类
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.flex { display: flex; }
.flex-center { display: flex; align-items: center; justify-content: center; }
.flex-between { display: flex; align-items: center; justify-content: space-between; }
.flex-column { display: flex; flex-direction: column; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.mb-xs { margin-bottom: var(--spacing-xs); }
.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }

.mt-xs { margin-top: var(--spacing-xs); }
.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mt-xl { margin-top: var(--spacing-xl); }

.p-xs { padding: var(--spacing-xs); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }

// 卡片样式
.card {
  background: white;
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow-base);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  
  &.card-hover {
    transition: all 0.3s ease;
    cursor: pointer;
    
    &:hover {
      box-shadow: var(--box-shadow-light);
      transform: translateY(-2px);
    }
  }
}

// 状态指示器
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-base);
  font-size: 12px;
  font-weight: 500;
  
  &.status-success {
    background: rgba(103, 194, 58, 0.1);
    color: var(--success-color);
  }
  
  &.status-warning {
    background: rgba(230, 162, 60, 0.1);
    color: var(--warning-color);
  }
  
  &.status-danger {
    background: rgba(245, 108, 108, 0.1);
    color: var(--danger-color);
  }
  
  &.status-info {
    background: rgba(144, 147, 153, 0.1);
    color: var(--info-color);
  }
}

// 数据表格样式
.data-table {
  .el-table {
    border-radius: var(--border-radius-large);
    overflow: hidden;
    
    .el-table__header {
      background: var(--bg-color-light);
      
      th {
        background: var(--bg-color-light);
        color: var(--text-color-regular);
        font-weight: 600;
      }
    }
    
    .el-table__row {
      &:hover {
        background: rgba(64, 158, 255, 0.05);
      }
    }
  }
}

// 表单样式
.form-container {
  .el-form-item {
    margin-bottom: var(--spacing-lg);
    
    .el-form-item__label {
      font-weight: 500;
      color: var(--text-color-regular);
    }
  }
  
  .form-section {
    margin-bottom: var(--spacing-xl);
    
    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--text-color-primary);
      margin-bottom: var(--spacing-md);
      padding-bottom: var(--spacing-sm);
      border-bottom: 2px solid var(--primary-color);
    }
  }
}

// 图表容器
.chart-container {
  background: white;
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow-base);
  padding: var(--spacing-lg);
  
  .chart-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
    
    .chart-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--text-color-primary);
    }
    
    .chart-controls {
      display: flex;
      gap: var(--spacing-sm);
    }
  }
  
  .chart-content {
    height: 400px;
    min-height: 300px;
  }
}

// 统计卡片
.stat-card {
  background: white;
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow-base);
  padding: var(--spacing-lg);
  text-align: center;
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: var(--box-shadow-light);
    transform: translateY(-2px);
  }
  
  .stat-icon {
    font-size: 32px;
    margin-bottom: var(--spacing-sm);
    
    &.success { color: var(--success-color); }
    &.warning { color: var(--warning-color); }
    &.danger { color: var(--danger-color); }
    &.info { color: var(--info-color); }
    &.primary { color: var(--primary-color); }
  }
  
  .stat-value {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-color-primary);
    margin-bottom: var(--spacing-xs);
  }
  
  .stat-label {
    font-size: 14px;
    color: var(--text-color-secondary);
  }
  
  .stat-change {
    font-size: 12px;
    margin-top: var(--spacing-xs);
    
    &.positive { color: var(--success-color); }
    &.negative { color: var(--danger-color); }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .card {
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
  }
  
  .chart-container {
    padding: var(--spacing-md);
    
    .chart-content {
      height: 300px;
    }
  }
  
  .stat-card {
    padding: var(--spacing-md);
    
    .stat-value {
      font-size: 20px;
    }
  }
}

// 动画
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.pulse {
  animation: pulse 2s infinite;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--bg-color-light);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
  
  &:hover {
    background: var(--text-color-placeholder);
  }
}

// Element Plus 组件样式覆盖
.el-button {
  border-radius: var(--border-radius-base);
  font-weight: 500;
}

.el-card {
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow-base);
}

.el-dialog {
  border-radius: var(--border-radius-large);
}

.el-message {
  border-radius: var(--border-radius-base);
}
