"""
交易所模块单元测试
测试交易所接口、数据结构等
"""

import unittest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime

from tests import TEST_CONFIG, get_event_loop
from exchanges.base_exchange import BaseExchange, KlineData, PositionInfo, OrderInfo
from exchanges.base_exchange import OrderSide, OrderType, PositionSide
from exchanges.exchange_factory import ExchangeFactory

class TestBaseExchange(unittest.TestCase):
    """基础交易所测试"""
    
    def setUp(self):
        """测试前准备"""
        self.loop = get_event_loop()
    
    def test_kline_data_structure(self):
        """测试K线数据结构"""
        kline = KlineData(
            timestamp=1234567890,
            open=50000.0,
            high=51000.0,
            low=49000.0,
            close=50500.0,
            volume=100.5
        )
        
        self.assertEqual(kline.timestamp, 1234567890)
        self.assertEqual(kline.open, 50000.0)
        self.assertEqual(kline.high, 51000.0)
        self.assertEqual(kline.low, 49000.0)
        self.assertEqual(kline.close, 50500.0)
        self.assertEqual(kline.volume, 100.5)
    
    def test_position_info_structure(self):
        """测试持仓信息结构"""
        position = PositionInfo(
            symbol="BTC-USDT",
            side=PositionSide.LONG,
            size=0.1,
            entry_price=50000.0,
            mark_price=50500.0,
            unrealized_pnl=50.0,
            liquidation_price=45000.0,
            leverage=10
        )
        
        self.assertEqual(position.symbol, "BTC-USDT")
        self.assertEqual(position.side, PositionSide.LONG)
        self.assertEqual(position.size, 0.1)
        self.assertEqual(position.entry_price, 50000.0)
        self.assertEqual(position.unrealized_pnl, 50.0)
    
    def test_order_info_structure(self):
        """测试订单信息结构"""
        order = OrderInfo(
            order_id="12345",
            symbol="BTC-USDT",
            side=OrderSide.BUY,
            type=OrderType.MARKET,
            amount=0.1,
            price=50000.0,
            status="filled",
            filled_amount=0.1,
            average_price=50000.0,
            timestamp=1234567890
        )
        
        self.assertEqual(order.order_id, "12345")
        self.assertEqual(order.symbol, "BTC-USDT")
        self.assertEqual(order.side, OrderSide.BUY)
        self.assertEqual(order.type, OrderType.MARKET)
        self.assertEqual(order.amount, 0.1)
    
    def test_order_side_enum(self):
        """测试订单方向枚举"""
        self.assertEqual(OrderSide.BUY.value, "buy")
        self.assertEqual(OrderSide.SELL.value, "sell")
    
    def test_order_type_enum(self):
        """测试订单类型枚举"""
        self.assertEqual(OrderType.MARKET.value, "market")
        self.assertEqual(OrderType.LIMIT.value, "limit")
    
    def test_position_side_enum(self):
        """测试持仓方向枚举"""
        self.assertEqual(PositionSide.LONG.value, "long")
        self.assertEqual(PositionSide.SHORT.value, "short")

class TestExchangeFactory(unittest.TestCase):
    """交易所工厂测试"""
    
    def setUp(self):
        """测试前准备"""
        self.loop = get_event_loop()
    
    def test_supported_exchanges(self):
        """测试支持的交易所列表"""
        exchanges = ExchangeFactory.get_supported_exchanges()
        self.assertIn('okx', exchanges)
        self.assertIn('gateio', exchanges)
    
    def test_exchange_support_check(self):
        """测试交易所支持检查"""
        self.assertTrue(ExchangeFactory.is_exchange_supported('okx'))
        self.assertTrue(ExchangeFactory.is_exchange_supported('gateio'))
        self.assertFalse(ExchangeFactory.is_exchange_supported('binance'))
    
    @patch('exchanges.okx_exchange.OKXExchange')
    def test_create_okx_exchange(self, mock_okx):
        """测试创建OKX交易所"""
        async def test_create():
            mock_instance = AsyncMock()
            mock_okx.return_value = mock_instance
            mock_instance.connect.return_value = True
            
            exchange = await ExchangeFactory.create_exchange(
                exchange_name='okx',
                api_key=TEST_CONFIG['api_key'],
                api_secret=TEST_CONFIG['api_secret'],
                passphrase=TEST_CONFIG['passphrase'],
                sandbox=True,
                auto_connect=True
            )
            
            self.assertIsNotNone(exchange)
            mock_instance.connect.assert_called_once()
        
        self.loop.run_until_complete(test_create())
    
    @patch('exchanges.gateio_exchange.GateIOExchange')
    def test_create_gateio_exchange(self, mock_gateio):
        """测试创建Gate.io交易所"""
        async def test_create():
            mock_instance = AsyncMock()
            mock_gateio.return_value = mock_instance
            mock_instance.connect.return_value = True
            
            exchange = await ExchangeFactory.create_exchange(
                exchange_name='gateio',
                api_key=TEST_CONFIG['api_key'],
                api_secret=TEST_CONFIG['api_secret'],
                sandbox=True,
                auto_connect=True
            )
            
            self.assertIsNotNone(exchange)
            mock_instance.connect.assert_called_once()
        
        self.loop.run_until_complete(test_create())
    
    def test_create_unsupported_exchange(self):
        """测试创建不支持的交易所"""
        async def test_create():
            with self.assertRaises(ValueError):
                await ExchangeFactory.create_exchange(
                    exchange_name='binance',
                    api_key=TEST_CONFIG['api_key'],
                    api_secret=TEST_CONFIG['api_secret']
                )
        
        self.loop.run_until_complete(test_create())

if __name__ == '__main__':
    unittest.main()
