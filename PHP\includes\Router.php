<?php
/**
 * 路由系统
 * 
 * @description 处理URL路由和控制器调度
 */

class Router {
    private $routes = [];
    private $middlewares = [];
    private $currentRoute = null;

    /**
     * 添加GET路由
     */
    public function get($pattern, $handler) {
        $this->addRoute('GET', $pattern, $handler);
    }

    /**
     * 添加POST路由
     */
    public function post($pattern, $handler) {
        $this->addRoute('POST', $pattern, $handler);
    }

    /**
     * 添加PUT路由
     */
    public function put($pattern, $handler) {
        $this->addRoute('PUT', $pattern, $handler);
    }

    /**
     * 添加DELETE路由
     */
    public function delete($pattern, $handler) {
        $this->addRoute('DELETE', $pattern, $handler);
    }

    /**
     * 添加路由
     */
    private function addRoute($method, $pattern, $handler) {
        $this->routes[] = [
            'method' => $method,
            'pattern' => $pattern,
            'handler' => $handler,
            'middlewares' => []
        ];
    }

    /**
     * 添加中间件
     */
    public function middleware($middleware) {
        $this->middlewares[] = $middleware;
        return $this;
    }

    /**
     * 分发请求
     */
    public function dispatch() {
        $method = $_SERVER['REQUEST_METHOD'];
        $uri = $this->getUri();

        // 查找匹配的路由
        foreach ($this->routes as $route) {
            if ($route['method'] === $method && $this->matchPattern($route['pattern'], $uri)) {
                $this->currentRoute = $route;
                
                try {
                    // 执行全局中间件
                    $this->executeMiddlewares($this->middlewares);
                    
                    // 执行路由特定中间件
                    $this->executeMiddlewares($route['middlewares']);
                    
                    // 执行路由处理器
                    $this->executeHandler($route['handler'], $this->getRouteParams($route['pattern'], $uri));
                    return;
                    
                } catch (Exception $e) {
                    $this->handleError($e);
                    return;
                }
            }
        }

        // 没有找到匹配的路由
        $this->handle404();
    }

    /**
     * 获取请求URI
     */
    private function getUri() {
        $uri = $_SERVER['REQUEST_URI'];

        // 移除查询字符串
        if (($pos = strpos($uri, '?')) !== false) {
            $uri = substr($uri, 0, $pos);
        }

        // 处理宝塔面板环境的路径
        $scriptName = $_SERVER['SCRIPT_NAME'];
        $scriptDir = dirname($scriptName);

        // 如果在子目录中，移除基础路径
        if ($scriptDir !== '/' && $scriptDir !== '\\') {
            $scriptDir = str_replace('\\', '/', $scriptDir);
            if (strpos($uri, $scriptDir) === 0) {
                $uri = substr($uri, strlen($scriptDir));
            }
        }

        // 确保URI以/开头
        $uri = '/' . ltrim($uri, '/');

        // 如果是根路径，直接返回
        if ($uri === '/') {
            return $uri;
        }

        // 移除尾部斜杠
        return rtrim($uri, '/');
    }

    /**
     * 匹配路由模式
     */
    private function matchPattern($pattern, $uri) {
        // 转换路由模式为正则表达式
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $pattern);
        $pattern = '#^' . $pattern . '$#';
        
        return preg_match($pattern, $uri);
    }

    /**
     * 获取路由参数
     */
    private function getRouteParams($pattern, $uri) {
        $params = [];
        
        // 提取参数名
        preg_match_all('/\{([^}]+)\}/', $pattern, $paramNames);
        
        // 转换为正则表达式并匹配
        $regexPattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $pattern);
        $regexPattern = '#^' . $regexPattern . '$#';
        
        if (preg_match($regexPattern, $uri, $matches)) {
            array_shift($matches); // 移除完整匹配
            
            foreach ($paramNames[1] as $index => $name) {
                if (isset($matches[$index])) {
                    $params[$name] = $matches[$index];
                }
            }
        }
        
        return $params;
    }

    /**
     * 执行中间件
     */
    private function executeMiddlewares($middlewares) {
        foreach ($middlewares as $middleware) {
            if (is_string($middleware)) {
                if (class_exists($middleware)) {
                    $middlewareInstance = new $middleware();
                    if (method_exists($middlewareInstance, 'handle')) {
                        $middlewareInstance->handle();
                    }
                }
            } elseif (is_callable($middleware)) {
                call_user_func($middleware);
            }
        }
    }

    /**
     * 执行路由处理器
     */
    private function executeHandler($handler, $params = []) {
        if (is_string($handler)) {
            // 控制器@方法格式
            if (strpos($handler, '@') !== false) {
                list($controllerName, $methodName) = explode('@', $handler);
                
                $controllerFile = ROOT_PATH . '/controllers/' . $controllerName . '.php';
                if (file_exists($controllerFile)) {
                    require_once $controllerFile;
                    
                    if (class_exists($controllerName)) {
                        $controller = new $controllerName();
                        if (method_exists($controller, $methodName)) {
                            call_user_func_array([$controller, $methodName], $params);
                        } else {
                            throw new Exception("方法 {$methodName} 在控制器 {$controllerName} 中不存在");
                        }
                    } else {
                        throw new Exception("控制器类 {$controllerName} 不存在");
                    }
                } else {
                    throw new Exception("控制器文件 {$controllerFile} 不存在");
                }
            } else {
                // 直接函数调用
                if (function_exists($handler)) {
                    call_user_func_array($handler, $params);
                } else {
                    throw new Exception("函数 {$handler} 不存在");
                }
            }
        } elseif (is_callable($handler)) {
            // 匿名函数或可调用对象
            call_user_func_array($handler, $params);
        } else {
            throw new Exception("无效的路由处理器");
        }
    }

    /**
     * 处理错误
     */
    private function handleError($exception) {
        http_response_code(500);
        
        if (DEBUG_MODE) {
            echo "<h1>路由错误</h1>";
            echo "<p>" . htmlspecialchars($exception->getMessage()) . "</p>";
            echo "<pre>" . htmlspecialchars($exception->getTraceAsString()) . "</pre>";
        } else {
            $this->loadErrorPage(500);
        }
        
        // 记录错误日志
        error_log("路由错误: " . $exception->getMessage());
    }

    /**
     * 处理404错误
     */
    private function handle404() {
        http_response_code(404);
        $this->loadErrorPage(404);
    }

    /**
     * 加载错误页面
     */
    private function loadErrorPage($code) {
        $errorFile = TEMPLATES_PATH . "/error_{$code}.php";
        if (file_exists($errorFile)) {
            include $errorFile;
        } else {
            $defaultErrorFile = TEMPLATES_PATH . "/error.php";
            if (file_exists($defaultErrorFile)) {
                include $defaultErrorFile;
            } else {
                echo "<h1>错误 {$code}</h1>";
                if ($code === 404) {
                    echo "<p>页面未找到</p>";
                } else {
                    echo "<p>服务器内部错误</p>";
                }
            }
        }
    }

    /**
     * 重定向
     */
    public static function redirect($url, $code = 302) {
        header("Location: {$url}", true, $code);
        exit;
    }

    /**
     * 生成URL
     */
    public static function url($path = '') {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'];
        $scriptName = dirname($_SERVER['SCRIPT_NAME']);
        $basePath = $scriptName !== '/' ? $scriptName : '';
        
        return $protocol . '://' . $host . $basePath . '/' . ltrim($path, '/');
    }

    /**
     * 获取当前路由信息
     */
    public function getCurrentRoute() {
        return $this->currentRoute;
    }

    /**
     * 检查是否为AJAX请求
     */
    public static function isAjax() {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }

    /**
     * 返回JSON响应
     */
    public static function json($data, $code = 200) {
        http_response_code($code);
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }

    /**
     * 获取请求数据
     */
    public static function getRequestData() {
        $method = $_SERVER['REQUEST_METHOD'];
        
        switch ($method) {
            case 'GET':
                return $_GET;
            case 'POST':
                $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
                if (strpos($contentType, 'application/json') !== false) {
                    return json_decode(file_get_contents('php://input'), true) ?: [];
                }
                return $_POST;
            case 'PUT':
            case 'DELETE':
                parse_str(file_get_contents('php://input'), $data);
                return $data;
            default:
                return [];
        }
    }
}
?>
