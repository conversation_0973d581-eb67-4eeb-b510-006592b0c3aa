#!/usr/bin/env python3
"""
BitV MACD智能加仓交易系统 - 主启动文件
"""

import asyncio
import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import logging
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/trading.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="BitV MACD智能加仓交易系统",
    description="基于Python异步架构的专业级交易后端",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局变量
trading_engine = None
websocket_manager = None

@app.on_startup
async def startup_event():
    """应用启动时的初始化"""
    global trading_engine, websocket_manager
    
    logger.info("🚀 启动BitV MACD智能加仓交易系统...")
    
    try:
        # 创建日志目录
        os.makedirs('logs', exist_ok=True)
        
        # 初始化交易引擎（如果存在）
        try:
            from core.trading_engine import TradingEngine
            trading_engine = TradingEngine()
            logger.info("✅ 交易引擎初始化成功")
        except ImportError:
            logger.warning("⚠️ 交易引擎模块未找到，使用模拟模式")
            trading_engine = None
        
        # 初始化WebSocket管理器（如果存在）
        try:
            from api.websocket import WebSocketManager
            websocket_manager = WebSocketManager()
            logger.info("✅ WebSocket管理器初始化成功")
        except ImportError:
            logger.warning("⚠️ WebSocket管理器未找到")
            websocket_manager = None
            
        logger.info("🎉 系统启动完成！")
        
    except Exception as e:
        logger.error(f"❌ 系统启动失败: {e}")

@app.on_shutdown
async def shutdown_event():
    """应用关闭时的清理"""
    logger.info("🛑 正在关闭BitV交易系统...")
    
    if trading_engine:
        try:
            await trading_engine.stop()
            logger.info("✅ 交易引擎已停止")
        except Exception as e:
            logger.error(f"❌ 停止交易引擎失败: {e}")
    
    logger.info("👋 系统已关闭")

# 基础路由
@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "BitV MACD智能加仓交易系统",
        "version": "1.0.0",
        "status": "running",
        "docs": "/docs"
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "trading_engine": trading_engine is not None,
        "websocket_manager": websocket_manager is not None
    }

@app.get("/api/status")
async def get_system_status():
    """获取系统状态"""
    return {
        "success": True,
        "data": {
            "system_status": "running",
            "trading_engine_status": "active" if trading_engine else "inactive",
            "websocket_status": "active" if websocket_manager else "inactive",
            "timestamp": asyncio.get_event_loop().time()
        }
    }

@app.get("/api/trading/status")
async def get_trading_status():
    """获取交易状态"""
    if trading_engine:
        try:
            status = await trading_engine.get_status()
            return {"success": True, "data": status}
        except Exception as e:
            return {"success": False, "error": str(e)}
    else:
        return {
            "success": True,
            "data": {
                "is_running": False,
                "trading_state": "idle",
                "current_session": None,
                "message": "交易引擎未初始化"
            }
        }

@app.post("/api/trading/start")
async def start_trading(config: dict):
    """启动交易"""
    if trading_engine:
        try:
            result = await trading_engine.start_trading(config)
            return {"success": True, "data": result}
        except Exception as e:
            return {"success": False, "error": str(e)}
    else:
        return {"success": False, "error": "交易引擎未初始化"}

@app.post("/api/trading/stop")
async def stop_trading():
    """停止交易"""
    if trading_engine:
        try:
            result = await trading_engine.stop_trading()
            return {"success": True, "data": result}
        except Exception as e:
            return {"success": False, "error": str(e)}
    else:
        return {"success": False, "error": "交易引擎未初始化"}

# 尝试加载其他API路由
try:
    from api.routes import router as api_router
    app.include_router(api_router, prefix="/api")
    logger.info("✅ API路由加载成功")
except ImportError:
    logger.warning("⚠️ API路由模块未找到，使用基础路由")

# WebSocket路由
try:
    from api.websocket import websocket_endpoint
    app.websocket("/ws")(websocket_endpoint)
    logger.info("✅ WebSocket路由加载成功")
except ImportError:
    logger.warning("⚠️ WebSocket路由未找到")

def main():
    """主函数"""
    print("🚀 启动BitV MACD智能加仓交易系统...")
    print("📊 系统信息:")
    print(f"   - Python版本: {sys.version}")
    print(f"   - 工作目录: {os.getcwd()}")
    print(f"   - API地址: http://localhost:8000")
    print(f"   - WebSocket: ws://localhost:8001")
    print(f"   - 文档地址: http://localhost:8000/docs")
    print("=" * 50)
    
    # 启动API服务器
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,  # 开发模式，生产环境应设为False
        log_level="info"
    )

if __name__ == "__main__":
    main()
