"""
公共的fallback导入模块
为可选依赖提供fallback实现
"""

def monitor_task(task_type):
    """空的监控装饰器fallback"""
    def decorator(func):
        return func
    return decorator

class TaskType:
    """任务类型fallback定义"""
    OPENING_CHECK = "opening_check"
    PRICE_FETCH = "price_fetch"
    ORDER_EXECUTION = "order_execution"
    POSITION_CHECK = "position_check"
    SIGNAL_ANALYSIS = "signal_analysis"
    RISK_CALCULATION = "risk_calculation"
