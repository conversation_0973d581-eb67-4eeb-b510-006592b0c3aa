"""
插针（Pin Bar）技术指标计算器
实现完整的插针形态识别和信号分析
"""

import asyncio
import logging
from typing import List, Optional, Tuple, Dict, Any
from dataclasses import dataclass
from enum import Enum
import numpy as np

logger = logging.getLogger(__name__)

class PinBarType(Enum):
    """插针类型"""
    BULLISH_PIN = "bullish_pin"        # 看涨插针（长下影线）
    BEARISH_PIN = "bearish_pin"        # 看跌插针（长上影线）
    DOJI_PIN = "doji_pin"              # 十字星插针
    NO_PIN = "no_pin"                  # 非插针

class PinBarSignal(Enum):
    """插针信号类型"""
    STRONG_BULLISH = "strong_bullish"      # 强烈看涨
    WEAK_BULLISH = "weak_bullish"          # 弱看涨
    NEUTRAL = "neutral"                    # 中性
    WEAK_BEARISH = "weak_bearish"          # 弱看跌
    STRONG_BEARISH = "strong_bearish"      # 强烈看跌

@dataclass
class PinBarData:
    """插针数据"""
    pin_type: PinBarType           # 插针类型
    signal: PinBarSignal           # 交易信号
    signal_strength: float         # 信号强度 (0.0-1.0)
    
    # K线数据
    open_price: float              # 开盘价
    high_price: float              # 最高价
    low_price: float               # 最低价
    close_price: float             # 收盘价
    
    # 插针特征
    body_size: float               # 实体大小
    upper_shadow: float            # 上影线长度
    lower_shadow: float            # 下影线长度
    total_range: float             # 总区间
    
    # 比例关系
    body_ratio: float              # 实体比例
    upper_shadow_ratio: float      # 上影线比例
    lower_shadow_ratio: float      # 下影线比例
    
    # 插针质量评分
    pin_quality: float             # 插针质量 (0.0-1.0)
    trend_context: str             # 趋势背景
    volume_confirmation: bool      # 成交量确认
    
    reliability: float = 1.0       # 可靠性评分 (0.0-1.0)

class PinBarCalculator:
    """插针（Pin Bar）计算器"""
    
    def __init__(self, min_body_ratio: float = 0.1, min_shadow_ratio: float = 0.6,
                 lookback_period: int = 20, volume_threshold: float = 1.2):
        """
        初始化插针计算器
        
        Args:
            min_body_ratio: 最小实体比例（默认10%）
            min_shadow_ratio: 最小影线比例（默认60%）
            lookback_period: 回看周期（默认20）
            volume_threshold: 成交量阈值倍数（默认1.2倍）
        """
        self.min_body_ratio = min_body_ratio
        self.min_shadow_ratio = min_shadow_ratio
        self.lookback_period = lookback_period
        self.volume_threshold = volume_threshold
        
        # 历史数据缓存
        self._pinbar_history: List[PinBarData] = []
        
        logger.info(f"插针计算器初始化: 最小实体比例={min_body_ratio}, "
                   f"最小影线比例={min_shadow_ratio}, 回看周期={lookback_period}")
    
    def get_required_data_count(self) -> int:
        """获取所需的最小数据点数"""
        return max(self.lookback_period, 10)
    
    async def calculate_pinbar(self, highs: List[float], lows: List[float], 
                              opens: List[float], closes: List[float],
                              volumes: Optional[List[float]] = None) -> Optional[PinBarData]:
        """
        计算插针指标
        
        Args:
            highs: 最高价列表
            lows: 最低价列表
            opens: 开盘价列表
            closes: 收盘价列表
            volumes: 成交量列表（可选）
            
        Returns:
            Optional[PinBarData]: 插针数据，数据不足时返回None
        """
        try:
            if len(highs) != len(lows) or len(highs) != len(opens) or len(highs) != len(closes):
                logger.error("OHLC价格数据长度不一致")
                return None
            
            required_count = self.get_required_data_count()
            if len(closes) < required_count:
                logger.warning(f"数据不足，需要至少{required_count}个数据点，当前{len(closes)}个")
                return None
            
            # 获取最新K线数据
            current_open = opens[-1]
            current_high = highs[-1]
            current_low = lows[-1]
            current_close = closes[-1]
            current_volume = volumes[-1] if volumes else None
            
            # 计算K线基本特征
            body_size = abs(current_close - current_open)
            upper_shadow = current_high - max(current_open, current_close)
            lower_shadow = min(current_open, current_close) - current_low
            total_range = current_high - current_low
            
            # 避免除零错误
            if total_range == 0:
                logger.warning("K线总区间为0，无法计算插针")
                return None
            
            # 计算比例关系
            body_ratio = body_size / total_range
            upper_shadow_ratio = upper_shadow / total_range
            lower_shadow_ratio = lower_shadow / total_range
            
            # 识别插针类型
            pin_type = await self._identify_pin_type(
                body_ratio, upper_shadow_ratio, lower_shadow_ratio
            )
            
            # 分析趋势背景
            trend_context = await self._analyze_trend_context(closes)
            
            # 成交量确认
            volume_confirmation = await self._check_volume_confirmation(volumes) if volumes else False
            
            # 计算插针质量
            pin_quality = await self._calculate_pin_quality(
                pin_type, body_ratio, upper_shadow_ratio, lower_shadow_ratio,
                trend_context, volume_confirmation
            )
            
            # 分析交易信号
            signal, signal_strength = await self._analyze_pin_signal(
                pin_type, pin_quality, trend_context, volume_confirmation
            )
            
            # 计算可靠性
            reliability = self._calculate_reliability(len(closes), required_count, pin_quality)
            
            result = PinBarData(
                pin_type=pin_type,
                signal=signal,
                signal_strength=round(signal_strength, 3),
                open_price=round(current_open, 4),
                high_price=round(current_high, 4),
                low_price=round(current_low, 4),
                close_price=round(current_close, 4),
                body_size=round(body_size, 4),
                upper_shadow=round(upper_shadow, 4),
                lower_shadow=round(lower_shadow, 4),
                total_range=round(total_range, 4),
                body_ratio=round(body_ratio, 3),
                upper_shadow_ratio=round(upper_shadow_ratio, 3),
                lower_shadow_ratio=round(lower_shadow_ratio, 3),
                pin_quality=round(pin_quality, 3),
                trend_context=trend_context,
                volume_confirmation=volume_confirmation,
                reliability=round(reliability, 3)
            )
            
            # 更新历史记录
            self._pinbar_history.append(result)
            if len(self._pinbar_history) > 100:
                self._pinbar_history = self._pinbar_history[-100:]
            
            logger.debug(f"插针计算完成: 类型={pin_type.value}, 信号={signal.value}, "
                        f"质量={pin_quality:.3f}, 强度={signal_strength:.3f}")
            
            return result
            
        except Exception as e:
            logger.error(f"插针计算异常: {e}")
            return None
    
    async def _identify_pin_type(self, body_ratio: float, upper_shadow_ratio: float, 
                                lower_shadow_ratio: float) -> PinBarType:
        """识别插针类型"""
        try:
            # 看涨插针：长下影线，小实体，短上影线
            if (lower_shadow_ratio >= self.min_shadow_ratio and 
                body_ratio <= self.min_body_ratio and 
                upper_shadow_ratio <= body_ratio * 2):
                return PinBarType.BULLISH_PIN
            
            # 看跌插针：长上影线，小实体，短下影线
            elif (upper_shadow_ratio >= self.min_shadow_ratio and 
                  body_ratio <= self.min_body_ratio and 
                  lower_shadow_ratio <= body_ratio * 2):
                return PinBarType.BEARISH_PIN
            
            # 十字星插针：实体极小，上下影线都较长
            elif (body_ratio <= 0.05 and 
                  upper_shadow_ratio >= 0.3 and 
                  lower_shadow_ratio >= 0.3):
                return PinBarType.DOJI_PIN
            
            else:
                return PinBarType.NO_PIN
                
        except Exception as e:
            logger.error(f"识别插针类型异常: {e}")
            return PinBarType.NO_PIN
    
    async def _analyze_trend_context(self, closes: List[float]) -> str:
        """分析趋势背景"""
        try:
            if len(closes) < 10:
                return "数据不足"
            
            # 计算短期和长期移动平均
            short_ma = np.mean(closes[-5:])
            long_ma = np.mean(closes[-20:]) if len(closes) >= 20 else np.mean(closes[-10:])
            current_price = closes[-1]
            
            # 判断趋势
            if current_price > short_ma > long_ma:
                return "上升趋势"
            elif current_price < short_ma < long_ma:
                return "下降趋势"
            elif abs(short_ma - long_ma) / long_ma < 0.02:  # 2%以内认为是横盘
                return "横盘整理"
            else:
                return "趋势不明"
                
        except Exception as e:
            logger.error(f"分析趋势背景异常: {e}")
            return "分析失败"
    
    async def _check_volume_confirmation(self, volumes: List[float]) -> bool:
        """检查成交量确认"""
        try:
            if not volumes or len(volumes) < 10:
                return False
            
            current_volume = volumes[-1]
            avg_volume = np.mean(volumes[-10:])
            
            # 当前成交量是否超过平均成交量的阈值倍数
            return current_volume >= avg_volume * self.volume_threshold
            
        except Exception as e:
            logger.error(f"检查成交量确认异常: {e}")
            return False
    
    async def _calculate_pin_quality(self, pin_type: PinBarType, body_ratio: float,
                                   upper_shadow_ratio: float, lower_shadow_ratio: float,
                                   trend_context: str, volume_confirmation: bool) -> float:
        """计算插针质量"""
        try:
            if pin_type == PinBarType.NO_PIN:
                return 0.0
            
            quality_score = 0.0
            
            # 基础质量评分（基于比例关系）
            if pin_type == PinBarType.BULLISH_PIN:
                # 看涨插针：下影线越长越好，实体越小越好
                quality_score += min(lower_shadow_ratio * 1.5, 1.0)  # 下影线贡献
                quality_score += max(0, (0.2 - body_ratio) * 5)      # 小实体贡献
                
            elif pin_type == PinBarType.BEARISH_PIN:
                # 看跌插针：上影线越长越好，实体越小越好
                quality_score += min(upper_shadow_ratio * 1.5, 1.0)  # 上影线贡献
                quality_score += max(0, (0.2 - body_ratio) * 5)      # 小实体贡献
                
            elif pin_type == PinBarType.DOJI_PIN:
                # 十字星：实体越小越好，影线平衡度
                quality_score += max(0, (0.05 - body_ratio) * 20)    # 极小实体
                balance = 1 - abs(upper_shadow_ratio - lower_shadow_ratio)
                quality_score += balance * 0.5                       # 影线平衡
            
            # 趋势背景加分
            if trend_context in ["上升趋势", "下降趋势"]:
                quality_score += 0.2
            elif trend_context == "横盘整理":
                quality_score += 0.1
            
            # 成交量确认加分
            if volume_confirmation:
                quality_score += 0.3
            
            # 标准化到0-1范围
            return min(quality_score, 1.0)
            
        except Exception as e:
            logger.error(f"计算插针质量异常: {e}")
            return 0.0
    
    async def _analyze_pin_signal(self, pin_type: PinBarType, pin_quality: float,
                                 trend_context: str, volume_confirmation: bool) -> Tuple[PinBarSignal, float]:
        """分析插针交易信号"""
        try:
            if pin_type == PinBarType.NO_PIN:
                return PinBarSignal.NEUTRAL, 0.0
            
            signal_score = 0.0
            
            # 基础信号强度（基于插针质量）
            signal_score = pin_quality
            
            # 趋势背景调整
            if pin_type == PinBarType.BULLISH_PIN:
                if trend_context == "下降趋势":
                    signal_score += 0.3  # 在下降趋势中的看涨插针更有意义
                elif trend_context == "横盘整理":
                    signal_score += 0.1
                    
            elif pin_type == PinBarType.BEARISH_PIN:
                if trend_context == "上升趋势":
                    signal_score += 0.3  # 在上升趋势中的看跌插针更有意义
                elif trend_context == "横盘整理":
                    signal_score += 0.1
            
            # 成交量确认调整
            if volume_confirmation:
                signal_score += 0.2
            
            # 标准化信号强度
            signal_strength = min(signal_score, 1.0)
            
            # 确定信号类型
            if pin_type == PinBarType.BULLISH_PIN:
                if signal_strength >= 0.7:
                    signal = PinBarSignal.STRONG_BULLISH
                elif signal_strength >= 0.4:
                    signal = PinBarSignal.WEAK_BULLISH
                else:
                    signal = PinBarSignal.NEUTRAL
                    
            elif pin_type == PinBarType.BEARISH_PIN:
                if signal_strength >= 0.7:
                    signal = PinBarSignal.STRONG_BEARISH
                elif signal_strength >= 0.4:
                    signal = PinBarSignal.WEAK_BEARISH
                else:
                    signal = PinBarSignal.NEUTRAL
                    
            else:  # DOJI_PIN
                if signal_strength >= 0.6:
                    signal = PinBarSignal.NEUTRAL  # 十字星通常表示不确定性
                else:
                    signal = PinBarSignal.NEUTRAL
            
            return signal, signal_strength
            
        except Exception as e:
            logger.error(f"分析插针信号异常: {e}")
            return PinBarSignal.NEUTRAL, 0.0
    
    def _calculate_reliability(self, data_count: int, required_count: int, pin_quality: float) -> float:
        """计算可靠性评分"""
        # 基于数据充足性的可靠性
        data_reliability = min(data_count / required_count, 1.0)
        
        # 基于插针质量的可靠性
        quality_reliability = pin_quality
        
        # 综合可靠性
        return (data_reliability * 0.3 + quality_reliability * 0.7)
    
    def detect_pinbar_signal(self, highs: List[float], lows: List[float],
                            opens: List[float], closes: List[float],
                            volumes: Optional[List[float]] = None) -> Tuple[bool, str, float]:
        """
        检测插针交易信号

        Args:
            highs: 最高价列表
            lows: 最低价列表
            opens: 开盘价列表
            closes: 收盘价列表
            volumes: 成交量列表（可选）

        Returns:
            Tuple[bool, str, float]: (是否有信号, 信号类型, 信号强度)
        """
        try:
            # 检查是否在事件循环中运行
            try:
                # 如果已经在事件循环中，直接使用同步计算
                current_loop = asyncio.get_running_loop()
                # 使用同步版本的计算
                pinbar_data = self._calculate_pinbar_sync(highs, lows, opens, closes, volumes)
            except RuntimeError:
                # 没有运行的事件循环，创建新的
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    pinbar_data = loop.run_until_complete(
                        self.calculate_pinbar(highs, lows, opens, closes, volumes)
                    )
                finally:
                    loop.close()

            if not pinbar_data:
                return False, "neutral", 0.0

            # 判断是否有明确信号
            if pinbar_data.signal in [PinBarSignal.STRONG_BULLISH, PinBarSignal.WEAK_BULLISH]:
                return True, "long", pinbar_data.signal_strength
            elif pinbar_data.signal in [PinBarSignal.STRONG_BEARISH, PinBarSignal.WEAK_BEARISH]:
                return True, "short", pinbar_data.signal_strength
            else:
                return False, "neutral", pinbar_data.signal_strength

        except Exception as e:
            logger.error(f"检测插针信号异常: {e}")
            return False, "neutral", 0.0

    def _calculate_pinbar_sync(self, highs: List[float], lows: List[float],
                              opens: List[float], closes: List[float],
                              volumes: Optional[List[float]] = None) -> Optional[PinBarData]:
        """同步版本的插针计算"""
        try:
            if len(highs) != len(lows) or len(highs) != len(opens) or len(highs) != len(closes):
                logger.error("OHLC价格数据长度不一致")
                return None

            required_count = self.get_required_data_count()
            if len(closes) < required_count:
                logger.warning(f"数据不足，需要至少{required_count}个数据点，当前{len(closes)}个")
                return None

            # 获取最新K线数据
            current_open = opens[-1]
            current_high = highs[-1]
            current_low = lows[-1]
            current_close = closes[-1]
            current_volume = volumes[-1] if volumes else None

            # 计算K线基本特征
            body_size = abs(current_close - current_open)
            upper_shadow = current_high - max(current_open, current_close)
            lower_shadow = min(current_open, current_close) - current_low
            total_range = current_high - current_low

            # 避免除零错误
            if total_range == 0:
                logger.warning("K线总区间为0，无法计算插针")
                return None

            # 计算比例关系
            body_ratio = body_size / total_range
            upper_shadow_ratio = upper_shadow / total_range
            lower_shadow_ratio = lower_shadow / total_range

            # 识别插针类型（同步版本）
            pin_type = self._identify_pin_type_sync(body_ratio, upper_shadow_ratio, lower_shadow_ratio)

            # 分析趋势背景（同步版本）
            trend_context = self._analyze_trend_context_sync(closes)

            # 成交量确认（同步版本）
            volume_confirmation = self._check_volume_confirmation_sync(volumes) if volumes else False

            # 计算插针质量（同步版本）
            pin_quality = self._calculate_pin_quality_sync(
                pin_type, body_ratio, upper_shadow_ratio, lower_shadow_ratio,
                trend_context, volume_confirmation
            )

            # 分析交易信号（同步版本）
            signal, signal_strength = self._analyze_pin_signal_sync(
                pin_type, pin_quality, trend_context, volume_confirmation
            )

            # 计算可靠性
            reliability = self._calculate_reliability(len(closes), required_count, pin_quality)

            result = PinBarData(
                pin_type=pin_type,
                signal=signal,
                signal_strength=round(signal_strength, 3),
                open_price=round(current_open, 4),
                high_price=round(current_high, 4),
                low_price=round(current_low, 4),
                close_price=round(current_close, 4),
                body_size=round(body_size, 4),
                upper_shadow=round(upper_shadow, 4),
                lower_shadow=round(lower_shadow, 4),
                total_range=round(total_range, 4),
                body_ratio=round(body_ratio, 3),
                upper_shadow_ratio=round(upper_shadow_ratio, 3),
                lower_shadow_ratio=round(lower_shadow_ratio, 3),
                pin_quality=round(pin_quality, 3),
                trend_context=trend_context,
                volume_confirmation=volume_confirmation,
                reliability=round(reliability, 3)
            )

            return result

        except Exception as e:
            logger.error(f"同步插针计算异常: {e}")
            return None

    def _identify_pin_type_sync(self, body_ratio: float, upper_shadow_ratio: float,
                               lower_shadow_ratio: float) -> PinBarType:
        """同步版本的插针类型识别"""
        try:
            # 看涨插针：长下影线，小实体，短上影线
            if (lower_shadow_ratio >= self.min_shadow_ratio and
                body_ratio <= self.min_body_ratio and
                upper_shadow_ratio <= body_ratio * 2):
                return PinBarType.BULLISH_PIN

            # 看跌插针：长上影线，小实体，短下影线
            elif (upper_shadow_ratio >= self.min_shadow_ratio and
                  body_ratio <= self.min_body_ratio and
                  lower_shadow_ratio <= body_ratio * 2):
                return PinBarType.BEARISH_PIN

            # 十字星插针：实体极小，上下影线都较长
            elif (body_ratio <= 0.05 and
                  upper_shadow_ratio >= 0.3 and
                  lower_shadow_ratio >= 0.3):
                return PinBarType.DOJI_PIN

            else:
                return PinBarType.NO_PIN

        except Exception as e:
            logger.error(f"同步识别插针类型异常: {e}")
            return PinBarType.NO_PIN

    def _analyze_trend_context_sync(self, closes: List[float]) -> str:
        """同步版本的趋势背景分析"""
        try:
            if len(closes) < 10:
                return "数据不足"

            # 计算短期和长期移动平均
            short_ma = np.mean(closes[-5:])
            long_ma = np.mean(closes[-20:]) if len(closes) >= 20 else np.mean(closes[-10:])
            current_price = closes[-1]

            # 判断趋势
            if current_price > short_ma > long_ma:
                return "上升趋势"
            elif current_price < short_ma < long_ma:
                return "下降趋势"
            elif abs(short_ma - long_ma) / long_ma < 0.02:  # 2%以内认为是横盘
                return "横盘整理"
            else:
                return "趋势不明"

        except Exception as e:
            logger.error(f"同步分析趋势背景异常: {e}")
            return "分析失败"

    def _check_volume_confirmation_sync(self, volumes: List[float]) -> bool:
        """同步版本的成交量确认"""
        try:
            if not volumes or len(volumes) < 10:
                return False

            current_volume = volumes[-1]
            avg_volume = np.mean(volumes[-10:])

            # 当前成交量是否超过平均成交量的阈值倍数
            return current_volume >= avg_volume * self.volume_threshold

        except Exception as e:
            logger.error(f"同步检查成交量确认异常: {e}")
            return False

    def _calculate_pin_quality_sync(self, pin_type: PinBarType, body_ratio: float,
                                   upper_shadow_ratio: float, lower_shadow_ratio: float,
                                   trend_context: str, volume_confirmation: bool) -> float:
        """同步版本的插针质量计算"""
        try:
            if pin_type == PinBarType.NO_PIN:
                return 0.0

            quality_score = 0.0

            # 基础质量评分（基于比例关系）
            if pin_type == PinBarType.BULLISH_PIN:
                # 看涨插针：下影线越长越好，实体越小越好
                quality_score += min(lower_shadow_ratio * 1.5, 1.0)  # 下影线贡献
                quality_score += max(0, (0.2 - body_ratio) * 5)      # 小实体贡献

            elif pin_type == PinBarType.BEARISH_PIN:
                # 看跌插针：上影线越长越好，实体越小越好
                quality_score += min(upper_shadow_ratio * 1.5, 1.0)  # 上影线贡献
                quality_score += max(0, (0.2 - body_ratio) * 5)      # 小实体贡献

            elif pin_type == PinBarType.DOJI_PIN:
                # 十字星：实体越小越好，影线平衡度
                quality_score += max(0, (0.05 - body_ratio) * 20)    # 极小实体
                balance = 1 - abs(upper_shadow_ratio - lower_shadow_ratio)
                quality_score += balance * 0.5                       # 影线平衡

            # 趋势背景加分
            if trend_context in ["上升趋势", "下降趋势"]:
                quality_score += 0.2
            elif trend_context == "横盘整理":
                quality_score += 0.1

            # 成交量确认加分
            if volume_confirmation:
                quality_score += 0.3

            # 标准化到0-1范围
            return min(quality_score, 1.0)

        except Exception as e:
            logger.error(f"同步计算插针质量异常: {e}")
            return 0.0

    def _analyze_pin_signal_sync(self, pin_type: PinBarType, pin_quality: float,
                                trend_context: str, volume_confirmation: bool) -> Tuple[PinBarSignal, float]:
        """同步版本的插针信号分析"""
        try:
            if pin_type == PinBarType.NO_PIN:
                return PinBarSignal.NEUTRAL, 0.0

            signal_score = 0.0

            # 基础信号强度（基于插针质量）
            signal_score = pin_quality

            # 趋势背景调整
            if pin_type == PinBarType.BULLISH_PIN:
                if trend_context == "下降趋势":
                    signal_score += 0.3  # 在下降趋势中的看涨插针更有意义
                elif trend_context == "横盘整理":
                    signal_score += 0.1

            elif pin_type == PinBarType.BEARISH_PIN:
                if trend_context == "上升趋势":
                    signal_score += 0.3  # 在上升趋势中的看跌插针更有意义
                elif trend_context == "横盘整理":
                    signal_score += 0.1

            # 成交量确认调整
            if volume_confirmation:
                signal_score += 0.2

            # 标准化信号强度
            signal_strength = min(signal_score, 1.0)

            # 确定信号类型
            if pin_type == PinBarType.BULLISH_PIN:
                if signal_strength >= 0.7:
                    signal = PinBarSignal.STRONG_BULLISH
                elif signal_strength >= 0.4:
                    signal = PinBarSignal.WEAK_BULLISH
                else:
                    signal = PinBarSignal.NEUTRAL

            elif pin_type == PinBarType.BEARISH_PIN:
                if signal_strength >= 0.7:
                    signal = PinBarSignal.STRONG_BEARISH
                elif signal_strength >= 0.4:
                    signal = PinBarSignal.WEAK_BEARISH
                else:
                    signal = PinBarSignal.NEUTRAL

            else:  # DOJI_PIN
                if signal_strength >= 0.6:
                    signal = PinBarSignal.NEUTRAL  # 十字星通常表示不确定性
                else:
                    signal = PinBarSignal.NEUTRAL

            return signal, signal_strength

        except Exception as e:
            logger.error(f"同步分析插针信号异常: {e}")
            return PinBarSignal.NEUTRAL, 0.0
