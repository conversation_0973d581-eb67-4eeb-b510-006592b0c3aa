<?php
/**
 * 调试页面 - 快速检查系统状态
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 定义常量
define('ROOT_PATH', __DIR__);
define('CONFIG_PATH', ROOT_PATH . '/config');
define('INCLUDES_PATH', ROOT_PATH . '/includes');

echo "<h1>BitV 系统调试信息</h1>";

// 检查基本文件
echo "<h2>1. 文件检查</h2>";
$files = [
    'config/config.php' => CONFIG_PATH . '/config.php',
    'includes/functions.php' => INCLUDES_PATH . '/functions.php',
    'includes/Database.php' => INCLUDES_PATH . '/Database.php',
    'includes/ApiClient.php' => INCLUDES_PATH . '/ApiClient.php',
    'includes/Session.php' => INCLUDES_PATH . '/Session.php',
    'includes/Router.php' => INCLUDES_PATH . '/Router.php'
];

foreach ($files as $name => $path) {
    $exists = file_exists($path);
    echo "<p>{$name}: " . ($exists ? '✅ 存在' : '❌ 不存在') . "</p>";
}

// 加载配置
if (file_exists(CONFIG_PATH . '/config.php')) {
    require_once CONFIG_PATH . '/config.php';
    echo "<h2>2. 配置加载</h2>";
    echo "<p>✅ 配置文件加载成功</p>";
    echo "<p>系统名称: " . (defined('SYSTEM_NAME') ? SYSTEM_NAME : '未定义') . "</p>";
    echo "<p>数据库主机: " . (defined('DB_HOST') ? DB_HOST : '未定义') . "</p>";
    echo "<p>Python API: " . (defined('PYTHON_API_BASE') ? PYTHON_API_BASE : '未定义') . "</p>";
} else {
    echo "<h2>2. 配置加载</h2>";
    echo "<p>❌ 配置文件不存在</p>";
    exit;
}

// 加载函数
if (file_exists(INCLUDES_PATH . '/functions.php')) {
    require_once INCLUDES_PATH . '/functions.php';
    echo "<h2>3. 函数检查</h2>";
    echo "<p>formatNumber 函数: " . (function_exists('formatNumber') ? '✅ 存在' : '❌ 不存在') . "</p>";
    if (function_exists('formatNumber')) {
        echo "<p>测试 formatNumber(123.456789, 2): " . formatNumber(123.456789, 2) . "</p>";
    }
} else {
    echo "<h2>3. 函数检查</h2>";
    echo "<p>❌ 函数文件不存在</p>";
}

// 测试数据库连接
echo "<h2>4. 数据库连接</h2>";
try {
    require_once INCLUDES_PATH . '/Database.php';
    $db = Database::getInstance();
    $result = $db->fetch('SELECT 1 as test');
    if ($result && $result['test'] == 1) {
        echo "<p>✅ 数据库连接成功</p>";
        
        // 检查表
        $tables = ['user_configs', 'trading_sessions', 'trading_records', 'position_monitors', 'system_logs'];
        echo "<h3>数据库表检查:</h3>";
        foreach ($tables as $table) {
            $exists = $db->fetch("SHOW TABLES LIKE '{$table}'");
            echo "<p>{$table}: " . ($exists ? '✅ 存在' : '❌ 不存在') . "</p>";
        }
    } else {
        echo "<p>❌ 数据库查询失败</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ 数据库连接失败: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// 测试API连接
echo "<h2>5. Python API连接</h2>";
try {
    require_once INCLUDES_PATH . '/ApiClient.php';
    $api = ApiClient::getInstance();
    $healthy = $api->healthCheck();
    echo "<p>API健康检查: " . ($healthy ? '✅ 正常' : '❌ 失败') . "</p>";
    echo "<p>API地址: " . PYTHON_API_BASE . "</p>";
} catch (Exception $e) {
    echo "<p>❌ API连接失败: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// 测试会话
echo "<h2>6. 会话系统</h2>";
try {
    require_once INCLUDES_PATH . '/Session.php';
    Session::start();
    Session::set('test_key', 'test_value');
    $value = Session::get('test_key');
    echo "<p>会话读写测试: " . ($value === 'test_value' ? '✅ 正常' : '❌ 失败') . "</p>";
    Session::remove('test_key');
} catch (Exception $e) {
    echo "<p>❌ 会话系统失败: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// PHP环境信息
echo "<h2>7. PHP环境</h2>";
echo "<p>PHP版本: " . PHP_VERSION . "</p>";
echo "<p>扩展检查:</p>";
$extensions = ['pdo', 'pdo_mysql', 'curl', 'json', 'openssl'];
foreach ($extensions as $ext) {
    echo "<p>{$ext}: " . (extension_loaded($ext) ? '✅ 已加载' : '❌ 未加载') . "</p>";
}

echo "<h2>8. 文件权限</h2>";
$paths = [
    'config目录' => CONFIG_PATH,
    'logs目录' => ROOT_PATH . '/logs',
    'assets目录' => ROOT_PATH . '/assets'
];

foreach ($paths as $name => $path) {
    if (!file_exists($path)) {
        echo "<p>{$name}: ❌ 不存在</p>";
        continue;
    }
    
    $readable = is_readable($path);
    $writable = is_writable($path);
    echo "<p>{$name}: 读取(" . ($readable ? '✅' : '❌') . ") 写入(" . ($writable ? '✅' : '❌') . ")</p>";
}

echo "<hr>";
echo "<p><a href='index.php'>返回首页</a> | <a href='install.php'>重新安装</a> | <a href='test.php'>完整测试</a></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1 { color: #333; }
h2 { color: #666; border-bottom: 1px solid #ccc; }
p { margin: 5px 0; }
</style>
