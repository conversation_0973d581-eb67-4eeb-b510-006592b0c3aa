<template>
  <div class="rsi-strategy">
    <div class="page-header">
      <h2>RSI策略配置</h2>
      <p>配置相对强弱指数(RSI)技术指标参数和交易策略</p>
    </div>

    <div class="card">
      <h3 class="mb-md">RSI策略开发中...</h3>
      <el-empty description="RSI策略配置页面正在开发中">
        <template #image>
          <el-icon size="60" color="#c0c4cc"><DataAnalysis /></el-icon>
        </template>
      </el-empty>
    </div>
  </div>
</template>

<script setup>
import { DataAnalysis } from '@element-plus/icons-vue'
</script>

<style lang="scss" scoped>
.rsi-strategy {
  .page-header {
    margin-bottom: var(--spacing-lg);
    
    h2 {
      margin-bottom: var(--spacing-xs);
      color: var(--text-color-primary);
    }
    
    p {
      color: var(--text-color-secondary);
    }
  }
}
</style>
