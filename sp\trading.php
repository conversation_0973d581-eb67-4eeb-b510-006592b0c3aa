<?php
/**
 * BitV MACD智能加仓交易系统 - 智能交易页面
 * 实盘交易版本 - 完整的交易控制界面
 */

session_start();

// 加载配置
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/security.php';

// 检查认证
requireAuth();
requirePermission('trade');

$pageTitle = '智能交易';
$currentPage = 'trading';

$success = '';
$error = '';

// 处理交易请求
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // 验证CSRF令牌
        if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
            throw new Exception('安全验证失败，请刷新页面重试');
        }
        
        $action = $_POST['action'] ?? '';
        
        if ($action === 'start_trading') {
            // 启动交易
            $config = [
                'symbol' => sanitizeInput($_POST['symbol'] ?? ''),
                'strategy' => sanitizeInput($_POST['strategy'] ?? 'macd'),
                'leverage' => (int)($_POST['leverage'] ?? 10),
                'initial_margin' => (float)($_POST['initial_margin'] ?? 100),
                'max_add_times' => (int)($_POST['max_add_times'] ?? 3),
                'stop_loss_ratio' => (float)($_POST['stop_loss_ratio'] ?? 0.1),
                'take_profit_ratio' => (float)($_POST['take_profit_ratio'] ?? 0.2),
                'macd_fast' => (int)($_POST['macd_fast'] ?? 12),
                'macd_slow' => (int)($_POST['macd_slow'] ?? 26),
                'macd_signal' => (int)($_POST['macd_signal'] ?? 9),
                'risk_level' => sanitizeInput($_POST['risk_level'] ?? 'moderate'),
                'auto_add_position' => isset($_POST['auto_add_position']),
                'emergency_stop_loss' => (float)($_POST['emergency_stop_loss'] ?? 0.05),
                'position_size_ratio' => (float)($_POST['position_size_ratio'] ?? 0.5)
            ];
            
            $result = startTrading($config);
            
            if ($result['success']) {
                $success = '交易启动成功！会话ID: ' . $result['data']['session_id'];
                
                // 记录操作日志
                logUserAction('START_TRADING', [
                    'config' => $config,
                    'session_id' => $result['data']['session_id']
                ]);
            } else {
                throw new Exception($result['error'] ?? '启动交易失败');
            }
            
        } elseif ($action === 'stop_trading') {
            // 停止交易
            $session_id = sanitizeInput($_POST['session_id'] ?? '');
            $emergency = isset($_POST['emergency']);
            
            $result = stopTrading($session_id, $emergency);
            
            if ($result['success']) {
                $success = '交易停止成功！';
                
                // 记录操作日志
                logUserAction('STOP_TRADING', [
                    'session_id' => $session_id,
                    'emergency' => $emergency
                ]);
            } else {
                throw new Exception($result['error'] ?? '停止交易失败');
            }
            
        } elseif ($action === 'emergency_stop_all') {
            // 紧急停止所有交易
            $result = emergencyStopAll();
            
            if ($result['success']) {
                $success = '紧急停止执行成功！';
                
                // 记录操作日志
                logUserAction('EMERGENCY_STOP_ALL', [
                    'timestamp' => date('Y-m-d H:i:s')
                ]);
            } else {
                throw new Exception($result['error'] ?? '紧急停止失败');
            }
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
        
        // 记录错误日志
        logUserAction('TRADING_ACTION_FAILED', [
            'action' => $action ?? 'unknown',
            'error' => $error
        ]);
    }
}

// 获取系统状态和数据
$systemStatus = getSystemStatus();
$marketPrices = getMarketPrices();
$tradingSessions = getTradingSessions();
$activePositions = getActivePositions();

include 'includes/header.php';
?>

<div class="container-fluid">
    <!-- 页面标题和控制按钮 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">
            <i class="fas fa-robot text-primary me-2"></i>
            智能交易控制台
        </h1>
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-info" onclick="refreshAllData()">
                <i class="fas fa-sync-alt me-2"></i>
                刷新数据
            </button>
            <button type="button" class="btn btn-outline-warning" onclick="showStrategyBacktest()">
                <i class="fas fa-chart-line me-2"></i>
                策略回测
            </button>
            <button type="button" class="btn btn-danger" onclick="showEmergencyStop()">
                <i class="fas fa-stop-circle me-2"></i>
                紧急停止
            </button>
        </div>
    </div>

    <!-- 成功/错误消息 -->
    <?php if ($success): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>
        <?php echo htmlspecialchars($success); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <?php echo htmlspecialchars($error); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <!-- 系统状态检查 -->
    <?php if (!$systemStatus['success'] || !$systemStatus['data']['exchange_connected']): ?>
    <div class="alert alert-warning alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <strong>系统未就绪：</strong>
        <?php if (!$systemStatus['success']): ?>
            交易引擎未连接，请检查后端服务。
        <?php elseif (!$systemStatus['data']['exchange_connected']): ?>
            交易所未连接，请先在<a href="settings.php" class="alert-link">系统设置</a>中配置交易所API。
        <?php endif; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <div class="row">
        <!-- 左侧：交易配置和控制 -->
        <div class="col-xl-8 col-lg-7">
            <!-- 交易策略配置 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-cogs me-2"></i>
                        MACD智能交易策略配置
                    </h6>
                    <div class="dropdown no-arrow">
                        <a class="dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end shadow">
                            <div class="dropdown-header">策略模板:</div>
                            <a class="dropdown-item" href="#" onclick="loadStrategyTemplate('conservative')">
                                <i class="fas fa-shield-alt fa-sm fa-fw mr-2 text-gray-400"></i>
                                保守策略
                            </a>
                            <a class="dropdown-item" href="#" onclick="loadStrategyTemplate('balanced')">
                                <i class="fas fa-balance-scale fa-sm fa-fw mr-2 text-gray-400"></i>
                                平衡策略
                            </a>
                            <a class="dropdown-item" href="#" onclick="loadStrategyTemplate('aggressive')">
                                <i class="fas fa-fire fa-sm fa-fw mr-2 text-gray-400"></i>
                                激进策略
                            </a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item" href="#" onclick="saveCurrentStrategy()">
                                <i class="fas fa-save fa-sm fa-fw mr-2 text-gray-400"></i>
                                保存当前配置
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <form id="tradingConfigForm" method="POST" action="">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="action" value="start_trading">
                        
                        <!-- 基础配置 -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-chart-line me-2"></i>
                                    基础交易配置
                                </h6>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="symbol" class="form-label">
                                    <i class="fas fa-coins me-2"></i>
                                    交易对 <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="symbol" name="symbol" required>
                                    <option value="">请选择交易对</option>
                                    <?php foreach (SUPPORTED_SYMBOLS as $symbol => $info): ?>
                                    <option value="<?php echo $symbol; ?>" 
                                            data-min-size="<?php echo $info['min_size']; ?>"
                                            data-tick-size="<?php echo $info['tick_size']; ?>">
                                        <?php echo htmlspecialchars($info['display_name']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="form-text">
                                    选择要交易的数字货币合约
                                </div>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="leverage" class="form-label">
                                    <i class="fas fa-expand-arrows-alt me-2"></i>
                                    杠杆倍数 <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="leverage" name="leverage" required>
                                    <option value="1">1x (无杠杆)</option>
                                    <option value="2">2x</option>
                                    <option value="3">3x</option>
                                    <option value="5">5x</option>
                                    <option value="10" selected>10x</option>
                                    <option value="20">20x</option>
                                    <option value="50">50x</option>
                                    <option value="100">100x (极高风险)</option>
                                </select>
                                <div class="form-text">
                                    <span class="text-warning">
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        杠杆越高风险越大
                                    </span>
                                </div>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="initial_margin" class="form-label">
                                    <i class="fas fa-dollar-sign me-2"></i>
                                    初始保证金 (USDT) <span class="text-danger">*</span>
                                </label>
                                <input type="number" class="form-control" id="initial_margin" name="initial_margin" 
                                       value="100" min="10" max="10000" step="10" required>
                                <div class="form-text">
                                    单次交易的初始保证金金额
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label for="max_add_times" class="form-label">
                                    <i class="fas fa-plus-circle me-2"></i>
                                    最大加仓次数
                                </label>
                                <select class="form-select" id="max_add_times" name="max_add_times">
                                    <option value="0">不加仓</option>
                                    <option value="1">1次</option>
                                    <option value="2">2次</option>
                                    <option value="3" selected>3次</option>
                                    <option value="5">5次</option>
                                    <option value="10">10次 (高风险)</option>
                                </select>
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <label for="stop_loss_ratio" class="form-label">
                                    <i class="fas fa-shield-alt me-2"></i>
                                    止损比例 (%)
                                </label>
                                <input type="number" class="form-control" id="stop_loss_ratio" name="stop_loss_ratio" 
                                       value="10" min="1" max="50" step="0.5">
                                <div class="form-text">
                                    亏损达到此比例时自动止损
                                </div>
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <label for="take_profit_ratio" class="form-label">
                                    <i class="fas fa-bullseye me-2"></i>
                                    止盈比例 (%)
                                </label>
                                <input type="number" class="form-control" id="take_profit_ratio" name="take_profit_ratio" 
                                       value="20" min="1" max="100" step="0.5">
                                <div class="form-text">
                                    盈利达到此比例时自动止盈
                                </div>
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <label for="risk_level" class="form-label">
                                    <i class="fas fa-thermometer-half me-2"></i>
                                    风险等级
                                </label>
                                <select class="form-select" id="risk_level" name="risk_level">
                                    <option value="conservative">保守</option>
                                    <option value="moderate" selected>适中</option>
                                    <option value="aggressive">激进</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- MACD策略参数 -->
                        <div class="row mb-4 mt-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-chart-bar me-2"></i>
                                    MACD策略参数
                                </h6>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="macd_fast" class="form-label">
                                    <i class="fas fa-tachometer-alt me-2"></i>
                                    MACD快线周期
                                </label>
                                <input type="number" class="form-control" id="macd_fast" name="macd_fast" 
                                       value="12" min="5" max="50">
                                <div class="form-text">
                                    快速EMA的计算周期
                                </div>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="macd_slow" class="form-label">
                                    <i class="fas fa-turtle me-2"></i>
                                    MACD慢线周期
                                </label>
                                <input type="number" class="form-control" id="macd_slow" name="macd_slow" 
                                       value="26" min="10" max="100">
                                <div class="form-text">
                                    慢速EMA的计算周期
                                </div>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="macd_signal" class="form-label">
                                    <i class="fas fa-signal me-2"></i>
                                    MACD信号线周期
                                </label>
                                <input type="number" class="form-control" id="macd_signal" name="macd_signal" 
                                       value="9" min="3" max="30">
                                <div class="form-text">
                                    信号线EMA的计算周期
                                </div>
                            </div>
                        </div>
                        
                        <!-- 高级配置 -->
                        <div class="row mb-4 mt-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-cog me-2"></i>
                                    高级配置选项
                                </h6>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="emergency_stop_loss" class="form-label">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    紧急止损比例 (%)
                                </label>
                                <input type="number" class="form-control" id="emergency_stop_loss" name="emergency_stop_loss" 
                                       value="5" min="1" max="20" step="0.1">
                                <div class="form-text">
                                    距离强平价格的紧急止损阈值
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="position_size_ratio" class="form-label">
                                    <i class="fas fa-percentage me-2"></i>
                                    仓位大小比例 (%)
                                </label>
                                <input type="number" class="form-control" id="position_size_ratio" name="position_size_ratio" 
                                       value="50" min="10" max="100" step="5">
                                <div class="form-text">
                                    相对于账户总资金的仓位比例
                                </div>
                            </div>
                        </div>
                        
                        <!-- 策略选项 -->
                        <div class="row">
                            <div class="col-12 mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="auto_add_position" name="auto_add_position" checked>
                                    <label class="form-check-label" for="auto_add_position">
                                        <i class="fas fa-robot me-2"></i>
                                        启用自动加仓功能
                                    </label>
                                </div>
                                <div class="form-text">
                                    根据MACD信号和亏损情况自动执行加仓操作
                                </div>
                            </div>
                        </div>
                        
                        <!-- 风险提示 -->
                        <div class="alert alert-danger mt-4">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>重要风险提示：</h6>
                            <ul class="mb-0">
                                <li><strong>实盘交易风险：</strong>这是真实的资金交易，可能导致重大损失</li>
                                <li><strong>杠杆风险：</strong>杠杆交易会放大盈亏，请谨慎设置杠杆倍数</li>
                                <li><strong>市场风险：</strong>数字货币市场波动极大，价格可能急剧变化</li>
                                <li><strong>技术风险：</strong>网络延迟、系统故障等可能影响交易执行</li>
                                <li><strong>策略风险：</strong>任何交易策略都不能保证盈利</li>
                            </ul>
                        </div>
                        
                        <!-- 确认和提交 -->
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="risk_acknowledge" required>
                            <label class="form-check-label fw-bold" for="risk_acknowledge">
                                我已充分了解上述风险，并愿意承担相应后果
                            </label>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="button" class="btn btn-outline-secondary me-md-2" onclick="resetForm()">
                                <i class="fas fa-undo me-2"></i>
                                重置配置
                            </button>
                            <button type="button" class="btn btn-info me-md-2" onclick="validateStrategy()">
                                <i class="fas fa-check-circle me-2"></i>
                                验证策略
                            </button>
                            <button type="submit" class="btn btn-success btn-lg" 
                                    <?php echo (!$systemStatus['success'] || !$systemStatus['data']['exchange_connected']) ? 'disabled' : ''; ?>>
                                <i class="fas fa-rocket me-2"></i>
                                启动智能交易
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 右侧：实时监控和状态 -->
        <div class="col-xl-4 col-lg-5">
            <!-- 实时价格监控 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-line me-2"></i>
                        实时价格监控
                    </h6>
                </div>
                <div class="card-body">
                    <div id="priceMonitorContainer">
                        <div class="text-center py-3">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <div class="mt-2">正在获取实时价格...</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 活跃交易会话 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-cogs me-2"></i>
                        活跃交易会话
                    </h6>
                    <span class="badge bg-primary" id="sessionCount">
                        <?php echo $tradingSessions['success'] ? count($tradingSessions['data']['sessions']) : 0; ?>
                    </span>
                </div>
                <div class="card-body">
                    <div id="activeSessionsContainer">
                        <?php if ($tradingSessions['success'] && !empty($tradingSessions['data']['sessions'])): ?>
                            <?php foreach ($tradingSessions['data']['sessions'] as $session): ?>
                                <div class="session-item mb-3 p-3 border rounded" data-session-id="<?php echo $session['session_id']; ?>">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">
                                                <?php echo htmlspecialchars($session['symbol']); ?>
                                                <span class="badge bg-<?php echo getStatusColor($session['status']); ?> ms-2">
                                                    <?php echo getStatusText($session['status']); ?>
                                                </span>
                                            </h6>
                                            <div class="small text-muted mb-2">
                                                <div>杠杆: <?php echo $session['leverage']; ?>x | 
                                                     保证金: <?php echo formatNumber($session['initial_margin'], 2); ?> USDT</div>
                                                <div>加仓: <?php echo $session['add_times']; ?>/<?php echo $session['max_add_times']; ?> | 
                                                     盈亏: <span class="<?php echo $session['total_pnl'] >= 0 ? 'text-success' : 'text-danger'; ?>">
                                                         <?php echo formatNumber($session['total_pnl'], 2); ?> USDT
                                                     </span>
                                                </div>
                                                <div>开始: <?php echo formatTime($session['start_time'], 'H:i:s'); ?></div>
                                            </div>
                                        </div>
                                        <div class="btn-group-vertical btn-group-sm">
                                            <button type="button" class="btn btn-outline-info btn-sm" 
                                                    onclick="viewSessionDetails('<?php echo $session['session_id']; ?>')">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-warning btn-sm" 
                                                    onclick="pauseSession('<?php echo $session['session_id']; ?>')"
                                                    <?php echo $session['status'] !== 'active' ? 'disabled' : ''; ?>>
                                                <i class="fas fa-pause"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-danger btn-sm" 
                                                    onclick="stopSession('<?php echo $session['session_id']; ?>')"
                                                    <?php echo $session['status'] !== 'active' ? 'disabled' : ''; ?>>
                                                <i class="fas fa-stop"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-robot fa-3x text-muted mb-3"></i>
                                <h6>暂无活跃交易会话</h6>
                                <p class="text-muted">配置策略参数后启动智能交易</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- 风险监控 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-shield-alt me-2"></i>
                        风险监控面板
                    </h6>
                </div>
                <div class="card-body">
                    <div id="riskMonitorContainer">
                        <div class="row g-0 text-center">
                            <div class="col-6">
                                <div class="p-3 border-end">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        总持仓风险
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold" id="totalRiskLevel">
                                        <span class="badge bg-success">安全</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        账户余额
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold" id="accountBalance">
                                        <span class="text-muted">加载中...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <hr class="my-3">
                        
                        <div class="risk-alerts" id="riskAlerts">
                            <div class="alert alert-info alert-sm">
                                <i class="fas fa-info-circle me-2"></i>
                                系统正在监控风险状态...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 交易会话详情模态框 -->
<div class="modal fade" id="sessionDetailsModal" tabindex="-1" aria-labelledby="sessionDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="sessionDetailsModalLabel">
                    <i class="fas fa-chart-line me-2"></i>
                    交易会话详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="sessionDetailsContent">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <div class="mt-2">正在加载会话详情...</div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="exportSessionData()">
                    <i class="fas fa-download me-2"></i>
                    导出数据
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 策略回测模态框 -->
<div class="modal fade" id="strategyBacktestModal" tabindex="-1" aria-labelledby="strategyBacktestModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="strategyBacktestModalLabel">
                    <i class="fas fa-chart-bar me-2"></i>
                    策略回测分析
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-4">
                        <h6>回测参数</h6>
                        <form id="backtestForm">
                            <div class="mb-3">
                                <label for="backtest_symbol" class="form-label">交易对</label>
                                <select class="form-select" id="backtest_symbol" name="backtest_symbol">
                                    <?php foreach (SUPPORTED_SYMBOLS as $symbol => $info): ?>
                                    <option value="<?php echo $symbol; ?>">
                                        <?php echo htmlspecialchars($info['display_name']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="backtest_period" class="form-label">回测周期</label>
                                <select class="form-select" id="backtest_period" name="backtest_period">
                                    <option value="7d">最近7天</option>
                                    <option value="30d" selected>最近30天</option>
                                    <option value="90d">最近90天</option>
                                    <option value="1y">最近1年</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="backtest_initial_capital" class="form-label">初始资金 (USDT)</label>
                                <input type="number" class="form-control" id="backtest_initial_capital"
                                       value="10000" min="1000" max="1000000" step="1000">
                            </div>
                            <button type="button" class="btn btn-primary" onclick="runBacktest()">
                                <i class="fas fa-play me-2"></i>
                                开始回测
                            </button>
                        </form>
                    </div>
                    <div class="col-md-8">
                        <div id="backtestResults">
                            <div class="text-center py-5">
                                <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                                <h6>策略回测结果</h6>
                                <p class="text-muted">配置参数后点击"开始回测"查看历史表现</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 紧急停止确认模态框 -->
<div class="modal fade" id="emergencyStopModal" tabindex="-1" aria-labelledby="emergencyStopModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="emergencyStopModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    紧急停止确认
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <h6><i class="fas fa-warning me-2"></i>警告：</h6>
                    <p class="mb-0">此操作将立即停止所有活跃的交易会话并尝试平仓所有持仓！</p>
                </div>
                <p>确认执行紧急停止操作吗？此操作不可撤销。</p>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="emergencyStopConfirm">
                    <label class="form-check-label fw-bold" for="emergencyStopConfirm">
                        我确认要执行紧急停止操作
                    </label>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmEmergencyStopBtn" disabled
                        onclick="executeEmergencyStop()">
                    <i class="fas fa-stop-circle me-2"></i>
                    确认紧急停止
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// 全局变量
let priceUpdateInterval = null;
let sessionUpdateInterval = null;
let riskUpdateInterval = null;
let websocketConnection = null;

// 页面加载完成后执行
$(document).ready(function() {
    // 初始化页面
    initializePage();

    // 启动实时更新
    startRealTimeUpdates();

    // 绑定事件
    bindEvents();

    // 加载初始数据
    loadInitialData();
});

// 初始化页面
function initializePage() {
    // 设置表单验证
    setupFormValidation();

    // 初始化WebSocket连接
    initializeWebSocket();

    // 设置策略模板
    setupStrategyTemplates();

    // 检查系统状态
    checkSystemStatus();
}

// 绑定事件
function bindEvents() {
    // 交易对选择变化
    $('#symbol').on('change', function() {
        updateSymbolInfo($(this).val());
    });

    // 杠杆变化时更新风险提示
    $('#leverage').on('change', function() {
        updateRiskWarning();
    });

    // 保证金变化时更新计算
    $('#initial_margin').on('input', function() {
        updatePositionCalculation();
    });

    // 风险确认复选框
    $('#risk_acknowledge').on('change', function() {
        updateSubmitButton();
    });

    // 紧急停止确认复选框
    $('#emergencyStopConfirm').on('change', function() {
        $('#confirmEmergencyStopBtn').prop('disabled', !this.checked);
    });

    // 表单提交
    $('#tradingConfigForm').on('submit', function(e) {
        e.preventDefault();
        if (validateTradingForm()) {
            submitTradingForm();
        }
    });
}

// 设置表单验证
function setupFormValidation() {
    // 实时验证规则
    $('#initial_margin').on('input', function() {
        const value = parseFloat($(this).val());
        const min = parseFloat($(this).attr('min'));
        const max = parseFloat($(this).attr('max'));

        if (value < min || value > max) {
            $(this).addClass('is-invalid');
            $(this).siblings('.invalid-feedback').remove();
            $(this).after('<div class="invalid-feedback">保证金必须在 ' + min + ' - ' + max + ' USDT 之间</div>');
        } else {
            $(this).removeClass('is-invalid');
            $(this).siblings('.invalid-feedback').remove();
        }
    });

    // MACD参数验证
    $('#macd_fast, #macd_slow, #macd_signal').on('input', function() {
        validateMACDParameters();
    });
}

// 验证MACD参数
function validateMACDParameters() {
    const fast = parseInt($('#macd_fast').val());
    const slow = parseInt($('#macd_slow').val());
    const signal = parseInt($('#macd_signal').val());

    let isValid = true;

    // 快线必须小于慢线
    if (fast >= slow) {
        $('#macd_fast').addClass('is-invalid');
        $('#macd_slow').addClass('is-invalid');
        isValid = false;
    } else {
        $('#macd_fast').removeClass('is-invalid');
        $('#macd_slow').removeClass('is-invalid');
    }

    // 信号线周期合理性检查
    if (signal >= fast) {
        $('#macd_signal').addClass('is-invalid');
        isValid = false;
    } else {
        $('#macd_signal').removeClass('is-invalid');
    }

    return isValid;
}

// 启动实时更新
function startRealTimeUpdates() {
    // 价格更新 - 每5秒
    priceUpdateInterval = setInterval(updatePrices, 5000);

    // 会话状态更新 - 每10秒
    sessionUpdateInterval = setInterval(updateActiveSessions, 10000);

    // 风险监控更新 - 每15秒
    riskUpdateInterval = setInterval(updateRiskMonitor, 15000);

    // 立即执行一次更新
    updatePrices();
    updateActiveSessions();
    updateRiskMonitor();
}

// 初始化WebSocket连接
function initializeWebSocket() {
    try {
        const wsUrl = window.BITV.WS_URL || 'ws://localhost:8000/ws';
        websocketConnection = new WebSocket(wsUrl);

        websocketConnection.onopen = function(event) {
            console.log('WebSocket连接已建立');
            showNotification('实时数据连接已建立', 'success');
        };

        websocketConnection.onmessage = function(event) {
            try {
                const data = JSON.parse(event.data);
                handleWebSocketMessage(data);
            } catch (e) {
                console.error('WebSocket消息解析失败:', e);
            }
        };

        websocketConnection.onclose = function(event) {
            console.log('WebSocket连接已关闭');
            showNotification('实时数据连接已断开，正在重连...', 'warning');

            // 5秒后尝试重连
            setTimeout(initializeWebSocket, 5000);
        };

        websocketConnection.onerror = function(error) {
            console.error('WebSocket连接错误:', error);
        };

    } catch (e) {
        console.error('WebSocket初始化失败:', e);
    }
}

// 处理WebSocket消息
function handleWebSocketMessage(data) {
    switch (data.type) {
        case 'price_update':
            updatePriceDisplay(data.data);
            break;
        case 'session_update':
            updateSessionDisplay(data.data);
            break;
        case 'risk_alert':
            showRiskAlert(data.data);
            break;
        case 'trade_signal':
            showTradeSignal(data.data);
            break;
        case 'system_notification':
            showNotification(data.data.message, data.data.type);
            break;
        default:
            console.log('未知WebSocket消息类型:', data.type);
    }
}

// 更新价格显示
function updatePrices() {
    fetch(window.BITV.API_BASE + '/api/market/prices', {
        headers: {
            'Authorization': 'Bearer ' + window.BITV.API_TOKEN
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayPrices(data.data);
        }
    })
    .catch(error => {
        console.error('获取价格数据失败:', error);
    });
}

// 显示价格数据
function displayPrices(pricesData) {
    const container = document.getElementById('priceMonitorContainer');

    if (!pricesData || pricesData.length === 0) {
        container.innerHTML = `
            <div class="text-center py-3">
                <i class="fas fa-exclamation-triangle text-warning"></i>
                <div class="mt-2">暂无价格数据</div>
            </div>
        `;
        return;
    }

    let html = '';
    pricesData.forEach(price => {
        const changeClass = price.change_24h >= 0 ? 'text-success' : 'text-danger';
        const changeIcon = price.change_24h >= 0 ? 'fa-arrow-up' : 'fa-arrow-down';

        html += `
            <div class="price-item mb-3 p-3 border rounded" data-symbol="${price.symbol}">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-1">${price.symbol}</h6>
                        <div class="h5 mb-0 font-weight-bold">
                            $${formatNumber(price.price, 2)}
                        </div>
                    </div>
                    <div class="text-end">
                        <div class="${changeClass}">
                            <i class="fas ${changeIcon} me-1"></i>
                            ${formatPercentage(price.percentage_24h)}
                        </div>
                        <div class="small text-muted">
                            24h: $${formatNumber(price.change_24h, 2)}
                        </div>
                    </div>
                </div>
                <div class="row mt-2 small text-muted">
                    <div class="col-4">
                        <div>高: $${formatNumber(price.high_24h, 2)}</div>
                    </div>
                    <div class="col-4">
                        <div>低: $${formatNumber(price.low_24h, 2)}</div>
                    </div>
                    <div class="col-4">
                        <div>量: ${formatNumber(price.volume_24h, 0)}</div>
                    </div>
                </div>
            </div>
        `;
    });

    container.innerHTML = html;
}

// 更新活跃会话
function updateActiveSessions() {
    fetch(window.BITV.API_BASE + '/api/trading/sessions', {
        headers: {
            'Authorization': 'Bearer ' + window.BITV.API_TOKEN
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayActiveSessions(data.data);
        }
    })
    .catch(error => {
        console.error('获取交易会话失败:', error);
    });
}

// 显示活跃会话
function displayActiveSessions(sessionsData) {
    const container = document.getElementById('activeSessionsContainer');
    const countElement = document.getElementById('sessionCount');

    if (!sessionsData.sessions || sessionsData.sessions.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-robot fa-3x text-muted mb-3"></i>
                <h6>暂无活跃交易会话</h6>
                <p class="text-muted">配置策略参数后启动智能交易</p>
            </div>
        `;
        countElement.textContent = '0';
        return;
    }

    countElement.textContent = sessionsData.sessions.length;

    let html = '';
    sessionsData.sessions.forEach(session => {
        const statusColor = getStatusColor(session.status);
        const statusText = getStatusText(session.status);
        const pnlClass = session.total_pnl >= 0 ? 'text-success' : 'text-danger';

        html += `
            <div class="session-item mb-3 p-3 border rounded" data-session-id="${session.session_id}">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <h6 class="mb-1">
                            ${session.symbol}
                            <span class="badge bg-${statusColor} ms-2">${statusText}</span>
                        </h6>
                        <div class="small text-muted mb-2">
                            <div>杠杆: ${session.leverage}x | 保证金: ${formatNumber(session.initial_margin, 2)} USDT</div>
                            <div>加仓: ${session.add_times}/${session.max_add_times} |
                                 盈亏: <span class="${pnlClass}">${formatNumber(session.total_pnl, 2)} USDT</span>
                            </div>
                            <div>开始: ${formatTime(session.start_time)}</div>
                        </div>
                    </div>
                    <div class="btn-group-vertical btn-group-sm">
                        <button type="button" class="btn btn-outline-info btn-sm"
                                onclick="viewSessionDetails('${session.session_id}')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button type="button" class="btn btn-outline-warning btn-sm"
                                onclick="pauseSession('${session.session_id}')"
                                ${session.status !== 'active' ? 'disabled' : ''}>
                            <i class="fas fa-pause"></i>
                        </button>
                        <button type="button" class="btn btn-outline-danger btn-sm"
                                onclick="stopSession('${session.session_id}')"
                                ${session.status !== 'active' ? 'disabled' : ''}>
                            <i class="fas fa-stop"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
    });

    container.innerHTML = html;
}

// 更新风险监控
function updateRiskMonitor() {
    fetch(window.BITV.API_BASE + '/api/risk/monitor', {
        headers: {
            'Authorization': 'Bearer ' + window.BITV.API_TOKEN
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayRiskMonitor(data.data);
        }
    })
    .catch(error => {
        console.error('获取风险监控数据失败:', error);
    });
}

// 显示风险监控
function displayRiskMonitor(riskData) {
    // 更新总风险等级
    const riskLevelElement = document.getElementById('totalRiskLevel');
    const riskColor = getRiskLevelColor(riskData.overall_risk_level);
    const riskText = getRiskLevelText(riskData.overall_risk_level);

    riskLevelElement.innerHTML = `<span class="badge bg-${riskColor}">${riskText}</span>`;

    // 更新账户余额
    const balanceElement = document.getElementById('accountBalance');
    if (riskData.account_balance) {
        balanceElement.innerHTML = `${formatNumber(riskData.account_balance.total, 2)} USDT`;
    }

    // 更新风险警报
    const alertsContainer = document.getElementById('riskAlerts');
    let alertsHtml = '';

    if (riskData.alerts && riskData.alerts.length > 0) {
        riskData.alerts.forEach(alert => {
            const alertClass = getAlertClass(alert.level);
            alertsHtml += `
                <div class="alert ${alertClass} alert-sm">
                    <i class="fas fa-${getAlertIcon(alert.level)} me-2"></i>
                    ${alert.message}
                </div>
            `;
        });
    } else {
        alertsHtml = `
            <div class="alert alert-success alert-sm">
                <i class="fas fa-check-circle me-2"></i>
                当前风险状态正常
            </div>
        `;
    }

    alertsContainer.innerHTML = alertsHtml;
}

// 工具函数
function getStatusColor(status) {
    const colors = {
        'active': 'success',
        'paused': 'warning',
        'stopped': 'secondary',
        'error': 'danger',
        'completed': 'info'
    };
    return colors[status] || 'secondary';
}

function getStatusText(status) {
    const texts = {
        'active': '运行中',
        'paused': '已暂停',
        'stopped': '已停止',
        'error': '错误',
        'completed': '已完成'
    };
    return texts[status] || '未知';
}

function getRiskLevelColor(level) {
    const colors = {
        'safe': 'success',
        'low': 'info',
        'warning': 'warning',
        'danger': 'danger',
        'critical': 'danger'
    };
    return colors[level] || 'secondary';
}

function getRiskLevelText(level) {
    const texts = {
        'safe': '安全',
        'low': '低风险',
        'warning': '警告',
        'danger': '危险',
        'critical': '极危险'
    };
    return texts[level] || '未知';
}

function getAlertClass(level) {
    const classes = {
        'info': 'alert-info',
        'warning': 'alert-warning',
        'danger': 'alert-danger',
        'critical': 'alert-danger'
    };
    return classes[level] || 'alert-info';
}

function getAlertIcon(level) {
    const icons = {
        'info': 'info-circle',
        'warning': 'exclamation-triangle',
        'danger': 'exclamation-circle',
        'critical': 'skull-crossbones'
    };
    return icons[level] || 'info-circle';
}

// 交易相关函数
function validateTradingForm() {
    // 基础验证
    const symbol = $('#symbol').val();
    const leverage = parseInt($('#leverage').val());
    const initialMargin = parseFloat($('#initial_margin').val());
    const riskAcknowledge = $('#risk_acknowledge').is(':checked');

    if (!symbol) {
        showNotification('请选择交易对', 'error');
        return false;
    }

    if (!riskAcknowledge) {
        showNotification('请确认风险提示', 'error');
        return false;
    }

    if (initialMargin < 10 || initialMargin > 10000) {
        showNotification('保证金金额超出允许范围', 'error');
        return false;
    }

    // MACD参数验证
    if (!validateMACDParameters()) {
        showNotification('MACD参数配置错误', 'error');
        return false;
    }

    // 高风险警告
    if (leverage >= 50) {
        return confirm('您选择了极高的杠杆倍数(' + leverage + 'x)，这将带来极大风险。确认继续吗？');
    }

    return true;
}

function submitTradingForm() {
    const formData = new FormData(document.getElementById('tradingConfigForm'));

    showLoading('正在启动智能交易...');

    fetch(window.location.href, {
        method: 'POST',
        body: formData
    })
    .then(response => response.text())
    .then(html => {
        hideLoading();

        // 检查响应中是否包含成功消息
        if (html.includes('交易启动成功')) {
            showNotification('智能交易启动成功！', 'success');

            // 刷新页面数据
            setTimeout(() => {
                location.reload();
            }, 2000);
        } else if (html.includes('alert-danger')) {
            // 提取错误消息
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            const errorAlert = doc.querySelector('.alert-danger');
            const errorMessage = errorAlert ? errorAlert.textContent.trim() : '启动失败';

            showNotification(errorMessage, 'error');
        } else {
            showNotification('启动失败，请检查配置', 'error');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('提交表单失败:', error);
        showNotification('网络错误，请重试', 'error');
    });
}

function updateSymbolInfo(symbol) {
    if (!symbol) return;

    // 获取交易对信息
    const option = document.querySelector(`#symbol option[value="${symbol}"]`);
    if (option) {
        const minSize = option.getAttribute('data-min-size');
        const tickSize = option.getAttribute('data-tick-size');

        // 更新相关显示
        updatePositionCalculation();

        // 显示交易对信息
        showNotification(`已选择 ${symbol}，最小交易量: ${minSize}，价格精度: ${tickSize}`, 'info');
    }
}

function updateRiskWarning() {
    const leverage = parseInt($('#leverage').val());
    const initialMargin = parseFloat($('#initial_margin').val()) || 0;

    // 计算实际交易金额
    const tradeAmount = initialMargin * leverage;

    // 更新风险提示
    let riskLevel = 'low';
    let riskText = '低风险';
    let riskColor = 'success';

    if (leverage >= 100) {
        riskLevel = 'critical';
        riskText = '极高风险';
        riskColor = 'danger';
    } else if (leverage >= 50) {
        riskLevel = 'high';
        riskText = '高风险';
        riskColor = 'danger';
    } else if (leverage >= 20) {
        riskLevel = 'medium';
        riskText = '中等风险';
        riskColor = 'warning';
    }

    // 显示风险提示
    const riskWarning = `
        <div class="alert alert-${riskColor} mt-2">
            <strong>风险等级: ${riskText}</strong><br>
            实际交易金额: ${formatNumber(tradeAmount, 2)} USDT<br>
            杠杆倍数: ${leverage}x
        </div>
    `;

    // 移除旧的风险提示
    $('#leverage').siblings('.alert').remove();
    $('#leverage').parent().append(riskWarning);
}

function updatePositionCalculation() {
    const symbol = $('#symbol').val();
    const leverage = parseInt($('#leverage').val()) || 1;
    const initialMargin = parseFloat($('#initial_margin').val()) || 0;

    if (!symbol || !initialMargin) return;

    // 获取当前价格（从价格监控中）
    const priceElement = document.querySelector(`[data-symbol="${symbol}"] .h5`);
    const currentPrice = priceElement ?
        parseFloat(priceElement.textContent.replace('$', '').replace(',', '')) : 0;

    if (currentPrice > 0) {
        const tradeAmount = initialMargin * leverage;
        const positionSize = tradeAmount / currentPrice;

        // 显示计算结果
        const calculationInfo = `
            <div class="alert alert-info mt-2">
                <strong>持仓计算:</strong><br>
                当前价格: $${formatNumber(currentPrice, 2)}<br>
                交易金额: ${formatNumber(tradeAmount, 2)} USDT<br>
                持仓数量: ${formatNumber(positionSize, 6)} ${symbol.split('-')[0]}
            </div>
        `;

        // 移除旧的计算信息
        $('#initial_margin').siblings('.alert-info').remove();
        $('#initial_margin').parent().append(calculationInfo);
    }
}

function updateSubmitButton() {
    const riskAcknowledge = $('#risk_acknowledge').is(':checked');
    const submitBtn = $('#tradingConfigForm button[type="submit"]');

    if (riskAcknowledge) {
        submitBtn.removeClass('btn-secondary').addClass('btn-success');
        submitBtn.prop('disabled', false);
    } else {
        submitBtn.removeClass('btn-success').addClass('btn-secondary');
        submitBtn.prop('disabled', true);
    }
}

// 策略模板函数
function setupStrategyTemplates() {
    window.strategyTemplates = {
        conservative: {
            leverage: 5,
            max_add_times: 2,
            stop_loss_ratio: 5,
            take_profit_ratio: 10,
            risk_level: 'conservative',
            emergency_stop_loss: 3,
            position_size_ratio: 30
        },
        balanced: {
            leverage: 10,
            max_add_times: 3,
            stop_loss_ratio: 10,
            take_profit_ratio: 20,
            risk_level: 'moderate',
            emergency_stop_loss: 5,
            position_size_ratio: 50
        },
        aggressive: {
            leverage: 20,
            max_add_times: 5,
            stop_loss_ratio: 15,
            take_profit_ratio: 30,
            risk_level: 'aggressive',
            emergency_stop_loss: 8,
            position_size_ratio: 80
        }
    };
}

function loadStrategyTemplate(templateName) {
    const template = window.strategyTemplates[templateName];
    if (!template) return;

    // 应用模板配置
    $('#leverage').val(template.leverage);
    $('#max_add_times').val(template.max_add_times);
    $('#stop_loss_ratio').val(template.stop_loss_ratio);
    $('#take_profit_ratio').val(template.take_profit_ratio);
    $('#risk_level').val(template.risk_level);
    $('#emergency_stop_loss').val(template.emergency_stop_loss);
    $('#position_size_ratio').val(template.position_size_ratio);

    // 更新相关计算
    updateRiskWarning();
    updatePositionCalculation();

    showNotification(`已加载${templateName}策略模板`, 'success');
}

function saveCurrentStrategy() {
    const config = {
        symbol: $('#symbol').val(),
        leverage: $('#leverage').val(),
        initial_margin: $('#initial_margin').val(),
        max_add_times: $('#max_add_times').val(),
        stop_loss_ratio: $('#stop_loss_ratio').val(),
        take_profit_ratio: $('#take_profit_ratio').val(),
        macd_fast: $('#macd_fast').val(),
        macd_slow: $('#macd_slow').val(),
        macd_signal: $('#macd_signal').val(),
        risk_level: $('#risk_level').val(),
        emergency_stop_loss: $('#emergency_stop_loss').val(),
        position_size_ratio: $('#position_size_ratio').val(),
        auto_add_position: $('#auto_add_position').is(':checked')
    };

    // 保存到本地存储
    localStorage.setItem('bitv_strategy_config', JSON.stringify(config));
    showNotification('策略配置已保存', 'success');
}

function loadSavedStrategy() {
    const saved = localStorage.getItem('bitv_strategy_config');
    if (!saved) return;

    try {
        const config = JSON.parse(saved);

        // 应用保存的配置
        Object.keys(config).forEach(key => {
            const element = document.getElementById(key);
            if (element) {
                if (element.type === 'checkbox') {
                    element.checked = config[key];
                } else {
                    element.value = config[key];
                }
            }
        });

        showNotification('已加载保存的策略配置', 'info');
    } catch (e) {
        console.error('加载策略配置失败:', e);
    }
}

// 会话管理函数
function viewSessionDetails(sessionId) {
    showLoading('正在加载会话详情...');

    fetch(window.BITV.API_BASE + '/api/trading/sessions/' + sessionId, {
        headers: {
            'Authorization': 'Bearer ' + window.BITV.API_TOKEN
        }
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        if (data.success) {
            displaySessionDetails(data.data);
            $('#sessionDetailsModal').modal('show');
        } else {
            showNotification('获取会话详情失败: ' + (data.error || '未知错误'), 'error');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('获取会话详情失败:', error);
        showNotification('网络错误，请重试', 'error');
    });
}

function displaySessionDetails(sessionData) {
    const container = document.getElementById('sessionDetailsContent');

    const html = `
        <div class="row">
            <div class="col-md-6">
                <h6>基本信息</h6>
                <table class="table table-sm">
                    <tr><td>会话ID:</td><td>${sessionData.session_id}</td></tr>
                    <tr><td>交易对:</td><td>${sessionData.symbol}</td></tr>
                    <tr><td>状态:</td><td><span class="badge bg-${getStatusColor(sessionData.status)}">${getStatusText(sessionData.status)}</span></td></tr>
                    <tr><td>杠杆:</td><td>${sessionData.leverage}x</td></tr>
                    <tr><td>初始保证金:</td><td>${formatNumber(sessionData.initial_margin, 2)} USDT</td></tr>
                    <tr><td>开始时间:</td><td>${formatTime(sessionData.start_time)}</td></tr>
                    <tr><td>运行时长:</td><td>${calculateDuration(sessionData.start_time)}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>盈亏统计</h6>
                <table class="table table-sm">
                    <tr><td>总盈亏:</td><td class="${sessionData.total_pnl >= 0 ? 'text-success' : 'text-danger'}">${formatNumber(sessionData.total_pnl, 2)} USDT</td></tr>
                    <tr><td>已实现盈亏:</td><td>${formatNumber(sessionData.realized_pnl || 0, 2)} USDT</td></tr>
                    <tr><td>未实现盈亏:</td><td>${formatNumber(sessionData.unrealized_pnl || 0, 2)} USDT</td></tr>
                    <tr><td>总交易次数:</td><td>${sessionData.total_trades || 0}</td></tr>
                    <tr><td>盈利交易:</td><td>${sessionData.winning_trades || 0}</td></tr>
                    <tr><td>亏损交易:</td><td>${sessionData.losing_trades || 0}</td></tr>
                    <tr><td>胜率:</td><td>${formatPercentage(sessionData.win_rate || 0)}</td></tr>
                </table>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <h6>持仓信息</h6>
                <div id="sessionPositions">
                    ${displaySessionPositions(sessionData.positions || [])}
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <h6>交易历史</h6>
                <div id="sessionTrades" style="max-height: 300px; overflow-y: auto;">
                    ${displaySessionTrades(sessionData.trades || [])}
                </div>
            </div>
        </div>
    `;

    container.innerHTML = html;
}

function displaySessionPositions(positions) {
    if (!positions || positions.length === 0) {
        return '<div class="text-center py-3 text-muted">暂无持仓</div>';
    }

    let html = '<div class="table-responsive"><table class="table table-sm">';
    html += '<thead><tr><th>交易对</th><th>方向</th><th>数量</th><th>入场价</th><th>当前价</th><th>盈亏</th><th>保证金</th></tr></thead><tbody>';

    positions.forEach(position => {
        const pnlClass = position.unrealized_pnl >= 0 ? 'text-success' : 'text-danger';
        html += `
            <tr>
                <td>${position.symbol}</td>
                <td><span class="badge bg-${position.side === 'long' ? 'success' : 'danger'}">${position.side.toUpperCase()}</span></td>
                <td>${formatNumber(position.size, 6)}</td>
                <td>$${formatNumber(position.entry_price, 2)}</td>
                <td>$${formatNumber(position.current_price, 2)}</td>
                <td class="${pnlClass}">$${formatNumber(position.unrealized_pnl, 2)}</td>
                <td>$${formatNumber(position.margin, 2)}</td>
            </tr>
        `;
    });

    html += '</tbody></table></div>';
    return html;
}

function displaySessionTrades(trades) {
    if (!trades || trades.length === 0) {
        return '<div class="text-center py-3 text-muted">暂无交易记录</div>';
    }

    let html = '<div class="table-responsive"><table class="table table-sm">';
    html += '<thead><tr><th>时间</th><th>交易对</th><th>方向</th><th>类型</th><th>数量</th><th>价格</th><th>手续费</th><th>状态</th></tr></thead><tbody>';

    trades.forEach(trade => {
        const sideClass = trade.side === 'buy' ? 'text-success' : 'text-danger';
        html += `
            <tr>
                <td>${formatTime(trade.timestamp, 'H:i:s')}</td>
                <td>${trade.symbol}</td>
                <td class="${sideClass}">${trade.side.toUpperCase()}</td>
                <td>${trade.type}</td>
                <td>${formatNumber(trade.amount, 6)}</td>
                <td>$${formatNumber(trade.price, 2)}</td>
                <td>$${formatNumber(trade.fee || 0, 4)}</td>
                <td><span class="badge bg-${trade.status === 'filled' ? 'success' : 'warning'}">${trade.status}</span></td>
            </tr>
        `;
    });

    html += '</tbody></table></div>';
    return html;
}

function pauseSession(sessionId) {
    if (!confirm('确认暂停此交易会话吗？')) return;

    showLoading('正在暂停交易会话...');

    fetch(window.BITV.API_BASE + '/api/trading/sessions/' + sessionId + '/pause', {
        method: 'POST',
        headers: {
            'Authorization': 'Bearer ' + window.BITV.API_TOKEN,
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        if (data.success) {
            showNotification('交易会话已暂停', 'success');
            updateActiveSessions();
        } else {
            showNotification('暂停失败: ' + (data.error || '未知错误'), 'error');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('暂停会话失败:', error);
        showNotification('网络错误，请重试', 'error');
    });
}

function stopSession(sessionId) {
    if (!confirm('确认停止此交易会话吗？这将平仓所有相关持仓。')) return;

    showLoading('正在停止交易会话...');

    fetch(window.BITV.API_BASE + '/api/trading/sessions/' + sessionId + '/stop', {
        method: 'POST',
        headers: {
            'Authorization': 'Bearer ' + window.BITV.API_TOKEN,
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        if (data.success) {
            showNotification('交易会话已停止', 'success');
            updateActiveSessions();
        } else {
            showNotification('停止失败: ' + (data.error || '未知错误'), 'error');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('停止会话失败:', error);
        showNotification('网络错误，请重试', 'error');
    });
}

// 页面卸载时清理
window.addEventListener('beforeunload', function() {
    if (priceUpdateInterval) clearInterval(priceUpdateInterval);
    if (sessionUpdateInterval) clearInterval(sessionUpdateInterval);
    if (riskUpdateInterval) clearInterval(riskUpdateInterval);
    if (websocketConnection) websocketConnection.close();
});
</script>

<?php include 'includes/footer.php'; ?>
