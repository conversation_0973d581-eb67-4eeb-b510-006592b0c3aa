"""
交易系统配置文件
"""
import os
from dotenv import load_dotenv

load_dotenv()

class TradingConfig:
    """交易配置类"""

    # 交易所配置
    EXCHANGE = os.getenv("EXCHANGE", "okx")  # 支持okx, gateio
    API_KEY = os.getenv("API_KEY", "")
    API_SECRET = os.getenv("API_SECRET", "")
    PASSPHRASE = os.getenv("PASSPHRASE", "")  # OKX需要
    SANDBOX = os.getenv("SANDBOX", "False").lower() == "true"
    
    # 交易参数 (期货合约)
    SYMBOL = "BTC-USDT-SWAP"  # 交易对 (OKX永续合约格式)
    TIMEFRAME = "30m"    # K线周期（30分钟）
    LEVERAGE = 10        # 杠杆倍数
    
    # MACD参数
    MACD_FAST = 12       # 快线周期
    MACD_SLOW = 26       # 慢线周期
    MACD_SIGNAL = 9      # 信号线周期
    
    # 风险管理参数
    INITIAL_MARGIN = 100.0      # 初始保证金（USDT）
    ALERT_POINTS = 0.5          # 预警点数（距离强平价的点数）
    ADD_POSITION_TYPE = "half"  # 加仓类型：'equal'（等量）或'half'（半量）
    MAX_ADD_TIMES = 3           # 最大加仓次数
    
    # 监控参数
    PRICE_CHECK_INTERVAL = 5    # 价格检查间隔（秒）
    POSITION_CHECK_INTERVAL = 10 # 持仓检查间隔（秒）
    
    # 日志配置
    LOG_LEVEL = "INFO"
    LOG_FILE = "trading.log"
    
    @classmethod
    def validate_config(cls):
        """验证配置"""
        # 临时跳过API密钥验证，用于测试
        if not cls.API_KEY or not cls.API_SECRET:
            print("⚠️ 警告：API_KEY和API_SECRET未设置，运行在演示模式")
            return

        if cls.EXCHANGE == "okx" and not cls.PASSPHRASE:
            print("⚠️ 警告：OKX交易所的PASSPHRASE未设置")

        if cls.EXCHANGE not in ["okx", "gateio"]:
            raise ValueError("交易所必须是'okx'或'gateio'")

        if cls.INITIAL_MARGIN <= 0:
            raise ValueError("初始保证金必须大于0")

        if cls.ALERT_POINTS <= 0:
            raise ValueError("预警点数必须大于0")

        if cls.ADD_POSITION_TYPE not in ["equal", "half"]:
            raise ValueError("加仓类型必须是'equal'或'half'")

        return True

# 创建全局配置实例
config = TradingConfig()
