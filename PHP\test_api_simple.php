<?php
/**
 * 简单API连接测试
 */

require_once 'config/config.php';
require_once 'includes/ApiClient.php';

echo "<h1>🧪 简单API连接测试</h1>";

// 测试基本连接
echo "<h2>1. 基本连接测试</h2>";
$api = ApiClient::getInstance();

echo "<p><strong>API基础URL:</strong> " . PYTHON_API_BASE . "</p>";
echo "<p><strong>API令牌:</strong> " . PYTHON_API_TOKEN . "</p>";

// 测试健康检查
echo "<h3>健康检查</h3>";
try {
    $healthResult = $api->healthCheck();
    echo "<p>结果: " . ($healthResult ? "✅ 成功" : "❌ 失败") . "</p>";
    
    // 获取原始响应
    $rawResponse = $api->get('/health');
    echo "<p><strong>原始响应:</strong></p>";
    echo "<pre style='background: #f0f0f0; padding: 10px; border: 1px solid #ddd;'>";
    echo htmlspecialchars(json_encode($rawResponse, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    echo "</pre>";
    
} catch (Exception $e) {
    echo "<p>❌ 异常: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// 测试系统状态
echo "<h3>系统状态</h3>";
try {
    $statusResult = $api->getSystemStatus();
    echo "<p>结果: " . ($statusResult['success'] ? "✅ 成功" : "❌ 失败") . "</p>";
    echo "<pre style='background: #f0f0f0; padding: 10px; border: 1px solid #ddd;'>";
    echo htmlspecialchars(json_encode($statusResult, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    echo "</pre>";
} catch (Exception $e) {
    echo "<p>❌ 异常: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// 测试交易状态
echo "<h3>交易状态</h3>";
try {
    $tradingResult = $api->getTradingStatus();
    echo "<p>结果: " . ($tradingResult['success'] ? "✅ 成功" : "❌ 失败") . "</p>";
    echo "<pre style='background: #f0f0f0; padding: 10px; border: 1px solid #ddd;'>";
    echo htmlspecialchars(json_encode($tradingResult, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    echo "</pre>";
} catch (Exception $e) {
    echo "<p>❌ 异常: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr>";
echo "<p><a href='index.php'>返回首页</a> | <a href='debug.php'>完整调试</a></p>";
?>
