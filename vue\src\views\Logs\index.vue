<template>
  <div class="logs-page">
    <div class="page-header">
      <h2>系统日志</h2>
      <p>查看和分析系统运行日志</p>
    </div>

    <div class="card">
      <h3 class="mb-md">系统日志开发中...</h3>
      <el-empty description="系统日志页面正在开发中">
        <template #image>
          <el-icon size="60" color="#c0c4cc"><Document /></el-icon>
        </template>
      </el-empty>
    </div>
  </div>
</template>

<script setup>
import { Document } from '@element-plus/icons-vue'
</script>

<style lang="scss" scoped>
.logs-page {
  .page-header {
    margin-bottom: var(--spacing-lg);
    
    h2 {
      margin-bottom: var(--spacing-xs);
      color: var(--text-color-primary);
    }
    
    p {
      color: var(--text-color-secondary);
    }
  }
}
</style>
