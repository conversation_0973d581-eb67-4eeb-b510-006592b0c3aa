#!/usr/bin/env python3
"""
端口管理工具
检查和清理端口占用
"""

import socket
import subprocess
import sys
import time
import psutil

def check_port(port):
    """检查端口是否被占用"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('127.0.0.1', port))
        sock.close()
        return result == 0
    except Exception as e:
        print(f"检查端口时出错: {e}")
        return False

def find_process_by_port(port):
    """查找占用指定端口的进程"""
    try:
        for conn in psutil.net_connections():
            if conn.laddr.port == port and conn.status == 'LISTEN':
                try:
                    process = psutil.Process(conn.pid)
                    return {
                        'pid': conn.pid,
                        'name': process.name(),
                        'cmdline': ' '.join(process.cmdline())
                    }
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    return {'pid': conn.pid, 'name': 'Unknown', 'cmdline': 'Unknown'}
    except Exception as e:
        print(f"查找进程时出错: {e}")
    return None

def kill_process_by_pid(pid):
    """终止指定PID的进程"""
    try:
        process = psutil.Process(pid)
        process.terminate()
        time.sleep(2)
        if process.is_running():
            process.kill()
        return True
    except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
        print(f"终止进程失败: {e}")
        return False

def kill_python_processes():
    """终止所有Python进程"""
    killed_count = 0
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['name'] and 'python' in proc.info['name'].lower():
                # 检查是否是我们的交易系统进程
                cmdline = proc.info['cmdline'] or []
                cmdline_str = ' '.join(cmdline).lower()
                if any(keyword in cmdline_str for keyword in ['trading', 'bitv', 'real_trading']):
                    print(f"终止Python进程: PID={proc.info['pid']}, CMD={cmdline_str}")
                    proc.terminate()
                    killed_count += 1
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    if killed_count > 0:
        print(f"等待进程终止...")
        time.sleep(3)
        
        # 强制终止仍在运行的进程
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['name'] and 'python' in proc.info['name'].lower():
                    cmdline = proc.info['cmdline'] or []
                    cmdline_str = ' '.join(cmdline).lower()
                    if any(keyword in cmdline_str for keyword in ['trading', 'bitv', 'real_trading']):
                        if proc.is_running():
                            print(f"强制终止进程: PID={proc.info['pid']}")
                            proc.kill()
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
    
    return killed_count

def main():
    """主函数"""
    print("🔧 BitV端口管理工具")
    print("=" * 50)
    
    port = 8000
    
    # 检查端口状态
    print(f"📊 检查端口 {port} 状态...")
    is_occupied = check_port(port)
    
    if is_occupied:
        print(f"⚠️ 端口 {port} 被占用")
        
        # 查找占用进程
        process_info = find_process_by_port(port)
        if process_info:
            print(f"📋 占用进程信息:")
            print(f"   PID: {process_info['pid']}")
            print(f"   名称: {process_info['name']}")
            print(f"   命令: {process_info['cmdline']}")
            
            # 询问是否终止
            response = input(f"\n❓ 是否终止进程 {process_info['pid']}? (y/n): ").lower()
            if response == 'y':
                if kill_process_by_pid(process_info['pid']):
                    print(f"✅ 进程 {process_info['pid']} 已终止")
                    time.sleep(2)
                    
                    # 再次检查
                    if not check_port(port):
                        print(f"✅ 端口 {port} 已释放")
                    else:
                        print(f"⚠️ 端口 {port} 仍被占用")
                else:
                    print(f"❌ 终止进程失败")
        else:
            print("❓ 无法找到占用进程，尝试清理所有Python进程...")
            killed = kill_python_processes()
            if killed > 0:
                print(f"✅ 已终止 {killed} 个Python进程")
                time.sleep(2)
                if not check_port(port):
                    print(f"✅ 端口 {port} 已释放")
                else:
                    print(f"⚠️ 端口 {port} 仍被占用")
            else:
                print("❓ 未找到相关Python进程")
    else:
        print(f"✅ 端口 {port} 可用")
    
    print("\n🔧 端口管理完成")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 用户中断")
    except Exception as e:
        print(f"❌ 出错: {e}")
