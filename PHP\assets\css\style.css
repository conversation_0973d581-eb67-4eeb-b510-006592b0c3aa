/**
 * BitV MACD智能加仓交易系统 - 自定义样式
 * 
 * @description 现代化、响应式的交易系统界面样式
 * <AUTHOR> Trading Team
 * @version 1.0.0
 */

/* ==================== 全局样式 ==================== */
:root {
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #059669;
    --danger-color: #dc2626;
    --warning-color: #d97706;
    --info-color: #0891b2;
    --dark-color: #1e293b;
    --light-color: #f8fafc;
    --border-color: #e2e8f0;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

* {
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--light-color);
    color: var(--dark-color);
    line-height: 1.6;
    font-size: 14px;
}

/* ==================== 导航栏样式 ==================== */
.navbar-brand {
    font-weight: 700;
    font-size: 1.25rem;
    color: var(--primary-color) !important;
}

.navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.85);
    transition: var(--transition);
}

.navbar-dark .navbar-nav .nav-link:hover {
    color: white;
}

/* ==================== 侧边栏样式 ==================== */
.sidebar {
    min-height: calc(100vh - 56px);
    background: linear-gradient(135deg, var(--primary-color), #3b82f6);
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
}

.sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    pointer-events: none;
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 12px 20px;
    border-radius: var(--border-radius);
    margin: 4px 8px;
    transition: var(--transition);
    position: relative;
    z-index: 1;
    font-weight: 500;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    color: white;
    background-color: rgba(255, 255, 255, 0.15);
    transform: translateX(5px);
    box-shadow: var(--shadow-md);
}

.sidebar .nav-link i {
    width: 20px;
    text-align: center;
}

/* ==================== 主内容区域 ==================== */
.main-content {
    background-color: white;
    border-radius: 12px;
    box-shadow: var(--shadow-md);
    margin: 20px 0;
    padding: 30px;
    min-height: calc(100vh - 200px);
}

/* ==================== 卡片样式 ==================== */
.card {
    border: none;
    border-radius: 12px;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.card-header {
    background-color: var(--light-color);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 1.25rem;
    font-weight: 600;
}

.card-title {
    margin-bottom: 0;
    font-size: 1.1rem;
    font-weight: 600;
}

/* ==================== 按钮样式 ==================== */
.btn {
    border-radius: var(--border-radius);
    padding: 10px 20px;
    font-weight: 500;
    transition: var(--transition);
    border: none;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), #3b82f6);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color), #10b981);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color), #ef4444);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color), #f59e0b);
}

.btn-info {
    background: linear-gradient(135deg, var(--info-color), #06b6d4);
}

/* ==================== 状态指示器 ==================== */
.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
    position: relative;
    animation: pulse 2s infinite;
}

.status-online {
    background-color: var(--success-color);
    box-shadow: 0 0 0 0 rgba(5, 150, 105, 0.7);
}

.status-offline {
    background-color: var(--danger-color);
    box-shadow: 0 0 0 0 rgba(220, 38, 38, 0.7);
}

.status-warning {
    background-color: var(--warning-color);
    box-shadow: 0 0 0 0 rgba(217, 119, 6, 0.7);
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 currentColor;
    }
    70% {
        box-shadow: 0 0 0 10px rgba(0, 0, 0, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
    }
}

/* ==================== 交易卡片样式 ==================== */
.trading-card {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border-left: 4px solid var(--primary-color);
    position: relative;
    overflow: hidden;
}

.trading-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(37, 99, 235, 0.1), transparent);
    border-radius: 50%;
    transform: translate(30px, -30px);
}

/* ==================== 指标卡片样式 ==================== */
.metric-card {
    text-align: center;
    padding: 25px 20px;
    background: linear-gradient(135deg, white, #f8fafc);
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.metric-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.metric-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 8px;
    line-height: 1;
}

.metric-label {
    color: var(--secondary-color);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 600;
}

/* ==================== 加载动画 ==================== */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* ==================== 警告框样式 ==================== */
.alert {
    border: none;
    border-radius: 10px;
    padding: 15px 20px;
    border-left: 4px solid;
    position: relative;
    overflow: hidden;
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.alert-success {
    background-color: #d1fae5;
    color: #065f46;
    border-left-color: var(--success-color);
}

.alert-danger {
    background-color: #fee2e2;
    color: #991b1b;
    border-left-color: var(--danger-color);
}

.alert-warning {
    background-color: #fef3c7;
    color: #92400e;
    border-left-color: var(--warning-color);
}

.alert-info {
    background-color: #cffafe;
    color: #155e75;
    border-left-color: var(--info-color);
}

/* ==================== 表格样式 ==================== */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.table thead th {
    background-color: var(--light-color);
    border: none;
    font-weight: 600;
    color: var(--dark-color);
    padding: 15px 12px;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: rgba(37, 99, 235, 0.05);
}

.table td {
    padding: 12px;
    vertical-align: middle;
    border-top: 1px solid var(--border-color);
}

/* ==================== 徽章样式 ==================== */
.badge {
    font-weight: 500;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
}

/* ==================== 表单样式 ==================== */
.form-control,
.form-select {
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 10px 15px;
    transition: var(--transition);
    font-size: 0.875rem;
}

.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 8px;
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* ==================== 页脚样式 ==================== */
.footer {
    background: linear-gradient(135deg, var(--dark-color), #334155);
    color: white;
    padding: 30px 0;
    margin-top: 50px;
    position: relative;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 56px;
        left: -100%;
        width: 280px;
        height: calc(100vh - 56px);
        z-index: 1000;
        transition: left 0.3s ease;
    }

    .sidebar.show {
        left: 0;
    }

    .main-content {
        margin: 10px;
        padding: 20px;
    }

    .metric-value {
        font-size: 2rem;
    }

    .card {
        margin-bottom: 1rem;
    }

    .btn {
        padding: 8px 16px;
        font-size: 0.875rem;
    }
}

@media (max-width: 576px) {
    .main-content {
        padding: 15px;
        margin: 5px;
    }

    .metric-card {
        padding: 20px 15px;
    }

    .metric-value {
        font-size: 1.75rem;
    }

    .table-responsive {
        font-size: 0.8rem;
    }
}

/* ==================== 图表容器样式 ==================== */
.chart-container {
    position: relative;
    height: 400px;
    background: white;
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--shadow-sm);
}

.chart-container canvas {
    max-height: 100%;
}

/* ==================== 自定义滚动条 ==================== */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--light-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--secondary-color);
    border-radius: 4px;
    transition: var(--transition);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

/* ==================== 动画效果 ==================== */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* ==================== 工具提示样式 ==================== */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    background-color: var(--dark-color);
    border-radius: var(--border-radius);
    padding: 8px 12px;
}

/* ==================== 进度条样式 ==================== */
.progress {
    height: 8px;
    border-radius: 4px;
    background-color: var(--border-color);
}

.progress-bar {
    border-radius: 4px;
    transition: width 0.6s ease;
}
