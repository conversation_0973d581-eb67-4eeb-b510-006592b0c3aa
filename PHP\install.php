<?php
/**
 * BitV MACD智能加仓交易系统 - 安装脚本
 * 
 * @description 系统安装、数据库初始化和环境检查
 * <AUTHOR> Trading Team
 * @version 1.0.0
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 定义常量
define('ROOT_PATH', __DIR__);
define('CONFIG_PATH', ROOT_PATH . '/config');
define('INCLUDES_PATH', ROOT_PATH . '/includes');

// 检查是否已安装
if (file_exists(CONFIG_PATH . '/.installed')) {
    die('系统已安装。如需重新安装，请删除 config/.installed 文件。');
}

// 处理安装请求
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $config = $_POST;
        
        // 验证配置
        validateConfig($config);
        
        // 测试数据库连接
        $pdo = testDatabaseConnection($config);
        
        // 创建数据库表
        createDatabaseTables($pdo);
        
        // 更新配置文件
        updateConfigFile($config);
        
        // 创建安装标记
        file_put_contents(CONFIG_PATH . '/.installed', date('Y-m-d H:i:s'));
        
        $success = true;
        $message = '系统安装成功！';
        
    } catch (Exception $e) {
        $success = false;
        $message = '安装失败: ' . $e->getMessage();
    }
}

/**
 * 验证配置
 */
function validateConfig($config) {
    $required = ['db_host', 'db_name', 'db_user', 'python_api_host', 'python_api_port'];
    
    foreach ($required as $field) {
        if (empty($config[$field])) {
            throw new Exception("字段 {$field} 不能为空");
        }
    }
    
    // 验证端口号
    if (!is_numeric($config['python_api_port']) || $config['python_api_port'] < 1 || $config['python_api_port'] > 65535) {
        throw new Exception('Python API端口号无效');
    }
    
    if (!empty($config['python_ws_port'])) {
        if (!is_numeric($config['python_ws_port']) || $config['python_ws_port'] < 1 || $config['python_ws_port'] > 65535) {
            throw new Exception('WebSocket端口号无效');
        }
    }
}

/**
 * 测试数据库连接
 */
function testDatabaseConnection($config) {
    try {
        $dsn = "mysql:host={$config['db_host']};charset=utf8mb4";
        $pdo = new PDO($dsn, $config['db_user'], $config['db_pass'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);
        
        // 创建数据库（如果不存在）
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$config['db_name']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $pdo->exec("USE `{$config['db_name']}`");
        
        return $pdo;
        
    } catch (PDOException $e) {
        throw new Exception('数据库连接失败: ' . $e->getMessage());
    }
}

/**
 * 创建数据库表
 */
function createDatabaseTables($pdo) {
    $tables = [
        'user_configs' => "
            CREATE TABLE IF NOT EXISTS user_configs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id VARCHAR(50) NOT NULL DEFAULT 'default',
                config_key VARCHAR(100) NOT NULL,
                config_value TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_user_config (user_id, config_key)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ",
        
        'trading_sessions' => "
            CREATE TABLE IF NOT EXISTS trading_sessions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                session_id VARCHAR(100) NOT NULL UNIQUE,
                user_id VARCHAR(50) NOT NULL DEFAULT 'default',
                exchange VARCHAR(20) NOT NULL,
                symbol VARCHAR(50) NOT NULL,
                strategy VARCHAR(50) NOT NULL,
                initial_margin DECIMAL(15,4) NOT NULL,
                leverage INT NOT NULL,
                status ENUM('active', 'stopped', 'error') DEFAULT 'active',
                start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                end_time TIMESTAMP NULL,
                total_pnl DECIMAL(15,4) DEFAULT 0,
                total_add_times INT DEFAULT 0,
                metadata JSON,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_user_status (user_id, status),
                INDEX idx_session_id (session_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ",
        
        'trading_records' => "
            CREATE TABLE IF NOT EXISTS trading_records (
                id INT AUTO_INCREMENT PRIMARY KEY,
                session_id VARCHAR(100) NOT NULL,
                order_id VARCHAR(100),
                exchange VARCHAR(20) NOT NULL,
                symbol VARCHAR(50) NOT NULL,
                side ENUM('buy', 'sell') NOT NULL,
                type ENUM('market', 'limit') NOT NULL,
                amount DECIMAL(15,8) NOT NULL,
                price DECIMAL(15,4),
                filled_amount DECIMAL(15,8) DEFAULT 0,
                status VARCHAR(20) DEFAULT 'pending',
                pnl DECIMAL(15,4) DEFAULT 0,
                fee DECIMAL(15,4) DEFAULT 0,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                metadata JSON,
                INDEX idx_session (session_id),
                INDEX idx_symbol (symbol),
                INDEX idx_timestamp (timestamp),
                FOREIGN KEY (session_id) REFERENCES trading_sessions(session_id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ",
        
        'position_monitors' => "
            CREATE TABLE IF NOT EXISTS position_monitors (
                id INT AUTO_INCREMENT PRIMARY KEY,
                session_id VARCHAR(100) NOT NULL,
                symbol VARCHAR(50) NOT NULL,
                position_side ENUM('long', 'short') NOT NULL,
                size DECIMAL(15,8) NOT NULL,
                entry_price DECIMAL(15,4) NOT NULL,
                mark_price DECIMAL(15,4) NOT NULL,
                liquidation_price DECIMAL(15,4),
                unrealized_pnl DECIMAL(15,4) DEFAULT 0,
                margin DECIMAL(15,4) NOT NULL,
                leverage INT NOT NULL,
                risk_level ENUM('safe', 'warning', 'danger', 'critical') DEFAULT 'safe',
                distance_to_liquidation DECIMAL(8,4),
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_session (session_id),
                INDEX idx_symbol (symbol),
                INDEX idx_timestamp (timestamp)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ",
        
        'system_logs' => "
            CREATE TABLE IF NOT EXISTS system_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                level ENUM('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL') NOT NULL,
                message TEXT NOT NULL,
                context JSON,
                user_id VARCHAR(50) DEFAULT 'system',
                ip_address VARCHAR(45),
                user_agent TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_level (level),
                INDEX idx_timestamp (timestamp),
                INDEX idx_user (user_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        "
    ];
    
    foreach ($tables as $tableName => $sql) {
        try {
            $pdo->exec($sql);
        } catch (PDOException $e) {
            throw new Exception("创建表 {$tableName} 失败: " . $e->getMessage());
        }
    }
}

/**
 * 更新配置文件
 */
function updateConfigFile($config) {
    $configContent = file_get_contents(CONFIG_PATH . '/config.php');
    
    // 替换数据库配置
    $configContent = preg_replace("/define\('DB_HOST', '[^']*'\);/", "define('DB_HOST', '{$config['db_host']}');", $configContent);
    $configContent = preg_replace("/define\('DB_NAME', '[^']*'\);/", "define('DB_NAME', '{$config['db_name']}');", $configContent);
    $configContent = preg_replace("/define\('DB_USER', '[^']*'\);/", "define('DB_USER', '{$config['db_user']}');", $configContent);
    $configContent = preg_replace("/define\('DB_PASS', '[^']*'\);/", "define('DB_PASS', '{$config['db_pass']}');", $configContent);
    
    // 替换Python API配置
    $configContent = preg_replace("/define\('PYTHON_API_HOST', '[^']*'\);/", "define('PYTHON_API_HOST', '{$config['python_api_host']}');", $configContent);
    $configContent = preg_replace("/define\('PYTHON_API_PORT', [^)]*\);/", "define('PYTHON_API_PORT', {$config['python_api_port']});", $configContent);
    
    if (!empty($config['python_ws_port'])) {
        $configContent = preg_replace("/define\('PYTHON_WS_PORT', [^)]*\);/", "define('PYTHON_WS_PORT', {$config['python_ws_port']});", $configContent);
    }
    
    // 设置调试模式
    $debugMode = isset($config['debug_mode']) ? 'true' : 'false';
    $configContent = preg_replace("/define\('DEBUG_MODE', [^)]*\);/", "define('DEBUG_MODE', {$debugMode});", $configContent);
    
    if (!file_put_contents(CONFIG_PATH . '/config.php', $configContent)) {
        throw new Exception('无法写入配置文件');
    }
}

/**
 * 检查系统环境
 */
function checkSystemRequirements() {
    $requirements = [
        'PHP版本' => version_compare(PHP_VERSION, '7.4.0', '>='),
        'PDO扩展' => extension_loaded('pdo'),
        'PDO MySQL' => extension_loaded('pdo_mysql'),
        'cURL扩展' => extension_loaded('curl'),
        'JSON扩展' => extension_loaded('json'),
        'OpenSSL扩展' => extension_loaded('openssl'),
        '配置目录可写' => is_writable(CONFIG_PATH),
        '日志目录存在' => is_dir(ROOT_PATH . '/logs') || mkdir(ROOT_PATH . '/logs', 0755, true)
    ];
    
    return $requirements;
}

$requirements = checkSystemRequirements();
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BitV MACD智能加仓交易系统 - 安装向导</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }
        .install-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .install-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 15px 15px 0 0;
            text-align: center;
        }
        .requirement-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #eee;
        }
        .requirement-status {
            font-weight: bold;
        }
        .status-pass { color: #28a745; }
        .status-fail { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <div class="install-container">
            <div class="install-header">
                <h1><i class="fas fa-cog me-2"></i>BitV MACD智能加仓交易系统</h1>
                <p class="mb-0">安装向导</p>
            </div>
            
            <div class="p-4">
                <?php if (isset($success)): ?>
                    <div class="alert alert-<?php echo $success ? 'success' : 'danger'; ?>" role="alert">
                        <i class="fas fa-<?php echo $success ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                        <?php echo htmlspecialchars($message); ?>
                    </div>
                    
                    <?php if ($success): ?>
                        <div class="text-center">
                            <a href="index.php" class="btn btn-primary btn-lg">
                                <i class="fas fa-arrow-right me-2"></i>进入系统
                            </a>
                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <!-- 系统要求检查 -->
                    <div class="mb-4">
                        <h3><i class="fas fa-check-circle me-2"></i>系统要求检查</h3>
                        <?php foreach ($requirements as $name => $status): ?>
                            <div class="requirement-item">
                                <span><?php echo $name; ?></span>
                                <span class="requirement-status <?php echo $status ? 'status-pass' : 'status-fail'; ?>">
                                    <i class="fas fa-<?php echo $status ? 'check' : 'times'; ?>"></i>
                                    <?php echo $status ? '通过' : '失败'; ?>
                                </span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <?php if (in_array(false, $requirements)): ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            请先解决上述系统要求问题，然后刷新页面继续安装。
                        </div>
                    <?php else: ?>
                        <!-- 安装表单 -->
                        <form method="POST">
                            <h3><i class="fas fa-database me-2"></i>数据库配置</h3>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="db_host" class="form-label">数据库主机</label>
                                    <input type="text" class="form-control" id="db_host" name="db_host" value="localhost" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="db_name" class="form-label">数据库名称</label>
                                    <input type="text" class="form-control" id="db_name" name="db_name" value="bitv_trading" required>
                                </div>
                            </div>
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <label for="db_user" class="form-label">数据库用户名</label>
                                    <input type="text" class="form-control" id="db_user" name="db_user" value="root" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="db_pass" class="form-label">数据库密码</label>
                                    <input type="password" class="form-control" id="db_pass" name="db_pass">
                                </div>
                            </div>
                            
                            <h3><i class="fas fa-api me-2"></i>Python后端配置</h3>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="python_api_host" class="form-label">API主机</label>
                                    <input type="text" class="form-control" id="python_api_host" name="python_api_host" value="localhost" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="python_api_port" class="form-label">API端口</label>
                                    <input type="number" class="form-control" id="python_api_port" name="python_api_port" value="8000" required>
                                </div>
                            </div>
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <label for="python_ws_port" class="form-label">WebSocket端口</label>
                                    <input type="number" class="form-control" id="python_ws_port" name="python_ws_port" value="8001">
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check mt-4">
                                        <input class="form-check-input" type="checkbox" id="debug_mode" name="debug_mode" checked>
                                        <label class="form-check-label" for="debug_mode">
                                            启用调试模式
                                        </label>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-download me-2"></i>开始安装
                                </button>
                            </div>
                        </form>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</body>
</html>
