"""
异步开仓策略模块
基于MACD指标实现自动开仓逻辑
严格遵守异步编程原则
"""
import asyncio
import time
from typing import Optional, Dict, Any, List
from dataclasses import dataclass
from enum import Enum
import logging

from exchanges import BaseExchange, OrderSide, PositionSide, OrderInfo
from indicators import AsyncMACDCalculator, MACDSignal, AsyncMultiTimeframeAnalyzer

logger = logging.getLogger(__name__)

class PositionDirection(Enum):
    """持仓方向"""
    LONG = "long"
    SHORT = "short"

@dataclass
class OpeningResult:
    """开仓结果"""
    success: bool
    order_info: Optional[OrderInfo]
    direction: Optional[PositionDirection]
    reason: str
    timestamp: int
    macd_signal: Optional[MACDSignal]

@dataclass
class PositionConfig:
    """持仓配置"""
    symbol: str
    initial_margin: float  # 初始保证金
    leverage: int         # 杠杆倍数
    timeframe: str        # 时间周期
    min_signal_strength: float = 0.3  # 最小信号强度

class AsyncOpeningStrategy:
    """
    异步开仓策略
    基于MACD指标判断开仓方向和时机
    """
    
    def __init__(self, exchange: BaseExchange, macd_calculator: AsyncMACDCalculator):
        """
        初始化开仓策略

        Args:
            exchange: 交易所实例
            macd_calculator: MACD计算器
        """
        self.exchange = exchange
        self.macd_calculator = macd_calculator
        self.multi_timeframe_analyzer = AsyncMultiTimeframeAnalyzer(exchange)
        self._current_position: Optional[PositionDirection] = None
        self._last_opening_time: int = 0
        self._opening_cooldown: int = 300  # 开仓冷却时间（秒）

        logger.info("异步开仓策略初始化完成")
    
    async def analyze_opening_opportunity(self, config: PositionConfig,
                                        multi_timeframe_confirmation: List[str] = None) -> Optional[MACDSignal]:
        """
        分析开仓机会（支持多时间周期确认）

        Args:
            config: 持仓配置
            multi_timeframe_confirmation: 多时间周期确认列表

        Returns:
            Optional[MACDSignal]: MACD信号，无信号时返回None
        """
        try:
            # 检查冷却时间
            current_time = int(time.time())
            if current_time - self._last_opening_time < self._opening_cooldown:
                logger.debug(f"开仓冷却中，剩余{self._opening_cooldown - (current_time - self._last_opening_time)}秒")
                return None

            # 获取主时间周期MACD信号
            signal = await self.macd_calculator.get_trend_signal(
                self.exchange,
                config.symbol,
                config.timeframe
            )

            if not signal:
                logger.warning("无法获取主时间周期MACD信号")
                return None

            # 检查信号强度
            if signal.strength < config.min_signal_strength:
                logger.debug(f"主时间周期信号强度不足: {signal.strength} < {config.min_signal_strength}")
                return None

            # 检查是否有明确的买卖信号
            if signal.signal_type not in ['buy', 'sell']:
                logger.debug(f"主时间周期无明确开仓信号: {signal.signal_type}")
                return None

            # 如果启用了多时间周期确认，进行趋势一致性检查
            if multi_timeframe_confirmation:
                is_allowed, reason = await self.multi_timeframe_analyzer.should_allow_opening(
                    config.symbol,
                    config.timeframe,
                    multi_timeframe_confirmation,
                    config.min_signal_strength
                )

                if not is_allowed:
                    logger.info(f"多时间周期确认失败: {reason}")
                    return None
                else:
                    logger.info(f"多时间周期确认通过: {reason}")

            logger.info(f"发现开仓机会: {signal.signal_type}, 强度: {signal.strength}, 原因: {signal.reason}")
            return signal

        except Exception as e:
            logger.error(f"分析开仓机会异常: {e}")
            return None
    
    async def calculate_position_size(self, config: PositionConfig, current_price: float) -> float:
        """
        计算持仓数量
        
        Args:
            config: 持仓配置
            current_price: 当前价格
            
        Returns:
            float: 持仓数量
        """
        try:
            # 根据保证金和杠杆计算持仓价值
            position_value = config.initial_margin * config.leverage
            
            # 计算持仓数量
            position_size = position_value / current_price
            
            logger.debug(f"计算持仓数量: 保证金={config.initial_margin}, 杠杆={config.leverage}, "
                        f"价格={current_price}, 数量={position_size}")
            
            return position_size
            
        except Exception as e:
            logger.error(f"计算持仓数量异常: {e}")
            return 0.0
    
    async def execute_opening(self, config: PositionConfig, signal: MACDSignal) -> OpeningResult:
        """
        执行开仓操作
        
        Args:
            config: 持仓配置
            signal: MACD信号
            
        Returns:
            OpeningResult: 开仓结果
        """
        try:
            # 检查是否已有持仓
            existing_positions = await self.exchange.get_positions(config.symbol)
            if existing_positions:
                return OpeningResult(
                    success=False,
                    order_info=None,
                    direction=None,
                    reason="已存在持仓，无法开新仓",
                    timestamp=int(time.time() * 1000),
                    macd_signal=signal
                )
            
            # 获取当前价格
            current_price = await self.exchange.get_current_price(config.symbol)
            if current_price <= 0:
                return OpeningResult(
                    success=False,
                    order_info=None,
                    direction=None,
                    reason="无法获取当前价格",
                    timestamp=int(time.time() * 1000),
                    macd_signal=signal
                )
            
            # 设置杠杆
            leverage_set = await self.exchange.set_leverage(config.symbol, config.leverage)
            if not leverage_set:
                logger.warning(f"设置杠杆失败，使用默认杠杆")
            
            # 计算持仓数量
            position_size = await self.calculate_position_size(config, current_price)
            if position_size <= 0:
                return OpeningResult(
                    success=False,
                    order_info=None,
                    direction=None,
                    reason="计算持仓数量失败",
                    timestamp=int(time.time() * 1000),
                    macd_signal=signal
                )
            
            # 确定开仓方向
            if signal.signal_type == 'buy':
                order_side = OrderSide.BUY
                position_side = PositionSide.LONG
                direction = PositionDirection.LONG
                reason = f"MACD看涨信号开多仓: {signal.reason}"
            else:  # sell
                order_side = OrderSide.SELL
                position_side = PositionSide.SHORT
                direction = PositionDirection.SHORT
                reason = f"MACD看跌信号开空仓: {signal.reason}"
            
            # 执行市价开仓
            logger.info(f"执行开仓: {direction.value}, 数量: {position_size}, 价格: {current_price}")
            
            order_info = await self.exchange.place_market_order(
                symbol=config.symbol,
                side=order_side,
                amount=position_size,
                position_side=position_side
            )
            
            # 更新状态
            self._current_position = direction
            self._last_opening_time = int(time.time())
            
            logger.info(f"开仓成功: 订单ID={order_info.order_id}, 方向={direction.value}")
            
            return OpeningResult(
                success=True,
                order_info=order_info,
                direction=direction,
                reason=reason,
                timestamp=int(time.time() * 1000),
                macd_signal=signal
            )
            
        except Exception as e:
            logger.error(f"执行开仓异常: {e}")
            return OpeningResult(
                success=False,
                order_info=None,
                direction=None,
                reason=f"开仓异常: {str(e)}",
                timestamp=int(time.time() * 1000),
                macd_signal=signal
            )
    
    async def auto_opening_loop(self, config: PositionConfig, check_interval: int = 60) -> None:
        """
        自动开仓循环
        
        Args:
            config: 持仓配置
            check_interval: 检查间隔（秒）
        """
        logger.info(f"启动自动开仓循环: 交易对={config.symbol}, 检查间隔={check_interval}秒")
        
        while True:
            try:
                # 分析开仓机会
                signal = await self.analyze_opening_opportunity(config)
                
                if signal:
                    # 执行开仓
                    result = await self.execute_opening(config, signal)
                    
                    if result.success:
                        logger.info(f"自动开仓成功: {result.reason}")
                        # 开仓成功后可以退出循环，或者继续监控
                        break
                    else:
                        logger.warning(f"自动开仓失败: {result.reason}")
                
                # 等待下次检查
                await asyncio.sleep(check_interval)
                
            except asyncio.CancelledError:
                logger.info("自动开仓循环被取消")
                break
            except Exception as e:
                logger.error(f"自动开仓循环异常: {e}")
                await asyncio.sleep(check_interval)
    
    def get_current_position(self) -> Optional[PositionDirection]:
        """获取当前持仓方向"""
        return self._current_position
    
    def set_current_position(self, direction: Optional[PositionDirection]) -> None:
        """设置当前持仓方向"""
        self._current_position = direction
        logger.info(f"更新持仓方向: {direction.value if direction else 'None'}")
    
    def set_opening_cooldown(self, cooldown: int) -> None:
        """设置开仓冷却时间"""
        self._opening_cooldown = cooldown
        logger.info(f"设置开仓冷却时间: {cooldown}秒")
    
    async def close_position(self, config: PositionConfig) -> bool:
        """
        平仓操作
        
        Args:
            config: 持仓配置
            
        Returns:
            bool: 平仓是否成功
        """
        try:
            # 获取当前持仓
            positions = await self.exchange.get_positions(config.symbol)
            if not positions:
                logger.warning("没有找到持仓")
                return False
            
            for position in positions:
                # 确定平仓方向
                if position.side == PositionSide.LONG:
                    close_side = OrderSide.SELL
                else:
                    close_side = OrderSide.BUY
                
                # 执行市价平仓
                order_info = await self.exchange.place_market_order(
                    symbol=config.symbol,
                    side=close_side,
                    amount=position.size,
                    position_side=position.side,
                    reduce_only=True  # 关键修复：明确标记为平仓单
                )
                
                logger.info(f"平仓成功: 订单ID={order_info.order_id}")
            
            # 清空持仓状态
            self._current_position = None
            return True
            
        except Exception as e:
            logger.error(f"平仓异常: {e}")
            return False
