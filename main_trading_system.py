#!/usr/bin/env python3
"""
BitV MACD智能加仓交易系统 - 完整版
专业级量化交易系统，支持完整的MACD策略、智能加仓、风险管理
"""

import asyncio
import uvicorn
from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import logging
import os
import json
from datetime import datetime
import time
from typing import Dict, Any, Optional, List
import random
import uuid
import sqlite3
from dataclasses import dataclass, asdict

# 配置日志系统
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)s | %(name)s | %(message)s',
    handlers=[
        logging.FileHandler('logs/trading_system.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# 创建必要的目录
os.makedirs('logs', exist_ok=True)

# 数据模型定义
@dataclass
class TradingSession:
    session_id: str
    exchange: str
    symbol: str
    strategy: str
    leverage: int
    initial_margin: float
    max_add_times: int
    status: str
    start_time: str
    end_time: Optional[str] = None
    total_pnl: float = 0.0
    add_times: int = 0
    positions: List[Dict] = None

    def __post_init__(self):
        if self.positions is None:
            self.positions = []

@dataclass
class Position:
    position_id: str
    session_id: str
    symbol: str
    side: str  # 'long' or 'short'
    size: float
    entry_price: float
    current_price: float
    unrealized_pnl: float
    margin: float
    leverage: int
    liquidation_price: float
    timestamp: str

@dataclass
class Trade:
    trade_id: str
    session_id: str
    symbol: str
    side: str  # 'buy' or 'sell'
    type: str  # 'market' or 'limit'
    amount: float
    price: float
    fee: float
    pnl: float
    timestamp: str
    status: str

# 数据库管理器
class DatabaseManager:
    def __init__(self, db_path: str = "data/trading.db"):
        self.db_path = db_path
        self.init_database()

    def init_database(self):
        """初始化数据库表"""
        os.makedirs('data', exist_ok=True)
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 创建交易会话表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS trading_sessions (
                session_id TEXT PRIMARY KEY,
                exchange TEXT NOT NULL,
                symbol TEXT NOT NULL,
                strategy TEXT NOT NULL,
                leverage INTEGER NOT NULL,
                initial_margin REAL NOT NULL,
                max_add_times INTEGER NOT NULL,
                status TEXT NOT NULL,
                start_time TEXT NOT NULL,
                end_time TEXT,
                total_pnl REAL DEFAULT 0.0,
                add_times INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # 创建持仓表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS positions (
                position_id TEXT PRIMARY KEY,
                session_id TEXT NOT NULL,
                symbol TEXT NOT NULL,
                side TEXT NOT NULL,
                size REAL NOT NULL,
                entry_price REAL NOT NULL,
                current_price REAL NOT NULL,
                unrealized_pnl REAL NOT NULL,
                margin REAL NOT NULL,
                leverage INTEGER NOT NULL,
                liquidation_price REAL NOT NULL,
                timestamp TEXT NOT NULL,
                FOREIGN KEY (session_id) REFERENCES trading_sessions (session_id)
            )
        ''')

        # 创建交易记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS trades (
                trade_id TEXT PRIMARY KEY,
                session_id TEXT NOT NULL,
                symbol TEXT NOT NULL,
                side TEXT NOT NULL,
                type TEXT NOT NULL,
                amount REAL NOT NULL,
                price REAL NOT NULL,
                fee REAL NOT NULL,
                pnl REAL NOT NULL,
                timestamp TEXT NOT NULL,
                status TEXT NOT NULL,
                FOREIGN KEY (session_id) REFERENCES trading_sessions (session_id)
            )
        ''')

        conn.commit()
        conn.close()
        logger.info("✅ 数据库初始化完成")

    def save_session(self, session_data: Dict):
        """保存交易会话"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT OR REPLACE INTO trading_sessions
            (session_id, exchange, symbol, strategy, leverage, initial_margin,
             max_add_times, status, start_time, end_time, total_pnl, add_times)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            session_data['session_id'], session_data['exchange'], session_data['symbol'],
            session_data['strategy'], session_data['leverage'], session_data['initial_margin'],
            session_data['max_add_times'], session_data['status'], session_data['start_time'],
            session_data.get('end_time'), session_data['total_pnl'], session_data['add_times']
        ))

        conn.commit()
        conn.close()

    def get_sessions(self, status: Optional[str] = None) -> List[Dict]:
        """获取交易会话列表"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        if status:
            cursor.execute('SELECT * FROM trading_sessions WHERE status = ?', (status,))
        else:
            cursor.execute('SELECT * FROM trading_sessions ORDER BY start_time DESC')

        columns = [description[0] for description in cursor.description]
        sessions = [dict(zip(columns, row)) for row in cursor.fetchall()]

        conn.close()
        return sessions

# 全局状态管理
class TradingSystemState:
    def __init__(self):
        self.is_running = False
        self.trading_sessions = {}
        self.active_positions = {}
        self.system_stats = {
            "total_trades": 0,
            "total_pnl": 0.0,
            "win_rate": 0.0,
            "start_time": datetime.now().isoformat()
        }
        self.price_data = {}
        self.macd_data = {}
        self.websocket_connections = []
        
    def get_session_id(self):
        return f"session_{int(time.time())}_{uuid.uuid4().hex[:8]}"

# 全局状态实例
trading_state = TradingSystemState()

# MACD计算器
class MACDCalculator:
    def __init__(self, fast=12, slow=26, signal=9):
        self.fast = fast
        self.slow = slow
        self.signal = signal
        self.price_history = {}
        
    def calculate_ema(self, prices: List[float], period: int) -> float:
        """计算指数移动平均线"""
        if len(prices) < period:
            return sum(prices) / len(prices)
        
        multiplier = 2 / (period + 1)
        ema = prices[0]
        
        for price in prices[1:]:
            ema = (price * multiplier) + (ema * (1 - multiplier))
        
        return ema
        
    def calculate(self, symbol: str, prices: List[float]):
        """计算MACD指标"""
        if len(prices) < self.slow:
            return None
            
        # 计算快线和慢线EMA
        fast_ema = self.calculate_ema(prices[-self.fast:], self.fast)
        slow_ema = self.calculate_ema(prices[-self.slow:], self.slow)
        
        # MACD线 = 快线EMA - 慢线EMA
        macd_line = fast_ema - slow_ema
        
        # 更新历史MACD值
        if symbol not in self.price_history:
            self.price_history[symbol] = []
        
        self.price_history[symbol].append(macd_line)
        
        # 保持历史数据在合理范围内
        if len(self.price_history[symbol]) > self.signal * 2:
            self.price_history[symbol] = self.price_history[symbol][-self.signal * 2:]
        
        # 计算信号线（MACD的EMA）
        signal_line = self.calculate_ema(self.price_history[symbol], self.signal)
        
        # 柱状图 = MACD线 - 信号线
        histogram = macd_line - signal_line
        
        return {
            "symbol": symbol,
            "macd": round(macd_line, 6),
            "signal": round(signal_line, 6),
            "histogram": round(histogram, 6),
            "fast_ema": round(fast_ema, 2),
            "slow_ema": round(slow_ema, 2),
            "timestamp": datetime.now().isoformat()
        }

# 价格数据模拟器
class PriceSimulator:
    def __init__(self):
        self.symbols = {
            "BTC-USDT-SWAP": {"base_price": 50000, "volatility": 0.02},
            "ETH-USDT-SWAP": {"base_price": 3000, "volatility": 0.025},
            "LTC-USDT-SWAP": {"base_price": 100, "volatility": 0.03},
            "XRP-USDT-SWAP": {"base_price": 0.5, "volatility": 0.04},
            "ADA-USDT-SWAP": {"base_price": 0.3, "volatility": 0.05}
        }
        self.price_history = {symbol: [] for symbol in self.symbols}
        self.running = False
        
    async def start(self):
        """启动价格模拟器"""
        self.running = True
        logger.info("🎯 价格模拟器启动")
        
        while self.running:
            for symbol, config in self.symbols.items():
                # 生成价格变化
                change_percent = random.gauss(0, config["volatility"])
                new_price = config["base_price"] * (1 + change_percent)
                
                # 添加一些趋势性
                if len(self.price_history[symbol]) > 10:
                    recent_trend = sum(self.price_history[symbol][-5:]) / 5
                    trend_factor = (new_price - recent_trend) / recent_trend
                    new_price += new_price * trend_factor * 0.1
                
                # 更新基础价格（缓慢调整）
                self.symbols[symbol]["base_price"] = (
                    self.symbols[symbol]["base_price"] * 0.99 + new_price * 0.01
                )
                
                # 保存价格历史
                self.price_history[symbol].append(new_price)
                if len(self.price_history[symbol]) > 1000:
                    self.price_history[symbol] = self.price_history[symbol][-500:]
                
                # 计算24小时变化
                if len(self.price_history[symbol]) >= 288:  # 24小时 * 12 (5分钟间隔)
                    price_24h_ago = self.price_history[symbol][-288]
                    change_24h = ((new_price - price_24h_ago) / price_24h_ago) * 100
                else:
                    change_24h = change_percent * 100
                
                price_data = {
                    "symbol": symbol,
                    "price": round(new_price, 6),
                    "change_24h": round(change_24h, 2),
                    "volume_24h": round(random.uniform(1000000, 50000000), 2),
                    "high_24h": round(new_price * random.uniform(1.01, 1.05), 6),
                    "low_24h": round(new_price * random.uniform(0.95, 0.99), 6),
                    "timestamp": datetime.now().isoformat()
                }
                
                # 更新全局价格数据
                trading_state.price_data[symbol] = price_data
                
                # 广播价格数据到WebSocket连接
                await self.broadcast_price_data(price_data)
            
            await asyncio.sleep(5)  # 每5秒更新一次
    
    async def broadcast_price_data(self, price_data: Dict):
        """广播价格数据到所有WebSocket连接 - 使用统一管理器"""
        from utils.websocket_manager import websocket_manager

        message = {
            "type": "price_update",
            "data": price_data
        }

        # 使用统一管理器广播到price_update频道
        await websocket_manager.broadcast(message, channel="price_update")
    
    def get_current_prices(self) -> Dict[str, Dict]:
        """获取当前所有价格"""
        return trading_state.price_data
    
    def get_price_history(self, symbol: str, limit: int = 100) -> List[float]:
        """获取价格历史"""
        if symbol in self.price_history:
            return self.price_history[symbol][-limit:]
        return []
    
    def stop(self):
        """停止价格模拟器"""
        self.running = False
        logger.info("🛑 价格模拟器已停止")

# 风险管理器
class RiskManager:
    def __init__(self):
        self.max_leverage = 100
        self.min_margin_ratio = 0.1  # 10%
        self.liquidation_threshold = 0.05  # 5%
        
    def calculate_liquidation_price(self, entry_price: float, leverage: int, side: str) -> float:
        """计算强平价格"""
        margin_ratio = 1 / leverage
        
        if side.lower() == 'long':
            liquidation_price = entry_price * (1 - margin_ratio + 0.001)
        else:
            liquidation_price = entry_price * (1 + margin_ratio + 0.001)
        
        return round(liquidation_price, 6)
    
    def calculate_unrealized_pnl(self, entry_price: float, current_price: float, 
                                size: float, side: str) -> float:
        """计算未实现盈亏"""
        if side.lower() == 'long':
            pnl = (current_price - entry_price) * size
        else:
            pnl = (entry_price - current_price) * size
        
        return round(pnl, 6)
    
    def get_risk_level(self, current_price: float, liquidation_price: float, side: str) -> str:
        """获取风险等级"""
        if side.lower() == 'long':
            distance_ratio = (current_price - liquidation_price) / current_price
        else:
            distance_ratio = (liquidation_price - current_price) / current_price
        
        if distance_ratio > 0.2:
            return "safe"
        elif distance_ratio > 0.1:
            return "warning"
        elif distance_ratio > 0.05:
            return "danger"
        else:
            return "critical"
    
    def should_add_position(self, current_pnl: float, initial_margin: float, 
                          add_times: int, max_add_times: int) -> bool:
        """判断是否应该加仓"""
        if add_times >= max_add_times:
            return False
        
        loss_ratio = abs(current_pnl) / initial_margin
        thresholds = [0.1, 0.2, 0.3, 0.4, 0.5]  # 10%, 20%, 30%, 40%, 50%
        
        if add_times < len(thresholds):
            return current_pnl < 0 and loss_ratio >= thresholds[add_times]
        
        return False

# 全局实例初始化
db_manager = DatabaseManager()
price_simulator = PriceSimulator()
risk_manager = RiskManager()

# 完整的交易引擎
class TradingEngine:
    def __init__(self):
        self.macd_calculator = MACDCalculator()
        self.monitoring_tasks = {}

    async def start_trading_session(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """启动交易会话"""
        session_id = trading_state.get_session_id()

        session_data = {
            "session_id": session_id,
            "exchange": config.get("exchange", "okx"),
            "symbol": config.get("symbol", "BTC-USDT-SWAP"),
            "strategy": config.get("strategy", "macd"),
            "leverage": config.get("leverage", 10),
            "initial_margin": config.get("initial_margin", 100.0),
            "max_add_times": config.get("max_add_times", 3),
            "status": "active",
            "start_time": datetime.now().isoformat(),
            "total_pnl": 0.0,
            "add_times": 0,
            "positions": []
        }

        # 保存到数据库
        db_manager.save_session(session_data)

        # 添加到活跃会话
        trading_state.trading_sessions[session_id] = session_data
        trading_state.is_running = True

        # 启动监控任务
        self.monitoring_tasks[session_id] = asyncio.create_task(
            self.monitor_session(session_id)
        )

        logger.info(f"🚀 交易会话启动: {session_id}")
        logger.info(f"   交易所: {session_data['exchange']}")
        logger.info(f"   交易对: {session_data['symbol']}")
        logger.info(f"   策略: {session_data['strategy']}")
        logger.info(f"   杠杆: {session_data['leverage']}x")
        logger.info(f"   初始保证金: {session_data['initial_margin']} USDT")

        return session_data

    async def stop_trading_session(self, session_id: str = None) -> Dict[str, Any]:
        """停止交易会话"""
        if session_id:
            if session_id not in trading_state.trading_sessions:
                raise ValueError(f"会话 {session_id} 不存在或已停止")

            session = trading_state.trading_sessions[session_id]
            session["status"] = "stopped"
            session["end_time"] = datetime.now().isoformat()

            # 停止监控任务
            if session_id in self.monitoring_tasks:
                self.monitoring_tasks[session_id].cancel()
                del self.monitoring_tasks[session_id]

            # 平仓所有持仓
            await self.close_all_positions(session_id)

            # 更新数据库
            db_manager.save_session(session)

            logger.info(f"🛑 交易会话停止: {session_id}")

            return {
                "session_id": session_id,
                "status": "stopped",
                "end_time": session["end_time"],
                "total_pnl": session["total_pnl"]
            }
        else:
            # 停止所有会话
            for sid in list(trading_state.trading_sessions.keys()):
                await self.stop_trading_session(sid)

            trading_state.is_running = False
            logger.info("🛑 所有交易会话已停止")

            return {"message": "所有交易会话已停止", "timestamp": datetime.now().isoformat()}

    async def monitor_session(self, session_id: str):
        """监控交易会话"""
        logger.info(f"📊 开始监控会话: {session_id}")

        try:
            while (session_id in trading_state.trading_sessions and
                   trading_state.trading_sessions[session_id]["status"] == "active"):

                session = trading_state.trading_sessions[session_id]
                symbol = session["symbol"]

                # 获取当前价格
                current_prices = price_simulator.get_current_prices()
                if symbol not in current_prices:
                    await asyncio.sleep(5)
                    continue

                current_price = current_prices[symbol]["price"]

                # 获取价格历史用于MACD计算
                price_history = price_simulator.get_price_history(symbol, 50)

                # 计算MACD指标
                macd_data = self.macd_calculator.calculate(symbol, price_history)

                if macd_data:
                    trading_state.macd_data[symbol] = macd_data

                    # 检查交易信号
                    await self.check_trading_signals(session_id, current_price, macd_data)

                # 更新持仓信息
                await self.update_positions(session_id, current_price)

                # 检查风险管理
                await self.check_risk_management(session_id, current_price)

                await asyncio.sleep(10)  # 每10秒检查一次

        except asyncio.CancelledError:
            logger.info(f"📊 会话监控已停止: {session_id}")
        except Exception as e:
            logger.error(f"❌ 监控会话错误 {session_id}: {e}")

    async def check_trading_signals(self, session_id: str, current_price: float, macd_data: Dict):
        """检查交易信号"""
        session = trading_state.trading_sessions[session_id]

        # MACD金叉信号（买入）
        if (macd_data["macd"] > macd_data["signal"] and
            macd_data["histogram"] > 0 and
            session_id not in trading_state.active_positions):

            await self.open_position(session_id, "long", current_price)

        # MACD死叉信号（卖出）
        elif (macd_data["macd"] < macd_data["signal"] and
              macd_data["histogram"] < 0 and
              session_id in trading_state.active_positions):

            await self.close_position(session_id, current_price)

    async def open_position(self, session_id: str, side: str, entry_price: float):
        """开仓"""
        session = trading_state.trading_sessions[session_id]

        # 计算仓位大小
        position_size = (session["initial_margin"] * session["leverage"]) / entry_price

        # 计算强平价格
        liquidation_price = risk_manager.calculate_liquidation_price(
            entry_price, session["leverage"], side
        )

        position = {
            "position_id": f"pos_{session_id}_{int(time.time())}",
            "session_id": session_id,
            "symbol": session["symbol"],
            "side": side,
            "size": position_size,
            "entry_price": entry_price,
            "current_price": entry_price,
            "unrealized_pnl": 0.0,
            "margin": session["initial_margin"],
            "leverage": session["leverage"],
            "liquidation_price": liquidation_price,
            "timestamp": datetime.now().isoformat()
        }

        trading_state.active_positions[session_id] = position
        session["positions"].append(position)

        logger.info(f"📈 开仓: {session_id} | {side.upper()} | {position_size:.6f} | ${entry_price}")

        # 更新统计
        trading_state.system_stats["total_trades"] += 1

    async def close_position(self, session_id: str, exit_price: float):
        """平仓"""
        if session_id not in trading_state.active_positions:
            return

        position = trading_state.active_positions[session_id]
        session = trading_state.trading_sessions[session_id]

        # 计算盈亏
        pnl = risk_manager.calculate_unrealized_pnl(
            position["entry_price"], exit_price, position["size"], position["side"]
        )

        # 计算手续费
        fee = position["size"] * exit_price * 0.001  # 0.1% 手续费
        net_pnl = pnl - fee

        # 更新会话盈亏
        session["total_pnl"] += net_pnl

        logger.info(f"📉 平仓: {session_id} | PnL: ${pnl:.2f} | Fee: ${fee:.2f} | Net: ${net_pnl:.2f}")

        # 更新统计
        trading_state.system_stats["total_pnl"] += net_pnl

        # 移除持仓
        del trading_state.active_positions[session_id]

    async def update_positions(self, session_id: str, current_price: float):
        """更新持仓信息"""
        if session_id not in trading_state.active_positions:
            return

        position = trading_state.active_positions[session_id]
        position["current_price"] = current_price

        # 计算未实现盈亏
        position["unrealized_pnl"] = risk_manager.calculate_unrealized_pnl(
            position["entry_price"], current_price, position["size"], position["side"]
        )

    async def check_risk_management(self, session_id: str, current_price: float):
        """检查风险管理"""
        if session_id not in trading_state.active_positions:
            return

        position = trading_state.active_positions[session_id]
        session = trading_state.trading_sessions[session_id]

        # 检查是否需要加仓
        if risk_manager.should_add_position(
            position["unrealized_pnl"], session["initial_margin"],
            session["add_times"], session["max_add_times"]
        ):
            await self.add_position(session_id, current_price)

        # 检查风险等级
        risk_level = risk_manager.get_risk_level(
            current_price, position["liquidation_price"], position["side"]
        )

        if risk_level == "critical":
            logger.warning(f"⚠️ 风险警告: {session_id} | 接近强平价格!")

    async def add_position(self, session_id: str, current_price: float):
        """加仓"""
        session = trading_state.trading_sessions[session_id]

        if session["add_times"] >= session["max_add_times"]:
            return

        if session_id not in trading_state.active_positions:
            return

        position = trading_state.active_positions[session_id]

        # 计算加仓大小（递减策略）
        add_ratio = 0.5 ** (session["add_times"] + 1)  # 50%, 25%, 12.5%...
        add_margin = session["initial_margin"] * add_ratio
        add_size = (add_margin * session["leverage"]) / current_price

        # 更新持仓
        total_size = position["size"] + add_size
        total_value = (position["size"] * position["entry_price"] + add_size * current_price)
        new_avg_price = total_value / total_size

        position["size"] = total_size
        position["entry_price"] = new_avg_price
        position["margin"] += add_margin

        # 重新计算强平价格
        position["liquidation_price"] = risk_manager.calculate_liquidation_price(
            new_avg_price, session["leverage"], position["side"]
        )

        session["add_times"] += 1

        logger.info(f"➕ 加仓: {session_id} | 第{session['add_times']}次 | +{add_size:.6f} | 新均价: ${new_avg_price:.2f}")

    async def close_all_positions(self, session_id: str):
        """平仓所有持仓"""
        if session_id in trading_state.active_positions:
            current_prices = price_simulator.get_current_prices()
            session = trading_state.trading_sessions[session_id]

            if session["symbol"] in current_prices:
                current_price = current_prices[session["symbol"]]["price"]
                await self.close_position(session_id, current_price)

# 全局交易引擎实例
trading_engine = TradingEngine()

# 应用生命周期管理
@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动事件
    logger.info("🚀 BitV MACD智能加仓交易系统启动中...")
    logger.info("📊 系统组件初始化:")
    logger.info("   ✅ 数据库管理器")
    logger.info("   ✅ 价格模拟器")
    logger.info("   ✅ 风险管理器")
    logger.info("   ✅ MACD计算器")
    logger.info("   ✅ 交易引擎")
    logger.info("   ✅ WebSocket管理器")

    # 启动价格模拟器
    asyncio.create_task(price_simulator.start())
    logger.info("🎯 价格模拟器已启动")
    logger.info("🎉 系统启动完成！")

    yield

    # 关闭事件
    logger.info("🛑 正在关闭交易系统...")

    # 停止所有交易会话
    for session_id in list(trading_state.trading_sessions.keys()):
        await trading_engine.stop_trading_session(session_id)

    # 停止价格模拟器
    price_simulator.stop()

    logger.info("👋 系统已关闭")

# 创建FastAPI应用
app = FastAPI(
    title="BitV MACD智能加仓交易系统 - 完整版",
    description="专业级量化交易系统，支持完整的MACD策略、智能加仓、风险管理",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# API路由
@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "BitV MACD智能加仓交易系统 - 完整版",
        "version": "1.0.0",
        "status": "running",
        "features": [
            "完整MACD策略分析",
            "智能加仓管理",
            "实时风险监控",
            "多交易所支持",
            "历史数据分析",
            "WebSocket实时推送"
        ],
        "timestamp": datetime.now().isoformat(),
        "docs": "/docs"
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "components": {
            "api": "active",
            "trading_engine": "active",
            "price_simulator": "active" if price_simulator.running else "inactive",
            "macd_calculator": "active",
            "risk_manager": "active"
        },
        "uptime": str(datetime.now() - datetime.fromisoformat(trading_state.system_stats["start_time"])),
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/status")
async def get_system_status():
    """获取系统状态"""
    active_sessions = len([s for s in trading_state.trading_sessions.values() if s["status"] == "active"])

    return {
        "success": True,
        "data": {
            "system_status": "running",
            "trading_engine_status": "active",
            "is_trading": trading_state.is_running,
            "active_sessions": active_sessions,
            "total_sessions": len(trading_state.trading_sessions),
            "active_positions": len(trading_state.active_positions),
            "system_stats": trading_state.system_stats,
            "timestamp": datetime.now().isoformat()
        }
    }

@app.get("/api/trading/status")
async def get_trading_status():
    """获取交易状态"""
    active_sessions = [s for s in trading_state.trading_sessions.values() if s["status"] == "active"]
    current_session = active_sessions[0] if active_sessions else None

    return {
        "success": True,
        "data": {
            "is_running": trading_state.is_running,
            "trading_state": "active" if trading_state.is_running else "idle",
            "current_session": current_session,
            "active_sessions": len(active_sessions),
            "total_sessions": len(trading_state.trading_sessions),
            "active_positions": len(trading_state.active_positions),
            "timestamp": datetime.now().isoformat()
        }
    }

@app.post("/api/trading/start")
async def start_trading(config: Dict[str, Any]):
    """启动交易"""
    try:
        session_data = await trading_engine.start_trading_session(config)
        return {
            "success": True,
            "data": session_data
        }
    except Exception as e:
        logger.error(f"启动交易失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/trading/stop")
async def stop_trading(session_id: Optional[str] = None):
    """停止交易"""
    try:
        result = await trading_engine.stop_trading_session(session_id)
        return {
            "success": True,
            "data": result
        }
    except Exception as e:
        logger.error(f"停止交易失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/prices")
async def get_all_prices():
    """获取所有价格数据"""
    return {
        "success": True,
        "data": trading_state.price_data
    }

@app.get("/api/prices/{symbol}")
async def get_price(symbol: str):
    """获取特定交易对价格数据"""
    if symbol in trading_state.price_data:
        return {
            "success": True,
            "data": trading_state.price_data[symbol]
        }
    else:
        return {
            "success": False,
            "error": f"Symbol {symbol} not found"
        }

@app.get("/api/macd")
async def get_all_macd_data():
    """获取所有MACD数据"""
    return {
        "success": True,
        "data": trading_state.macd_data
    }

@app.get("/api/macd/{symbol}")
async def get_macd_data(symbol: str):
    """获取特定交易对MACD数据"""
    if symbol in trading_state.macd_data:
        return {
            "success": True,
            "data": trading_state.macd_data[symbol]
        }
    else:
        return {
            "success": False,
            "error": f"MACD data for {symbol} not found"
        }

@app.get("/api/sessions")
async def get_trading_sessions():
    """获取交易会话列表"""
    return {
        "success": True,
        "data": list(trading_state.trading_sessions.values())
    }

@app.get("/api/sessions/{session_id}")
async def get_session_details(session_id: str):
    """获取特定会话详情"""
    if session_id in trading_state.trading_sessions:
        session = trading_state.trading_sessions[session_id]
        position = trading_state.active_positions.get(session_id)

        return {
            "success": True,
            "data": {
                "session": session,
                "position": position,
                "risk_level": risk_manager.get_risk_level(
                    position["current_price"], position["liquidation_price"], position["side"]
                ) if position else "safe"
            }
        }
    else:
        return {
            "success": False,
            "error": f"Session {session_id} not found"
        }

@app.get("/api/positions")
async def get_positions():
    """获取所有持仓信息"""
    return {
        "success": True,
        "data": list(trading_state.active_positions.values())
    }

@app.get("/api/positions/{session_id}")
async def get_position(session_id: str):
    """获取特定会话的持仓信息"""
    if session_id in trading_state.active_positions:
        position = trading_state.active_positions[session_id]
        risk_level = risk_manager.get_risk_level(
            position["current_price"], position["liquidation_price"], position["side"]
        )

        return {
            "success": True,
            "data": {
                "position": position,
                "risk_level": risk_level
            }
        }
    else:
        return {
            "success": False,
            "error": f"No position found for session {session_id}"
        }

@app.get("/api/config")
async def get_config():
    """获取系统配置信息"""
    return {
        "success": True,
        "data": {
            "exchanges": ["okx", "gate"],
            "strategies": ["macd", "bollinger", "rsi"],
            "timeframes": ["1m", "5m", "15m", "30m", "1h", "4h", "1d"],
            "max_leverage": risk_manager.max_leverage,
            "supported_symbols": list(price_simulator.symbols.keys()),
            "macd_parameters": {
                "fast_period": trading_engine.macd_calculator.fast,
                "slow_period": trading_engine.macd_calculator.slow,
                "signal_period": trading_engine.macd_calculator.signal
            }
        }
    }

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket端点 - 使用统一管理器"""
    from utils.websocket_manager import websocket_manager

    connection_id = None
    try:
        # 使用统一管理器建立连接
        connection_id = await websocket_manager.connect(websocket, {
            "user_agent": websocket.headers.get("user-agent", ""),
            "origin": websocket.headers.get("origin", "")
        })

        # 保持连接并处理客户端消息
        while True:
            try:
                # 等待客户端消息
                data = await websocket.receive_text()
                message = json.loads(data)

                # 使用统一管理器处理消息
                await websocket_manager.handle_client_message(websocket, message)

                # 处理特殊的数据快照请求
                if message.get("action") == "get_current_data":
                    snapshot = {
                        "type": "data_snapshot",
                        "data": {
                            "prices": trading_state.price_data,
                            "macd": trading_state.macd_data,
                            "positions": list(trading_state.active_positions.values()),
                            "sessions": list(trading_state.trading_sessions.values()),
                            "timestamp": datetime.now().isoformat()
                        }
                    }
                    await websocket_manager.send_to_connection(websocket, snapshot)

            except WebSocketDisconnect:
                break
            except Exception as e:
                logger.error(f"WebSocket消息处理错误: {e}")
                break

    except WebSocketDisconnect:
        logger.info("🔌 WebSocket连接已断开")
    except Exception as e:
        logger.error(f"WebSocket错误: {e}")
    finally:
        # 使用统一管理器清理连接
        if connection_id:
            await websocket_manager.disconnect(websocket)

def main():
    """主函数"""
    print("🚀 启动BitV MACD智能加仓交易系统 - 完整版")
    print("=" * 70)
    print("📊 系统特性:")
    print("   🎯 完整MACD技术指标分析")
    print("   📈 智能加仓策略管理")
    print("   ⚠️  实时风险监控系统")
    print("   🔄 多交易所支持 (OKX, Gate.io)")
    print("   📊 完整数据分析和历史记录")
    print("   🔌 WebSocket实时数据推送")
    print("   📱 RESTful API接口")
    print("=" * 70)
    print("🌐 服务地址:")
    print("   - API服务: http://localhost:8000")
    print("   - API文档: http://localhost:8000/docs")
    print("   - 健康检查: http://localhost:8000/health")
    print("   - 系统状态: http://localhost:8000/api/status")
    print("   - WebSocket: ws://localhost:8000/ws")
    print("=" * 70)
    print("🔧 控制命令:")
    print("   - 按 Ctrl+C 停止系统")
    print("   - 访问 /docs 查看完整API文档")
    print("   - 访问 /health 检查系统健康状态")
    print("=" * 70)

    try:
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            log_level="info",
            access_log=True,
            reload=False  # 生产环境建议设为False
        )
    except KeyboardInterrupt:
        print("\n👋 系统已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
