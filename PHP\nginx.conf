# BitV MACD智能加仓交易系统 - Nginx配置
# 将此配置添加到你的Nginx虚拟主机配置中

server {
    listen 80;
    server_name your-domain.com;  # 替换为你的域名
    root /path/to/bitV/PHP;       # 替换为实际路径
    index index.php index.html;

    # 日志配置
    access_log /var/log/nginx/bitv_access.log;
    error_log /var/log/nginx/bitv_error.log;

    # 安全设置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # 阻止访问敏感文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    location ~ \.(env|log|md)$ {
        deny all;
        access_log off;
        log_not_found off;
    }

    location /config/ {
        deny all;
        access_log off;
        log_not_found off;
    }

    # 静态资源处理
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1M;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # 特殊文件直接访问
    location ~ ^/(install|test|debug)\.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;  # 根据你的PHP版本调整
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # PHP处理
    location ~ \.php$ {
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;  # 根据你的PHP版本调整
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # URL重写 - 所有请求转发到index.php
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # 压缩设置
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
}
