#!/usr/bin/env python3
"""
BitV MACD智能加仓交易系统 - 完整版主启动文件
专业级量化交易系统，支持完整的MACD策略、智能加仓、风险管理
"""

import asyncio
import uvicorn
from fastapi import FastAPI, HTTPException, BackgroundTasks, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import logging
import sys
import os
import json
from datetime import datetime, timedelta
import threading
import time
from typing import Dict, Any, Optional, List
import random
import math
import sqlite3
from dataclasses import dataclass, asdict
import uuid

# 配置日志系统
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)s | %(name)s | %(message)s',
    handlers=[
        logging.FileHandler('logs/trading_complete.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# 创建必要的目录
os.makedirs('logs', exist_ok=True)
os.makedirs('data', exist_ok=True)

# 数据模型定义
@dataclass
class TradingSession:
    session_id: str
    exchange: str
    symbol: str
    strategy: str
    leverage: int
    initial_margin: float
    max_add_times: int
    status: str
    start_time: str
    end_time: Optional[str] = None
    total_pnl: float = 0.0
    add_times: int = 0
    positions: List[Dict] = None
    
    def __post_init__(self):
        if self.positions is None:
            self.positions = []

@dataclass
class Position:
    position_id: str
    session_id: str
    symbol: str
    side: str  # 'long' or 'short'
    size: float
    entry_price: float
    current_price: float
    unrealized_pnl: float
    margin: float
    leverage: int
    liquidation_price: float
    timestamp: str

@dataclass
class Trade:
    trade_id: str
    session_id: str
    symbol: str
    side: str  # 'buy' or 'sell'
    type: str  # 'market' or 'limit'
    amount: float
    price: float
    fee: float
    pnl: float
    timestamp: str
    status: str

# 数据库管理器
class DatabaseManager:
    def __init__(self, db_path: str = "data/trading.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """初始化数据库表"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建交易会话表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS trading_sessions (
                session_id TEXT PRIMARY KEY,
                exchange TEXT NOT NULL,
                symbol TEXT NOT NULL,
                strategy TEXT NOT NULL,
                leverage INTEGER NOT NULL,
                initial_margin REAL NOT NULL,
                max_add_times INTEGER NOT NULL,
                status TEXT NOT NULL,
                start_time TEXT NOT NULL,
                end_time TEXT,
                total_pnl REAL DEFAULT 0.0,
                add_times INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建持仓表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS positions (
                position_id TEXT PRIMARY KEY,
                session_id TEXT NOT NULL,
                symbol TEXT NOT NULL,
                side TEXT NOT NULL,
                size REAL NOT NULL,
                entry_price REAL NOT NULL,
                current_price REAL NOT NULL,
                unrealized_pnl REAL NOT NULL,
                margin REAL NOT NULL,
                leverage INTEGER NOT NULL,
                liquidation_price REAL NOT NULL,
                timestamp TEXT NOT NULL,
                FOREIGN KEY (session_id) REFERENCES trading_sessions (session_id)
            )
        ''')
        
        # 创建交易记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS trades (
                trade_id TEXT PRIMARY KEY,
                session_id TEXT NOT NULL,
                symbol TEXT NOT NULL,
                side TEXT NOT NULL,
                type TEXT NOT NULL,
                amount REAL NOT NULL,
                price REAL NOT NULL,
                fee REAL NOT NULL,
                pnl REAL NOT NULL,
                timestamp TEXT NOT NULL,
                status TEXT NOT NULL,
                FOREIGN KEY (session_id) REFERENCES trading_sessions (session_id)
            )
        ''')
        
        # 创建价格数据表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS price_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                price REAL NOT NULL,
                volume REAL NOT NULL,
                timestamp TEXT NOT NULL
            )
        ''')
        
        # 创建MACD数据表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS macd_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                timeframe TEXT NOT NULL,
                macd REAL NOT NULL,
                signal REAL NOT NULL,
                histogram REAL NOT NULL,
                timestamp TEXT NOT NULL
            )
        ''')
        
        conn.commit()
        conn.close()
        logger.info("✅ 数据库初始化完成")
    
    def save_session(self, session: TradingSession):
        """保存交易会话"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO trading_sessions 
            (session_id, exchange, symbol, strategy, leverage, initial_margin, 
             max_add_times, status, start_time, end_time, total_pnl, add_times)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            session.session_id, session.exchange, session.symbol, session.strategy,
            session.leverage, session.initial_margin, session.max_add_times,
            session.status, session.start_time, session.end_time,
            session.total_pnl, session.add_times
        ))
        
        conn.commit()
        conn.close()
    
    def get_sessions(self, status: Optional[str] = None) -> List[Dict]:
        """获取交易会话列表"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        if status:
            cursor.execute('SELECT * FROM trading_sessions WHERE status = ?', (status,))
        else:
            cursor.execute('SELECT * FROM trading_sessions ORDER BY start_time DESC')
        
        columns = [description[0] for description in cursor.description]
        sessions = [dict(zip(columns, row)) for row in cursor.fetchall()]
        
        conn.close()
        return sessions

# MACD技术指标计算器
class MACDCalculator:
    def __init__(self, fast_period: int = 12, slow_period: int = 26, signal_period: int = 9):
        self.fast_period = fast_period
        self.slow_period = slow_period
        self.signal_period = signal_period
        self.price_history = {}
        
    def calculate_ema(self, prices: List[float], period: int) -> float:
        """计算指数移动平均线"""
        if len(prices) < period:
            return sum(prices) / len(prices)
        
        multiplier = 2 / (period + 1)
        ema = prices[0]
        
        for price in prices[1:]:
            ema = (price * multiplier) + (ema * (1 - multiplier))
        
        return ema
    
    def calculate_macd(self, symbol: str, prices: List[float]) -> Optional[Dict]:
        """计算MACD指标"""
        if len(prices) < self.slow_period:
            return None
        
        # 计算快线和慢线EMA
        fast_ema = self.calculate_ema(prices[-self.fast_period:], self.fast_period)
        slow_ema = self.calculate_ema(prices[-self.slow_period:], self.slow_period)
        
        # MACD线 = 快线EMA - 慢线EMA
        macd_line = fast_ema - slow_ema
        
        # 更新历史MACD值
        if symbol not in self.price_history:
            self.price_history[symbol] = []
        
        self.price_history[symbol].append(macd_line)
        
        # 保持历史数据在合理范围内
        if len(self.price_history[symbol]) > self.signal_period * 2:
            self.price_history[symbol] = self.price_history[symbol][-self.signal_period * 2:]
        
        # 计算信号线（MACD的EMA）
        signal_line = self.calculate_ema(self.price_history[symbol], self.signal_period)
        
        # 柱状图 = MACD线 - 信号线
        histogram = macd_line - signal_line
        
        return {
            "symbol": symbol,
            "macd": round(macd_line, 6),
            "signal": round(signal_line, 6),
            "histogram": round(histogram, 6),
            "fast_ema": round(fast_ema, 2),
            "slow_ema": round(slow_ema, 2),
            "timestamp": datetime.now().isoformat()
        }

# 价格数据模拟器
class PriceSimulator:
    def __init__(self):
        self.symbols = {
            "BTC-USDT-SWAP": {"base_price": 50000, "volatility": 0.02},
            "ETH-USDT-SWAP": {"base_price": 3000, "volatility": 0.025},
            "LTC-USDT-SWAP": {"base_price": 100, "volatility": 0.03},
            "XRP-USDT-SWAP": {"base_price": 0.5, "volatility": 0.04},
            "ADA-USDT-SWAP": {"base_price": 0.3, "volatility": 0.05}
        }
        self.price_history = {symbol: [] for symbol in self.symbols}
        self.running = False
        self.websocket_connections = []
        
    async def start(self):
        """启动价格模拟器"""
        self.running = True
        logger.info("🎯 价格模拟器启动")
        
        while self.running:
            for symbol, config in self.symbols.items():
                # 生成价格变化
                change_percent = random.gauss(0, config["volatility"])
                new_price = config["base_price"] * (1 + change_percent)
                
                # 添加一些趋势性
                if len(self.price_history[symbol]) > 10:
                    recent_trend = sum(self.price_history[symbol][-5:]) / 5
                    trend_factor = (new_price - recent_trend) / recent_trend
                    new_price += new_price * trend_factor * 0.1
                
                # 更新基础价格（缓慢调整）
                self.symbols[symbol]["base_price"] = (
                    self.symbols[symbol]["base_price"] * 0.99 + new_price * 0.01
                )
                
                # 保存价格历史
                self.price_history[symbol].append(new_price)
                if len(self.price_history[symbol]) > 1000:
                    self.price_history[symbol] = self.price_history[symbol][-500:]
                
                # 计算24小时变化
                if len(self.price_history[symbol]) >= 288:  # 24小时 * 12 (5分钟间隔)
                    price_24h_ago = self.price_history[symbol][-288]
                    change_24h = ((new_price - price_24h_ago) / price_24h_ago) * 100
                else:
                    change_24h = change_percent * 100
                
                price_data = {
                    "symbol": symbol,
                    "price": round(new_price, 6),
                    "change_24h": round(change_24h, 2),
                    "volume_24h": round(random.uniform(1000000, 50000000), 2),
                    "high_24h": round(new_price * random.uniform(1.01, 1.05), 6),
                    "low_24h": round(new_price * random.uniform(0.95, 0.99), 6),
                    "timestamp": datetime.now().isoformat()
                }
                
                # 广播价格数据到WebSocket连接
                await self.broadcast_price_data(price_data)
            
            await asyncio.sleep(5)  # 每5秒更新一次
    
    async def broadcast_price_data(self, price_data: Dict):
        """广播价格数据到所有WebSocket连接"""
        if self.websocket_connections:
            message = json.dumps({
                "type": "price_update",
                "data": price_data
            })
            
            # 移除断开的连接
            active_connections = []
            for websocket in self.websocket_connections:
                try:
                    await websocket.send_text(message)
                    active_connections.append(websocket)
                except:
                    pass
            
            self.websocket_connections = active_connections
    
    def add_websocket_connection(self, websocket: WebSocket):
        """添加WebSocket连接"""
        self.websocket_connections.append(websocket)
    
    def remove_websocket_connection(self, websocket: WebSocket):
        """移除WebSocket连接"""
        if websocket in self.websocket_connections:
            self.websocket_connections.remove(websocket)
    
    def get_current_prices(self) -> Dict[str, Dict]:
        """获取当前所有价格"""
        current_prices = {}
        for symbol in self.symbols:
            if self.price_history[symbol]:
                current_price = self.price_history[symbol][-1]
                current_prices[symbol] = {
                    "symbol": symbol,
                    "price": round(current_price, 6),
                    "timestamp": datetime.now().isoformat()
                }
        return current_prices
    
    def get_price_history(self, symbol: str, limit: int = 100) -> List[float]:
        """获取价格历史"""
        if symbol in self.price_history:
            return self.price_history[symbol][-limit:]
        return []
    
    def stop(self):
        """停止价格模拟器"""
        self.running = False
        logger.info("🛑 价格模拟器已停止")

# 风险管理器
class RiskManager:
    def __init__(self):
        self.max_leverage = 100
        self.min_margin_ratio = 0.1  # 10%
        self.liquidation_threshold = 0.05  # 5%
        
    def calculate_liquidation_price(self, entry_price: float, leverage: int, side: str) -> float:
        """计算强平价格"""
        margin_ratio = 1 / leverage
        
        if side.lower() == 'long':
            # 多头强平价格 = 入场价格 * (1 - 保证金率 + 手续费率)
            liquidation_price = entry_price * (1 - margin_ratio + 0.001)
        else:
            # 空头强平价格 = 入场价格 * (1 + 保证金率 + 手续费率)
            liquidation_price = entry_price * (1 + margin_ratio + 0.001)
        
        return round(liquidation_price, 6)
    
    def calculate_unrealized_pnl(self, entry_price: float, current_price: float, 
                                size: float, side: str) -> float:
        """计算未实现盈亏"""
        if side.lower() == 'long':
            pnl = (current_price - entry_price) * size
        else:
            pnl = (entry_price - current_price) * size
        
        return round(pnl, 6)
    
    def get_risk_level(self, current_price: float, liquidation_price: float, side: str) -> str:
        """获取风险等级"""
        if side.lower() == 'long':
            distance_ratio = (current_price - liquidation_price) / current_price
        else:
            distance_ratio = (liquidation_price - current_price) / current_price
        
        if distance_ratio > 0.2:
            return "safe"
        elif distance_ratio > 0.1:
            return "warning"
        elif distance_ratio > 0.05:
            return "danger"
        else:
            return "critical"
    
    def should_add_position(self, current_pnl: float, initial_margin: float, 
                          add_times: int, max_add_times: int) -> bool:
        """判断是否应该加仓"""
        # 基本条件检查
        if add_times >= max_add_times:
            return False
        
        # 亏损达到一定比例才考虑加仓
        loss_ratio = abs(current_pnl) / initial_margin
        
        # 根据加仓次数调整阈值
        thresholds = [0.1, 0.2, 0.3, 0.4, 0.5]  # 10%, 20%, 30%, 40%, 50%
        
        if add_times < len(thresholds):
            return current_pnl < 0 and loss_ratio >= thresholds[add_times]
        
        return False
