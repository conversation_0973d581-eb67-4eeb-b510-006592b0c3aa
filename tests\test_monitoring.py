"""
监控模块单元测试
测试持仓监控、价格监控等
"""

import unittest
import asyncio
from unittest.mock import Mock, AsyncMock

from tests import TEST_CONFIG, get_event_loop
from monitoring.position_monitor import AsyncPositionMonitor, PositionRisk, PositionAlert, RiskLevel
from monitoring.price_monitor import AsyncPriceMonitor, PriceAlert, PriceDirection, AlertType
from exchanges.base_exchange import PositionInfo, PositionSide

class TestPositionMonitor(unittest.TestCase):
    """持仓监控测试"""
    
    def setUp(self):
        """测试前准备"""
        self.loop = get_event_loop()
        self.mock_exchange = AsyncMock()
        self.monitor = AsyncPositionMonitor(self.mock_exchange)
    
    def test_monitor_initialization(self):
        """测试监控器初始化"""
        self.assertIsNotNone(self.monitor)
        self.assertEqual(self.monitor.exchange, self.mock_exchange)
        self.assertFalse(self.monitor._is_monitoring)
        self.assertEqual(len(self.monitor._monitoring_tasks), 0)
    
    def test_risk_level_enum(self):
        """测试风险级别枚举"""
        self.assertEqual(RiskLevel.SAFE.value, "safe")
        self.assertEqual(RiskLevel.WARNING.value, "warning")
        self.assertEqual(RiskLevel.DANGER.value, "danger")
        self.assertEqual(RiskLevel.CRITICAL.value, "critical")
    
    def test_calculate_liquidation_distance(self):
        """测试计算强平距离"""
        async def test_calculate():
            # 创建测试持仓
            position = PositionInfo(
                symbol="BTC-USDT",
                side=PositionSide.LONG,
                size=0.1,
                entry_price=50000.0,
                mark_price=50500.0,
                unrealized_pnl=50.0,
                liquidation_price=45000.0,
                leverage=10
            )
            
            current_price = 50000.0
            
            abs_distance, pct_distance = await self.monitor.calculate_liquidation_distance(
                position, current_price
            )
            
            self.assertIsInstance(abs_distance, float)
            self.assertIsInstance(pct_distance, float)
            self.assertGreaterEqual(abs_distance, 0)
            self.assertGreaterEqual(pct_distance, 0)
        
        self.loop.run_until_complete(test_calculate())
    
    def test_assess_risk_level(self):
        """测试风险评估"""
        async def test_assess():
            # 创建测试持仓
            position = PositionInfo(
                symbol="BTC-USDT",
                side=PositionSide.LONG,
                size=0.1,
                entry_price=50000.0,
                mark_price=50500.0,
                unrealized_pnl=50.0,
                liquidation_price=45000.0,
                leverage=10
            )
            
            current_price = 50000.0
            
            risk = await self.monitor.assess_risk_level(position, current_price)
            
            self.assertIsInstance(risk, PositionRisk)
            self.assertEqual(risk.symbol, "BTC-USDT")
            self.assertIn(risk.risk_level, [RiskLevel.SAFE, RiskLevel.WARNING, RiskLevel.DANGER, RiskLevel.CRITICAL])
            self.assertIsInstance(risk.liquidation_distance_pct, float)
        
        self.loop.run_until_complete(test_assess())
    
    def test_position_risk_structure(self):
        """测试持仓风险数据结构"""
        risk = PositionRisk(
            symbol="BTC-USDT",
            risk_level=RiskLevel.WARNING,
            liquidation_distance_pct=0.25,
            liquidation_distance_abs=2500.0,
            current_price=50000.0,
            liquidation_price=47500.0,
            unrealized_pnl=100.0,
            timestamp=1234567890
        )
        
        self.assertEqual(risk.symbol, "BTC-USDT")
        self.assertEqual(risk.risk_level, RiskLevel.WARNING)
        self.assertEqual(risk.liquidation_distance_pct, 0.25)
        self.assertEqual(risk.current_price, 50000.0)

class TestPriceMonitor(unittest.TestCase):
    """价格监控测试"""
    
    def setUp(self):
        """测试前准备"""
        self.loop = get_event_loop()
        self.mock_exchange = AsyncMock()
        self.monitor = AsyncPriceMonitor(self.mock_exchange)
    
    def test_price_monitor_initialization(self):
        """测试价格监控器初始化"""
        self.assertIsNotNone(self.monitor)
        self.assertEqual(self.monitor.exchange, self.mock_exchange)
        self.assertFalse(self.monitor._is_monitoring)
    
    def test_price_direction_enum(self):
        """测试价格方向枚举"""
        self.assertEqual(PriceDirection.UP.value, "up")
        self.assertEqual(PriceDirection.DOWN.value, "down")
        self.assertEqual(PriceDirection.SIDEWAYS.value, "sideways")
    
    def test_alert_type_enum(self):
        """测试预警类型枚举"""
        self.assertEqual(AlertType.PRICE_TARGET.value, "price_target")
        self.assertEqual(AlertType.TREND_CHANGE.value, "trend_change")
        self.assertEqual(AlertType.VOLATILITY.value, "volatility")
    
    def test_price_alert_structure(self):
        """测试价格预警数据结构"""
        alert = PriceAlert(
            symbol="BTC-USDT",
            alert_type=AlertType.PRICE_TARGET,
            current_price=50000.0,
            target_price=51000.0,
            direction=PriceDirection.UP,
            message="价格突破目标位",
            timestamp=1234567890
        )
        
        self.assertEqual(alert.symbol, "BTC-USDT")
        self.assertEqual(alert.alert_type, AlertType.PRICE_TARGET)
        self.assertEqual(alert.current_price, 50000.0)
        self.assertEqual(alert.target_price, 51000.0)
        self.assertEqual(alert.direction, PriceDirection.UP)
    
    def test_add_price_target(self):
        """测试添加价格目标"""
        async def test_add_target():
            await self.monitor.add_price_target("BTC-USDT", 51000.0, PriceDirection.UP)
            
            # 检查目标是否添加
            targets = self.monitor.get_price_targets("BTC-USDT")
            self.assertGreater(len(targets), 0)
        
        self.loop.run_until_complete(test_add_target())
    
    def test_remove_price_target(self):
        """测试移除价格目标"""
        async def test_remove_target():
            # 先添加目标
            await self.monitor.add_price_target("BTC-USDT", 51000.0, PriceDirection.UP)
            
            # 再移除目标
            await self.monitor.remove_price_target("BTC-USDT", 51000.0)
            
            # 检查目标是否移除
            targets = self.monitor.get_price_targets("BTC-USDT")
            target_prices = [t.price for t in targets]
            self.assertNotIn(51000.0, target_prices)
        
        self.loop.run_until_complete(test_remove_target())

if __name__ == '__main__':
    unittest.main()
