<?php
/**
 * 仪表板控制器
 * 
 * @description 实时监控仪表板和数据可视化
 */

require_once 'BaseController.php';

class DashboardController extends BaseController {

    /**
     * 仪表板主页
     */
    public function index() {
        try {
            // 检查API连接
            $this->checkApiConnection();
            
            // 获取实时数据
            $dashboardData = $this->getDashboardData();
            
            $this->render('dashboard/index', [
                'title' => '实时仪表板 - ' . SYSTEM_NAME,
                'dashboard_data' => $dashboardData,
                'refresh_interval' => 5000 // 5秒刷新间隔
            ]);
            
        } catch (Exception $e) {
            $this->render('dashboard/error', [
                'title' => '仪表板错误',
                'error_message' => $e->getMessage()
            ]);
        }
    }

    /**
     * 获取仪表板数据
     */
    private function getDashboardData() {
        $data = [
            'trading_status' => null,
            'positions' => [],
            'account_balance' => null,
            'price_data' => [],
            'risk_metrics' => null,
            'recent_trades' => [],
            'system_metrics' => []
        ];

        try {
            // 获取交易状态
            $response = $this->api->getTradingStatus();
            if ($response['success']) {
                $data['trading_status'] = $response['data'];
            }

            // 获取持仓信息
            $response = $this->api->getPositions();
            if ($response['success']) {
                $data['positions'] = $response['data'];
            }

            // 获取账户余额
            $response = $this->api->getAccountBalance();
            if ($response['success']) {
                $data['account_balance'] = $response['data'];
            }

            // 获取风险指标
            $response = $this->api->getRiskMetrics();
            if ($response['success']) {
                $data['risk_metrics'] = $response['data'];
            }

            // 获取最近交易记录
            $data['recent_trades'] = $this->getRecentTrades(10);

            // 获取系统指标
            $data['system_metrics'] = $this->getSystemMetrics();

        } catch (Exception $e) {
            error_log('获取仪表板数据失败: ' . $e->getMessage());
        }

        return $data;
    }

    /**
     * 获取实时数据 (AJAX)
     */
    public function getRealTimeData() {
        try {
            $data = $this->getDashboardData();
            $this->success('获取实时数据成功', $data);
        } catch (Exception $e) {
            $this->error('获取实时数据失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取价格数据
     */
    public function getPriceData() {
        try {
            $symbol = $this->request['symbol'] ?? 'BTC-USDT-SWAP';
            $timeframe = $this->request['timeframe'] ?? '30m';
            $limit = min(100, intval($this->request['limit'] ?? 50));

            // 获取K线数据
            $response = $this->api->getKlineData($symbol, $timeframe, $limit);
            
            if ($response['success']) {
                $klines = $response['data'];
                
                // 格式化数据用于图表显示
                $chartData = [
                    'labels' => [],
                    'prices' => [],
                    'volumes' => []
                ];
                
                foreach ($klines as $kline) {
                    $chartData['labels'][] = date('H:i', $kline['timestamp']);
                    $chartData['prices'][] = $kline['close'];
                    $chartData['volumes'][] = $kline['volume'];
                }
                
                $this->success('获取价格数据成功', $chartData);
            } else {
                $this->error('获取价格数据失败');
            }
            
        } catch (Exception $e) {
            $this->error('获取价格数据失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取MACD指标数据
     */
    public function getMACDData() {
        try {
            $symbol = $this->request['symbol'] ?? 'BTC-USDT-SWAP';
            $timeframe = $this->request['timeframe'] ?? '30m';

            $response = $this->api->getMACDData($symbol, $timeframe);
            
            if ($response['success']) {
                $macdData = $response['data'];
                
                // 格式化MACD数据
                $chartData = [
                    'labels' => [],
                    'dif' => [],
                    'dea' => [],
                    'histogram' => []
                ];
                
                foreach ($macdData as $data) {
                    $chartData['labels'][] = date('H:i', $data['timestamp']);
                    $chartData['dif'][] = $data['dif'];
                    $chartData['dea'][] = $data['dea'];
                    $chartData['histogram'][] = $data['histogram'];
                }
                
                $this->success('获取MACD数据成功', $chartData);
            } else {
                $this->error('获取MACD数据失败');
            }
            
        } catch (Exception $e) {
            $this->error('获取MACD数据失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取布林带数据
     */
    public function getBollingerData() {
        try {
            $symbol = $this->request['symbol'] ?? 'BTC-USDT-SWAP';
            $timeframe = $this->request['timeframe'] ?? '30m';
            $period = intval($this->request['period'] ?? 20);
            $stdDev = floatval($this->request['std_dev'] ?? 2.0);

            $response = $this->api->getBollingerData($symbol, $timeframe, $period, $stdDev);
            
            if ($response['success']) {
                $bollingerData = $response['data'];
                
                // 格式化布林带数据
                $chartData = [
                    'labels' => [],
                    'upper' => [],
                    'middle' => [],
                    'lower' => [],
                    'price' => []
                ];
                
                foreach ($bollingerData as $data) {
                    $chartData['labels'][] = date('H:i', $data['timestamp']);
                    $chartData['upper'][] = $data['upper'];
                    $chartData['middle'][] = $data['middle'];
                    $chartData['lower'][] = $data['lower'];
                    $chartData['price'][] = $data['price'];
                }
                
                $this->success('获取布林带数据成功', $chartData);
            } else {
                $this->error('获取布林带数据失败');
            }
            
        } catch (Exception $e) {
            $this->error('获取布林带数据失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取持仓风险分析
     */
    public function getPositionRisk() {
        try {
            $symbol = $this->request['symbol'] ?? null;
            
            $response = $this->api->getRiskMetrics($symbol);
            
            if ($response['success']) {
                $riskData = $response['data'];
                
                // 计算风险等级分布
                $riskDistribution = [
                    'safe' => 0,
                    'warning' => 0,
                    'danger' => 0,
                    'critical' => 0
                ];
                
                foreach ($riskData as $risk) {
                    $level = $risk['risk_level'];
                    if (isset($riskDistribution[$level])) {
                        $riskDistribution[$level]++;
                    }
                }
                
                $this->success('获取风险分析成功', [
                    'risk_data' => $riskData,
                    'risk_distribution' => $riskDistribution
                ]);
            } else {
                $this->error('获取风险分析失败');
            }
            
        } catch (Exception $e) {
            $this->error('获取风险分析失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取最近交易记录
     */
    private function getRecentTrades($limit = 10) {
        try {
            $userId = Session::getUserId() ?? 'default';
            
            return $this->db->fetchAll(
                'SELECT tr.*, ts.symbol, ts.exchange 
                 FROM trading_records tr
                 JOIN trading_sessions ts ON tr.session_id = ts.session_id
                 WHERE ts.user_id = ?
                 ORDER BY tr.timestamp DESC
                 LIMIT ?',
                [$userId, $limit]
            );
        } catch (Exception $e) {
            error_log('获取最近交易记录失败: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 获取系统指标
     */
    private function getSystemMetrics() {
        try {
            $userId = Session::getUserId() ?? 'default';
            
            // 今日交易统计
            $todayStats = $this->db->fetch(
                'SELECT 
                    COUNT(*) as trade_count,
                    SUM(CASE WHEN pnl > 0 THEN 1 ELSE 0 END) as profit_trades,
                    SUM(CASE WHEN pnl < 0 THEN 1 ELSE 0 END) as loss_trades,
                    SUM(pnl) as total_pnl
                 FROM trading_records tr
                 JOIN trading_sessions ts ON tr.session_id = ts.session_id
                 WHERE ts.user_id = ? AND DATE(tr.timestamp) = CURDATE()',
                [$userId]
            );
            
            // 本周统计
            $weekStats = $this->db->fetch(
                'SELECT 
                    COUNT(*) as trade_count,
                    SUM(pnl) as total_pnl
                 FROM trading_records tr
                 JOIN trading_sessions ts ON tr.session_id = ts.session_id
                 WHERE ts.user_id = ? AND WEEK(tr.timestamp) = WEEK(NOW())',
                [$userId]
            );
            
            // 胜率计算
            $winRate = 0;
            if ($todayStats['trade_count'] > 0) {
                $winRate = ($todayStats['profit_trades'] / $todayStats['trade_count']) * 100;
            }
            
            return [
                'today' => $todayStats,
                'week' => $weekStats,
                'win_rate' => $winRate
            ];
            
        } catch (Exception $e) {
            error_log('获取系统指标失败: ' . $e->getMessage());
            return [
                'today' => ['trade_count' => 0, 'total_pnl' => 0],
                'week' => ['trade_count' => 0, 'total_pnl' => 0],
                'win_rate' => 0
            ];
        }
    }

    /**
     * 获取性能统计
     */
    public function getPerformanceStats() {
        try {
            $period = $this->request['period'] ?? '7d'; // 7d, 30d, 90d
            
            $days = 7;
            switch ($period) {
                case '30d': $days = 30; break;
                case '90d': $days = 90; break;
            }
            
            $userId = Session::getUserId() ?? 'default';
            
            // 获取期间内的交易统计
            $stats = $this->db->fetchAll(
                'SELECT 
                    DATE(tr.timestamp) as trade_date,
                    COUNT(*) as trade_count,
                    SUM(pnl) as daily_pnl,
                    SUM(CASE WHEN pnl > 0 THEN 1 ELSE 0 END) as profit_trades
                 FROM trading_records tr
                 JOIN trading_sessions ts ON tr.session_id = ts.session_id
                 WHERE ts.user_id = ? AND tr.timestamp >= DATE_SUB(NOW(), INTERVAL ? DAY)
                 GROUP BY DATE(tr.timestamp)
                 ORDER BY trade_date',
                [$userId, $days]
            );
            
            // 格式化数据
            $chartData = [
                'labels' => [],
                'pnl' => [],
                'trades' => []
            ];
            
            foreach ($stats as $stat) {
                $chartData['labels'][] = date('m-d', strtotime($stat['trade_date']));
                $chartData['pnl'][] = floatval($stat['daily_pnl']);
                $chartData['trades'][] = intval($stat['trade_count']);
            }
            
            $this->success('获取性能统计成功', $chartData);
            
        } catch (Exception $e) {
            $this->error('获取性能统计失败: ' . $e->getMessage());
        }
    }
}
?>
