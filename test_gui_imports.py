"""
测试GUI导入问题
"""
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("开始测试导入...")

# 测试基础模块
try:
    import tkinter as tk
    print("✅ tkinter 导入成功")
except ImportError as e:
    print(f"❌ tkinter 导入失败: {e}")

try:
    import asyncio
    print("✅ asyncio 导入成功")
except ImportError as e:
    print(f"❌ asyncio 导入失败: {e}")

# 测试exchanges模块
try:
    from exchanges import BaseExchange
    print("✅ BaseExchange 导入成功")
except ImportError as e:
    print(f"❌ BaseExchange 导入失败: {e}")

try:
    from exchanges import OKXExchange
    print("✅ OKXExchange 导入成功")
except ImportError as e:
    print(f"❌ OKXExchange 导入失败: {e}")

try:
    from exchanges import GateIOExchange
    print("✅ GateIOExchange 导入成功")
except ImportError as e:
    print(f"❌ GateIOExchange 导入失败: {e}")

try:
    from exchanges import ExchangeFactory
    print("✅ ExchangeFactory 导入成功")
except ImportError as e:
    print(f"❌ ExchangeFactory 导入失败: {e}")

# 测试core模块
try:
    from core import AsyncTradingController
    print("✅ AsyncTradingController 导入成功")
except ImportError as e:
    print(f"❌ AsyncTradingController 导入失败: {e}")

try:
    from core import TradingState
    print("✅ TradingState 导入成功")
except ImportError as e:
    print(f"❌ TradingState 导入失败: {e}")

# 测试indicators模块
try:
    from indicators import AsyncMACDCalculator
    print("✅ AsyncMACDCalculator 导入成功")
except ImportError as e:
    print(f"❌ AsyncMACDCalculator 导入失败: {e}")

# 测试strategies模块
try:
    from strategies.opening_strategy import AsyncOpeningStrategy
    print("✅ AsyncOpeningStrategy 导入成功")
except ImportError as e:
    print(f"❌ AsyncOpeningStrategy 导入失败: {e}")

# 测试monitoring模块
try:
    from monitoring.position_monitor import AsyncPositionMonitor
    print("✅ AsyncPositionMonitor 导入成功")
except ImportError as e:
    print(f"❌ AsyncPositionMonitor 导入失败: {e}")

# 测试config模块
try:
    from config import TradingConfig
    print("✅ TradingConfig 导入成功")
except ImportError as e:
    print(f"❌ TradingConfig 导入失败: {e}")

# 测试GUI模块
try:
    from gui import MainWindow
    print("✅ MainWindow 导入成功")
except ImportError as e:
    print(f"❌ MainWindow 导入失败: {e}")

try:
    from utils.async_gui import run_async_gui, set_async_tk
    print("✅ async_gui 导入成功")
except ImportError as e:
    print(f"❌ async_gui 导入失败: {e}")

print("\n导入测试完成！")
