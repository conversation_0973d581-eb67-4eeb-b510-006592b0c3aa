<?php
/**
 * 数据库连接和操作类
 * 
 * @description 提供安全的数据库连接和操作方法
 */

class Database {
    private static $instance = null;
    private $connection;
    private $host;
    private $dbname;
    private $username;
    private $password;
    private $charset;

    private function __construct() {
        $this->host = DB_HOST;
        $this->dbname = DB_NAME;
        $this->username = DB_USER;
        $this->password = DB_PASS;
        $this->charset = DB_CHARSET;
        
        $this->connect();
    }

    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function connect() {
        try {
            $dsn = "mysql:host={$this->host};dbname={$this->dbname};charset={$this->charset}";
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$this->charset}"
            ];
            
            $this->connection = new PDO($dsn, $this->username, $this->password, $options);
            
            // 创建必要的表
            $this->createTables();
            
        } catch (PDOException $e) {
            if (DEBUG_MODE) {
                throw new Exception("数据库连接失败: " . $e->getMessage());
            } else {
                throw new Exception("数据库连接失败");
            }
        }
    }

    private function createTables() {
        $tables = [
            // 用户配置表
            'user_configs' => "
                CREATE TABLE IF NOT EXISTS user_configs (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id VARCHAR(50) NOT NULL DEFAULT 'default',
                    config_key VARCHAR(100) NOT NULL,
                    config_value TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    UNIQUE KEY unique_user_config (user_id, config_key)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            ",
            
            // 交易会话表
            'trading_sessions' => "
                CREATE TABLE IF NOT EXISTS trading_sessions (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    session_id VARCHAR(100) NOT NULL UNIQUE,
                    user_id VARCHAR(50) NOT NULL DEFAULT 'default',
                    exchange VARCHAR(20) NOT NULL,
                    symbol VARCHAR(50) NOT NULL,
                    strategy VARCHAR(50) NOT NULL,
                    initial_margin DECIMAL(15,4) NOT NULL,
                    leverage INT NOT NULL,
                    status ENUM('active', 'stopped', 'error') DEFAULT 'active',
                    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    end_time TIMESTAMP NULL,
                    total_pnl DECIMAL(15,4) DEFAULT 0,
                    total_add_times INT DEFAULT 0,
                    metadata JSON,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            ",
            
            // 交易记录表
            'trading_records' => "
                CREATE TABLE IF NOT EXISTS trading_records (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    session_id VARCHAR(100) NOT NULL,
                    order_id VARCHAR(100),
                    exchange VARCHAR(20) NOT NULL,
                    symbol VARCHAR(50) NOT NULL,
                    side ENUM('buy', 'sell') NOT NULL,
                    type ENUM('market', 'limit') NOT NULL,
                    amount DECIMAL(15,8) NOT NULL,
                    price DECIMAL(15,4),
                    filled_amount DECIMAL(15,8) DEFAULT 0,
                    status VARCHAR(20) DEFAULT 'pending',
                    pnl DECIMAL(15,4) DEFAULT 0,
                    fee DECIMAL(15,4) DEFAULT 0,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    metadata JSON,
                    INDEX idx_session (session_id),
                    INDEX idx_symbol (symbol),
                    INDEX idx_timestamp (timestamp)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            ",
            
            // 持仓监控记录表
            'position_monitors' => "
                CREATE TABLE IF NOT EXISTS position_monitors (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    session_id VARCHAR(100) NOT NULL,
                    symbol VARCHAR(50) NOT NULL,
                    position_side ENUM('long', 'short') NOT NULL,
                    size DECIMAL(15,8) NOT NULL,
                    entry_price DECIMAL(15,4) NOT NULL,
                    mark_price DECIMAL(15,4) NOT NULL,
                    liquidation_price DECIMAL(15,4),
                    unrealized_pnl DECIMAL(15,4) DEFAULT 0,
                    margin DECIMAL(15,4) NOT NULL,
                    leverage INT NOT NULL,
                    risk_level ENUM('safe', 'warning', 'danger', 'critical') DEFAULT 'safe',
                    distance_to_liquidation DECIMAL(8,4),
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_session (session_id),
                    INDEX idx_symbol (symbol),
                    INDEX idx_timestamp (timestamp)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            ",
            
            // 系统日志表
            'system_logs' => "
                CREATE TABLE IF NOT EXISTS system_logs (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    level ENUM('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL') NOT NULL,
                    message TEXT NOT NULL,
                    context JSON,
                    user_id VARCHAR(50) DEFAULT 'system',
                    ip_address VARCHAR(45),
                    user_agent TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_level (level),
                    INDEX idx_timestamp (timestamp),
                    INDEX idx_user (user_id)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            "
        ];

        foreach ($tables as $tableName => $sql) {
            try {
                $this->connection->exec($sql);
            } catch (PDOException $e) {
                error_log("创建表 {$tableName} 失败: " . $e->getMessage());
            }
        }
    }

    public function getConnection() {
        return $this->connection;
    }

    public function query($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            if (DEBUG_MODE) {
                throw new Exception("查询执行失败: " . $e->getMessage() . " SQL: " . $sql);
            } else {
                throw new Exception("查询执行失败");
            }
        }
    }

    public function fetch($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }

    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }

    public function insert($table, $data) {
        $columns = implode(',', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        
        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        $stmt = $this->query($sql, $data);
        
        return $this->connection->lastInsertId();
    }

    public function update($table, $data, $where, $whereParams = []) {
        $setClause = [];
        foreach (array_keys($data) as $key) {
            $setClause[] = "{$key} = :{$key}";
        }
        $setClause = implode(', ', $setClause);
        
        $sql = "UPDATE {$table} SET {$setClause} WHERE {$where}";
        $params = array_merge($data, $whereParams);
        
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }

    public function delete($table, $where, $params = []) {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }

    public function beginTransaction() {
        return $this->connection->beginTransaction();
    }

    public function commit() {
        return $this->connection->commit();
    }

    public function rollback() {
        return $this->connection->rollback();
    }

    public function lastInsertId() {
        return $this->connection->lastInsertId();
    }

    // 防止克隆
    private function __clone() {}
    
    // 防止反序列化
    private function __wakeup() {}
}
?>
