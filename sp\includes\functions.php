<?php
/**
 * BitV MACD智能加仓交易系统 - 核心功能函数
 * 实盘交易版本 - 与Python后端API交互
 */

if (!defined('BITV_ACCESS')) {
    die('Direct access not allowed');
}

/**
 * 发送HTTP请求到Python后端API
 */
function sendAPIRequest($endpoint, $method = 'GET', $data = null, $headers = []) {
    $url = PYTHON_API_BASE . $endpoint;
    
    // 默认头部
    $defaultHeaders = [
        'Content-Type: application/json',
        'Authorization: Bearer ' . API_TOKEN,
        'User-Agent: BitV-PHP-Client/2.0'
    ];
    
    $headers = array_merge($defaultHeaders, $headers);
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_CONNECTTIMEOUT => 10,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_HTTPHEADER => $headers,
        CURLOPT_USERAGENT => 'BitV-PHP-Client/2.0'
    ]);
    
    // 设置请求方法和数据
    switch (strtoupper($method)) {
        case 'POST':
            curl_setopt($ch, CURLOPT_POST, true);
            if ($data) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
            break;
        case 'PUT':
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
            if ($data) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
            break;
        case 'DELETE':
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
            break;
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    // 记录API调用日志
    writeLog('INFO', "API调用: {$method} {$endpoint}", [
        'http_code' => $httpCode,
        'response_size' => strlen($response),
        'error' => $error
    ]);
    
    if ($error) {
        writeLog('ERROR', "API请求失败: {$error}", ['url' => $url]);
        return [
            'success' => false,
            'error' => 'API连接失败: ' . $error,
            'http_code' => 0
        ];
    }
    
    $decodedResponse = json_decode($response, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        writeLog('ERROR', 'API响应JSON解析失败', [
            'json_error' => json_last_error_msg(),
            'response' => substr($response, 0, 500)
        ]);
        return [
            'success' => false,
            'error' => 'API响应格式错误',
            'http_code' => $httpCode
        ];
    }
    
    return [
        'success' => $httpCode >= 200 && $httpCode < 300,
        'data' => $decodedResponse,
        'http_code' => $httpCode,
        'error' => $httpCode >= 400 ? ($decodedResponse['detail'] ?? 'API错误') : null
    ];
}

/**
 * 获取系统状态
 */
function getSystemStatus() {
    return sendAPIRequest('/api/status');
}

/**
 * 获取账户余额
 */
function getAccountBalance() {
    return sendAPIRequest('/api/account/balance');
}

/**
 * 获取活跃持仓
 */
function getActivePositions() {
    return sendAPIRequest('/api/account/positions');
}

/**
 * 获取交易会话
 */
function getTradingSessions() {
    return sendAPIRequest('/api/trading/sessions');
}

/**
 * 获取市场价格
 */
function getMarketPrices() {
    return sendAPIRequest('/api/market/prices');
}

/**
 * 获取特定交易对价格
 */
function getSymbolPrice($symbol) {
    return sendAPIRequest("/api/market/prices/{$symbol}");
}

/**
 * 启动交易
 */
function startTrading($config) {
    // 验证配置
    $validationResult = validateTradingConfig($config);
    if (!$validationResult['valid']) {
        return [
            'success' => false,
            'error' => $validationResult['error']
        ];
    }
    
    return sendAPIRequest('/api/trading/start', 'POST', $config);
}

/**
 * 停止交易
 */
function stopTrading($sessionId = null, $emergency = false) {
    $data = [];
    if ($sessionId) {
        $data['session_id'] = $sessionId;
    }
    if ($emergency) {
        $data['emergency'] = true;
    }
    
    return sendAPIRequest('/api/trading/stop', 'POST', $data);
}

/**
 * 紧急停止所有交易
 */
function emergencyStopAll() {
    return sendAPIRequest('/api/emergency/stop', 'POST');
}

/**
 * 配置交易所
 */
function configureExchange($config) {
    // 验证配置
    $required = ['exchange', 'api_key', 'api_secret'];
    foreach ($required as $field) {
        if (empty($config[$field])) {
            return [
                'success' => false,
                'error' => "缺少必要字段: {$field}"
            ];
        }
    }
    
    // 验证交易所是否支持
    if (!array_key_exists($config['exchange'], $GLOBALS['SUPPORTED_EXCHANGES'])) {
        return [
            'success' => false,
            'error' => '不支持的交易所: ' . $config['exchange']
        ];
    }
    
    return sendAPIRequest('/api/configure-exchange', 'POST', $config);
}

/**
 * 获取审计日志
 */
function getAuditLogs($limit = 100) {
    return sendAPIRequest("/api/audit/logs?limit={$limit}");
}

/**
 * 验证交易配置
 */
function validateTradingConfig($config) {
    $errors = [];
    
    // 验证交易对
    if (empty($config['symbol'])) {
        $errors[] = '交易对不能为空';
    } elseif (!array_key_exists($config['symbol'], $GLOBALS['SUPPORTED_SYMBOLS'])) {
        $errors[] = '不支持的交易对: ' . $config['symbol'];
    }
    
    // 验证杠杆
    if (empty($config['leverage']) || !is_numeric($config['leverage'])) {
        $errors[] = '杠杆倍数必须是数字';
    } elseif ($config['leverage'] < 1 || $config['leverage'] > MAX_LEVERAGE) {
        $errors[] = "杠杆倍数必须在1-" . MAX_LEVERAGE . "之间";
    }
    
    // 验证保证金
    if (empty($config['initial_margin']) || !is_numeric($config['initial_margin'])) {
        $errors[] = '初始保证金必须是数字';
    } elseif ($config['initial_margin'] < MIN_MARGIN || $config['initial_margin'] > MAX_MARGIN) {
        $errors[] = "初始保证金必须在" . MIN_MARGIN . "-" . MAX_MARGIN . " USDT之间";
    }
    
    // 验证加仓次数
    if (isset($config['max_add_times'])) {
        if (!is_numeric($config['max_add_times']) || $config['max_add_times'] < 0 || $config['max_add_times'] > 10) {
            $errors[] = '最大加仓次数必须在0-10之间';
        }
    }
    
    return [
        'valid' => empty($errors),
        'error' => implode('; ', $errors)
    ];
}

/**
 * 格式化数字显示
 */
function formatNumber($number, $decimals = 2, $suffix = '') {
    if (!is_numeric($number)) {
        return '---';
    }
    
    $formatted = number_format($number, $decimals);
    return $formatted . $suffix;
}

/**
 * 格式化价格显示
 */
function formatPrice($price, $symbol = '') {
    if (!is_numeric($price)) {
        return '---';
    }
    
    // 根据交易对确定小数位数
    $decimals = 2;
    if (strpos($symbol, 'BTC') !== false) {
        $decimals = 1;
    } elseif (strpos($symbol, 'ETH') !== false) {
        $decimals = 2;
    }
    
    return '$' . number_format($price, $decimals);
}

/**
 * 格式化百分比显示
 */
function formatPercentage($percentage, $showSign = true) {
    if (!is_numeric($percentage)) {
        return '---';
    }
    
    $sign = '';
    if ($showSign && $percentage > 0) {
        $sign = '+';
    }
    
    return $sign . number_format($percentage, 2) . '%';
}

/**
 * 格式化时间显示
 */
function formatTime($timestamp, $format = 'Y-m-d H:i:s') {
    if (empty($timestamp)) {
        return '---';
    }
    
    try {
        $date = new DateTime($timestamp);
        return $date->format($format);
    } catch (Exception $e) {
        return '---';
    }
}

/**
 * 获取风险等级颜色
 */
function getRiskLevelColor($level) {
    $colors = [
        'safe' => 'success',
        'low' => 'info',
        'warning' => 'warning',
        'danger' => 'danger',
        'critical' => 'danger'
    ];
    
    return $colors[$level] ?? 'secondary';
}

/**
 * 获取风险等级文本
 */
function getRiskLevelText($level) {
    $texts = [
        'safe' => '安全',
        'low' => '低风险',
        'warning' => '警告',
        'danger' => '危险',
        'critical' => '极危险'
    ];
    
    return $texts[$level] ?? '未知';
}

/**
 * 获取交易状态颜色
 */
function getStatusColor($status) {
    $colors = [
        'active' => 'success',
        'stopped' => 'secondary',
        'error' => 'danger',
        'pending' => 'warning'
    ];
    
    return $colors[$status] ?? 'secondary';
}

/**
 * 获取交易状态文本
 */
function getStatusText($status) {
    $texts = [
        'active' => '运行中',
        'stopped' => '已停止',
        'error' => '错误',
        'pending' => '等待中'
    ];
    
    return $texts[$status] ?? $status;
}

/**
 * 生成CSRF令牌
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * 验证CSRF令牌
 */
function validateCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * 清理输入数据
 */
function sanitizeInput($input) {
    if (is_array($input)) {
        return array_map('sanitizeInput', $input);
    }
    
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

/**
 * 验证邮箱格式
 */
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * 生成随机字符串
 */
function generateRandomString($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

/**
 * 检查系统健康状态
 */
function checkSystemHealth() {
    $health = [
        'overall' => 'healthy',
        'components' => []
    ];
    
    // 检查Python后端
    $apiHealth = sendAPIRequest('/health');
    $health['components']['python_api'] = [
        'status' => $apiHealth['success'] ? 'healthy' : 'unhealthy',
        'details' => $apiHealth['data'] ?? null
    ];
    
    // 检查数据库
    try {
        $pdo = getDBConnection();
        $pdo->query('SELECT 1');
        $health['components']['database'] = [
            'status' => 'healthy',
            'details' => 'Database connection successful'
        ];
    } catch (Exception $e) {
        $health['components']['database'] = [
            'status' => 'unhealthy',
            'details' => $e->getMessage()
        ];
        $health['overall'] = 'unhealthy';
    }
    
    // 检查文件系统
    $writableDirectories = [LOGS_PATH, UPLOADS_PATH, CACHE_PATH];
    $filesystemHealthy = true;
    
    foreach ($writableDirectories as $dir) {
        if (!is_writable($dir)) {
            $filesystemHealthy = false;
            break;
        }
    }
    
    $health['components']['filesystem'] = [
        'status' => $filesystemHealthy ? 'healthy' : 'unhealthy',
        'details' => $filesystemHealthy ? 'All directories writable' : 'Some directories not writable'
    ];
    
    if (!$filesystemHealthy) {
        $health['overall'] = 'unhealthy';
    }
    
    return $health;
}

/**
 * 记录用户操作
 */
function logUserAction($action, $details = [], $userId = null) {
    if (!$userId && isset($_SESSION['user_id'])) {
        $userId = $_SESSION['user_id'];
    }
    
    $logData = [
        'user_id' => $userId,
        'action' => $action,
        'details' => $details,
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    writeLog('INFO', "用户操作: {$action}", $logData);
    
    // 可以在这里添加数据库记录逻辑
}

/**
 * 获取客户端IP地址
 */
function getClientIP() {
    $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
    
    foreach ($ipKeys as $key) {
        if (!empty($_SERVER[$key])) {
            $ips = explode(',', $_SERVER[$key]);
            $ip = trim($ips[0]);
            
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                return $ip;
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
}

/**
 * 检查是否为AJAX请求
 */
function isAjaxRequest() {
    return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
}

/**
 * 返回JSON响应
 */
function jsonResponse($data, $httpCode = 200) {
    http_response_code($httpCode);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit;
}

/**
 * 重定向到指定页面
 */
function redirect($url, $permanent = false) {
    $code = $permanent ? 301 : 302;
    http_response_code($code);
    header("Location: {$url}");
    exit;
}

/**
 * 交易相关函数
 */

// 启动交易
function startTrading($config) {
    return sendAPIRequest('/api/trading/start', 'POST', $config);
}

// 停止交易
function stopTrading($sessionId, $emergency = false) {
    return sendAPIRequest('/api/trading/stop', 'POST', [
        'session_id' => $sessionId,
        'emergency' => $emergency
    ]);
}

// 紧急停止所有交易
function emergencyStopAll() {
    return sendAPIRequest('/api/emergency/stop', 'POST');
}

// 获取系统状态
function getSystemStatus() {
    return sendAPIRequest('/api/status');
}

// 获取市场价格
function getMarketPrices() {
    return sendAPIRequest('/api/market/prices');
}

// 获取交易会话
function getTradingSessions() {
    return sendAPIRequest('/api/trading/sessions');
}

// 获取活跃持仓
function getActivePositions() {
    return sendAPIRequest('/api/positions');
}

// 获取账户余额
function getAccountBalance() {
    return sendAPIRequest('/api/account/balance');
}

// 获取风险指标
function getRiskMetrics() {
    return sendAPIRequest('/api/risk/metrics');
}

// 获取交易历史
function getTradingHistory($filters = []) {
    $query = http_build_query($filters);
    return sendAPIRequest('/api/trading/history?' . $query);
}

// 获取交易统计
function getTradingStatistics($filters = []) {
    $query = http_build_query($filters);
    return sendAPIRequest('/api/trading/statistics?' . $query);
}

// 获取绩效指标
function getPerformanceMetrics($filters = []) {
    $query = http_build_query($filters);
    return sendAPIRequest('/api/performance/metrics?' . $query);
}

// 获取系统日志
function getSystemLogs($filters = []) {
    $query = http_build_query($filters);
    return sendAPIRequest('/api/logs?' . $query);
}

// 获取日志统计
function getLogStatistics($filters = []) {
    $query = http_build_query($filters);
    return sendAPIRequest('/api/logs/statistics?' . $query);
}

// 获取系统健康指标
function getSystemHealthMetrics() {
    return sendAPIRequest('/api/system/health');
}

// 持仓操作函数
function closePosition($positionId, $closeRatio = 100, $orderType = 'market', $limitPrice = 0) {
    return sendAPIRequest('/api/positions/close', 'POST', [
        'position_id' => $positionId,
        'close_ratio' => $closeRatio,
        'order_type' => $orderType,
        'limit_price' => $limitPrice
    ]);
}

function addPosition($positionId, $addMargin, $orderType = 'market', $limitPrice = 0) {
    return sendAPIRequest('/api/positions/add', 'POST', [
        'position_id' => $positionId,
        'add_margin' => $addMargin,
        'order_type' => $orderType,
        'limit_price' => $limitPrice
    ]);
}

function setStopLoss($positionId, $stopLossPrice, $stopLossType = 'stop_market') {
    return sendAPIRequest('/api/positions/stop-loss', 'POST', [
        'position_id' => $positionId,
        'stop_loss_price' => $stopLossPrice,
        'stop_loss_type' => $stopLossType
    ]);
}

function setTakeProfit($positionId, $takeProfitPrice, $takeProfitType = 'limit') {
    return sendAPIRequest('/api/positions/take-profit', 'POST', [
        'position_id' => $positionId,
        'take_profit_price' => $takeProfitPrice,
        'take_profit_type' => $takeProfitType
    ]);
}

function emergencyCloseAllPositions() {
    return sendAPIRequest('/api/positions/emergency-close-all', 'POST');
}

// 辅助函数
function getTradeStatusColor($status) {
    $colors = [
        'filled' => 'success',
        'partial' => 'warning',
        'cancelled' => 'secondary',
        'rejected' => 'danger',
        'pending' => 'info'
    ];
    return $colors[$status] ?? 'secondary';
}

function getTradeStatusText($status) {
    $texts = [
        'filled' => '已成交',
        'partial' => '部分成交',
        'cancelled' => '已取消',
        'rejected' => '已拒绝',
        'pending' => '待成交'
    ];
    return $texts[$status] ?? '未知';
}

function getAlertClass($level) {
    $classes = [
        'info' => 'alert-info',
        'warning' => 'alert-warning',
        'danger' => 'alert-danger',
        'critical' => 'alert-danger'
    ];
    return $classes[$level] ?? 'alert-info';
}

function getAlertIcon($level) {
    $icons = [
        'info' => 'info-circle',
        'warning' => 'exclamation-triangle',
        'danger' => 'exclamation-circle',
        'critical' => 'skull-crossbones'
    ];
    return $icons[$level] ?? 'info-circle';
}

function getRiskColor($distance) {
    if ($distance > 0.3) return 'success';
    if ($distance > 0.2) return 'info';
    if ($distance > 0.1) return 'warning';
    if ($distance > 0.05) return 'danger';
    return 'danger';
}

function getHealthStatusColor($status) {
    $colors = [
        'healthy' => 'success',
        'warning' => 'warning',
        'critical' => 'danger',
        'unknown' => 'secondary'
    ];
    return $colors[$status] ?? 'secondary';
}

function getHealthStatusText($status) {
    $texts = [
        'healthy' => '健康',
        'warning' => '警告',
        'critical' => '严重',
        'unknown' => '未知'
    ];
    return $texts[$status] ?? '未知';
}

function getLogLevelColor($level) {
    $colors = [
        'debug' => 'secondary',
        'info' => 'primary',
        'warning' => 'warning',
        'error' => 'danger',
        'critical' => 'danger',
        'audit' => 'success'
    ];
    return $colors[$level] ?? 'secondary';
}

?>
