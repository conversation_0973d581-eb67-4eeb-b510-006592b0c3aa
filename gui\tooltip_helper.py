"""
工具提示辅助类
为GUI控件提供悬停说明功能
"""
import tkinter as tk
from tkinter import ttk

class ToolTip:
    """
    工具提示类，为控件添加悬停说明
    """
    
    def __init__(self, widget, text, delay=500, wraplength=300):
        """
        初始化工具提示
        
        Args:
            widget: 要添加提示的控件
            text: 提示文本
            delay: 延迟显示时间(毫秒)
            wraplength: 文本换行长度
        """
        self.widget = widget
        self.text = text
        self.delay = delay
        self.wraplength = wraplength
        self.tooltip_window = None
        self.id = None
        
        # 绑定事件
        self.widget.bind('<Enter>', self.on_enter)
        self.widget.bind('<Leave>', self.on_leave)
        self.widget.bind('<Motion>', self.on_motion)
    
    def on_enter(self, event=None):
        """鼠标进入控件时的处理"""
        self.schedule_tooltip()
    
    def on_leave(self, event=None):
        """鼠标离开控件时的处理"""
        self.cancel_tooltip()
        self.hide_tooltip()
    
    def on_motion(self, event=None):
        """鼠标移动时的处理"""
        self.cancel_tooltip()
        self.schedule_tooltip()
    
    def schedule_tooltip(self):
        """安排显示工具提示"""
        self.cancel_tooltip()
        self.id = self.widget.after(self.delay, self.show_tooltip)
    
    def cancel_tooltip(self):
        """取消显示工具提示"""
        if self.id:
            self.widget.after_cancel(self.id)
            self.id = None
    
    def show_tooltip(self):
        """显示工具提示"""
        if self.tooltip_window:
            return
        
        # 获取控件位置
        x = self.widget.winfo_rootx() + 25
        y = self.widget.winfo_rooty() + 25
        
        # 创建提示窗口
        self.tooltip_window = tk.Toplevel(self.widget)
        self.tooltip_window.wm_overrideredirect(True)
        self.tooltip_window.wm_geometry(f"+{x}+{y}")
        
        # 创建提示标签
        label = tk.Label(
            self.tooltip_window,
            text=self.text,
            justify=tk.LEFT,
            background="#ffffe0",
            relief=tk.SOLID,
            borderwidth=1,
            font=("Arial", 9),
            wraplength=self.wraplength,
            padx=8,
            pady=6
        )
        label.pack()
    
    def hide_tooltip(self):
        """隐藏工具提示"""
        if self.tooltip_window:
            self.tooltip_window.destroy()
            self.tooltip_window = None

class HelpText:
    """
    帮助文本类，提供统一的说明文字样式
    """
    
    @staticmethod
    def create_info_label(parent, text, color="blue"):
        """创建信息标签"""
        label = ttk.Label(
            parent,
            text=f"💡 {text}",
            font=("Arial", 9),
            foreground=color,
            wraplength=600
        )
        return label
    
    @staticmethod
    def create_warning_label(parent, text):
        """创建警告标签"""
        label = ttk.Label(
            parent,
            text=f"⚠️ {text}",
            font=("Arial", 9),
            foreground="orange",
            wraplength=600
        )
        return label
    
    @staticmethod
    def create_danger_label(parent, text):
        """创建危险警告标签"""
        label = ttk.Label(
            parent,
            text=f"🚨 {text}",
            font=("Arial", 9),
            foreground="red",
            wraplength=600
        )
        return label
    
    @staticmethod
    def create_success_label(parent, text):
        """创建成功标签"""
        label = ttk.Label(
            parent,
            text=f"✅ {text}",
            font=("Arial", 9),
            foreground="green",
            wraplength=600
        )
        return label

class UserGuide:
    """
    用户指南类，提供详细的操作说明
    """
    
    # 术语解释字典
    TERMS = {
        "API密钥": "就像你的银行卡密码，用来连接交易所账户。包含API Key（账户名）、API Secret（密码）和Passphrase（安全码）",
        "杠杆": "用少量资金控制更大金额的交易。比如10倍杠杆，用100元可以交易1000元的商品",
        "保证金": "交易时需要冻结的资金，就像买房的首付款。交易结束后会返还",
        "MACD": "一种技术分析指标，帮助判断买卖时机。就像股市的'红绿灯'，告诉你什么时候该买卖",
        "时间周期": "分析价格的时间长度。5分钟周期就是每5分钟看一次价格变化",
        "加仓": "在已有持仓的基础上继续买入，就像定投基金一样分批买入",
        "止损": "当亏损达到一定程度时自动卖出，防止损失扩大",
        "持仓": "目前持有的交易品种和数量",
        "盈亏": "赚钱或亏钱的金额",
        "强平": "当亏损过大时，系统强制平仓以保护资金安全"
    }
    
    @staticmethod
    def get_term_explanation(term):
        """获取术语解释"""
        return UserGuide.TERMS.get(term, f"{term}：专业交易术语，建议查阅相关资料了解")
    
    @staticmethod
    def create_help_button(parent, help_text, title="帮助说明"):
        """创建帮助按钮"""
        def show_help():
            help_window = tk.Toplevel(parent)
            help_window.title(title)
            help_window.geometry("500x400")
            help_window.resizable(True, True)
            
            # 创建滚动文本框
            text_frame = ttk.Frame(help_window)
            text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            text_widget = tk.Text(text_frame, wrap=tk.WORD, font=("Arial", 10))
            scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)
            
            text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            
            # 插入帮助文本
            text_widget.insert(tk.END, help_text)
            text_widget.config(state=tk.DISABLED)
            
            # 关闭按钮
            close_btn = ttk.Button(help_window, text="关闭", command=help_window.destroy)
            close_btn.pack(pady=10)
        
        help_btn = ttk.Button(parent, text="❓ 帮助", command=show_help)
        return help_btn

def add_tooltip(widget, text):
    """为控件添加工具提示的便捷函数"""
    return ToolTip(widget, text)

def create_section_help(parent, title, description, tips=None):
    """创建区域帮助说明"""
    help_frame = ttk.Frame(parent)
    help_frame.pack(fill=tk.X, pady=(0, 10))
    
    # 标题
    title_label = ttk.Label(help_frame, text=title, font=("Arial", 11, "bold"))
    title_label.pack(anchor=tk.W)
    
    # 描述
    desc_label = HelpText.create_info_label(help_frame, description)
    desc_label.pack(anchor=tk.W, pady=(2, 0))
    
    # 提示
    if tips:
        for tip in tips:
            tip_label = ttk.Label(help_frame, text=f"• {tip}", font=("Arial", 8), foreground="gray")
            tip_label.pack(anchor=tk.W, padx=(20, 0))
    
    return help_frame
