#!/usr/bin/env python3
"""
BitV 交易系统启动脚本
简化版本，专注于稳定启动
"""

import sys
import os
import time
import subprocess
import signal
from pathlib import Path

def check_port(port):
    """检查端口是否被占用"""
    import socket
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    result = sock.connect_ex(('127.0.0.1', port))
    sock.close()
    return result == 0

def kill_port_process(port):
    """终止占用指定端口的进程"""
    try:
        # Windows命令
        cmd = f'netstat -ano | findstr :{port}'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if result.stdout:
            lines = result.stdout.strip().split('\n')
            for line in lines:
                if 'LISTENING' in line:
                    parts = line.split()
                    if len(parts) >= 5:
                        pid = parts[-1]
                        print(f"终止占用端口{port}的进程 PID: {pid}")
                        subprocess.run(f'taskkill /F /PID {pid}', shell=True)
                        time.sleep(2)
                        break
    except Exception as e:
        print(f"清理端口进程时出错: {e}")

def start_simple_server():
    """启动简化的API服务器"""
    print("🚀 启动BitV交易系统...")
    
    # 检查并清理端口8000
    if check_port(8000):
        print("⚠️ 端口8000被占用，正在清理...")
        kill_port_process(8000)
        time.sleep(3)
    
    # 尝试启动实盘交易系统
    try:
        print("📊 启动实盘交易系统...")
        import real_trading_system_windows
        # 如果导入成功，说明没有语法错误
        print("✅ 实盘交易系统模块加载成功")
        
        # 直接运行
        os.system("python real_trading_system_windows.py")
        
    except ImportError as e:
        print(f"❌ 实盘交易系统导入失败: {e}")
        print("🔄 尝试启动简化API服务器...")
        
        try:
            import start_api_server
            os.system("python start_api_server.py")
        except Exception as e2:
            print(f"❌ 简化服务器也启动失败: {e2}")
            return False
    
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("=" * 60)
    print("🎯 BitV MACD智能加仓交易系统")
    print("=" * 60)
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        return
    
    print(f"✅ Python版本: {sys.version}")
    
    # 检查工作目录
    current_dir = Path.cwd()
    print(f"📁 工作目录: {current_dir}")
    
    # 检查关键文件
    key_files = [
        "real_trading_system_windows.py",
        "start_api_server.py",
        "config.py"
    ]
    
    missing_files = []
    for file in key_files:
        if not Path(file).exists():
            missing_files.append(file)
        else:
            print(f"✅ 找到文件: {file}")
    
    if missing_files:
        print(f"❌ 缺少文件: {missing_files}")
        return
    
    # 启动服务器
    if start_simple_server():
        print("🎉 系统启动成功！")
        print("📊 访问地址:")
        print("   - API服务: http://localhost:8000")
        print("   - API文档: http://localhost:8000/docs")
        print("   - 健康检查: http://localhost:8000/health")
        print("   - PHP前端: http://127.0.0.1/")
    else:
        print("❌ 系统启动失败")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 用户中断，系统退出")
    except Exception as e:
        print(f"❌ 启动脚本出错: {e}")
