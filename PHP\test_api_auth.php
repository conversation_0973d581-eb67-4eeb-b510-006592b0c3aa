<?php
/**
 * 测试实盘交易系统API认证
 */

// 加载配置
require_once 'config/config.php';
require_once 'includes/ApiClient.php';

echo "<h1>🔐 实盘交易系统API认证测试</h1>";

// 显示配置信息
echo "<h2>📊 当前配置</h2>";
echo "<p><strong>API地址:</strong> " . PYTHON_API_BASE . "</p>";
echo "<p><strong>API令牌:</strong> " . PYTHON_API_TOKEN . "</p>";

// 测试函数
function testApiEndpoint($endpoint, $name) {
    echo "<h3>🧪 测试 {$name}</h3>";
    echo "<p>端点: {$endpoint}</p>";
    
    $url = PYTHON_API_BASE . $endpoint;
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json',
        'Authorization: Bearer ' . PYTHON_API_TOKEN
    ]);
    
    $start_time = microtime(true);
    $response = curl_exec($ch);
    $end_time = microtime(true);
    
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    $response_time = round(($end_time - $start_time) * 1000, 2);
    
    curl_close($ch);
    
    echo "<div style='background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
    
    if ($curl_error) {
        echo "<p style='color: red;'>❌ <strong>连接错误:</strong> {$curl_error}</p>";
        echo "</div>";
        return false;
    }
    
    echo "<p><strong>HTTP状态码:</strong> {$http_code}</p>";
    echo "<p><strong>响应时间:</strong> {$response_time}ms</p>";
    
    if ($http_code == 200) {
        echo "<p style='color: green;'>✅ <strong>状态:</strong> 成功</p>";
        
        $data = json_decode($response, true);
        if ($data) {
            echo "<p><strong>响应数据:</strong></p>";
            echo "<pre style='background: #fff; padding: 10px; border: 1px solid #ddd; overflow-x: auto;'>";
            echo htmlspecialchars(json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
            echo "</pre>";
        }
        echo "</div>";
        return true;
    } else {
        echo "<p style='color: red;'>❌ <strong>状态:</strong> 失败</p>";
        echo "<p><strong>响应内容:</strong> " . htmlspecialchars($response) . "</p>";
        echo "</div>";
        return false;
    }
}

// 测试不同的API端点
echo "<h2>🧪 API端点测试</h2>";

$endpoints = [
    '/health' => '健康检查 (无需认证)',
    '/api/status' => '系统状态 (需要认证)',
    '/api/market/prices' => '市场价格 (需要认证)',
    '/api/positions' => '持仓信息 (需要认证)'
];

$results = [];
foreach ($endpoints as $endpoint => $name) {
    $results[$endpoint] = testApiEndpoint($endpoint, $name);
}

// 测试ApiClient类
echo "<h2>🔧 ApiClient类测试</h2>";

try {
    $api = ApiClient::getInstance();
    
    echo "<h3>🏥 健康检查</h3>";
    $healthResult = $api->healthCheck();
    echo "<p><strong>结果:</strong> " . ($healthResult ? "✅ 成功" : "❌ 失败") . "</p>";
    
    echo "<h3>📊 系统状态</h3>";
    $statusResult = $api->getSystemStatus();
    echo "<p><strong>结果:</strong></p>";
    echo "<pre style='background: #f0f0f0; padding: 10px; border: 1px solid #ddd; overflow-x: auto;'>";
    echo htmlspecialchars(json_encode($statusResult, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    echo "</pre>";
    
    echo "<h3>🎯 交易状态</h3>";
    $tradingResult = $api->getTradingStatus();
    echo "<p><strong>结果:</strong></p>";
    echo "<pre style='background: #f0f0f0; padding: 10px; border: 1px solid #ddd; overflow-x: auto;'>";
    echo htmlspecialchars(json_encode($tradingResult, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    echo "</pre>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ <strong>ApiClient测试失败:</strong> " . $e->getMessage() . "</p>";
}

// 总结
echo "<h2>📋 测试总结</h2>";
$success_count = count(array_filter($results));
$total_count = count($results);

echo "<p><strong>成功:</strong> {$success_count}/{$total_count}</p>";

if ($success_count == $total_count) {
    echo "<p style='color: green; font-size: 18px; font-weight: bold;'>🎉 所有测试通过！API连接正常！</p>";
} else {
    echo "<p style='color: orange; font-size: 18px; font-weight: bold;'>⚠️ 部分测试失败，请检查配置和服务状态。</p>";
}

echo "<hr>";
echo "<p><small>测试时间: " . date('Y-m-d H:i:s') . "</small></p>";
?>
