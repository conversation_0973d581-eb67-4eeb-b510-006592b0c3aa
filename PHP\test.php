<?php
/**
 * BitV MACD智能加仓交易系统 - 系统测试
 * 
 * @description 系统功能测试和健康检查
 * <AUTHOR> Trading Team
 * @version 1.0.0
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 定义常量
define('ROOT_PATH', __DIR__);
define('CONFIG_PATH', ROOT_PATH . '/config');
define('INCLUDES_PATH', ROOT_PATH . '/includes');

// 检查是否已安装
if (!file_exists(CONFIG_PATH . '/.installed')) {
    die('系统尚未安装。请先运行 install.php 进行安装。');
}

// 加载配置
require_once CONFIG_PATH . '/config.php';

// 加载核心类
require_once INCLUDES_PATH . '/Database.php';
require_once INCLUDES_PATH . '/ApiClient.php';
require_once INCLUDES_PATH . '/Session.php';

/**
 * 测试结果类
 */
class TestResult {
    public $name;
    public $status;
    public $message;
    public $details;
    
    public function __construct($name, $status, $message = '', $details = []) {
        $this->name = $name;
        $this->status = $status;
        $this->message = $message;
        $this->details = $details;
    }
}

/**
 * 系统测试类
 */
class SystemTest {
    private $results = [];
    
    /**
     * 运行所有测试
     */
    public function runAllTests() {
        $this->testDatabaseConnection();
        $this->testDatabaseTables();
        $this->testApiConnection();
        $this->testFilePermissions();
        $this->testSessionSystem();
        $this->testConfigurationLoad();
        
        return $this->results;
    }
    
    /**
     * 测试数据库连接
     */
    private function testDatabaseConnection() {
        try {
            $db = Database::getInstance();
            $result = $db->fetch('SELECT 1 as test');
            
            if ($result && $result['test'] == 1) {
                $this->addResult('数据库连接', true, '连接成功');
            } else {
                $this->addResult('数据库连接', false, '查询测试失败');
            }
        } catch (Exception $e) {
            $this->addResult('数据库连接', false, $e->getMessage());
        }
    }
    
    /**
     * 测试数据库表
     */
    private function testDatabaseTables() {
        try {
            $db = Database::getInstance();
            $requiredTables = [
                'user_configs',
                'trading_sessions', 
                'trading_records',
                'position_monitors',
                'system_logs'
            ];
            
            $existingTables = [];
            $missingTables = [];
            
            foreach ($requiredTables as $table) {
                $result = $db->fetch("SHOW TABLES LIKE '{$table}'");
                if ($result) {
                    $existingTables[] = $table;
                } else {
                    $missingTables[] = $table;
                }
            }
            
            if (empty($missingTables)) {
                $this->addResult('数据库表', true, '所有必需表都存在', [
                    'existing_tables' => $existingTables
                ]);
            } else {
                $this->addResult('数据库表', false, '缺少必需的表', [
                    'missing_tables' => $missingTables,
                    'existing_tables' => $existingTables
                ]);
            }
        } catch (Exception $e) {
            $this->addResult('数据库表', false, $e->getMessage());
        }
    }
    
    /**
     * 测试API连接
     */
    private function testApiConnection() {
        try {
            $api = ApiClient::getInstance();
            $isHealthy = $api->healthCheck();
            
            if ($isHealthy) {
                // 尝试获取系统状态
                try {
                    $response = $api->getSystemStatus();
                    $this->addResult('Python API连接', true, 'API连接正常', [
                        'api_url' => PYTHON_API_BASE,
                        'response' => $response
                    ]);
                } catch (Exception $e) {
                    $this->addResult('Python API连接', true, 'API可达但功能异常', [
                        'api_url' => PYTHON_API_BASE,
                        'error' => $e->getMessage()
                    ]);
                }
            } else {
                $this->addResult('Python API连接', false, 'API健康检查失败', [
                    'api_url' => PYTHON_API_BASE
                ]);
            }
        } catch (Exception $e) {
            $this->addResult('Python API连接', false, $e->getMessage(), [
                'api_url' => PYTHON_API_BASE
            ]);
        }
    }
    
    /**
     * 测试文件权限
     */
    private function testFilePermissions() {
        $paths = [
            'config' => CONFIG_PATH,
            'logs' => ROOT_PATH . '/logs',
            'assets' => ROOT_PATH . '/assets'
        ];
        
        $permissions = [];
        $allGood = true;
        
        foreach ($paths as $name => $path) {
            $readable = is_readable($path);
            $writable = is_writable($path);
            
            $permissions[$name] = [
                'path' => $path,
                'readable' => $readable,
                'writable' => $writable,
                'exists' => file_exists($path)
            ];
            
            if (!$readable || !$writable) {
                $allGood = false;
            }
        }
        
        $this->addResult('文件权限', $allGood, 
            $allGood ? '所有目录权限正常' : '部分目录权限异常', 
            $permissions
        );
    }
    
    /**
     * 测试会话系统
     */
    private function testSessionSystem() {
        try {
            Session::start();
            
            // 测试会话读写
            Session::set('test_key', 'test_value');
            $value = Session::get('test_key');
            
            if ($value === 'test_value') {
                // 测试CSRF令牌
                $token = Session::getCSRFToken();
                $isValid = Session::validateCSRFToken($token);
                
                if ($isValid) {
                    $this->addResult('会话系统', true, '会话系统正常', [
                        'session_id' => session_id(),
                        'csrf_token_length' => strlen($token)
                    ]);
                } else {
                    $this->addResult('会话系统', false, 'CSRF令牌验证失败');
                }
            } else {
                $this->addResult('会话系统', false, '会话读写测试失败');
            }
            
            // 清理测试数据
            Session::remove('test_key');
            
        } catch (Exception $e) {
            $this->addResult('会话系统', false, $e->getMessage());
        }
    }
    
    /**
     * 测试配置加载
     */
    private function testConfigurationLoad() {
        try {
            $configTests = [
                'SYSTEM_NAME' => defined('SYSTEM_NAME'),
                'SYSTEM_VERSION' => defined('SYSTEM_VERSION'),
                'DB_HOST' => defined('DB_HOST'),
                'PYTHON_API_BASE' => defined('PYTHON_API_BASE'),
                'SUPPORTED_EXCHANGES' => isset($GLOBALS['SUPPORTED_EXCHANGES']),
                'SUPPORTED_STRATEGIES' => isset($GLOBALS['SUPPORTED_STRATEGIES'])
            ];
            
            $passed = array_filter($configTests);
            $failed = array_diff_key($configTests, $passed);
            
            if (empty($failed)) {
                $this->addResult('配置加载', true, '所有配置项加载成功', [
                    'loaded_configs' => array_keys($passed)
                ]);
            } else {
                $this->addResult('配置加载', false, '部分配置项加载失败', [
                    'failed_configs' => array_keys($failed),
                    'loaded_configs' => array_keys($passed)
                ]);
            }
        } catch (Exception $e) {
            $this->addResult('配置加载', false, $e->getMessage());
        }
    }
    
    /**
     * 添加测试结果
     */
    private function addResult($name, $status, $message = '', $details = []) {
        $this->results[] = new TestResult($name, $status, $message, $details);
    }
}

// 运行测试
$tester = new SystemTest();
$results = $tester->runAllTests();

// 计算统计
$totalTests = count($results);
$passedTests = count(array_filter($results, function($r) { return $r->status; }));
$failedTests = $totalTests - $passedTests;
$successRate = $totalTests > 0 ? ($passedTests / $totalTests) * 100 : 0;

?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BitV MACD智能加仓交易系统 - 系统测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }
        .test-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 900px;
            margin: 0 auto;
        }
        .test-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 15px 15px 0 0;
            text-align: center;
        }
        .test-item {
            border-bottom: 1px solid #eee;
            padding: 1rem;
        }
        .test-item:last-child {
            border-bottom: none;
        }
        .test-status {
            font-size: 1.2rem;
            font-weight: bold;
        }
        .status-pass { color: #28a745; }
        .status-fail { color: #dc3545; }
        .details {
            background: #f8f9fa;
            border-radius: 5px;
            padding: 0.5rem;
            margin-top: 0.5rem;
            font-family: monospace;
            font-size: 0.9rem;
        }
        .progress-ring {
            width: 120px;
            height: 120px;
            margin: 0 auto;
        }
        .progress-ring-circle {
            stroke: #e9ecef;
            stroke-width: 8;
            fill: transparent;
            r: 52;
            cx: 60;
            cy: 60;
        }
        .progress-ring-progress {
            stroke: #28a745;
            stroke-width: 8;
            stroke-linecap: round;
            fill: transparent;
            r: 52;
            cx: 60;
            cy: 60;
            stroke-dasharray: 326.73;
            stroke-dashoffset: 326.73;
            transform: rotate(-90deg);
            transform-origin: 60px 60px;
            transition: stroke-dashoffset 1s ease-in-out;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-container">
            <div class="test-header">
                <h1><i class="fas fa-vial me-2"></i>BitV MACD智能加仓交易系统</h1>
                <p class="mb-0">系统测试报告</p>
            </div>
            
            <div class="p-4">
                <!-- 测试统计 -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="text-center">
                            <svg class="progress-ring">
                                <circle class="progress-ring-circle"></circle>
                                <circle class="progress-ring-progress" 
                                        style="stroke-dashoffset: <?php echo 326.73 - (326.73 * $successRate / 100); ?>"></circle>
                            </svg>
                            <div class="mt-2">
                                <h3><?php echo number_format($successRate, 1); ?>%</h3>
                                <p class="text-muted">测试通过率</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="row text-center">
                            <div class="col-4">
                                <h4 class="text-primary"><?php echo $totalTests; ?></h4>
                                <small class="text-muted">总测试</small>
                            </div>
                            <div class="col-4">
                                <h4 class="text-success"><?php echo $passedTests; ?></h4>
                                <small class="text-muted">通过</small>
                            </div>
                            <div class="col-4">
                                <h4 class="text-danger"><?php echo $failedTests; ?></h4>
                                <small class="text-muted">失败</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 测试结果 -->
                <h3><i class="fas fa-list-check me-2"></i>测试结果详情</h3>
                
                <?php foreach ($results as $result): ?>
                    <div class="test-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="mb-1"><?php echo htmlspecialchars($result->name); ?></h5>
                                <?php if ($result->message): ?>
                                    <p class="mb-0 text-muted"><?php echo htmlspecialchars($result->message); ?></p>
                                <?php endif; ?>
                            </div>
                            <div class="test-status <?php echo $result->status ? 'status-pass' : 'status-fail'; ?>">
                                <i class="fas fa-<?php echo $result->status ? 'check-circle' : 'times-circle'; ?>"></i>
                                <?php echo $result->status ? '通过' : '失败'; ?>
                            </div>
                        </div>
                        
                        <?php if (!empty($result->details)): ?>
                            <div class="details">
                                <strong>详细信息:</strong><br>
                                <?php echo htmlspecialchars(json_encode($result->details, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
                
                <!-- 操作按钮 -->
                <div class="text-center mt-4">
                    <a href="index.php" class="btn btn-primary me-2">
                        <i class="fas fa-home me-1"></i>进入系统
                    </a>
                    <button onclick="location.reload()" class="btn btn-outline-secondary">
                        <i class="fas fa-sync-alt me-1"></i>重新测试
                    </button>
                </div>
                
                <!-- 系统信息 -->
                <div class="mt-4 p-3 bg-light rounded">
                    <h6><i class="fas fa-info-circle me-2"></i>系统信息</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <small><strong>PHP版本:</strong> <?php echo PHP_VERSION; ?></small><br>
                            <small><strong>系统版本:</strong> <?php echo SYSTEM_VERSION; ?></small><br>
                            <small><strong>测试时间:</strong> <?php echo date('Y-m-d H:i:s'); ?></small>
                        </div>
                        <div class="col-md-6">
                            <small><strong>数据库:</strong> <?php echo DB_HOST . '/' . DB_NAME; ?></small><br>
                            <small><strong>Python API:</strong> <?php echo PYTHON_API_BASE; ?></small><br>
                            <small><strong>调试模式:</strong> <?php echo DEBUG_MODE ? '开启' : '关闭'; ?></small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
