"""
OKX交易所异步适配器
严格遵守交易所完全分离原则
"""
import asyncio
import aiohttp
import hmac
import hashlib
import base64
import json
import time
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
import logging
import websockets

from .base_exchange import (
    BaseExchange, KlineData, PositionInfo, OrderInfo, 
    OrderSide, OrderType, PositionSide
)

logger = logging.getLogger(__name__)

class OKXExchange(BaseExchange):
    """
    OKX交易所异步适配器
    实现OKX API V5的完整功能
    """
    
    def __init__(self, api_key: str, api_secret: str, passphrase: str, sandbox: bool = False):
        """
        初始化OKX交易所连接
        
        Args:
            api_key: API密钥
            api_secret: API密钥
            passphrase: API密码短语
            sandbox: 是否使用沙盒环境
        """
        super().__init__(api_key, api_secret, passphrase, sandbox)
        
        # OKX API配置
        if sandbox:
            self.base_url = "https://www.okx.com"  # OKX沙盒环境
            self.ws_url = "wss://ws.okx.com:8443/ws/v5/public"
            self.ws_private_url = "wss://ws.okx.com:8443/ws/v5/private"
        else:
            self.base_url = "https://www.okx.com"
            self.ws_url = "wss://ws.okx.com:8443/ws/v5/public"
            self.ws_private_url = "wss://ws.okx.com:8443/ws/v5/private"
        
        self.session = None
        self.ws_connection = None
        
    async def connect(self) -> bool:
        """建立异步连接"""
        try:
            # 创建HTTP会话
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30),
                headers={
                    'Content-Type': 'application/json',
                    'OK-ACCESS-KEY': self.api_key,
                    'OK-ACCESS-PASSPHRASE': self.passphrase,
                }
            )
            
            # 测试连接 - 获取账户余额
            balance = await self.get_account_balance()
            if balance is not None:
                self._is_connected = True
                logger.info(f"✅ OKX交易所连接成功 (沙盒: {self.sandbox})")
                return True
            else:
                logger.error("❌ OKX交易所连接失败 - 无法获取账户余额")
                return False
                
        except Exception as e:
            logger.error(f"❌ OKX交易所连接失败: {e}")
            return False
    
    async def disconnect(self) -> None:
        """断开连接"""
        try:
            if self.ws_connection:
                await self.ws_connection.close()
                self.ws_connection = None
                
            if self.session:
                await self.session.close()
                self.session = None
                
            self._is_connected = False
            logger.info("OKX交易所连接已断开")
            
        except Exception as e:
            logger.error(f"断开OKX连接时发生错误: {e}")
    
    def _generate_signature(self, timestamp: str, method: str, request_path: str, body: str = '') -> str:
        """生成OKX API签名"""
        message = timestamp + method + request_path + body
        signature = base64.b64encode(
            hmac.new(
                self.api_secret.encode('utf-8'),
                message.encode('utf-8'),
                hashlib.sha256
            ).digest()
        ).decode('utf-8')
        return signature
    
    async def _make_request(self, method: str, endpoint: str, params: Dict = None, data: Dict = None) -> Dict:
        """发送HTTP请求"""
        if not self.session:
            raise Exception("未建立连接")
        
        url = f"{self.base_url}{endpoint}"
        timestamp = str(time.time())
        
        # 准备请求体
        body = json.dumps(data) if data else ''
        
        # 生成签名
        signature = self._generate_signature(timestamp, method.upper(), endpoint, body)
        
        headers = {
            'OK-ACCESS-KEY': self.api_key,
            'OK-ACCESS-SIGN': signature,
            'OK-ACCESS-TIMESTAMP': timestamp,
            'OK-ACCESS-PASSPHRASE': self.passphrase,
            'Content-Type': 'application/json'
        }
        
        try:
            async with self.session.request(
                method=method,
                url=url,
                headers=headers,
                params=params,
                data=body if body else None
            ) as response:
                result = await response.json()
                
                if response.status != 200:
                    raise Exception(f"HTTP {response.status}: {result}")
                
                if result.get('code') != '0':
                    raise Exception(f"API错误: {result.get('msg', 'Unknown error')}")
                
                return result
                
        except Exception as e:
            logger.error(f"OKX API请求失败: {e}")
            raise
    
    async def get_account_balance(self) -> Optional[Dict[str, float]]:
        """获取账户余额"""
        try:
            result = await self._make_request('GET', '/api/v5/account/balance')
            
            balances = {}
            for item in result.get('data', []):
                for detail in item.get('details', []):
                    currency = detail.get('ccy')
                    available = float(detail.get('availBal', 0))
                    if available > 0:
                        balances[currency] = available
            
            return balances
            
        except Exception as e:
            logger.error(f"获取OKX账户余额失败: {e}")
            return None
    
    async def get_kline_data(self, symbol: str, timeframe: str, limit: int = 100) -> List[KlineData]:
        """获取K线数据"""
        try:
            # OKX时间周期映射
            timeframe_map = {
                '1m': '1m', '5m': '5m', '15m': '15m', '30m': '30m',
                '1h': '1H', '4h': '4H', '1d': '1D'
            }
            
            okx_timeframe = timeframe_map.get(timeframe, '30m')
            
            params = {
                'instId': symbol,
                'bar': okx_timeframe,
                'limit': str(limit)
            }
            
            result = await self._make_request('GET', '/api/v5/market/candles', params=params)
            
            klines = []
            for item in result.get('data', []):
                klines.append(KlineData(
                    timestamp=int(item[0]),
                    open=float(item[1]),
                    high=float(item[2]),
                    low=float(item[3]),
                    close=float(item[4]),
                    volume=float(item[5])
                ))
            
            return klines
            
        except Exception as e:
            logger.error(f"获取OKX K线数据失败: {e}")
            return []
    
    async def get_positions(self, symbol: Optional[str] = None) -> List[PositionInfo]:
        """获取持仓信息"""
        try:
            params = {}
            if symbol:
                params['instId'] = symbol
            
            result = await self._make_request('GET', '/api/v5/account/positions', params=params)
            
            positions = []
            for item in result.get('data', []):
                if float(item.get('pos', 0)) != 0:  # 只返回有持仓的
                    positions.append(PositionInfo(
                        symbol=item.get('instId'),
                        side=PositionSide.LONG if item.get('posSide') == 'long' else PositionSide.SHORT,
                        size=abs(float(item.get('pos', 0))),
                        entry_price=float(item.get('avgPx', 0)),
                        mark_price=float(item.get('markPx', 0)),
                        unrealized_pnl=float(item.get('upl', 0)),
                        liquidation_price=float(item.get('liqPx', 0)),
                        margin=float(item.get('margin', 0)),
                        leverage=int(float(item.get('lever', 1)))
                    ))
            
            return positions
            
        except Exception as e:
            logger.error(f"获取OKX持仓信息失败: {e}")
            return []
    
    async def place_market_order(self, symbol: str, side: OrderSide, amount: float,
                                position_side: Optional[PositionSide] = None,
                                reduce_only: bool = False) -> OrderInfo:
        """下市价单"""
        try:
            data = {
                'instId': symbol,
                'tdMode': 'cross',  # 全仓模式
                'side': 'buy' if side == OrderSide.BUY else 'sell',
                'ordType': 'market',
                'sz': str(amount)
            }
            
            if position_side:
                data['posSide'] = 'long' if position_side == PositionSide.LONG else 'short'
            
            if reduce_only:
                data['reduceOnly'] = 'true'
            
            result = await self._make_request('POST', '/api/v5/trade/order', data=data)
            
            order_data = result.get('data', [{}])[0]
            
            return OrderInfo(
                order_id=order_data.get('ordId'),
                symbol=symbol,
                side=side,
                type=OrderType.MARKET,
                amount=amount,
                price=None,
                status=order_data.get('sCode', 'unknown'),
                filled=0.0,  # 需要后续查询
                remaining=amount,
                timestamp=int(time.time() * 1000)
            )
            
        except Exception as e:
            logger.error(f"OKX下市价单失败: {e}")
            raise
    
    async def place_limit_order(self, symbol: str, side: OrderSide, amount: float,
                               price: float, position_side: Optional[PositionSide] = None,
                               reduce_only: bool = False) -> OrderInfo:
        """下限价单"""
        try:
            data = {
                'instId': symbol,
                'tdMode': 'cross',
                'side': 'buy' if side == OrderSide.BUY else 'sell',
                'ordType': 'limit',
                'sz': str(amount),
                'px': str(price)
            }
            
            if position_side:
                data['posSide'] = 'long' if position_side == PositionSide.LONG else 'short'
            
            if reduce_only:
                data['reduceOnly'] = 'true'
            
            result = await self._make_request('POST', '/api/v5/trade/order', data=data)
            
            order_data = result.get('data', [{}])[0]
            
            return OrderInfo(
                order_id=order_data.get('ordId'),
                symbol=symbol,
                side=side,
                type=OrderType.LIMIT,
                amount=amount,
                price=price,
                status=order_data.get('sCode', 'unknown'),
                filled=0.0,
                remaining=amount,
                timestamp=int(time.time() * 1000)
            )
            
        except Exception as e:
            logger.error(f"OKX下限价单失败: {e}")
            raise
    
    async def cancel_order(self, symbol: str, order_id: str) -> bool:
        """取消订单"""
        try:
            data = {
                'instId': symbol,
                'ordId': order_id
            }
            
            result = await self._make_request('POST', '/api/v5/trade/cancel-order', data=data)
            
            return result.get('code') == '0'
            
        except Exception as e:
            logger.error(f"OKX取消订单失败: {e}")
            return False
    
    async def get_order_status(self, symbol: str, order_id: str) -> OrderInfo:
        """获取订单状态"""
        try:
            params = {
                'instId': symbol,
                'ordId': order_id
            }
            
            result = await self._make_request('GET', '/api/v5/trade/order', params=params)
            
            order_data = result.get('data', [{}])[0]
            
            return OrderInfo(
                order_id=order_data.get('ordId'),
                symbol=order_data.get('instId'),
                side=OrderSide.BUY if order_data.get('side') == 'buy' else OrderSide.SELL,
                type=OrderType.MARKET if order_data.get('ordType') == 'market' else OrderType.LIMIT,
                amount=float(order_data.get('sz', 0)),
                price=float(order_data.get('px', 0)) if order_data.get('px') else None,
                status=order_data.get('state', 'unknown'),
                filled=float(order_data.get('fillSz', 0)),
                remaining=float(order_data.get('sz', 0)) - float(order_data.get('fillSz', 0)),
                timestamp=int(order_data.get('cTime', 0))
            )
            
        except Exception as e:
            logger.error(f"获取OKX订单状态失败: {e}")
            raise
    
    async def subscribe_price_stream(self, symbol: str, callback) -> None:
        """订阅价格流"""
        # WebSocket实现将在后续添加
        logger.warning("OKX价格流订阅功能尚未实现")
        pass
    
    async def subscribe_position_stream(self, callback) -> None:
        """订阅持仓流"""
        # WebSocket实现将在后续添加
        logger.warning("OKX持仓流订阅功能尚未实现")
        pass
