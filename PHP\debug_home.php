<?php
/**
 * 调试版首页 - 显示详细的API调用过程
 */

require_once 'config/config.php';
require_once 'includes/Database.php';
require_once 'includes/Session.php';
require_once 'includes/ApiClient.php';

// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 调试版首页</h1>";

// 初始化组件
echo "<h2>1. 初始化组件</h2>";
try {
    Session::start();
    echo "<p>✅ 会话系统启动成功</p>";
    
    $db = Database::getInstance();
    echo "<p>✅ 数据库连接成功</p>";
    
    $api = ApiClient::getInstance();
    echo "<p>✅ API客户端创建成功</p>";
} catch (Exception $e) {
    echo "<p>❌ 初始化失败: " . htmlspecialchars($e->getMessage()) . "</p>";
    exit;
}

// 测试API连接
echo "<h2>2. API连接测试</h2>";
$apiStatus = false;
$systemStatus = null;
$tradingStatus = null;

try {
    echo "<p>🔍 开始健康检查...</p>";
    $apiStatus = $api->healthCheck();
    echo "<p>健康检查结果: " . ($apiStatus ? "✅ 成功" : "❌ 失败") . "</p>";

    if ($apiStatus) {
        echo "<p>🔍 获取系统状态...</p>";
        $systemResponse = $api->getSystemStatus();
        echo "<p>系统状态响应: " . ($systemResponse['success'] ? "✅ 成功" : "❌ 失败") . "</p>";
        echo "<pre>" . htmlspecialchars(json_encode($systemResponse, JSON_PRETTY_PRINT)) . "</pre>";
        $systemStatus = $systemResponse['success'] ? $systemResponse['data'] : null;

        echo "<p>🔍 获取交易状态...</p>";
        $tradingResponse = $api->getTradingStatus();
        echo "<p>交易状态响应: " . ($tradingResponse['success'] ? "✅ 成功" : "❌ 失败") . "</p>";
        echo "<pre>" . htmlspecialchars(json_encode($tradingResponse, JSON_PRETTY_PRINT)) . "</pre>";
        $tradingStatus = $tradingResponse['success'] ? $tradingResponse['data'] : null;
    }
} catch (Exception $e) {
    echo "<p>❌ API调用异常: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>异常详情: " . htmlspecialchars($e->getTraceAsString()) . "</p>";
    $apiStatus = false;
}

// 显示最终状态
echo "<h2>3. 最终状态</h2>";
echo "<p><strong>API状态:</strong> " . ($apiStatus ? "✅ 连接正常" : "❌ 连接失败") . "</p>";
echo "<p><strong>系统状态:</strong> " . ($systemStatus ? "✅ 已获取" : "❌ 未获取") . "</p>";
echo "<p><strong>交易状态:</strong> " . ($tradingStatus ? "✅ 已获取" : "❌ 未获取") . "</p>";

// 模拟首页显示逻辑
echo "<h2>4. 首页显示逻辑模拟</h2>";
if ($apiStatus && $trading_status = $tradingStatus) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>";
    echo "<h3>✅ 正常显示交易信息</h3>";
    if (isset($trading_status['current_session']) && $trading_status['current_session']) {
        echo "<p>当前有活跃的交易会话</p>";
    } else {
        echo "<p>当前没有活跃的交易会话</p>";
    }
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ 显示连接错误信息</h3>";
    echo "<p>无法连接到Python后端服务</p>";
    echo "<p>请确保Python交易系统正在运行</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><a href='index.php'>返回正常首页</a> | <a href='api_debug.php'>API调试页面</a></p>";
?>
