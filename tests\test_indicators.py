"""
技术指标模块单元测试
测试MACD计算器、多时间周期分析器等
"""

import unittest
import asyncio
from unittest.mock import Mock, AsyncMock
import numpy as np

from tests import TEST_CONFIG, get_event_loop
from indicators.macd_calculator import AsyncMACDCal<PERSON>tor, MACDResult, MACDSignal
from exchanges.base_exchange import KlineData

class TestMACDCalculator(unittest.TestCase):
    """MACD计算器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.loop = get_event_loop()
        self.calculator = AsyncMACDCalculator(
            fast_period=12,
            slow_period=26,
            signal_period=9
        )
    
    def test_calculator_initialization(self):
        """测试计算器初始化"""
        self.assertEqual(self.calculator.fast_period, 12)
        self.assertEqual(self.calculator.slow_period, 26)
        self.assertEqual(self.calculator.signal_period, 9)
    
    def test_ema_calculation(self):
        """测试EMA计算"""
        async def test_ema():
            prices = [50000, 50100, 50200, 50150, 50300, 50250, 50400, 50350, 50500, 50450]
            
            # 测试EMA计算
            ema = await self.calculator._calculate_ema_async(prices, 5)
            self.assertIsInstance(ema, float)
            self.assertGreater(ema, 0)
        
        self.loop.run_until_complete(test_ema())
    
    def test_macd_calculation_with_klines(self):
        """测试使用K线数据计算MACD"""
        async def test_macd():
            # 创建测试K线数据
            klines = []
            base_price = 50000
            for i in range(50):  # 需要足够的数据点
                price = base_price + (i * 10) + np.random.randint(-50, 50)
                kline = KlineData(
                    timestamp=1234567890 + i * 60,
                    open=price - 5,
                    high=price + 10,
                    low=price - 10,
                    close=price,
                    volume=100.0
                )
                klines.append(kline)
            
            # 计算MACD
            result = await self.calculator.calculate_macd_from_klines(klines)
            
            if result:  # 如果有足够数据
                self.assertIsInstance(result, MACDResult)
                self.assertIsInstance(result.macd, float)
                self.assertIsInstance(result.signal, float)
                self.assertIsInstance(result.histogram, float)
        
        self.loop.run_until_complete(test_macd())
    
    def test_macd_signal_detection(self):
        """测试MACD信号检测"""
        async def test_signal():
            # 创建模拟的MACD历史数据（金叉信号）
            macd_history = [
                MACDResult(macd=-0.5, signal=-0.3, histogram=-0.2, timestamp=1),
                MACDResult(macd=-0.2, signal=-0.1, histogram=-0.1, timestamp=2),
                MACDResult(macd=0.1, signal=0.05, histogram=0.05, timestamp=3),  # 金叉
            ]
            
            signal = await self.calculator.detect_signal(macd_history)
            
            if signal:
                self.assertIsInstance(signal, MACDSignal)
                self.assertIn(signal.signal_type, ['bullish', 'bearish', 'neutral'])
                self.assertIsInstance(signal.strength, float)
                self.assertGreaterEqual(signal.strength, 0.0)
                self.assertLessEqual(signal.strength, 1.0)
        
        self.loop.run_until_complete(test_signal())
    
    def test_insufficient_data_handling(self):
        """测试数据不足的处理"""
        async def test_insufficient():
            # 只提供很少的K线数据
            klines = [
                KlineData(1234567890, 50000, 50100, 49900, 50050, 100),
                KlineData(1234567950, 50050, 50150, 49950, 50100, 100)
            ]
            
            result = await self.calculator.calculate_macd_from_klines(klines)
            # 数据不足时应该返回None
            self.assertIsNone(result)
        
        self.loop.run_until_complete(test_insufficient())

class TestMACDResult(unittest.TestCase):
    """MACD结果测试"""
    
    def test_macd_result_structure(self):
        """测试MACD结果数据结构"""
        result = MACDResult(
            macd=0.15,
            signal=0.12,
            histogram=0.03,
            timestamp=1234567890
        )
        
        self.assertEqual(result.macd, 0.15)
        self.assertEqual(result.signal, 0.12)
        self.assertEqual(result.histogram, 0.03)
        self.assertEqual(result.timestamp, 1234567890)

class TestMACDSignal(unittest.TestCase):
    """MACD信号测试"""
    
    def test_macd_signal_structure(self):
        """测试MACD信号数据结构"""
        signal = MACDSignal(
            signal_type="bullish",
            strength=0.75,
            timestamp=1234567890,
            description="MACD金叉信号"
        )
        
        self.assertEqual(signal.signal_type, "bullish")
        self.assertEqual(signal.strength, 0.75)
        self.assertEqual(signal.timestamp, 1234567890)
        self.assertEqual(signal.description, "MACD金叉信号")

if __name__ == '__main__':
    unittest.main()
