<?php
/**
 * 测试模板修复
 */

require_once 'config/config.php';
require_once 'includes/ApiClient.php';

// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🧪 模板修复测试</h1>";

try {
    $api = ApiClient::getInstance();
    
    // 获取交易状态
    $tradingResponse = $api->getTradingStatus();
    $trading_status = $tradingResponse['success'] ? $tradingResponse['data'] : null;
    
    echo "<h2>1. 原始数据</h2>";
    echo "<pre>" . htmlspecialchars(json_encode($trading_status, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . "</pre>";
    
    echo "<h2>2. 模板代码测试</h2>";
    
    // 测试交易状态显示
    echo "<h3>交易状态显示:</h3>";
    if ($trading_status && is_array($trading_status)) {
        $state = isset($trading_status['trading_state']) ? $trading_status['trading_state'] : 'unknown';
        $displayState = isset($GLOBALS['TRADING_STATES'][$state]) ? $GLOBALS['TRADING_STATES'][$state] : ucfirst($state);
        echo "<p>✅ 状态: <strong>" . htmlspecialchars($displayState) . "</strong></p>";
    } else {
        echo "<p>❌ 无法获取交易状态</p>";
    }
    
    // 测试运行状态显示
    echo "<h3>运行状态显示:</h3>";
    if ($trading_status && is_array($trading_status)) {
        $isRunning = !empty($trading_status['is_running']);
        echo "<p>✅ 运行状态: <strong>" . ($isRunning ? '运行中' : '已停止') . "</strong></p>";
    } else {
        echo "<p>❌ 无法获取运行状态</p>";
    }
    
    // 测试当前会话显示
    echo "<h3>当前会话显示:</h3>";
    if ($trading_status && is_array($trading_status)) {
        if (isset($trading_status['current_session']) && $trading_status['current_session'] && is_array($trading_status['current_session'])) {
            $session = $trading_status['current_session'];
            echo "<p>✅ 有活跃会话:</p>";
            echo "<ul>";
            echo "<li>交易对: " . htmlspecialchars($session['symbol'] ?? 'N/A') . "</li>";
            echo "<li>杠杆: " . ($session['leverage'] ?? 0) . "x</li>";
            echo "<li>保证金: " . number_format($session['initial_margin'] ?? 0, 2) . " USDT</li>";
            echo "<li>加仓次数: " . ($session['total_add_times'] ?? 0) . "</li>";
            $pnl = $session['total_pnl'] ?? 0;
            echo "<li>未实现盈亏: <span style='color: " . ($pnl >= 0 ? 'green' : 'red') . "'>" . number_format($pnl, 2) . " USDT</span></li>";
            echo "</ul>";
        } else {
            echo "<p>✅ 当前没有活跃的交易会话</p>";
        }
    } else {
        echo "<p>❌ 无法获取会话信息</p>";
    }
    
    echo "<h2>3. 错误检查</h2>";
    echo "<p>✅ 所有模板代码都使用了安全的数组访问方式</p>";
    echo "<p>✅ 使用了 isset() 和 null coalescing operator (??)</p>";
    echo "<p>✅ 添加了类型检查 is_array()</p>";
    
} catch (Exception $e) {
    echo "<p>❌ 异常: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr>";
echo "<p><a href='index.php'>返回首页</a></p>";
?>
