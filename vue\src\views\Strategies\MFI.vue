<template>
  <div class="mfi-strategy">
    <div class="page-header">
      <h2>资金流量指标策略配置</h2>
      <p>配置资金流量指标(MFI)技术指标参数和交易策略</p>
    </div>

    <div class="card">
      <h3 class="mb-md">MFI策略开发中...</h3>
      <el-empty description="MFI策略配置页面正在开发中">
        <template #image>
          <el-icon size="60" color="#c0c4cc"><Money /></el-icon>
        </template>
      </el-empty>
    </div>
  </div>
</template>

<script setup>
import { Money } from '@element-plus/icons-vue'
</script>

<style lang="scss" scoped>
.mfi-strategy {
  .page-header {
    margin-bottom: var(--spacing-lg);
    
    h2 {
      margin-bottom: var(--spacing-xs);
      color: var(--text-color-primary);
    }
    
    p {
      color: var(--text-color-secondary);
    }
  }
}
</style>
