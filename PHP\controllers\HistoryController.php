<?php
/**
 * 历史数据分析控制器
 * 
 * @description 处理交易历史、盈亏分析、策略回测等数据分析功能
 */

require_once 'BaseController.php';

class HistoryController extends BaseController {

    /**
     * 历史数据主页
     */
    public function index() {
        try {
            // 获取分页参数
            $pagination = $this->getPaginationParams();
            
            // 获取筛选参数
            $filters = $this->getFilters();
            
            // 获取交易历史
            $tradingHistory = $this->getTradingHistory($pagination, $filters);
            
            // 获取统计数据
            $statistics = $this->getStatistics($filters);
            
            // 获取性能分析
            $performance = $this->getPerformanceAnalysis($filters);
            
            $this->render('history/index', [
                'title' => '交易历史分析 - ' . SYSTEM_NAME,
                'trading_history' => $tradingHistory,
                'statistics' => $statistics,
                'performance' => $performance,
                'filters' => $filters,
                'pagination' => $pagination,
                'supported_exchanges' => $GLOBALS['SUPPORTED_EXCHANGES'],
                'supported_strategies' => $GLOBALS['SUPPORTED_STRATEGIES']
            ]);
            
        } catch (Exception $e) {
            $this->render('history/error', [
                'title' => '历史数据错误',
                'error_message' => $e->getMessage()
            ]);
        }
    }

    /**
     * 获取交易历史数据
     */
    public function getHistoryData() {
        try {
            $pagination = $this->getPaginationParams();
            $filters = $this->getFilters();
            
            $history = $this->getTradingHistory($pagination, $filters);
            
            $this->success('获取交易历史成功', $history);
            
        } catch (Exception $e) {
            $this->error('获取交易历史失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取盈亏分析
     */
    public function getPnlAnalysis() {
        try {
            $period = $this->request['period'] ?? '30d';
            $groupBy = $this->request['group_by'] ?? 'day';
            
            $analysis = $this->calculatePnlAnalysis($period, $groupBy);
            
            $this->success('获取盈亏分析成功', $analysis);
            
        } catch (Exception $e) {
            $this->error('获取盈亏分析失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取策略性能分析
     */
    public function getStrategyPerformance() {
        try {
            $strategy = $this->request['strategy'] ?? null;
            $period = $this->request['period'] ?? '30d';
            
            $performance = $this->calculateStrategyPerformance($strategy, $period);
            
            $this->success('获取策略性能成功', $performance);
            
        } catch (Exception $e) {
            $this->error('获取策略性能失败: ' . $e->getMessage());
        }
    }

    /**
     * 导出交易记录
     */
    public function exportTrades() {
        try {
            $filters = $this->getFilters();
            $format = $this->request['format'] ?? 'csv';
            
            $trades = $this->getTradingHistory(['limit' => 10000, 'offset' => 0], $filters);
            
            if ($format === 'csv') {
                $this->exportToCSV($trades['data']);
            } elseif ($format === 'excel') {
                $this->exportToExcel($trades['data']);
            } else {
                $this->error('不支持的导出格式');
            }
            
        } catch (Exception $e) {
            $this->error('导出失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取交易历史
     */
    private function getTradingHistory($pagination, $filters) {
        $userId = Session::getUserId() ?? 'default';
        
        // 构建查询条件
        $whereConditions = ['ts.user_id = ?'];
        $params = [$userId];
        
        if (!empty($filters['exchange'])) {
            $whereConditions[] = 'ts.exchange = ?';
            $params[] = $filters['exchange'];
        }
        
        if (!empty($filters['symbol'])) {
            $whereConditions[] = 'ts.symbol = ?';
            $params[] = $filters['symbol'];
        }
        
        if (!empty($filters['strategy'])) {
            $whereConditions[] = 'ts.strategy = ?';
            $params[] = $filters['strategy'];
        }
        
        if (!empty($filters['start_date'])) {
            $whereConditions[] = 'tr.timestamp >= ?';
            $params[] = $filters['start_date'] . ' 00:00:00';
        }
        
        if (!empty($filters['end_date'])) {
            $whereConditions[] = 'tr.timestamp <= ?';
            $params[] = $filters['end_date'] . ' 23:59:59';
        }
        
        $whereClause = implode(' AND ', $whereConditions);
        
        // 获取总数
        $totalSql = "
            SELECT COUNT(*) as total
            FROM trading_records tr
            JOIN trading_sessions ts ON tr.session_id = ts.session_id
            WHERE {$whereClause}
        ";
        $total = $this->db->fetch($totalSql, $params)['total'];
        
        // 获取数据
        $dataSql = "
            SELECT tr.*, ts.exchange, ts.symbol, ts.strategy, ts.leverage, ts.initial_margin
            FROM trading_records tr
            JOIN trading_sessions ts ON tr.session_id = ts.session_id
            WHERE {$whereClause}
            ORDER BY tr.timestamp DESC
            LIMIT ? OFFSET ?
        ";
        $params[] = $pagination['limit'];
        $params[] = $pagination['offset'];
        
        $data = $this->db->fetchAll($dataSql, $params);
        
        return [
            'data' => $data,
            'total' => $total,
            'page' => $pagination['page'],
            'limit' => $pagination['limit'],
            'total_pages' => ceil($total / $pagination['limit'])
        ];
    }

    /**
     * 获取统计数据
     */
    private function getStatistics($filters) {
        $userId = Session::getUserId() ?? 'default';
        
        // 构建基础查询条件
        $whereConditions = ['ts.user_id = ?'];
        $params = [$userId];
        
        if (!empty($filters['start_date'])) {
            $whereConditions[] = 'tr.timestamp >= ?';
            $params[] = $filters['start_date'] . ' 00:00:00';
        }
        
        if (!empty($filters['end_date'])) {
            $whereConditions[] = 'tr.timestamp <= ?';
            $params[] = $filters['end_date'] . ' 23:59:59';
        }
        
        $whereClause = implode(' AND ', $whereConditions);
        
        // 基础统计
        $basicStats = $this->db->fetch("
            SELECT 
                COUNT(*) as total_trades,
                SUM(CASE WHEN tr.pnl > 0 THEN 1 ELSE 0 END) as profit_trades,
                SUM(CASE WHEN tr.pnl < 0 THEN 1 ELSE 0 END) as loss_trades,
                SUM(tr.pnl) as total_pnl,
                AVG(tr.pnl) as avg_pnl,
                MAX(tr.pnl) as max_profit,
                MIN(tr.pnl) as max_loss,
                SUM(tr.fee) as total_fees
            FROM trading_records tr
            JOIN trading_sessions ts ON tr.session_id = ts.session_id
            WHERE {$whereClause}
        ", $params);
        
        // 计算胜率
        $winRate = 0;
        if ($basicStats['total_trades'] > 0) {
            $winRate = ($basicStats['profit_trades'] / $basicStats['total_trades']) * 100;
        }
        
        // 按交易所统计
        $exchangeStats = $this->db->fetchAll("
            SELECT 
                ts.exchange,
                COUNT(*) as trade_count,
                SUM(tr.pnl) as total_pnl,
                AVG(tr.pnl) as avg_pnl
            FROM trading_records tr
            JOIN trading_sessions ts ON tr.session_id = ts.session_id
            WHERE {$whereClause}
            GROUP BY ts.exchange
            ORDER BY total_pnl DESC
        ", $params);
        
        // 按策略统计
        $strategyStats = $this->db->fetchAll("
            SELECT 
                ts.strategy,
                COUNT(*) as trade_count,
                SUM(tr.pnl) as total_pnl,
                AVG(tr.pnl) as avg_pnl,
                SUM(CASE WHEN tr.pnl > 0 THEN 1 ELSE 0 END) as profit_trades
            FROM trading_records tr
            JOIN trading_sessions ts ON tr.session_id = ts.session_id
            WHERE {$whereClause}
            GROUP BY ts.strategy
            ORDER BY total_pnl DESC
        ", $params);
        
        return [
            'basic' => array_merge($basicStats, ['win_rate' => $winRate]),
            'by_exchange' => $exchangeStats,
            'by_strategy' => $strategyStats
        ];
    }

    /**
     * 获取性能分析
     */
    private function getPerformanceAnalysis($filters) {
        $userId = Session::getUserId() ?? 'default';
        
        // 获取每日盈亏
        $dailyPnl = $this->db->fetchAll("
            SELECT 
                DATE(tr.timestamp) as trade_date,
                SUM(tr.pnl) as daily_pnl,
                COUNT(*) as trade_count
            FROM trading_records tr
            JOIN trading_sessions ts ON tr.session_id = ts.session_id
            WHERE ts.user_id = ? 
            AND tr.timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            GROUP BY DATE(tr.timestamp)
            ORDER BY trade_date
        ", [$userId]);
        
        // 计算累计盈亏
        $cumulativePnl = 0;
        $cumulativeData = [];
        foreach ($dailyPnl as $day) {
            $cumulativePnl += $day['daily_pnl'];
            $cumulativeData[] = [
                'date' => $day['trade_date'],
                'daily_pnl' => $day['daily_pnl'],
                'cumulative_pnl' => $cumulativePnl,
                'trade_count' => $day['trade_count']
            ];
        }
        
        // 计算最大回撤
        $maxDrawdown = $this->calculateMaxDrawdown($cumulativeData);
        
        // 计算夏普比率（简化版）
        $sharpeRatio = $this->calculateSharpeRatio($dailyPnl);
        
        return [
            'daily_pnl' => $dailyPnl,
            'cumulative_pnl' => $cumulativeData,
            'max_drawdown' => $maxDrawdown,
            'sharpe_ratio' => $sharpeRatio
        ];
    }

    /**
     * 计算盈亏分析
     */
    private function calculatePnlAnalysis($period, $groupBy) {
        $userId = Session::getUserId() ?? 'default';
        
        // 确定时间范围
        $days = 30;
        switch ($period) {
            case '7d': $days = 7; break;
            case '30d': $days = 30; break;
            case '90d': $days = 90; break;
            case '1y': $days = 365; break;
        }
        
        // 确定分组格式
        $dateFormat = 'DATE(tr.timestamp)';
        switch ($groupBy) {
            case 'hour': $dateFormat = 'DATE_FORMAT(tr.timestamp, "%Y-%m-%d %H:00:00")'; break;
            case 'day': $dateFormat = 'DATE(tr.timestamp)'; break;
            case 'week': $dateFormat = 'YEARWEEK(tr.timestamp)'; break;
            case 'month': $dateFormat = 'DATE_FORMAT(tr.timestamp, "%Y-%m")'; break;
        }
        
        $data = $this->db->fetchAll("
            SELECT 
                {$dateFormat} as period,
                SUM(tr.pnl) as total_pnl,
                COUNT(*) as trade_count,
                SUM(CASE WHEN tr.pnl > 0 THEN 1 ELSE 0 END) as profit_trades,
                SUM(CASE WHEN tr.pnl < 0 THEN 1 ELSE 0 END) as loss_trades,
                AVG(tr.pnl) as avg_pnl,
                MAX(tr.pnl) as max_profit,
                MIN(tr.pnl) as max_loss
            FROM trading_records tr
            JOIN trading_sessions ts ON tr.session_id = ts.session_id
            WHERE ts.user_id = ? 
            AND tr.timestamp >= DATE_SUB(NOW(), INTERVAL ? DAY)
            GROUP BY {$dateFormat}
            ORDER BY period
        ", [$userId, $days]);
        
        return $data;
    }

    /**
     * 计算策略性能
     */
    private function calculateStrategyPerformance($strategy, $period) {
        $userId = Session::getUserId() ?? 'default';
        
        $days = 30;
        switch ($period) {
            case '7d': $days = 7; break;
            case '30d': $days = 30; break;
            case '90d': $days = 90; break;
        }
        
        $whereConditions = ['ts.user_id = ?', 'tr.timestamp >= DATE_SUB(NOW(), INTERVAL ? DAY)'];
        $params = [$userId, $days];
        
        if ($strategy) {
            $whereConditions[] = 'ts.strategy = ?';
            $params[] = $strategy;
        }
        
        $whereClause = implode(' AND ', $whereConditions);
        
        $performance = $this->db->fetchAll("
            SELECT 
                ts.strategy,
                COUNT(*) as total_trades,
                SUM(tr.pnl) as total_pnl,
                AVG(tr.pnl) as avg_pnl,
                SUM(CASE WHEN tr.pnl > 0 THEN 1 ELSE 0 END) as profit_trades,
                SUM(CASE WHEN tr.pnl < 0 THEN 1 ELSE 0 END) as loss_trades,
                MAX(tr.pnl) as max_profit,
                MIN(tr.pnl) as max_loss,
                STDDEV(tr.pnl) as pnl_std
            FROM trading_records tr
            JOIN trading_sessions ts ON tr.session_id = ts.session_id
            WHERE {$whereClause}
            GROUP BY ts.strategy
            ORDER BY total_pnl DESC
        ", $params);
        
        // 计算每个策略的胜率
        foreach ($performance as &$perf) {
            $perf['win_rate'] = $perf['total_trades'] > 0 ? 
                ($perf['profit_trades'] / $perf['total_trades']) * 100 : 0;
        }
        
        return $performance;
    }

    /**
     * 计算最大回撤
     */
    private function calculateMaxDrawdown($cumulativeData) {
        $maxDrawdown = 0;
        $peak = 0;
        
        foreach ($cumulativeData as $data) {
            $value = $data['cumulative_pnl'];
            if ($value > $peak) {
                $peak = $value;
            }
            $drawdown = ($peak - $value) / max($peak, 1) * 100;
            if ($drawdown > $maxDrawdown) {
                $maxDrawdown = $drawdown;
            }
        }
        
        return $maxDrawdown;
    }

    /**
     * 计算夏普比率
     */
    private function calculateSharpeRatio($dailyPnl) {
        if (empty($dailyPnl)) return 0;
        
        $returns = array_column($dailyPnl, 'daily_pnl');
        $avgReturn = array_sum($returns) / count($returns);
        
        // 计算标准差
        $variance = 0;
        foreach ($returns as $return) {
            $variance += pow($return - $avgReturn, 2);
        }
        $stdDev = sqrt($variance / count($returns));
        
        return $stdDev > 0 ? $avgReturn / $stdDev : 0;
    }

    /**
     * 获取筛选参数
     */
    private function getFilters() {
        return [
            'exchange' => $this->request['exchange'] ?? '',
            'symbol' => $this->request['symbol'] ?? '',
            'strategy' => $this->request['strategy'] ?? '',
            'start_date' => $this->request['start_date'] ?? '',
            'end_date' => $this->request['end_date'] ?? ''
        ];
    }

    /**
     * 导出为CSV
     */
    private function exportToCSV($trades) {
        $filename = 'trading_history_' . date('Y-m-d_H-i-s') . '.csv';
        
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: no-cache, must-revalidate');
        
        $output = fopen('php://output', 'w');
        
        // 写入BOM以支持中文
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
        
        // 写入标题行
        fputcsv($output, [
            '时间', '交易所', '交易对', '策略', '方向', '数量', '价格', '盈亏', '手续费', '状态'
        ]);
        
        // 写入数据
        foreach ($trades as $trade) {
            fputcsv($output, [
                $trade['timestamp'],
                $trade['exchange'],
                $trade['symbol'],
                $trade['strategy'],
                $trade['side'],
                $trade['amount'],
                $trade['price'],
                $trade['pnl'],
                $trade['fee'],
                $trade['status']
            ]);
        }
        
        fclose($output);
        exit;
    }

    /**
     * 导出为Excel（简化版）
     */
    private function exportToExcel($trades) {
        // 这里可以使用PhpSpreadsheet库来生成Excel文件
        // 为了简化，我们先用CSV格式
        $this->exportToCSV($trades);
    }
}
?>
