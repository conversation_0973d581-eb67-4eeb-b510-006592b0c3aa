"""
策略模块单元测试
测试开仓策略、加仓策略等
"""

import unittest
import asyncio
from unittest.mock import Mock, AsyncMock, patch

from tests import TEST_CONFIG, get_event_loop
from strategies.opening_strategy import AsyncOpeningStrategy, PositionConfig, OpeningResult, PositionDirection
from strategies.add_position_strategy import AsyncAddPositionStrategy, AddPositionConfig, AddPositionType
from indicators.macd_calculator import MACDSignal

class TestOpeningStrategy(unittest.TestCase):
    """开仓策略测试"""
    
    def setUp(self):
        """测试前准备"""
        self.loop = get_event_loop()
        self.mock_exchange = AsyncMock()
        self.mock_macd_calculator = AsyncMock()
        
        self.strategy = AsyncOpeningStrategy(
            exchange=self.mock_exchange,
            macd_calculator=self.mock_macd_calculator
        )
    
    def test_strategy_initialization(self):
        """测试策略初始化"""
        self.assertIsNotNone(self.strategy)
        self.assertEqual(self.strategy.exchange, self.mock_exchange)
        self.assertEqual(self.strategy.macd_calculator, self.mock_macd_calculator)
    
    def test_position_config_creation(self):
        """测试持仓配置创建"""
        config = PositionConfig(
            symbol="BTC-USDT",
            direction=PositionDirection.LONG,
            amount=0.1,
            leverage=10,
            stop_loss_percent=0.05,
            take_profit_percent=0.10
        )
        
        self.assertEqual(config.symbol, "BTC-USDT")
        self.assertEqual(config.direction, PositionDirection.LONG)
        self.assertEqual(config.amount, 0.1)
        self.assertEqual(config.leverage, 10)
        self.assertEqual(config.stop_loss_percent, 0.05)
        self.assertEqual(config.take_profit_percent, 0.10)
    
    def test_analyze_opening_opportunity(self):
        """测试分析开仓机会"""
        async def test_analyze():
            # 模拟MACD信号
            mock_signal = MACDSignal(
                signal_type="bullish",
                strength=0.8,
                timestamp=1234567890,
                description="强烈看涨信号"
            )
            
            # 模拟K线数据获取
            self.mock_exchange.get_klines.return_value = [
                Mock(close=50000), Mock(close=50100), Mock(close=50200)
            ]
            
            # 模拟MACD计算
            self.mock_macd_calculator.calculate_macd_from_klines.return_value = Mock()
            self.mock_macd_calculator.detect_signal.return_value = mock_signal
            
            config = PositionConfig(
                symbol="BTC-USDT",
                direction=PositionDirection.LONG,
                amount=0.1,
                leverage=10
            )
            
            result = await self.strategy.analyze_opening_opportunity(config)
            
            if result:
                self.assertIsInstance(result, MACDSignal)
                self.assertEqual(result.signal_type, "bullish")
        
        self.loop.run_until_complete(test_analyze())
    
    def test_execute_opening(self):
        """测试执行开仓"""
        async def test_execute():
            # 模拟订单执行
            mock_order = Mock()
            mock_order.order_id = "12345"
            mock_order.status = "filled"
            self.mock_exchange.place_market_order.return_value = mock_order
            
            # 模拟信号
            signal = MACDSignal(
                signal_type="bullish",
                strength=0.8,
                timestamp=1234567890
            )
            
            config = PositionConfig(
                symbol="BTC-USDT",
                direction=PositionDirection.LONG,
                amount=0.1,
                leverage=10
            )
            
            result = await self.strategy.execute_opening(config, signal)
            
            self.assertIsInstance(result, OpeningResult)
            if result.success:
                self.assertTrue(result.success)
                self.assertIsNotNone(result.order_id)
        
        self.loop.run_until_complete(test_execute())

class TestAddPositionStrategy(unittest.TestCase):
    """加仓策略测试"""
    
    def setUp(self):
        """测试前准备"""
        self.loop = get_event_loop()
        self.mock_exchange = AsyncMock()
        
        self.strategy = AsyncAddPositionStrategy(
            exchange=self.mock_exchange
        )
    
    def test_add_position_config_creation(self):
        """测试加仓配置创建"""
        config = AddPositionConfig(
            symbol="BTC-USDT",
            add_type=AddPositionType.HALF,
            initial_margin=100.0,
            max_add_times=3,
            min_interval=300,
            risk_threshold=0.1
        )
        
        self.assertEqual(config.symbol, "BTC-USDT")
        self.assertEqual(config.add_type, AddPositionType.HALF)
        self.assertEqual(config.initial_margin, 100.0)
        self.assertEqual(config.max_add_times, 3)
        self.assertEqual(config.min_interval, 300)
        self.assertEqual(config.risk_threshold, 0.1)
    
    def test_add_position_type_enum(self):
        """测试加仓类型枚举"""
        self.assertEqual(AddPositionType.EQUAL.value, "equal")
        self.assertEqual(AddPositionType.HALF.value, "half")
        self.assertEqual(AddPositionType.CUSTOM.value, "custom")
    
    def test_calculate_add_amount(self):
        """测试计算加仓数量"""
        async def test_calculate():
            config = AddPositionConfig(
                symbol="BTC-USDT",
                add_type=AddPositionType.HALF,
                initial_margin=100.0,
                max_add_times=3,
                min_interval=300,
                risk_threshold=0.1
            )
            
            # 模拟当前持仓
            mock_position = Mock()
            mock_position.size = 0.1
            mock_position.leverage = 10
            
            amount, margin = await self.strategy.calculate_add_amount(config, mock_position)
            
            self.assertIsInstance(amount, float)
            self.assertIsInstance(margin, float)
            self.assertGreater(amount, 0)
            self.assertGreater(margin, 0)
        
        self.loop.run_until_complete(test_calculate())

class TestPositionDirection(unittest.TestCase):
    """持仓方向测试"""
    
    def test_position_direction_enum(self):
        """测试持仓方向枚举"""
        self.assertEqual(PositionDirection.LONG.value, "long")
        self.assertEqual(PositionDirection.SHORT.value, "short")

if __name__ == '__main__':
    unittest.main()
