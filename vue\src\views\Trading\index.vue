<template>
  <div class="trading-page">
    <div class="page-header">
      <h2>交易控制</h2>
      <p>启动、停止和监控交易系统运行状态</p>
    </div>

    <div class="card">
      <h3 class="mb-md">交易控制开发中...</h3>
      <el-empty description="交易控制页面正在开发中">
        <template #image>
          <el-icon size="60" color="#c0c4cc"><VideoPlay /></el-icon>
        </template>
      </el-empty>
    </div>
  </div>
</template>

<script setup>
import { VideoPlay } from '@element-plus/icons-vue'
</script>

<style lang="scss" scoped>
.trading-page {
  .page-header {
    margin-bottom: var(--spacing-lg);
    
    h2 {
      margin-bottom: var(--spacing-xs);
      color: var(--text-color-primary);
    }
    
    p {
      color: var(--text-color-secondary);
    }
  }
}
</style>
