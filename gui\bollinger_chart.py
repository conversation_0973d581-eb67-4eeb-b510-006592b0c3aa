#!/usr/bin/env python3
"""
布林带实时图表显示组件
提供价格和布林带的可视化显示
"""

import tkinter as tk
from tkinter import ttk
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import numpy as np
from datetime import datetime, timedelta
from typing import List, Optional, Tuple
import logging

# 配置matplotlib中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

logger = logging.getLogger(__name__)

class BollingerChart:
    """布林带实时图表组件"""
    
    def __init__(self, parent_frame):
        self.parent_frame = parent_frame
        self.figure = None
        self.canvas = None
        self.ax = None
        
        # 数据存储
        self.timestamps = []
        self.prices = []
        self.upper_bands = []
        self.middle_bands = []
        self.lower_bands = []
        self.signals = []  # 买卖信号
        
        # 显示设置
        self.max_points = 100  # 最多显示100个数据点
        self.chart_enabled = True
        
        self.setup_chart()
    
    def setup_chart(self):
        """设置图表"""
        try:
            # 创建图表框架
            chart_frame = ttk.LabelFrame(self.parent_frame, text="布林带实时图表", padding="5")
            chart_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
            
            # 创建matplotlib图表
            self.figure = Figure(figsize=(10, 6), dpi=80)
            self.ax = self.figure.add_subplot(111)
            
            # 设置图表样式
            self.ax.set_title("布林带策略实时监控", fontsize=12, fontweight='bold')
            self.ax.set_xlabel("时间", fontsize=10)
            self.ax.set_ylabel("价格 (USDT)", fontsize=10)
            self.ax.grid(True, alpha=0.3)
            
            # 创建画布
            self.canvas = FigureCanvasTkAgg(self.figure, chart_frame)
            self.canvas.draw()
            self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
            
            # 添加控制按钮
            control_frame = ttk.Frame(chart_frame)
            control_frame.pack(fill=tk.X, pady=(5, 0))
            
            # 清除数据按钮
            clear_btn = ttk.Button(control_frame, text="清除数据", command=self.clear_data)
            clear_btn.pack(side=tk.LEFT, padx=(0, 5))
            
            # 启用/禁用图表
            self.chart_enabled_var = tk.BooleanVar(value=True)
            enable_check = ttk.Checkbutton(
                control_frame, 
                text="启用实时图表", 
                variable=self.chart_enabled_var,
                command=self.toggle_chart
            )
            enable_check.pack(side=tk.LEFT, padx=(5, 0))
            
            # 数据点数量控制
            ttk.Label(control_frame, text="显示点数:").pack(side=tk.LEFT, padx=(10, 5))
            self.points_var = tk.IntVar(value=100)
            points_spin = ttk.Spinbox(
                control_frame, 
                from_=20, 
                to=500, 
                textvariable=self.points_var,
                width=8,
                command=self.update_max_points
            )
            points_spin.pack(side=tk.LEFT)
            
            logger.info("布林带图表组件初始化完成")
            
        except Exception as e:
            logger.error(f"设置布林带图表失败: {e}")
    
    def add_data_point(self, price: float, upper_band: float = None,
                      middle_band: float = None, lower_band: float = None,
                      signal: str = None):
        """添加新的数据点"""
        try:
            if not self.chart_enabled_var.get():
                logger.debug("图表已禁用，跳过数据点添加")
                return

            # 验证数据有效性
            if price is None or price <= 0:
                logger.warning(f"无效的价格数据: {price}")
                return

            # 添加时间戳
            current_time = datetime.now()
            self.timestamps.append(current_time)
            self.prices.append(price)

            # 添加布林带数据
            self.upper_bands.append(upper_band if upper_band is not None else price)
            self.middle_bands.append(middle_band if middle_band is not None else price)
            self.lower_bands.append(lower_band if lower_band is not None else price)

            # 添加信号
            self.signals.append(signal)

            # 限制数据点数量
            max_points = self.points_var.get()
            if len(self.timestamps) > max_points:
                self.timestamps = self.timestamps[-max_points:]
                self.prices = self.prices[-max_points:]
                self.upper_bands = self.upper_bands[-max_points:]
                self.middle_bands = self.middle_bands[-max_points:]
                self.lower_bands = self.lower_bands[-max_points:]
                self.signals = self.signals[-max_points:]

            # 记录数据点添加
            upper_band_str = f"{upper_band:.4f}" if upper_band is not None else "N/A"
            logger.debug(f"📈 图表添加数据点: 价格={price:.4f}, 上轨={upper_band_str}, 总点数={len(self.timestamps)}")

            # 更新图表
            self.update_chart()

        except Exception as e:
            logger.error(f"添加图表数据点失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
    
    def update_chart(self):
        """更新图表显示"""
        try:
            if not self.chart_enabled_var.get() or not self.timestamps:
                return
            
            # 清除当前图表
            self.ax.clear()
            
            # 重新设置图表样式
            self.ax.set_title("布林带策略实时监控", fontsize=12, fontweight='bold')
            self.ax.set_xlabel("时间", fontsize=10)
            self.ax.set_ylabel("价格 (USDT)", fontsize=10)
            self.ax.grid(True, alpha=0.3)
            
            # 绘制布林带
            if len(self.timestamps) > 1:
                # 上轨
                self.ax.plot(self.timestamps, self.upper_bands, 
                           color='red', linestyle='--', alpha=0.7, label='上轨')
                
                # 中轨（移动平均线）
                self.ax.plot(self.timestamps, self.middle_bands, 
                           color='blue', linestyle='-', alpha=0.8, label='中轨(MA)')
                
                # 下轨
                self.ax.plot(self.timestamps, self.lower_bands, 
                           color='green', linestyle='--', alpha=0.7, label='下轨')
                
                # 填充布林带区域
                self.ax.fill_between(self.timestamps, self.upper_bands, self.lower_bands,
                                   alpha=0.1, color='gray', label='布林带区域')
                
                # 价格线
                self.ax.plot(self.timestamps, self.prices, 
                           color='black', linewidth=2, label='价格')
                
                # 添加买卖信号标记
                for i, (timestamp, price, signal) in enumerate(zip(self.timestamps, self.prices, self.signals)):
                    if signal == 'buy':
                        self.ax.scatter(timestamp, price, color='green', marker='^', 
                                      s=100, zorder=5, label='买入信号' if i == 0 else "")
                    elif signal == 'sell':
                        self.ax.scatter(timestamp, price, color='red', marker='v', 
                                      s=100, zorder=5, label='卖出信号' if i == 0 else "")
            
            # 设置时间轴格式
            if len(self.timestamps) > 0:
                self.ax.tick_params(axis='x', rotation=45)
                
                # 只显示部分时间标签以避免拥挤
                if len(self.timestamps) > 10:
                    step = max(1, len(self.timestamps) // 10)
                    tick_positions = range(0, len(self.timestamps), step)
                    tick_labels = [self.timestamps[i].strftime("%H:%M:%S") for i in tick_positions]
                    self.ax.set_xticks([self.timestamps[i] for i in tick_positions])
                    self.ax.set_xticklabels(tick_labels)
            
            # 添加图例（仅在有标签数据时）
            handles, labels = self.ax.get_legend_handles_labels()
            if handles and labels:
                self.ax.legend(loc='upper left', fontsize=8)
            
            # 调整布局
            self.figure.tight_layout()
            
            # 刷新画布
            self.canvas.draw()
            
        except Exception as e:
            logger.error(f"更新图表显示失败: {e}")
    
    def clear_data(self):
        """清除所有数据"""
        try:
            self.timestamps.clear()
            self.prices.clear()
            self.upper_bands.clear()
            self.middle_bands.clear()
            self.lower_bands.clear()
            self.signals.clear()
            
            # 清除图表
            if self.ax:
                self.ax.clear()
                self.ax.set_title("布林带策略实时监控", fontsize=12, fontweight='bold')
                self.ax.set_xlabel("时间", fontsize=10)
                self.ax.set_ylabel("价格 (USDT)", fontsize=10)
                self.ax.grid(True, alpha=0.3)
                self.ax.text(0.5, 0.5, '等待数据...', 
                           horizontalalignment='center', verticalalignment='center',
                           transform=self.ax.transAxes, fontsize=14, alpha=0.5)
                self.canvas.draw()
            
            logger.info("图表数据已清除")
            
        except Exception as e:
            logger.error(f"清除图表数据失败: {e}")
    
    def toggle_chart(self):
        """切换图表启用状态"""
        try:
            self.chart_enabled = self.chart_enabled_var.get()
            if not self.chart_enabled:
                # 禁用时清除图表
                if self.ax:
                    self.ax.clear()
                    self.ax.set_title("图表已禁用", fontsize=12)
                    self.ax.text(0.5, 0.5, '图表已禁用\n勾选"启用实时图表"以重新启用', 
                               horizontalalignment='center', verticalalignment='center',
                               transform=self.ax.transAxes, fontsize=12, alpha=0.7)
                    self.canvas.draw()
            else:
                # 启用时重新绘制
                self.update_chart()
            
            logger.info(f"图表状态: {'启用' if self.chart_enabled else '禁用'}")
            
        except Exception as e:
            logger.error(f"切换图表状态失败: {e}")
    
    def update_max_points(self):
        """更新最大显示点数"""
        try:
            max_points = self.points_var.get()
            
            # 如果当前数据超过新的限制，截取数据
            if len(self.timestamps) > max_points:
                self.timestamps = self.timestamps[-max_points:]
                self.prices = self.prices[-max_points:]
                self.upper_bands = self.upper_bands[-max_points:]
                self.middle_bands = self.middle_bands[-max_points:]
                self.lower_bands = self.lower_bands[-max_points:]
                self.signals = self.signals[-max_points:]
                
                # 更新图表
                self.update_chart()
            
            logger.info(f"图表最大显示点数更新为: {max_points}")
            
        except Exception as e:
            logger.error(f"更新最大显示点数失败: {e}")
    
    def add_signal(self, signal_type: str, price: float):
        """添加交易信号标记"""
        try:
            if not self.chart_enabled_var.get():
                return
            
            # 如果有数据点，在最后一个点添加信号
            if self.signals:
                self.signals[-1] = signal_type
                self.update_chart()
            
        except Exception as e:
            logger.error(f"添加交易信号失败: {e}")
    
    def get_chart_stats(self) -> dict:
        """获取图表统计信息"""
        try:
            if not self.prices:
                return {}
            
            return {
                'data_points': len(self.prices),
                'price_range': {
                    'min': min(self.prices),
                    'max': max(self.prices),
                    'current': self.prices[-1] if self.prices else 0
                },
                'time_range': {
                    'start': self.timestamps[0] if self.timestamps else None,
                    'end': self.timestamps[-1] if self.timestamps else None
                },
                'signals': {
                    'buy_count': self.signals.count('buy'),
                    'sell_count': self.signals.count('sell')
                }
            }
            
        except Exception as e:
            logger.error(f"获取图表统计信息失败: {e}")
            return {}
