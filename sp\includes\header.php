<?php
/**
 * BitV MACD智能加仓交易系统 - 页面头部模板
 * 实盘交易版本 - 专业级界面设计
 */

if (!defined('BITV_ACCESS')) {
    define('BITV_ACCESS', true);
}

// 确保已加载配置
if (!defined('SYSTEM_NAME')) {
    require_once __DIR__ . '/../config/config.php';
}

// 设置默认值
$pageTitle = $pageTitle ?? 'BitV交易系统';
$currentPage = $currentPage ?? '';
$bodyClass = $bodyClass ?? '';

// 获取当前用户信息
$currentUser = getCurrentUser();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="BitV MACD智能加仓交易系统 - 专业级量化交易平台">
    <meta name="keywords" content="量化交易,MACD,智能加仓,实盘交易,OKX,Gate.io">
    <meta name="author" content="<?php echo SYSTEM_AUTHOR; ?>">
    <meta name="robots" content="noindex, nofollow">
    
    <title><?php echo htmlspecialchars($pageTitle . ' - ' . SYSTEM_NAME); ?></title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/img/favicon.ico">
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    <!-- Toastr for notifications -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    
    <style>
        :root {
            --primary-color: #007bff;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --dark-color: #343a40;
            --light-color: #f8f9fa;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f4f6f9;
        }
        
        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }
        
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            margin: 2px 0;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
            transform: translateX(5px);
        }
        
        .sidebar .nav-link i {
            width: 20px;
            text-align: center;
            margin-right: 10px;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }
        
        .btn {
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .border-left-primary {
            border-left: 4px solid var(--primary-color) !important;
        }
        
        .border-left-success {
            border-left: 4px solid var(--success-color) !important;
        }
        
        .border-left-info {
            border-left: 4px solid var(--info-color) !important;
        }
        
        .border-left-warning {
            border-left: 4px solid var(--warning-color) !important;
        }
        
        .border-left-danger {
            border-left: 4px solid var(--danger-color) !important;
        }
        
        .text-xs {
            font-size: 0.75rem;
        }
        
        .position-item,
        .session-item {
            transition: all 0.3s ease;
            border: 1px solid #e3e6f0 !important;
        }
        
        .position-item:hover,
        .session-item:hover {
            background-color: #f8f9fc;
            transform: translateX(5px);
        }
        
        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        
        .status-online {
            background-color: var(--success-color);
            animation: pulse 2s infinite;
        }
        
        .status-offline {
            background-color: var(--danger-color);
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: var(--danger-color);
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        
        .risk-meter {
            width: 100%;
            height: 20px;
            background: linear-gradient(to right, 
                var(--success-color) 0%, 
                var(--warning-color) 50%, 
                var(--danger-color) 100%);
            border-radius: 10px;
            position: relative;
            margin: 10px 0;
        }
        
        .risk-indicator {
            position: absolute;
            top: -5px;
            width: 30px;
            height: 30px;
            background-color: white;
            border: 3px solid var(--dark-color);
            border-radius: 50%;
            transform: translateX(-50%);
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                top: 0;
                left: -250px;
                width: 250px;
                height: 100vh;
                z-index: 1000;
                transition: left 0.3s ease;
            }
            
            .sidebar.show {
                left: 0;
            }
            
            .main-content {
                margin-left: 0 !important;
            }
            
            .mobile-menu-btn {
                display: block !important;
            }
        }
        
        @media (min-width: 769px) {
            .mobile-menu-btn {
                display: none !important;
            }
        }
    </style>
</head>
<body class="<?php echo $bodyClass; ?>">
    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="text-center text-white">
            <div class="loading-spinner"></div>
            <div class="mt-3" id="loadingText">加载中...</div>
        </div>
    </div>

    <div class="d-flex">
        <!-- Sidebar -->
        <nav class="sidebar" id="sidebar">
            <div class="p-4">
                <div class="text-center mb-4">
                    <h4 class="text-white mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        BitV交易系统
                    </h4>
                    <small class="text-white-50">实盘版本 v<?php echo SYSTEM_VERSION; ?></small>
                </div>
                
                <?php if ($currentUser): ?>
                <div class="user-info text-center mb-4 p-3" style="background-color: rgba(255,255,255,0.1); border-radius: 10px;">
                    <div class="text-white">
                        <i class="fas fa-user-circle fa-2x mb-2"></i>
                        <div class="fw-bold"><?php echo htmlspecialchars($currentUser['username']); ?></div>
                        <small class="text-white-50"><?php echo ucfirst($currentUser['role']); ?></small>
                    </div>
                </div>
                <?php endif; ?>
                
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link <?php echo $currentPage === 'dashboard' ? 'active' : ''; ?>" href="index.php">
                            <i class="fas fa-tachometer-alt"></i>
                            仪表板
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $currentPage === 'trading' ? 'active' : ''; ?>" href="trading.php">
                            <i class="fas fa-robot"></i>
                            智能交易
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $currentPage === 'positions' ? 'active' : ''; ?>" href="positions.php">
                            <i class="fas fa-chart-pie"></i>
                            持仓管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $currentPage === 'history' ? 'active' : ''; ?>" href="history.php">
                            <i class="fas fa-history"></i>
                            交易历史
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $currentPage === 'analysis' ? 'active' : ''; ?>" href="analysis.php">
                            <i class="fas fa-chart-bar"></i>
                            数据分析
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $currentPage === 'settings' ? 'active' : ''; ?>" href="settings.php">
                            <i class="fas fa-cog"></i>
                            系统设置
                        </a>
                    </li>
                    
                    <?php if (hasPermission('admin')): ?>
                    <li class="nav-item mt-3">
                        <div class="text-white-50 small px-3 mb-2">管理功能</div>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $currentPage === 'users' ? 'active' : ''; ?>" href="users.php">
                            <i class="fas fa-users"></i>
                            用户管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $currentPage === 'logs' ? 'active' : ''; ?>" href="logs.php">
                            <i class="fas fa-file-alt"></i>
                            系统日志
                        </a>
                    </li>
                    <?php endif; ?>
                    
                    <li class="nav-item mt-4">
                        <a class="nav-link text-warning" href="logout.php">
                            <i class="fas fa-sign-out-alt"></i>
                            退出登录
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="main-content flex-grow-1" style="margin-left: 0;">
            <!-- Top Navigation -->
            <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm mb-4">
                <div class="container-fluid">
                    <button class="btn btn-outline-primary mobile-menu-btn me-3" type="button" onclick="toggleSidebar()">
                        <i class="fas fa-bars"></i>
                    </button>
                    
                    <div class="d-flex align-items-center">
                        <span class="navbar-text me-3">
                            <span class="status-indicator status-online"></span>
                            实盘交易模式
                        </span>
                        
                        <!-- 系统状态指示器 -->
                        <div class="d-flex align-items-center me-3" id="systemStatus">
                            <span class="badge bg-secondary">检查中...</span>
                        </div>
                        
                        <!-- 通知按钮 -->
                        <div class="position-relative me-3">
                            <button class="btn btn-outline-secondary" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-bell"></i>
                                <span class="notification-badge" id="notificationCount" style="display: none;">0</span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" id="notificationDropdown">
                                <li><h6 class="dropdown-header">通知</h6></li>
                                <li><span class="dropdown-item-text">暂无新通知</span></li>
                            </ul>
                        </div>
                        
                        <!-- 用户菜单 -->
                        <?php if ($currentUser): ?>
                        <div class="dropdown">
                            <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>
                                <?php echo htmlspecialchars($currentUser['username']); ?>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="profile.php">
                                    <i class="fas fa-user-edit me-2"></i>个人资料
                                </a></li>
                                <li><a class="dropdown-item" href="security.php">
                                    <i class="fas fa-shield-alt me-2"></i>安全设置
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="logout.php">
                                    <i class="fas fa-sign-out-alt me-2"></i>退出登录
                                </a></li>
                            </ul>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </nav>

            <!-- Page Content -->
            <main class="container-fluid">
                <?php
                // 显示Flash消息
                if (isset($_SESSION['flash_message'])) {
                    $flashType = $_SESSION['flash_type'] ?? 'info';
                    $flashMessage = $_SESSION['flash_message'];
                    
                    echo "<div class='alert alert-{$flashType} alert-dismissible fade show' role='alert'>";
                    echo htmlspecialchars($flashMessage);
                    echo "<button type='button' class='btn-close' data-bs-dismiss='alert'></button>";
                    echo "</div>";
                    
                    unset($_SESSION['flash_message'], $_SESSION['flash_type']);
                }
                ?>

<script>
// 全局JavaScript变量
window.BITV = {
    API_BASE: '<?php echo PYTHON_API_BASE; ?>',
    WS_BASE: '<?php echo PYTHON_WS_BASE; ?>',
    API_TOKEN: '<?php echo API_TOKEN; ?>',
    CSRF_TOKEN: '<?php echo generateCSRFToken(); ?>',
    USER_ROLE: '<?php echo $currentUser['role'] ?? 'guest'; ?>'
};

// 工具函数
function toggleSidebar() {
    document.getElementById('sidebar').classList.toggle('show');
}

function showLoading(text = '加载中...') {
    document.getElementById('loadingText').textContent = text;
    document.getElementById('loadingOverlay').style.display = 'flex';
}

function hideLoading() {
    document.getElementById('loadingOverlay').style.display = 'none';
}

// 配置toastr
toastr.options = {
    "closeButton": true,
    "debug": false,
    "newestOnTop": true,
    "progressBar": true,
    "positionClass": "toast-top-right",
    "preventDuplicates": false,
    "onclick": null,
    "showDuration": "300",
    "hideDuration": "1000",
    "timeOut": "5000",
    "extendedTimeOut": "1000",
    "showEasing": "swing",
    "hideEasing": "linear",
    "showMethod": "fadeIn",
    "hideMethod": "fadeOut"
};

// 检查系统状态
function checkSystemStatus() {
    fetch(window.BITV.API_BASE + '/health')
        .then(response => response.json())
        .then(data => {
            const statusElement = document.getElementById('systemStatus');
            if (data.status === 'healthy') {
                statusElement.innerHTML = '<span class="badge bg-success">系统正常</span>';
            } else {
                statusElement.innerHTML = '<span class="badge bg-danger">系统异常</span>';
            }
        })
        .catch(error => {
            const statusElement = document.getElementById('systemStatus');
            statusElement.innerHTML = '<span class="badge bg-danger">连接失败</span>';
        });
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 检查系统状态
    checkSystemStatus();
    
    // 每30秒检查一次系统状态
    setInterval(checkSystemStatus, 30000);
    
    // 初始化Bootstrap组件
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
