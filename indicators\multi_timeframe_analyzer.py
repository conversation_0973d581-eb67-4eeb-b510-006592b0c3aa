"""
多时间周期MACD趋势分析模块
实现多时间周期趋势确认功能
严格遵守异步编程原则
"""
import asyncio
import logging
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from exchanges import BaseExchange
from indicators.macd_calculator import AsyncMACDCalculator, MACDSignal

logger = logging.getLogger(__name__)

class TrendDirection(Enum):
    """趋势方向"""
    BULLISH = "bullish"    # 看涨
    BEARISH = "bearish"    # 看跌
    NEUTRAL = "neutral"    # 中性

@dataclass
class TimeframeTrend:
    """时间周期趋势"""
    timeframe: str
    direction: TrendDirection
    signal: Optional[MACDSignal]
    confidence: float  # 信号置信度

@dataclass
class MultiTimeframeTrendResult:
    """多时间周期趋势分析结果"""
    main_timeframe: str
    main_trend: TimeframeTrend
    confirmation_trends: List[TimeframeTrend]
    is_aligned: bool  # 是否趋势一致
    overall_direction: TrendDirection
    confidence_score: float  # 整体置信度
    analysis_summary: str

class AsyncMultiTimeframeAnalyzer:
    """
    异步多时间周期趋势分析器
    分析多个时间周期的MACD趋势一致性
    """
    
    def __init__(self, exchange: BaseExchange):
        """
        初始化多时间周期分析器
        
        Args:
            exchange: 交易所实例
        """
        self.exchange = exchange
        self._macd_calculators: Dict[str, AsyncMACDCalculator] = {}
        
        logger.info("多时间周期趋势分析器初始化完成")
    
    def _get_macd_calculator(self, timeframe: str) -> AsyncMACDCalculator:
        """获取指定时间周期的MACD计算器"""
        if timeframe not in self._macd_calculators:
            self._macd_calculators[timeframe] = AsyncMACDCalculator()
        return self._macd_calculators[timeframe]
    
    async def analyze_single_timeframe_trend(self, symbol: str, timeframe: str) -> Optional[TimeframeTrend]:
        """
        分析单个时间周期的趋势
        
        Args:
            symbol: 交易对
            timeframe: 时间周期
            
        Returns:
            Optional[TimeframeTrend]: 时间周期趋势，失败时返回None
        """
        try:
            # 获取MACD计算器
            macd_calculator = self._get_macd_calculator(timeframe)
            
            # 获取MACD信号
            signal = await macd_calculator.get_trend_signal(self.exchange, symbol, timeframe)
            
            if not signal:
                logger.warning(f"无法获取{timeframe}周期的MACD信号")
                return None
            
            # 判断趋势方向
            if signal.signal_type == 'buy':
                direction = TrendDirection.BULLISH
            elif signal.signal_type == 'sell':
                direction = TrendDirection.BEARISH
            else:
                direction = TrendDirection.NEUTRAL
            
            # 计算置信度（基于信号强度）
            confidence = signal.strength
            
            return TimeframeTrend(
                timeframe=timeframe,
                direction=direction,
                signal=signal,
                confidence=confidence
            )
            
        except Exception as e:
            logger.error(f"分析{timeframe}周期趋势异常: {e}")
            return None
    
    async def analyze_multi_timeframe_trend(self, symbol: str, main_timeframe: str, 
                                          confirmation_timeframes: List[str]) -> Optional[MultiTimeframeTrendResult]:
        """
        分析多时间周期趋势一致性
        
        Args:
            symbol: 交易对
            main_timeframe: 主时间周期
            confirmation_timeframes: 确认时间周期列表
            
        Returns:
            Optional[MultiTimeframeTrendResult]: 多时间周期趋势分析结果
        """
        try:
            # 分析主时间周期趋势
            main_trend = await self.analyze_single_timeframe_trend(symbol, main_timeframe)
            if not main_trend:
                logger.error(f"无法分析主时间周期{main_timeframe}的趋势")
                return None
            
            # 如果没有确认时间周期，直接返回主趋势
            if not confirmation_timeframes:
                return MultiTimeframeTrendResult(
                    main_timeframe=main_timeframe,
                    main_trend=main_trend,
                    confirmation_trends=[],
                    is_aligned=True,
                    overall_direction=main_trend.direction,
                    confidence_score=main_trend.confidence,
                    analysis_summary=f"仅使用主时间周期{main_timeframe}，趋势方向: {main_trend.direction.value}"
                )
            
            # 并发分析所有确认时间周期
            confirmation_tasks = [
                self.analyze_single_timeframe_trend(symbol, tf) 
                for tf in confirmation_timeframes
            ]
            
            confirmation_results = await asyncio.gather(*confirmation_tasks, return_exceptions=True)
            
            # 过滤有效的确认趋势
            confirmation_trends = []
            for result in confirmation_results:
                if isinstance(result, TimeframeTrend):
                    confirmation_trends.append(result)
                elif isinstance(result, Exception):
                    logger.error(f"确认时间周期分析异常: {result}")
            
            if not confirmation_trends:
                logger.warning("所有确认时间周期分析都失败")
                return None
            
            # 分析趋势一致性
            is_aligned, overall_direction, confidence_score, summary = self._analyze_trend_alignment(
                main_trend, confirmation_trends
            )
            
            return MultiTimeframeTrendResult(
                main_timeframe=main_timeframe,
                main_trend=main_trend,
                confirmation_trends=confirmation_trends,
                is_aligned=is_aligned,
                overall_direction=overall_direction,
                confidence_score=confidence_score,
                analysis_summary=summary
            )
            
        except Exception as e:
            logger.error(f"多时间周期趋势分析异常: {e}")
            return None
    
    def _analyze_trend_alignment(self, main_trend: TimeframeTrend, 
                               confirmation_trends: List[TimeframeTrend]) -> Tuple[bool, TrendDirection, float, str]:
        """
        分析趋势一致性
        
        Args:
            main_trend: 主趋势
            confirmation_trends: 确认趋势列表
            
        Returns:
            Tuple[bool, TrendDirection, float, str]: (是否一致, 整体方向, 置信度, 分析摘要)
        """
        try:
            # 如果主趋势是中性，则不进行交易
            if main_trend.direction == TrendDirection.NEUTRAL:
                return False, TrendDirection.NEUTRAL, 0.0, "主时间周期趋势中性，不适合开仓"
            
            # 统计确认趋势方向
            aligned_count = 0
            total_confidence = main_trend.confidence
            
            trend_summary = [f"主周期{main_trend.timeframe}: {main_trend.direction.value}"]
            
            for trend in confirmation_trends:
                trend_summary.append(f"{trend.timeframe}: {trend.direction.value}")
                
                if trend.direction == main_trend.direction:
                    aligned_count += 1
                    total_confidence += trend.confidence
                elif trend.direction == TrendDirection.NEUTRAL:
                    # 中性趋势不算对立，但也不算支持
                    total_confidence += trend.confidence * 0.5
                else:
                    # 相反趋势，降低置信度
                    total_confidence -= trend.confidence * 0.3
            
            # 计算一致性
            total_confirmation_trends = len(confirmation_trends)
            alignment_ratio = aligned_count / total_confirmation_trends if total_confirmation_trends > 0 else 1.0
            
            # 判断是否趋势一致（至少70%的确认周期方向一致）
            is_aligned = alignment_ratio >= 0.7
            
            # 计算整体置信度
            confidence_score = max(0.0, min(1.0, total_confidence / (1 + total_confirmation_trends)))
            
            # 确定整体方向
            if is_aligned:
                overall_direction = main_trend.direction
            else:
                overall_direction = TrendDirection.NEUTRAL
            
            # 生成分析摘要
            summary = f"趋势一致性: {alignment_ratio*100:.1f}% ({aligned_count}/{total_confirmation_trends}), " \
                     f"整体方向: {overall_direction.value}, 置信度: {confidence_score:.2f}. " \
                     f"详情: {'; '.join(trend_summary)}"
            
            return is_aligned, overall_direction, confidence_score, summary
            
        except Exception as e:
            logger.error(f"分析趋势一致性异常: {e}")
            return False, TrendDirection.NEUTRAL, 0.0, f"趋势分析异常: {str(e)}"
    
    async def should_allow_opening(self, symbol: str, main_timeframe: str, 
                                 confirmation_timeframes: List[str], 
                                 min_confidence: float = 0.3) -> Tuple[bool, str]:
        """
        判断是否允许开仓
        
        Args:
            symbol: 交易对
            main_timeframe: 主时间周期
            confirmation_timeframes: 确认时间周期列表
            min_confidence: 最小置信度要求
            
        Returns:
            Tuple[bool, str]: (是否允许开仓, 原因说明)
        """
        try:
            # 进行多时间周期趋势分析
            result = await self.analyze_multi_timeframe_trend(symbol, main_timeframe, confirmation_timeframes)
            
            if not result:
                return False, "多时间周期趋势分析失败"
            
            # 检查趋势是否一致
            if not result.is_aligned:
                return False, f"多时间周期趋势不一致: {result.analysis_summary}"
            
            # 检查整体方向是否明确
            if result.overall_direction == TrendDirection.NEUTRAL:
                return False, f"整体趋势方向不明确: {result.analysis_summary}"
            
            # 检查置信度是否满足要求
            if result.confidence_score < min_confidence:
                return False, f"趋势置信度不足: {result.confidence_score:.2f} < {min_confidence}"
            
            # 所有条件满足，允许开仓
            return True, f"多时间周期趋势一致，允许开仓: {result.analysis_summary}"
            
        except Exception as e:
            logger.error(f"判断开仓条件异常: {e}")
            return False, f"判断开仓条件异常: {str(e)}"
    
    def clear_cache(self):
        """清空MACD计算器缓存"""
        for calculator in self._macd_calculators.values():
            calculator.reset()
        self._macd_calculators.clear()
        logger.info("多时间周期分析器缓存已清空")
