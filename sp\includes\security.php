<?php
/**
 * BitV MACD智能加仓交易系统 - 安全管理模块
 * 实盘交易版本 - 专业级安全防护
 */

if (!defined('BITV_ACCESS')) {
    die('Direct access not allowed');
}

/**
 * 用户认证类
 */
class UserAuth {
    private $pdo;
    
    public function __construct() {
        $this->pdo = getDBConnection();
        $this->initUserTable();
    }
    
    /**
     * 初始化用户表
     */
    private function initUserTable() {
        $sql = "CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            salt VARCHAR(32) NOT NULL,
            role ENUM('admin', 'trader', 'viewer') DEFAULT 'trader',
            status ENUM('active', 'inactive', 'locked') DEFAULT 'active',
            last_login DATETIME NULL,
            login_attempts INT DEFAULT 0,
            locked_until DATETIME NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            two_factor_secret VARCHAR(32) NULL,
            two_factor_enabled BOOLEAN DEFAULT FALSE
        )";
        
        $this->pdo->exec($sql);
        
        // 创建默认管理员账户
        $this->createDefaultAdmin();
    }
    
    /**
     * 创建默认管理员账户
     */
    private function createDefaultAdmin() {
        $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM users WHERE role = 'admin'");
        $stmt->execute();
        
        if ($stmt->fetchColumn() == 0) {
            $this->createUser('admin', '<EMAIL>', 'BitV2024!', 'admin');
            writeLog('INFO', '默认管理员账户已创建', ['username' => 'admin']);
        }
    }
    
    /**
     * 创建用户
     */
    public function createUser($username, $email, $password, $role = 'trader') {
        // 验证输入
        if (!$this->validateUsername($username)) {
            throw new Exception('用户名格式无效');
        }
        
        if (!validateEmail($email)) {
            throw new Exception('邮箱格式无效');
        }
        
        if (!$this->validatePassword($password)) {
            throw new Exception('密码强度不足');
        }
        
        // 检查用户名和邮箱是否已存在
        if ($this->userExists($username, $email)) {
            throw new Exception('用户名或邮箱已存在');
        }
        
        // 生成盐值和密码哈希
        $salt = bin2hex(random_bytes(16));
        $passwordHash = $this->hashPassword($password, $salt);
        
        $stmt = $this->pdo->prepare("
            INSERT INTO users (username, email, password_hash, salt, role) 
            VALUES (?, ?, ?, ?, ?)
        ");
        
        $result = $stmt->execute([$username, $email, $passwordHash, $salt, $role]);
        
        if ($result) {
            writeLog('INFO', '用户创建成功', ['username' => $username, 'role' => $role]);
            return $this->pdo->lastInsertId();
        }
        
        throw new Exception('用户创建失败');
    }
    
    /**
     * 用户登录
     */
    public function login($username, $password, $rememberMe = false) {
        // 检查登录尝试次数
        if ($this->isAccountLocked($username)) {
            throw new Exception('账户已被锁定，请稍后再试');
        }
        
        $user = $this->getUserByUsername($username);
        
        if (!$user) {
            $this->recordFailedLogin($username);
            throw new Exception('用户名或密码错误');
        }
        
        // 验证密码
        if (!$this->verifyPassword($password, $user['password_hash'], $user['salt'])) {
            $this->recordFailedLogin($username);
            throw new Exception('用户名或密码错误');
        }
        
        // 检查账户状态
        if ($user['status'] !== 'active') {
            throw new Exception('账户已被禁用');
        }
        
        // 登录成功
        $this->recordSuccessfulLogin($user['id']);
        $this->createSession($user, $rememberMe);
        
        writeLog('INFO', '用户登录成功', [
            'user_id' => $user['id'],
            'username' => $user['username'],
            'ip' => getClientIP()
        ]);
        
        return $user;
    }
    
    /**
     * 用户登出
     */
    public function logout() {
        if (isset($_SESSION['user_id'])) {
            writeLog('INFO', '用户登出', [
                'user_id' => $_SESSION['user_id'],
                'username' => $_SESSION['username'] ?? 'unknown'
            ]);
        }
        
        // 清除会话
        session_unset();
        session_destroy();
        
        // 清除记住我的Cookie
        if (isset($_COOKIE['remember_token'])) {
            setcookie('remember_token', '', time() - 3600, '/');
        }
    }
    
    /**
     * 检查用户是否已认证
     */
    public function isAuthenticated() {
        // 检查会话
        if (isset($_SESSION['user_id']) && isset($_SESSION['username'])) {
            // 验证会话有效性
            if ($this->validateSession()) {
                return true;
            }
        }
        
        // 检查记住我的Cookie
        if (isset($_COOKIE['remember_token'])) {
            return $this->validateRememberToken($_COOKIE['remember_token']);
        }
        
        return false;
    }
    
    /**
     * 获取当前用户信息
     */
    public function getCurrentUser() {
        if (!$this->isAuthenticated()) {
            return null;
        }
        
        return $this->getUserById($_SESSION['user_id']);
    }
    
    /**
     * 检查用户权限
     */
    public function hasPermission($permission) {
        $user = $this->getCurrentUser();
        if (!$user) {
            return false;
        }
        
        $permissions = [
            'admin' => ['view', 'trade', 'manage', 'admin'],
            'trader' => ['view', 'trade'],
            'viewer' => ['view']
        ];
        
        $userPermissions = $permissions[$user['role']] ?? [];
        return in_array($permission, $userPermissions);
    }
    
    /**
     * 验证用户名格式
     */
    private function validateUsername($username) {
        return preg_match('/^[a-zA-Z0-9_]{3,20}$/', $username);
    }
    
    /**
     * 验证密码强度
     */
    private function validatePassword($password) {
        // 至少8位，包含大小写字母、数字和特殊字符
        return strlen($password) >= 8 &&
               preg_match('/[a-z]/', $password) &&
               preg_match('/[A-Z]/', $password) &&
               preg_match('/[0-9]/', $password) &&
               preg_match('/[^a-zA-Z0-9]/', $password);
    }
    
    /**
     * 检查用户是否存在
     */
    private function userExists($username, $email) {
        $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM users WHERE username = ? OR email = ?");
        $stmt->execute([$username, $email]);
        return $stmt->fetchColumn() > 0;
    }
    
    /**
     * 生成密码哈希
     */
    private function hashPassword($password, $salt) {
        return hash('sha256', $password . $salt . SECURITY_SALT);
    }
    
    /**
     * 验证密码
     */
    private function verifyPassword($password, $hash, $salt) {
        return hash_equals($hash, $this->hashPassword($password, $salt));
    }
    
    /**
     * 根据用户名获取用户
     */
    private function getUserByUsername($username) {
        $stmt = $this->pdo->prepare("SELECT * FROM users WHERE username = ?");
        $stmt->execute([$username]);
        return $stmt->fetch();
    }
    
    /**
     * 根据ID获取用户
     */
    private function getUserById($id) {
        $stmt = $this->pdo->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$id]);
        return $stmt->fetch();
    }
    
    /**
     * 检查账户是否被锁定
     */
    private function isAccountLocked($username) {
        $stmt = $this->pdo->prepare("
            SELECT login_attempts, locked_until 
            FROM users 
            WHERE username = ?
        ");
        $stmt->execute([$username]);
        $user = $stmt->fetch();
        
        if (!$user) {
            return false;
        }
        
        // 检查是否超过最大尝试次数
        if ($user['login_attempts'] >= MAX_LOGIN_ATTEMPTS) {
            // 检查锁定时间是否已过
            if ($user['locked_until'] && new DateTime() < new DateTime($user['locked_until'])) {
                return true;
            }
            
            // 锁定时间已过，重置尝试次数
            $this->resetLoginAttempts($username);
        }
        
        return false;
    }
    
    /**
     * 记录失败的登录尝试
     */
    private function recordFailedLogin($username) {
        $stmt = $this->pdo->prepare("
            UPDATE users 
            SET login_attempts = login_attempts + 1,
                locked_until = CASE 
                    WHEN login_attempts + 1 >= ? THEN DATE_ADD(NOW(), INTERVAL ? SECOND)
                    ELSE locked_until 
                END
            WHERE username = ?
        ");
        
        $stmt->execute([MAX_LOGIN_ATTEMPTS, LOGIN_LOCKOUT_TIME, $username]);
        
        writeLog('WARNING', '登录失败', [
            'username' => $username,
            'ip' => getClientIP()
        ]);
    }
    
    /**
     * 记录成功的登录
     */
    private function recordSuccessfulLogin($userId) {
        $stmt = $this->pdo->prepare("
            UPDATE users 
            SET last_login = NOW(), 
                login_attempts = 0, 
                locked_until = NULL 
            WHERE id = ?
        ");
        
        $stmt->execute([$userId]);
    }
    
    /**
     * 重置登录尝试次数
     */
    private function resetLoginAttempts($username) {
        $stmt = $this->pdo->prepare("
            UPDATE users 
            SET login_attempts = 0, locked_until = NULL 
            WHERE username = ?
        ");
        
        $stmt->execute([$username]);
    }
    
    /**
     * 创建用户会话
     */
    private function createSession($user, $rememberMe = false) {
        session_regenerate_id(true);
        
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['role'] = $user['role'];
        $_SESSION['login_time'] = time();
        $_SESSION['last_activity'] = time();
        
        if ($rememberMe) {
            $token = bin2hex(random_bytes(32));
            $expires = time() + (30 * 24 * 60 * 60); // 30天
            
            setcookie('remember_token', $token, $expires, '/', '', true, true);
            
            // 存储记住我的令牌到数据库
            $this->storeRememberToken($user['id'], $token, $expires);
        }
    }
    
    /**
     * 验证会话有效性
     */
    private function validateSession() {
        // 检查会话超时
        if (isset($_SESSION['last_activity'])) {
            if (time() - $_SESSION['last_activity'] > SESSION_TIMEOUT) {
                $this->logout();
                return false;
            }
            $_SESSION['last_activity'] = time();
        }
        
        return true;
    }
    
    /**
     * 存储记住我的令牌
     */
    private function storeRememberToken($userId, $token, $expires) {
        // 创建记住我的令牌表
        $this->pdo->exec("
            CREATE TABLE IF NOT EXISTS remember_tokens (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                token VARCHAR(64) NOT NULL,
                expires_at DATETIME NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            )
        ");
        
        $stmt = $this->pdo->prepare("
            INSERT INTO remember_tokens (user_id, token, expires_at) 
            VALUES (?, ?, FROM_UNIXTIME(?))
        ");
        
        $stmt->execute([$userId, hash('sha256', $token), $expires]);
    }
    
    /**
     * 验证记住我的令牌
     */
    private function validateRememberToken($token) {
        $hashedToken = hash('sha256', $token);
        
        $stmt = $this->pdo->prepare("
            SELECT u.* FROM users u
            JOIN remember_tokens rt ON u.id = rt.user_id
            WHERE rt.token = ? AND rt.expires_at > NOW() AND u.status = 'active'
        ");
        
        $stmt->execute([$hashedToken]);
        $user = $stmt->fetch();
        
        if ($user) {
            $this->createSession($user, false);
            return true;
        }
        
        return false;
    }
}

// 全局认证实例
$userAuth = new UserAuth();

/**
 * 检查用户是否已认证
 */
function isAuthenticated() {
    global $userAuth;
    return $userAuth->isAuthenticated();
}

/**
 * 获取当前用户
 */
function getCurrentUser() {
    global $userAuth;
    return $userAuth->getCurrentUser();
}

/**
 * 检查用户权限
 */
function hasPermission($permission) {
    global $userAuth;
    return $userAuth->hasPermission($permission);
}

/**
 * 要求用户认证
 */
function requireAuth() {
    if (!isAuthenticated()) {
        if (isAjaxRequest()) {
            jsonResponse(['error' => '需要登录'], 401);
        } else {
            redirect('login.php');
        }
    }
}

/**
 * 要求特定权限
 */
function requirePermission($permission) {
    requireAuth();
    
    if (!hasPermission($permission)) {
        if (isAjaxRequest()) {
            jsonResponse(['error' => '权限不足'], 403);
        } else {
            redirect('403.php');
        }
    }
}

/**
 * 安全的文件上传
 */
function secureFileUpload($file, $allowedTypes = [], $maxSize = 5242880) { // 5MB
    if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
        throw new Exception('无效的文件上传');
    }
    
    // 检查文件大小
    if ($file['size'] > $maxSize) {
        throw new Exception('文件大小超过限制');
    }
    
    // 检查文件类型
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mimeType = finfo_file($finfo, $file['tmp_name']);
    finfo_close($finfo);
    
    if (!empty($allowedTypes) && !in_array($mimeType, $allowedTypes)) {
        throw new Exception('不允许的文件类型');
    }
    
    // 生成安全的文件名
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = bin2hex(random_bytes(16)) . '.' . $extension;
    $destination = UPLOADS_PATH . '/' . $filename;
    
    if (!move_uploaded_file($file['tmp_name'], $destination)) {
        throw new Exception('文件保存失败');
    }
    
    return $filename;
}

/**
 * 防止XSS攻击
 */
function xssProtect($input) {
    return htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
}

/**
 * 防止SQL注入（使用PDO预处理语句）
 */
function sqlProtect($input) {
    // 这个函数主要是提醒使用PDO预处理语句
    // 实际的SQL保护应该通过PDO的prepare和execute来实现
    return trim($input);
}

/**
 * 生成安全的随机令牌
 */
function generateSecureToken($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

/**
 * 验证请求来源
 */
function validateReferer($allowedDomains = []) {
    if (empty($_SERVER['HTTP_REFERER'])) {
        return false;
    }
    
    $refererHost = parse_url($_SERVER['HTTP_REFERER'], PHP_URL_HOST);
    $currentHost = $_SERVER['HTTP_HOST'];
    
    if ($refererHost === $currentHost) {
        return true;
    }
    
    return in_array($refererHost, $allowedDomains);
}

/**
 * 限制请求频率
 */
function rateLimitCheck($key, $maxRequests = 60, $timeWindow = 60) {
    $cacheFile = CACHE_PATH . '/rate_limit_' . md5($key) . '.json';
    
    $now = time();
    $requests = [];
    
    if (file_exists($cacheFile)) {
        $data = json_decode(file_get_contents($cacheFile), true);
        $requests = $data['requests'] ?? [];
    }
    
    // 清理过期的请求记录
    $requests = array_filter($requests, function($timestamp) use ($now, $timeWindow) {
        return ($now - $timestamp) < $timeWindow;
    });
    
    // 检查是否超过限制
    if (count($requests) >= $maxRequests) {
        return false;
    }
    
    // 记录当前请求
    $requests[] = $now;
    
    // 保存到缓存
    file_put_contents($cacheFile, json_encode(['requests' => $requests]));
    
    return true;
}

?>
