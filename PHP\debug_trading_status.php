<?php
/**
 * 调试交易状态数据结构
 */

require_once 'config/config.php';
require_once 'includes/ApiClient.php';

// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 交易状态数据结构调试</h1>";

try {
    $api = ApiClient::getInstance();
    
    echo "<h2>1. 原始API响应</h2>";
    $rawResponse = $api->get('/api/trading/status');
    echo "<pre>" . htmlspecialchars(json_encode($rawResponse, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . "</pre>";
    
    echo "<h2>2. 处理后的数据</h2>";
    if ($rawResponse['success']) {
        $tradingStatus = $rawResponse['data'];
        echo "<p><strong>数据类型:</strong> " . gettype($tradingStatus) . "</p>";
        echo "<p><strong>数据内容:</strong></p>";
        echo "<pre>" . htmlspecialchars(json_encode($tradingStatus, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . "</pre>";
        
        echo "<h3>字段检查:</h3>";
        $fields = ['is_running', 'trading_state', 'current_session', 'active_sessions', 'total_sessions', 'emergency_stop', 'exchange_connected'];
        
        foreach ($fields as $field) {
            $exists = isset($tradingStatus[$field]);
            $value = $exists ? $tradingStatus[$field] : 'N/A';
            echo "<p><strong>{$field}:</strong> " . ($exists ? "✅" : "❌") . " - " . htmlspecialchars(json_encode($value)) . "</p>";
        }
        
        echo "<h3>TRADING_STATES 映射测试:</h3>";
        if (isset($tradingStatus['trading_state'])) {
            $state = $tradingStatus['trading_state'];
            echo "<p><strong>原始状态:</strong> " . htmlspecialchars($state) . "</p>";
            echo "<p><strong>映射结果:</strong> " . ($GLOBALS['TRADING_STATES'][$state] ?? '未找到映射') . "</p>";
        } else {
            echo "<p>❌ trading_state 字段不存在</p>";
        }
        
        echo "<h3>模板代码模拟:</h3>";
        echo "<div style='background: #f0f0f0; padding: 15px; border: 1px solid #ddd;'>";
        if (isset($tradingStatus['trading_state'])) {
            $state = $tradingStatus['trading_state'];
            $displayState = isset($GLOBALS['TRADING_STATES'][$state]) ? $GLOBALS['TRADING_STATES'][$state] : $state;
            echo "<p>✅ 模板显示: <strong>" . htmlspecialchars($displayState) . "</strong></p>";
        } else {
            echo "<p>❌ 模板会出错: trading_state 字段不存在</p>";
        }
        echo "</div>";
        
    } else {
        echo "<p>❌ API调用失败</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ 异常: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>3. TRADING_STATES 配置</h2>";
echo "<pre>" . htmlspecialchars(json_encode($GLOBALS['TRADING_STATES'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . "</pre>";

echo "<hr>";
echo "<p><a href='index.php'>返回首页</a></p>";
?>
