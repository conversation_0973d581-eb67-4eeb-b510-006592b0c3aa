#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BitV 交易系统 - Windows编码修复测试
"""

import sys
import os
import logging
from datetime import datetime

# Windows编码修复
if sys.platform == 'win32':
    # 设置控制台编码
    os.system('chcp 65001 >nul')
    # 重新配置标准输出
    try:
        sys.stdout.reconfigure(encoding='utf-8')
        sys.stderr.reconfigure(encoding='utf-8')
    except AttributeError:
        # Python 3.6及以下版本的兼容性处理
        import io
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

# 创建日志目录
os.makedirs('logs', exist_ok=True)

# 配置日志系统 - Windows兼容
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)s | %(message)s',
    handlers=[
        logging.FileHandler('logs/test_windows.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def test_chinese_output():
    """测试中文输出"""
    print("=" * 60)
    print("[测试] BitV MACD智能加仓交易系统 - Windows兼容性测试")
    print("=" * 60)
    
    # 测试各种中文字符
    test_messages = [
        "[启动] 系统正在启动...",
        "[OK] 连接成功",
        "[ERROR] 连接失败",
        "[警告] 这是一个警告消息",
        "[完成] 测试完成",
        "[交易] 真实订单创建成功",
        "[平仓] 持仓已平仓",
        "[风险] 风险等级: 安全",
        "[配置] 交易所配置成功",
        "[监控] 实时监控运行中"
    ]
    
    for i, message in enumerate(test_messages, 1):
        print(f"{i:2d}. {message}")
        logger.info(f"测试消息 {i}: {message}")
    
    print("=" * 60)
    print("[结果] 如果您能看到上述中文消息，说明编码修复成功！")
    print("[时间] 当前时间:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    print("=" * 60)

def test_logging():
    """测试日志记录"""
    logger.info("[测试] 开始日志记录测试")
    logger.warning("[警告] 这是一个警告日志")
    logger.error("[错误] 这是一个错误日志")
    logger.info("[完成] 日志记录测试完成")

def test_file_operations():
    """测试文件操作"""
    try:
        test_file = 'logs/chinese_test.txt'
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write("BitV MACD智能加仓交易系统\n")
            f.write("中文编码测试文件\n")
            f.write(f"创建时间: {datetime.now()}\n")
        
        print(f"[文件] 测试文件已创建: {test_file}")
        
        # 读取文件验证
        with open(test_file, 'r', encoding='utf-8') as f:
            content = f.read()
            print(f"[读取] 文件内容读取成功，长度: {len(content)} 字符")
            
    except Exception as e:
        print(f"[错误] 文件操作失败: {e}")

def main():
    """主函数"""
    print("\n" + "=" * 80)
    print("BitV MACD智能加仓交易系统 - Windows编码兼容性测试")
    print("=" * 80)
    
    print(f"Python版本: {sys.version}")
    print(f"操作系统: {sys.platform}")
    print(f"默认编码: {sys.getdefaultencoding()}")
    print(f"文件系统编码: {sys.getfilesystemencoding()}")
    
    try:
        print(f"标准输出编码: {sys.stdout.encoding}")
        print(f"标准错误编码: {sys.stderr.encoding}")
    except:
        print("无法获取标准输出编码信息")
    
    print("=" * 80)
    
    # 执行测试
    test_chinese_output()
    test_logging()
    test_file_operations()
    
    print("\n[总结] Windows编码兼容性测试完成！")
    print("[说明] 如果所有中文都能正常显示，说明修复成功。")
    print("[下一步] 可以安全使用 real_trading_system_windows.py")
    
    # 等待用户确认
    try:
        input("\n按回车键退出...")
    except:
        pass

if __name__ == "__main__":
    main()
