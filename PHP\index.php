<?php
/**
 * MACD智能加仓交易系统 - PHP前端
 * 主入口文件
 * 
 * <AUTHOR> Trading System
 * @version 1.0.0
 * @description 基于Python后端的现代化Web交易界面
 */

// 错误报告设置
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 定义常量
define('ROOT_PATH', __DIR__);
define('CONFIG_PATH', ROOT_PATH . '/config');
define('INCLUDES_PATH', ROOT_PATH . '/includes');
define('ASSETS_PATH', ROOT_PATH . '/assets');
define('TEMPLATES_PATH', ROOT_PATH . '/templates');
define('API_PATH', ROOT_PATH . '/api');

// 自动加载器
spl_autoload_register(function ($class) {
    $file = INCLUDES_PATH . '/' . str_replace('\\', '/', $class) . '.php';
    if (file_exists($file)) {
        require_once $file;
    }
});

// 加载核心配置
require_once CONFIG_PATH . '/config.php';
require_once INCLUDES_PATH . '/Database.php';
require_once INCLUDES_PATH . '/ApiClient.php';
require_once INCLUDES_PATH . '/Session.php';
require_once INCLUDES_PATH . '/Router.php';

// 启动会话
Session::start();

// 创建路由器实例
$router = new Router();

// 定义路由
$router->get('/', 'HomeController@index');
$router->get('/dashboard', 'DashboardController@index');
$router->get('/config', 'ConfigController@index');
$router->post('/config/save', 'ConfigController@save');
$router->get('/trading', 'TradingController@index');
$router->post('/trading/start', 'TradingController@start');
$router->post('/trading/stop', 'TradingController@stop');
$router->get('/monitoring', 'MonitoringController@index');
$router->get('/history', 'HistoryController@index');
$router->get('/api/status', 'ApiController@status');
$router->get('/api/positions', 'ApiController@positions');
$router->get('/api/prices', 'ApiController@prices');
$router->post('/api/config/test', 'ApiController@testConnection');

// 处理请求
try {
    $router->dispatch();
} catch (Exception $e) {
    // 错误处理
    http_response_code(500);
    if (DEBUG_MODE) {
        echo "<h1>系统错误</h1>";
        echo "<p>" . $e->getMessage() . "</p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    } else {
        include TEMPLATES_PATH . '/error.php';
    }
}
?>
