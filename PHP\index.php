<?php
/**
 * MACD智能加仓交易系统 - PHP前端
 * 主入口文件
 * 
 * <AUTHOR> Trading System
 * @version 1.0.0
 * @description 基于Python后端的现代化Web交易界面
 */

// 错误报告设置
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 定义常量
define('ROOT_PATH', __DIR__);
define('CONFIG_PATH', ROOT_PATH . '/config');
define('INCLUDES_PATH', ROOT_PATH . '/includes');
define('ASSETS_PATH', ROOT_PATH . '/assets');
define('TEMPLATES_PATH', ROOT_PATH . '/templates');
define('API_PATH', ROOT_PATH . '/api');

// 自动加载器
spl_autoload_register(function ($class) {
    $file = INCLUDES_PATH . '/' . str_replace('\\', '/', $class) . '.php';
    if (file_exists($file)) {
        require_once $file;
    }
});

// 加载核心配置
require_once CONFIG_PATH . '/config.php';
require_once INCLUDES_PATH . '/Database.php';
require_once INCLUDES_PATH . '/ApiClient.php';
require_once INCLUDES_PATH . '/Session.php';
require_once INCLUDES_PATH . '/Router.php';

// 启动会话
Session::start();

// 创建路由器实例
$router = new Router();

// 定义路由
$router->get('/', 'HomeController@index');
$router->get('/about', 'HomeController@about');
$router->get('/help', 'HomeController@help');
$router->get('/health', 'HomeController@health');

// 仪表板路由
$router->get('/dashboard', 'DashboardController@index');
$router->get('/dashboard/real-time-data', 'DashboardController@getRealTimeData');
$router->get('/dashboard/price-data', 'DashboardController@getPriceData');
$router->get('/dashboard/macd-data', 'DashboardController@getMACDData');
$router->get('/dashboard/bollinger-data', 'DashboardController@getBollingerData');
$router->get('/dashboard/position-risk', 'DashboardController@getPositionRisk');
$router->get('/dashboard/performance-stats', 'DashboardController@getPerformanceStats');

// 配置管理路由
$router->get('/config', 'ConfigController@index');
$router->post('/config/save', 'ConfigController@save');
$router->post('/config/test', 'ConfigController@testConnection');
$router->get('/config/symbols', 'ConfigController@getSymbols');
$router->post('/config/reset', 'ConfigController@reset');
$router->get('/config/export', 'ConfigController@export');
$router->post('/config/import', 'ConfigController@import');
$router->get('/config/strategy-form', 'ConfigController@getStrategyForm');
$router->get('/config/risk-config', 'ConfigController@getRiskConfig');
$router->post('/config/risk-config/save', 'ConfigController@saveRiskConfig');

// 交易控制路由
$router->get('/trading', 'TradingController@index');
$router->post('/trading/start', 'TradingController@start');
$router->post('/trading/stop', 'TradingController@stop');
$router->post('/trading/emergency-stop', 'TradingController@emergencyStop');
$router->post('/trading/close-position', 'TradingController@closePosition');
$router->get('/trading/status', 'TradingController@getStatus');
$router->post('/trading/update-config', 'TradingController@updateConfig');
$router->get('/trading/active-sessions', 'TradingController@getActiveSessions');
$router->get('/trading/session-details', 'TradingController@getSessionDetails');

// 历史数据路由
$router->get('/history', 'HistoryController@index');
$router->get('/history/data', 'HistoryController@getHistoryData');
$router->get('/history/pnl-analysis', 'HistoryController@getPnlAnalysis');
$router->get('/history/strategy-performance', 'HistoryController@getStrategyPerformance');
$router->get('/history/export', 'HistoryController@exportTrades');

// API状态路由
$router->get('/api/status', 'HomeController@apiStatus');

// 处理请求
try {
    $router->dispatch();
} catch (Exception $e) {
    // 错误处理
    http_response_code(500);
    if (DEBUG_MODE) {
        echo "<h1>系统错误</h1>";
        echo "<p>" . $e->getMessage() . "</p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    } else {
        include TEMPLATES_PATH . '/error.php';
    }
}
?>
