"""
核心模块单元测试
测试交易控制器、状态管理等核心功能
"""

import unittest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
import logging

from tests import TEST_CONFIG, get_event_loop
from core.trading_controller import AsyncTradingController, TradingState, TradingSession
from config import TradingConfig

logger = logging.getLogger(__name__)

class TestTradingController(unittest.TestCase):
    """交易控制器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.loop = get_event_loop()
        self.config = TradingConfig()
        # 使用测试配置
        for key, value in TEST_CONFIG.items():
            setattr(self.config, key.upper(), value)
        
        self.controller = AsyncTradingController(self.config)
    
    def tearDown(self):
        """测试后清理"""
        if hasattr(self, 'controller'):
            self.loop.run_until_complete(self.controller.close())
    
    def test_controller_initialization(self):
        """测试控制器初始化"""
        self.assertIsNotNone(self.controller)
        self.assertEqual(self.controller.trading_state, TradingState.IDLE)
        self.assertIsNone(self.controller.current_session)
        self.assertFalse(self.controller._is_running)
    
    def test_state_management(self):
        """测试状态管理"""
        async def test_state_update():
            # 测试状态更新
            await self.controller.update_trading_state(TradingState.ANALYZING)
            self.assertEqual(self.controller.trading_state, TradingState.ANALYZING)
            
            # 测试状态锁
            self.assertIsNotNone(self.controller._state_lock)
        
        self.loop.run_until_complete(test_state_update())
    
    @patch('core.trading_controller.ExchangeFactory.create_exchange')
    def test_initialization_with_mock_exchange(self, mock_create_exchange):
        """测试使用模拟交易所的初始化"""
        async def test_init():
            # 模拟交易所
            mock_exchange = AsyncMock()
            mock_exchange.connect.return_value = True
            mock_create_exchange.return_value = mock_exchange
            
            # 测试初始化
            result = await self.controller.initialize()
            self.assertTrue(result)
            self.assertIsNotNone(self.controller.exchange)
        
        self.loop.run_until_complete(test_init())
    
    def test_trading_session_creation(self):
        """测试交易会话创建"""
        session = TradingSession(
            session_id="test_session",
            symbol="BTC-USDT",
            initial_margin=100.0,
            leverage=10,
            start_time=1234567890
        )
        
        self.assertEqual(session.session_id, "test_session")
        self.assertEqual(session.symbol, "BTC-USDT")
        self.assertEqual(session.initial_margin, 100.0)
        self.assertEqual(session.leverage, 10)
        self.assertEqual(session.total_pnl, 0.0)
    
    @patch('core.trading_controller.AsyncOpeningStrategy')
    @patch('core.trading_controller.AsyncAddPositionStrategy')
    @patch('core.trading_controller.AsyncPositionMonitor')
    @patch('core.trading_controller.AsyncPriceMonitor')
    def test_component_initialization(self, mock_price_monitor, mock_position_monitor, 
                                    mock_add_strategy, mock_opening_strategy):
        """测试组件初始化"""
        async def test_components():
            # 模拟组件
            mock_opening_strategy.return_value = AsyncMock()
            mock_add_strategy.return_value = AsyncMock()
            mock_position_monitor.return_value = AsyncMock()
            mock_price_monitor.return_value = AsyncMock()
            
            # 模拟交易所
            self.controller.exchange = AsyncMock()
            
            # 测试组件初始化
            await self.controller._initialize_components()
            
            self.assertIsNotNone(self.controller.opening_strategy)
            self.assertIsNotNone(self.controller.add_position_strategy)
            self.assertIsNotNone(self.controller.position_monitor)
            self.assertIsNotNone(self.controller.price_monitor)
        
        self.loop.run_until_complete(test_components())

class TestTradingState(unittest.TestCase):
    """交易状态测试"""
    
    def test_trading_state_enum(self):
        """测试交易状态枚举"""
        self.assertEqual(TradingState.IDLE.value, "idle")
        self.assertEqual(TradingState.ANALYZING.value, "analyzing")
        self.assertEqual(TradingState.OPENING.value, "opening")
        self.assertEqual(TradingState.MONITORING.value, "monitoring")
        self.assertEqual(TradingState.ERROR.value, "error")

if __name__ == '__main__':
    unittest.main()
