#!/usr/bin/env python3
"""
最简单的HTTP服务器 - 用于测试PHP连接
只使用Python标准库，无需额外安装包
"""

import http.server
import socketserver
import json
import urllib.parse
from datetime import datetime
import threading
import time

class BitVHandler(http.server.BaseHTTPRequestHandler):
    """自定义请求处理器"""
    
    def do_GET(self):
        """处理GET请求"""
        path = urllib.parse.urlparse(self.path).path
        
        # 路由处理
        if path == '/':
            self.send_json_response({
                "message": "BitV MACD智能加仓交易系统 API",
                "version": "1.0.0",
                "status": "running",
                "timestamp": datetime.now().isoformat()
            })
        elif path == '/health':
            self.send_json_response({
                "status": "healthy",
                "timestamp": datetime.now().isoformat()
            })
        elif path == '/api/status':
            self.send_json_response({
                "success": True,
                "data": {
                    "system_status": "running",
                    "trading_engine_status": "simulated",
                    "timestamp": datetime.now().isoformat()
                }
            })
        elif path == '/api/trading/status':
            self.send_json_response({
                "success": True,
                "data": {
                    "is_running": False,
                    "trading_state": "idle",
                    "current_session": None,
                    "message": "模拟模式运行中"
                }
            })
        else:
            self.send_json_response({
                "error": "Not Found",
                "path": path
            }, 404)
    
    def do_POST(self):
        """处理POST请求"""
        path = urllib.parse.urlparse(self.path).path
        
        if path == '/api/trading/start':
            self.send_json_response({
                "success": True,
                "data": {
                    "message": "交易启动请求已接收（模拟模式）",
                    "session_id": f"sim_{int(time.time())}"
                }
            })
        elif path == '/api/trading/stop':
            self.send_json_response({
                "success": True,
                "data": {
                    "message": "交易停止请求已接收（模拟模式）"
                }
            })
        else:
            self.send_json_response({
                "error": "Not Found",
                "path": path
            }, 404)
    
    def send_json_response(self, data, status_code=200):
        """发送JSON响应"""
        self.send_response(status_code)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        
        json_data = json.dumps(data, ensure_ascii=False, indent=2)
        self.wfile.write(json_data.encode('utf-8'))
    
    def do_OPTIONS(self):
        """处理OPTIONS请求（CORS预检）"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] {format % args}")

def start_server(port=8000):
    """启动服务器"""
    try:
        with socketserver.TCPServer(("", port), BitVHandler) as httpd:
            print("🚀 BitV简易API服务器启动成功!")
            print("=" * 50)
            print(f"📊 服务信息:")
            print(f"   - 监听端口: {port}")
            print(f"   - API地址: http://localhost:{port}")
            print(f"   - 健康检查: http://localhost:{port}/health")
            print(f"   - 系统状态: http://localhost:{port}/api/status")
            print("=" * 50)
            print("💡 这是一个简化的测试服务器")
            print("🔧 按 Ctrl+C 停止服务器")
            print("=" * 50)
            
            httpd.serve_forever()
            
    except OSError as e:
        if "Address already in use" in str(e):
            print(f"❌ 端口 {port} 已被占用!")
            print("💡 请检查是否有其他程序在使用此端口")
            print("🔧 或者尝试使用其他端口:")
            print(f"   python simple_server.py 8001")
        else:
            print(f"❌ 启动失败: {e}")
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")

if __name__ == "__main__":
    import sys
    
    # 检查是否指定了端口
    port = 8000
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print("❌ 端口号必须是数字")
            sys.exit(1)
    
    start_server(port)
