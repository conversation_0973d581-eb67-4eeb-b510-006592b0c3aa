"""
性能监控模块
收集和分析系统性能指标
"""

import time
import asyncio
import threading

# 尝试导入psutil，如果失败则使用模拟实现
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    # 创建模拟的psutil模块
    class MockPsutil:
        @staticmethod
        def cpu_percent(interval=None):
            return 50.0  # 模拟50% CPU使用率

        @staticmethod
        def virtual_memory():
            class MockMemory:
                percent = 60.0
                used = 8 * 1024 * 1024 * 1024  # 8GB
                available = 8 * 1024 * 1024 * 1024  # 8GB
            return MockMemory()

        @staticmethod
        def disk_usage(path):
            class MockDisk:
                percent = 70.0
            return MockDisk()

        @staticmethod
        def net_io_counters():
            class MockNetwork:
                bytes_sent = 1024 * 1024 * 100  # 100MB
                bytes_recv = 1024 * 1024 * 200  # 200MB
            return MockNetwork()

    psutil = MockPsutil()
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
from collections import deque
import logging
import json
from datetime import datetime

logger = logging.getLogger(__name__)

class MetricType(Enum):
    """指标类型"""
    COUNTER = "counter"          # 计数器
    GAUGE = "gauge"             # 仪表盘
    HISTOGRAM = "histogram"      # 直方图
    TIMER = "timer"             # 计时器

@dataclass
class PerformanceMetric:
    """性能指标"""
    name: str
    type: MetricType
    value: float
    timestamp: float
    tags: Dict[str, str] = field(default_factory=dict)
    description: str = ""

@dataclass
class SystemMetrics:
    """系统指标"""
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    memory_available_mb: float
    disk_usage_percent: float
    network_sent_mb: float
    network_recv_mb: float
    timestamp: float

@dataclass
class TradingMetrics:
    """交易指标"""
    orders_per_second: float
    api_response_time_ms: float
    strategy_execution_time_ms: float
    position_updates_per_second: float
    error_rate_percent: float
    timestamp: float

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, collection_interval: float = 5.0, max_history: int = 1000):
        """
        初始化性能监控器
        
        Args:
            collection_interval: 收集间隔（秒）
            max_history: 最大历史记录数
        """
        self.collection_interval = collection_interval
        self.max_history = max_history
        
        # 指标存储
        self.metrics: Dict[str, deque] = {}
        self.system_metrics: deque = deque(maxlen=max_history)
        self.trading_metrics: deque = deque(maxlen=max_history)
        
        # 计数器
        self.counters: Dict[str, float] = {}
        self.timers: Dict[str, List[float]] = {}
        
        # 监控状态
        self.is_monitoring = False
        self.monitor_task: Optional[asyncio.Task] = None
        self.lock = threading.Lock()
        
        # 回调函数
        self.alert_callbacks: List[Callable] = []
        
        # 性能阈值
        self.thresholds = {
            'cpu_percent': 80.0,
            'memory_percent': 85.0,
            'api_response_time_ms': 1000.0,
            'error_rate_percent': 5.0
        }
        
        if not PSUTIL_AVAILABLE:
            logger.warning("psutil模块未安装，使用模拟数据。建议安装: pip install psutil")

        logger.info("性能监控器初始化完成")
    
    def add_metric(self, name: str, type: MetricType, value: float, 
                   tags: Dict[str, str] = None, description: str = ""):
        """添加性能指标"""
        with self.lock:
            metric = PerformanceMetric(
                name=name,
                type=type,
                value=value,
                timestamp=time.time(),
                tags=tags or {},
                description=description
            )
            
            if name not in self.metrics:
                self.metrics[name] = deque(maxlen=self.max_history)
            
            self.metrics[name].append(metric)
    
    def increment_counter(self, name: str, value: float = 1.0, tags: Dict[str, str] = None):
        """增加计数器"""
        with self.lock:
            if name not in self.counters:
                self.counters[name] = 0.0
            self.counters[name] += value
            
            self.add_metric(name, MetricType.COUNTER, self.counters[name], tags)
    
    def set_gauge(self, name: str, value: float, tags: Dict[str, str] = None):
        """设置仪表盘值"""
        self.add_metric(name, MetricType.GAUGE, value, tags)
    
    def record_timer(self, name: str, duration_ms: float, tags: Dict[str, str] = None):
        """记录计时器"""
        with self.lock:
            if name not in self.timers:
                self.timers[name] = []
            self.timers[name].append(duration_ms)
            
            # 只保留最近的100个记录
            if len(self.timers[name]) > 100:
                self.timers[name] = self.timers[name][-100:]
            
            self.add_metric(name, MetricType.TIMER, duration_ms, tags)
    
    def timer_context(self, name: str, tags: Dict[str, str] = None):
        """计时器上下文管理器"""
        return TimerContext(self, name, tags)
    
    def collect_system_metrics(self) -> SystemMetrics:
        """收集系统指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=0.1)
            
            # 内存使用情况
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_used_mb = memory.used / 1024 / 1024
            memory_available_mb = memory.available / 1024 / 1024
            
            # 磁盘使用情况
            disk = psutil.disk_usage('/')
            disk_usage_percent = disk.percent
            
            # 网络使用情况
            network = psutil.net_io_counters()
            network_sent_mb = network.bytes_sent / 1024 / 1024
            network_recv_mb = network.bytes_recv / 1024 / 1024
            
            metrics = SystemMetrics(
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                memory_used_mb=memory_used_mb,
                memory_available_mb=memory_available_mb,
                disk_usage_percent=disk_usage_percent,
                network_sent_mb=network_sent_mb,
                network_recv_mb=network_recv_mb,
                timestamp=time.time()
            )
            
            self.system_metrics.append(metrics)
            
            # 检查阈值
            self._check_thresholds(metrics)
            
            return metrics
            
        except Exception as e:
            logger.error(f"收集系统指标失败: {e}")
            return None
    
    def _check_thresholds(self, metrics: SystemMetrics):
        """检查性能阈值"""
        alerts = []
        
        if metrics.cpu_percent > self.thresholds['cpu_percent']:
            alerts.append(f"CPU使用率过高: {metrics.cpu_percent:.1f}%")
        
        if metrics.memory_percent > self.thresholds['memory_percent']:
            alerts.append(f"内存使用率过高: {metrics.memory_percent:.1f}%")
        
        # 触发告警回调
        for alert in alerts:
            logger.warning(f"性能告警: {alert}")
            for callback in self.alert_callbacks:
                try:
                    callback(alert)
                except Exception as e:
                    logger.error(f"告警回调执行失败: {e}")
    
    async def start_monitoring(self):
        """开始监控"""
        if self.is_monitoring:
            logger.warning("性能监控已在运行")
            return
        
        self.is_monitoring = True
        self.monitor_task = asyncio.create_task(self._monitoring_loop())
        logger.info("性能监控已启动")
    
    async def stop_monitoring(self):
        """停止监控"""
        if not self.is_monitoring:
            return
        
        self.is_monitoring = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        
        logger.info("性能监控已停止")
    
    async def _monitoring_loop(self):
        """监控循环"""
        try:
            while self.is_monitoring:
                # 收集系统指标
                self.collect_system_metrics()
                
                # 等待下次收集
                await asyncio.sleep(self.collection_interval)
                
        except asyncio.CancelledError:
            logger.info("性能监控循环被取消")
        except Exception as e:
            logger.error(f"性能监控循环异常: {e}")
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """获取指标摘要"""
        summary = {
            'timestamp': time.time(),
            'system': {},
            'trading': {},
            'counters': self.counters.copy(),
            'timers': {}
        }
        
        # 系统指标摘要
        if self.system_metrics:
            latest = self.system_metrics[-1]
            summary['system'] = {
                'cpu_percent': latest.cpu_percent,
                'memory_percent': latest.memory_percent,
                'memory_used_mb': latest.memory_used_mb,
                'disk_usage_percent': latest.disk_usage_percent
            }
        
        # 计时器统计
        for name, times in self.timers.items():
            if times:
                summary['timers'][name] = {
                    'count': len(times),
                    'avg_ms': sum(times) / len(times),
                    'min_ms': min(times),
                    'max_ms': max(times)
                }
        
        return summary
    
    def export_metrics(self, format: str = 'json') -> str:
        """导出指标数据"""
        summary = self.get_metrics_summary()
        
        if format == 'json':
            return json.dumps(summary, indent=2, ensure_ascii=False)
        else:
            raise ValueError(f"不支持的导出格式: {format}")
    
    def add_alert_callback(self, callback: Callable):
        """添加告警回调"""
        self.alert_callbacks.append(callback)

class TimerContext:
    """计时器上下文管理器"""
    
    def __init__(self, monitor: PerformanceMonitor, name: str, tags: Dict[str, str] = None):
        self.monitor = monitor
        self.name = name
        self.tags = tags
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time:
            duration_ms = (time.time() - self.start_time) * 1000
            self.monitor.record_timer(self.name, duration_ms, self.tags)

# 全局性能监控器实例
performance_monitor = PerformanceMonitor()

def monitor_performance(name: str, tags: Dict[str, str] = None):
    """性能监控装饰器"""
    def decorator(func):
        if asyncio.iscoroutinefunction(func):
            async def async_wrapper(*args, **kwargs):
                with performance_monitor.timer_context(name, tags):
                    return await func(*args, **kwargs)
            return async_wrapper
        else:
            def sync_wrapper(*args, **kwargs):
                with performance_monitor.timer_context(name, tags):
                    return func(*args, **kwargs)
            return sync_wrapper
    return decorator
