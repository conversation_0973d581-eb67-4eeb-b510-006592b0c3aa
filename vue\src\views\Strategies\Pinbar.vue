<template>
  <div class="pinbar-strategy">
    <div class="page-header">
      <h2>插针策略配置</h2>
      <p>配置插针(Pin Bar)形态识别参数和交易策略</p>
    </div>

    <div class="card">
      <h3 class="mb-md">插针策略开发中...</h3>
      <el-empty description="插针策略配置页面正在开发中">
        <template #image>
          <el-icon size="60" color="#c0c4cc"><Position /></el-icon>
        </template>
      </el-empty>
    </div>
  </div>
</template>

<script setup>
import { Position } from '@element-plus/icons-vue'
</script>

<style lang="scss" scoped>
.pinbar-strategy {
  .page-header {
    margin-bottom: var(--spacing-lg);
    
    h2 {
      margin-bottom: var(--spacing-xs);
      color: var(--text-color-primary);
    }
    
    p {
      color: var(--text-color-secondary);
    }
  }
}
</style>
