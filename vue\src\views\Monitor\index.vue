<template>
  <div class="monitor-page">
    <div class="page-header">
      <h2>实时监控</h2>
      <p>实时监控持仓状态、风险指标和交易信号</p>
    </div>

    <div class="card">
      <h3 class="mb-md">实时监控开发中...</h3>
      <el-empty description="实时监控页面正在开发中">
        <template #image>
          <el-icon size="60" color="#c0c4cc"><View /></el-icon>
        </template>
      </el-empty>
    </div>
  </div>
</template>

<script setup>
import { View } from '@element-plus/icons-vue'
</script>

<style lang="scss" scoped>
.monitor-page {
  .page-header {
    margin-bottom: var(--spacing-lg);
    
    h2 {
      margin-bottom: var(--spacing-xs);
      color: var(--text-color-primary);
    }
    
    p {
      color: var(--text-color-secondary);
    }
  }
}
</style>
