<template>
  <div class="strategy-page">
    <div class="page-header">
      <h2>基础策略配置</h2>
      <p>配置通用的交易策略参数</p>
    </div>

    <div class="card">
      <h3 class="mb-md">策略开发中...</h3>
      <el-empty description="基础策略配置页面正在开发中" />
    </div>
  </div>
</template>

<script setup>
// 基础策略配置页面
</script>

<style lang="scss" scoped>
.strategy-page {
  .page-header {
    margin-bottom: var(--spacing-lg);
    
    h2 {
      margin-bottom: var(--spacing-xs);
      color: var(--text-color-primary);
    }
    
    p {
      color: var(--text-color-secondary);
    }
  }
}
</style>
