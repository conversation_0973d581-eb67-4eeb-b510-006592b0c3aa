<div class="row mb-4">
    <div class="col-12">
        <h1 class="h2 mb-3">
            <i class="fas fa-chart-line text-primary me-2"></i>
            交易系统控制台
        </h1>
        <p class="text-muted">欢迎使用BitV MACD智能加仓交易系统，基于Python异步架构的专业级交易平台</p>
    </div>
</div>

<!-- 系统状态卡片 -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value">
                    <?php if ($api_status): ?>
                        <i class="fas fa-check-circle text-success"></i>
                    <?php else: ?>
                        <i class="fas fa-times-circle text-danger"></i>
                    <?php endif; ?>
                </div>
                <div class="metric-label">Python API</div>
                <small class="text-muted">
                    <?php echo $api_status ? '连接正常' : '连接失败'; ?>
                </small>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value text-primary">
                    <?php echo $statistics['total_sessions']; ?>
                </div>
                <div class="metric-label">总交易会话</div>
                <small class="text-muted">历史记录</small>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value text-success">
                    <?php echo $statistics['active_sessions']; ?>
                </div>
                <div class="metric-label">活跃会话</div>
                <small class="text-muted">正在运行</small>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value <?php echo $statistics['total_pnl'] >= 0 ? 'text-success' : 'text-danger'; ?>">
                    <?php echo formatNumber($statistics['total_pnl'], 2); ?>
                </div>
                <div class="metric-label">总盈亏 (USDT)</div>
                <small class="text-muted">累计收益</small>
            </div>
        </div>
    </div>
</div>

<!-- 主要功能区域 -->
<div class="row">
    <!-- 交易状态 -->
    <div class="col-lg-8 mb-4">
        <div class="card trading-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-robot me-2"></i>交易系统状态
                </h5>
                <button class="btn btn-sm btn-outline-primary" onclick="refreshTradingStatus()">
                    <i class="fas fa-sync-alt"></i> 刷新
                </button>
            </div>
            <div class="card-body">
                <?php if ($api_status && $trading_status): ?>
                    <div class="row">
                        <div class="col-md-6">
                            <h6>系统状态</h6>
                            <p class="mb-2">
                                <span class="badge bg-<?php echo $trading_status['is_running'] ? 'success' : 'secondary'; ?>">
                                    <?php echo $trading_status['is_running'] ? '运行中' : '已停止'; ?>
                                </span>
                            </p>
                            <p class="mb-2">
                                <strong>交易状态:</strong> 
                                <span class="text-primary"><?php echo $GLOBALS['TRADING_STATES'][$trading_status['trading_state']] ?? $trading_status['trading_state']; ?></span>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <?php if (isset($trading_status['current_session']) && $trading_status['current_session']): ?>
                                <h6>当前会话</h6>
                                <p class="mb-1"><strong>交易对:</strong> <?php echo htmlspecialchars($trading_status['current_session']['symbol']); ?></p>
                                <p class="mb-1"><strong>杠杆:</strong> <?php echo $trading_status['current_session']['leverage']; ?>x</p>
                                <p class="mb-1"><strong>保证金:</strong> <?php echo formatNumber($trading_status['current_session']['initial_margin']); ?> USDT</p>
                                <p class="mb-1"><strong>加仓次数:</strong> <?php echo $trading_status['current_session']['total_add_times']; ?></p>
                                <p class="mb-0">
                                    <strong>未实现盈亏:</strong> 
                                    <span class="<?php echo $trading_status['current_session']['total_pnl'] >= 0 ? 'text-success' : 'text-danger'; ?>">
                                        <?php echo formatNumber($trading_status['current_session']['total_pnl']); ?> USDT
                                    </span>
                                </p>
                            <?php else: ?>
                                <div class="text-center text-muted">
                                    <i class="fas fa-pause-circle fa-2x mb-2"></i>
                                    <p>当前没有活跃的交易会话</p>
                                    <a href="<?php echo Router::url('trading'); ?>" class="btn btn-primary btn-sm">
                                        <i class="fas fa-play me-1"></i>开始交易
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="text-center text-muted">
                        <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                        <p>无法连接到Python后端服务</p>
                        <small>请确保Python交易系统正在运行</small>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- 快速操作 -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>快速操作
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="<?php echo Router::url('config'); ?>" class="btn btn-outline-primary">
                        <i class="fas fa-cog me-2"></i>交易配置
                    </a>
                    <a href="<?php echo Router::url('trading'); ?>" class="btn btn-primary">
                        <i class="fas fa-play me-2"></i>交易控制
                    </a>
                    <a href="<?php echo Router::url('monitoring'); ?>" class="btn btn-outline-info">
                        <i class="fas fa-chart-area me-2"></i>实时监控
                    </a>
                    <a href="<?php echo Router::url('history'); ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-history me-2"></i>交易历史
                    </a>
                </div>

                <hr>

                <h6 class="mb-3">系统信息</h6>
                <small class="text-muted d-block mb-1">
                    <strong>版本:</strong> <?php echo SYSTEM_VERSION; ?>
                </small>
                <small class="text-muted d-block mb-1">
                    <strong>Python API:</strong> <?php echo PYTHON_API_BASE; ?>
                </small>
                <?php if ($statistics['favorite_exchange']): ?>
                    <small class="text-muted d-block mb-1">
                        <strong>常用交易所:</strong> <?php echo $supported_exchanges[$statistics['favorite_exchange']]['display_name']; ?>
                    </small>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- 最近交易会话 -->
<?php if (!empty($recent_sessions)): ?>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clock me-2"></i>最近交易会话
                </h5>
                <a href="<?php echo Router::url('history'); ?>" class="btn btn-sm btn-outline-primary">
                    查看全部
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>会话ID</th>
                                <th>交易所</th>
                                <th>交易对</th>
                                <th>策略</th>
                                <th>保证金</th>
                                <th>杠杆</th>
                                <th>状态</th>
                                <th>盈亏</th>
                                <th>开始时间</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recent_sessions as $session): ?>
                            <tr>
                                <td>
                                    <code class="small"><?php echo substr($session['session_id'], -8); ?></code>
                                </td>
                                <td>
                                    <span class="badge bg-info">
                                        <?php echo strtoupper($session['exchange']); ?>
                                    </span>
                                </td>
                                <td><?php echo htmlspecialchars($session['symbol']); ?></td>
                                <td>
                                    <span class="badge bg-secondary">
                                        <?php echo strtoupper($session['strategy']); ?>
                                    </span>
                                </td>
                                <td><?php echo formatNumber($session['initial_margin']); ?> USDT</td>
                                <td><?php echo $session['leverage']; ?>x</td>
                                <td>
                                    <span class="badge bg-<?php 
                                        echo $session['status'] === 'active' ? 'success' : 
                                            ($session['status'] === 'error' ? 'danger' : 'secondary'); 
                                    ?>">
                                        <?php echo $session['status']; ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="<?php echo $session['total_pnl'] >= 0 ? 'text-success' : 'text-danger'; ?>">
                                        <?php echo formatNumber($session['total_pnl'], 2); ?>
                                    </span>
                                </td>
                                <td>
                                    <small><?php echo date('m-d H:i', strtotime($session['start_time'])); ?></small>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<script>
// 刷新交易状态
function refreshTradingStatus() {
    const button = event.target.closest('button');
    const originalHtml = button.innerHTML;
    button.innerHTML = '<span class="loading-spinner"></span> 刷新中...';
    button.disabled = true;
    
    fetch('<?php echo Router::url("api/status"); ?>')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                showAlert('warning', '刷新失败: ' + (data.message || '未知错误'));
            }
        })
        .catch(error => {
            showAlert('danger', '刷新失败: 网络错误');
        })
        .finally(() => {
            button.innerHTML = originalHtml;
            button.disabled = false;
        });
}

// 格式化数字的PHP函数在JavaScript中的实现
function formatNumber(num, decimals = 4) {
    return parseFloat(num).toFixed(decimals);
}
</script>
