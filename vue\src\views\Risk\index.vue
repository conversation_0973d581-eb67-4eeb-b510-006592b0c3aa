<template>
  <div class="risk-page">
    <div class="page-header">
      <h2>资金设置</h2>
      <p>配置风险管理参数和资金控制策略</p>
    </div>

    <el-row :gutter="20">
      <el-col :span="16">
        <div class="card">
          <h3 class="mb-md">风险管理配置</h3>
          
          <el-form :model="riskConfig" :rules="rules" ref="formRef" label-width="140px" class="form-container">
            <div class="form-section">
              <h4 class="section-title">基础资金设置</h4>
              
              <el-form-item label="初始保证金" prop="initial_margin">
                <el-input-number 
                  v-model="riskConfig.initial_margin" 
                  :min="10" 
                  :max="100000" 
                  :step="10"
                  style="width: 200px"
                />
                <span class="param-desc">USDT</span>
              </el-form-item>
              
              <el-form-item label="最大总保证金" prop="max_total_margin">
                <el-input-number 
                  v-model="riskConfig.max_total_margin" 
                  :min="100" 
                  :max="1000000" 
                  :step="100"
                  style="width: 200px"
                />
                <span class="param-desc">USDT</span>
              </el-form-item>
              
              <el-form-item label="单笔最大损失" prop="max_loss_per_trade">
                <el-input-number 
                  v-model="riskConfig.max_loss_per_trade" 
                  :min="1" 
                  :max="50" 
                  :step="0.1"
                  :precision="1"
                  style="width: 200px"
                />
                <span class="param-desc">%</span>
              </el-form-item>
            </div>

            <div class="form-section">
              <h4 class="section-title">持仓控制</h4>
              
              <el-form-item label="最大持仓数量" prop="max_positions">
                <el-input-number 
                  v-model="riskConfig.max_positions" 
                  :min="1" 
                  :max="20" 
                  style="width: 200px"
                />
                <span class="param-desc">个</span>
              </el-form-item>
              
              <el-form-item label="单品种最大仓位" prop="max_position_per_symbol">
                <el-input-number 
                  v-model="riskConfig.max_position_per_symbol" 
                  :min="1" 
                  :max="10" 
                  style="width: 200px"
                />
                <span class="param-desc">个</span>
              </el-form-item>
              
              <el-form-item label="最大杠杆倍数" prop="max_leverage">
                <el-input-number 
                  v-model="riskConfig.max_leverage" 
                  :min="1" 
                  :max="100" 
                  style="width: 200px"
                />
                <span class="param-desc">倍</span>
              </el-form-item>
            </div>

            <div class="form-section">
              <h4 class="section-title">止损止盈设置</h4>
              
              <el-form-item label="全局止损比例" prop="global_stop_loss">
                <el-input-number 
                  v-model="riskConfig.global_stop_loss" 
                  :min="0.1" 
                  :max="50" 
                  :step="0.1"
                  :precision="1"
                  style="width: 200px"
                />
                <span class="param-desc">%</span>
              </el-form-item>
              
              <el-form-item label="全局止盈比例" prop="global_take_profit">
                <el-input-number 
                  v-model="riskConfig.global_take_profit" 
                  :min="0.1" 
                  :max="100" 
                  :step="0.1"
                  :precision="1"
                  style="width: 200px"
                />
                <span class="param-desc">%</span>
              </el-form-item>
              
              <el-form-item label="启用动态止损" prop="enable_trailing_stop">
                <el-switch v-model="riskConfig.enable_trailing_stop" />
              </el-form-item>
              
              <el-form-item label="追踪止损距离" prop="trailing_stop_distance" v-if="riskConfig.enable_trailing_stop">
                <el-input-number 
                  v-model="riskConfig.trailing_stop_distance" 
                  :min="0.1" 
                  :max="10" 
                  :step="0.1"
                  :precision="1"
                  style="width: 200px"
                />
                <span class="param-desc">%</span>
              </el-form-item>
            </div>

            <div class="form-section">
              <h4 class="section-title">风险控制</h4>
              
              <el-form-item label="最大回撤限制" prop="max_drawdown">
                <el-input-number 
                  v-model="riskConfig.max_drawdown" 
                  :min="1" 
                  :max="50" 
                  :step="0.1"
                  :precision="1"
                  style="width: 200px"
                />
                <span class="param-desc">%</span>
              </el-form-item>
              
              <el-form-item label="日最大亏损" prop="daily_max_loss">
                <el-input-number 
                  v-model="riskConfig.daily_max_loss" 
                  :min="1" 
                  :max="50" 
                  :step="0.1"
                  :precision="1"
                  style="width: 200px"
                />
                <span class="param-desc">%</span>
              </el-form-item>
              
              <el-form-item label="连续亏损限制" prop="max_consecutive_losses">
                <el-input-number 
                  v-model="riskConfig.max_consecutive_losses" 
                  :min="1" 
                  :max="20" 
                  style="width: 200px"
                />
                <span class="param-desc">次</span>
              </el-form-item>
            </div>

            <div class="form-section">
              <h4 class="section-title">紧急控制</h4>
              
              <el-form-item label="启用紧急停止" prop="enable_emergency_stop">
                <el-switch v-model="riskConfig.enable_emergency_stop" />
              </el-form-item>
              
              <el-form-item label="紧急停止触发条件" prop="emergency_stop_trigger" v-if="riskConfig.enable_emergency_stop">
                <el-select v-model="riskConfig.emergency_stop_trigger" style="width: 200px">
                  <el-option label="达到最大回撤" value="max_drawdown" />
                  <el-option label="连续亏损过多" value="consecutive_losses" />
                  <el-option label="日亏损超限" value="daily_loss" />
                  <el-option label="手动触发" value="manual" />
                </el-select>
              </el-form-item>
              
              <el-form-item label="紧急停止后操作" prop="emergency_action" v-if="riskConfig.enable_emergency_stop">
                <el-select v-model="riskConfig.emergency_action" style="width: 200px">
                  <el-option label="停止交易" value="stop_trading" />
                  <el-option label="平仓所有持仓" value="close_all" />
                  <el-option label="仅停止开新仓" value="stop_new_positions" />
                </el-select>
              </el-form-item>
            </div>

            <div class="form-actions">
              <el-button type="primary" @click="saveConfig" :loading="saving">
                保存配置
              </el-button>
              <el-button type="danger" @click="triggerEmergencyStop" :loading="emergencyLoading">
                紧急停止
              </el-button>
              <el-button @click="resetConfig">
                重置配置
              </el-button>
            </div>
          </el-form>
        </div>
      </el-col>

      <el-col :span="8">
        <!-- 当前风险状态 -->
        <div class="card">
          <h3 class="mb-md">当前风险状态</h3>
          
          <div class="risk-status">
            <div class="risk-item">
              <span class="risk-label">风险等级</span>
              <el-tag :type="getRiskLevelType(currentRisk.level)" size="small">
                {{ getRiskLevelText(currentRisk.level) }}
              </el-tag>
            </div>
            
            <div class="risk-item">
              <span class="risk-label">当前回撤</span>
              <span class="risk-value" :class="currentRisk.drawdown > 10 ? 'text-danger' : 'text-warning'">
                {{ formatPercent(currentRisk.drawdown) }}
              </span>
            </div>
            
            <div class="risk-item">
              <span class="risk-label">保证金使用率</span>
              <span class="risk-value">{{ formatPercent(currentRisk.marginUsage) }}</span>
            </div>
            
            <div class="risk-item">
              <span class="risk-label">持仓数量</span>
              <span class="risk-value">{{ currentRisk.positionCount }}/{{ riskConfig.max_positions }}</span>
            </div>
            
            <div class="risk-item">
              <span class="risk-label">今日盈亏</span>
              <span class="risk-value" :class="currentRisk.dailyPnl >= 0 ? 'text-success' : 'text-danger'">
                {{ formatNumber(currentRisk.dailyPnl) }} USDT
              </span>
            </div>
          </div>
        </div>

        <!-- 风险提示 -->
        <div class="card mt-md">
          <h3 class="mb-md">风险提示</h3>
          
          <el-alert
            title="重要提醒"
            type="warning"
            :closable="false"
            show-icon
          >
            <template #default>
              <ul style="margin: 0; padding-left: 20px;">
                <li>合理设置止损止盈，控制单笔交易风险</li>
                <li>不要将所有资金投入单一品种</li>
                <li>定期检查和调整风险参数</li>
                <li>紧急情况下及时手动干预</li>
              </ul>
            </template>
          </el-alert>
        </div>

        <!-- 风险计算器 -->
        <div class="card mt-md">
          <h3 class="mb-md">风险计算器</h3>
          
          <div class="risk-calculator">
            <el-form label-width="100px" size="small">
              <el-form-item label="交易金额">
                <el-input-number v-model="calculator.amount" :min="1" style="width: 150px" />
              </el-form-item>
              <el-form-item label="杠杆倍数">
                <el-input-number v-model="calculator.leverage" :min="1" :max="100" style="width: 150px" />
              </el-form-item>
              <el-form-item label="止损比例">
                <el-input-number v-model="calculator.stopLoss" :min="0.1" :step="0.1" style="width: 150px" />
              </el-form-item>
            </el-form>
            
            <div class="calculator-result">
              <p><strong>最大亏损:</strong> {{ formatNumber(calculateMaxLoss()) }} USDT</p>
              <p><strong>风险比例:</strong> {{ formatPercent(calculateRiskRatio()) }}</p>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import api from '@/utils/api'

// 响应式数据
const saving = ref(false)
const emergencyLoading = ref(false)
const formRef = ref()

// 风险配置
const riskConfig = reactive({
  // 基础资金设置
  initial_margin: 100,
  max_total_margin: 10000,
  max_loss_per_trade: 2.0,
  
  // 持仓控制
  max_positions: 5,
  max_position_per_symbol: 2,
  max_leverage: 20,
  
  // 止损止盈设置
  global_stop_loss: 5.0,
  global_take_profit: 10.0,
  enable_trailing_stop: false,
  trailing_stop_distance: 2.0,
  
  // 风险控制
  max_drawdown: 15.0,
  daily_max_loss: 10.0,
  max_consecutive_losses: 5,
  
  // 紧急控制
  enable_emergency_stop: true,
  emergency_stop_trigger: 'max_drawdown',
  emergency_action: 'stop_trading'
})

// 当前风险状态
const currentRisk = reactive({
  level: 'safe',
  drawdown: 2.5,
  marginUsage: 0.3,
  positionCount: 2,
  dailyPnl: 150.5
})

// 风险计算器
const calculator = reactive({
  amount: 1000,
  leverage: 10,
  stopLoss: 2.0
})

// 验证规则
const rules = {
  initial_margin: [{ required: true, message: '请输入初始保证金', trigger: 'blur' }],
  max_total_margin: [{ required: true, message: '请输入最大总保证金', trigger: 'blur' }],
  max_positions: [{ required: true, message: '请输入最大持仓数量', trigger: 'blur' }]
}

// 计算属性
const calculateMaxLoss = () => {
  return (calculator.amount * calculator.leverage * calculator.stopLoss) / 100
}

const calculateRiskRatio = () => {
  return calculateMaxLoss() / calculator.amount
}

// 方法
const formatNumber = (value, decimals = 2) => {
  if (value === null || value === undefined) return '0.00'
  return Number(value).toFixed(decimals)
}

const formatPercent = (value) => {
  if (value === null || value === undefined) return '0.00%'
  return (Number(value) * 100).toFixed(2) + '%'
}

const getRiskLevelText = (level) => {
  const levelMap = {
    'safe': '安全',
    'low': '低风险',
    'medium': '中风险',
    'high': '高风险',
    'critical': '极高风险'
  }
  return levelMap[level] || level
}

const getRiskLevelType = (level) => {
  const typeMap = {
    'safe': 'success',
    'low': 'success',
    'medium': 'warning',
    'high': 'danger',
    'critical': 'danger'
  }
  return typeMap[level] || 'info'
}

const saveConfig = async () => {
  if (!formRef.value) return
  
  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return
  
  saving.value = true
  
  try {
    await api.post('/api/risk/config', riskConfig)
    ElMessage.success('风险管理配置保存成功')
  } catch (error) {
    ElMessage.error(`保存失败: ${error.message}`)
  } finally {
    saving.value = false
  }
}

const triggerEmergencyStop = async () => {
  ElMessageBox.confirm(
    '确定要触发紧急停止吗？这将立即执行紧急停止操作。',
    '紧急停止确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'error'
    }
  ).then(async () => {
    emergencyLoading.value = true
    
    try {
      await api.post('/api/trading/emergency-stop')
      ElMessage.warning('紧急停止已触发')
    } catch (error) {
      ElMessage.error(`紧急停止失败: ${error.message}`)
    } finally {
      emergencyLoading.value = false
    }
  })
}

const resetConfig = () => {
  Object.assign(riskConfig, {
    initial_margin: 100,
    max_total_margin: 10000,
    max_loss_per_trade: 2.0,
    max_positions: 5,
    max_position_per_symbol: 2,
    max_leverage: 20,
    global_stop_loss: 5.0,
    global_take_profit: 10.0,
    enable_trailing_stop: false,
    trailing_stop_distance: 2.0,
    max_drawdown: 15.0,
    daily_max_loss: 10.0,
    max_consecutive_losses: 5,
    enable_emergency_stop: true,
    emergency_stop_trigger: 'max_drawdown',
    emergency_action: 'stop_trading'
  })
}

const loadConfig = async () => {
  try {
    const response = await api.get('/api/risk/config')
    if (response.data.success && response.data.data) {
      Object.assign(riskConfig, response.data.data)
    }
  } catch (error) {
    console.error('加载风险配置失败:', error)
  }
}

const loadRiskStatus = async () => {
  try {
    const response = await api.get('/api/risk/status')
    if (response.data.success && response.data.data) {
      Object.assign(currentRisk, response.data.data)
    }
  } catch (error) {
    console.error('加载风险状态失败:', error)
  }
}

// 生命周期
onMounted(() => {
  loadConfig()
  loadRiskStatus()
})
</script>

<style lang="scss" scoped>
.risk-page {
  .page-header {
    margin-bottom: var(--spacing-lg);
    
    h2 {
      margin-bottom: var(--spacing-xs);
      color: var(--text-color-primary);
    }
    
    p {
      color: var(--text-color-secondary);
    }
  }
  
  .param-desc {
    margin-left: var(--spacing-sm);
    color: var(--text-color-placeholder);
    font-size: 12px;
  }
  
  .form-actions {
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color-lighter);
    
    .el-button {
      margin-right: var(--spacing-sm);
    }
  }
  
  .risk-status {
    .risk-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: var(--spacing-sm) 0;
      border-bottom: 1px solid var(--border-color-lighter);
      
      &:last-child {
        border-bottom: none;
      }
      
      .risk-label {
        color: var(--text-color-secondary);
        font-size: 14px;
      }
      
      .risk-value {
        font-weight: 600;
      }
    }
  }
  
  .risk-calculator {
    .calculator-result {
      margin-top: var(--spacing-md);
      padding-top: var(--spacing-md);
      border-top: 1px solid var(--border-color-lighter);
      
      p {
        margin: var(--spacing-xs) 0;
        font-size: 14px;
      }
    }
  }
}

.text-success {
  color: var(--success-color);
}

.text-warning {
  color: var(--warning-color);
}

.text-danger {
  color: var(--danger-color);
}
</style>
