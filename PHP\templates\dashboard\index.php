<div class="row mb-4">
    <div class="col-12">
        <h1 class="h2 mb-3">
            <i class="fas fa-tachometer-alt text-primary me-2"></i>
            实时监控仪表板
        </h1>
        <p class="text-muted">实时监控交易状态、价格变化、持仓风险和系统性能</p>
    </div>
</div>

<!-- 系统状态概览 -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value" id="tradingStatus">
                    <i class="fas fa-circle text-secondary"></i>
                </div>
                <div class="metric-label">交易状态</div>
                <small class="text-muted" id="tradingStatusText">检查中...</small>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value text-primary" id="currentPrice">
                    --
                </div>
                <div class="metric-label">当前价格</div>
                <small class="text-muted" id="priceChange">USDT</small>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value" id="unrealizedPnl">
                    --
                </div>
                <div class="metric-label">未实现盈亏</div>
                <small class="text-muted">USDT</small>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value" id="riskLevel">
                    <i class="fas fa-shield-alt text-success"></i>
                </div>
                <div class="metric-label">风险等级</div>
                <small class="text-muted" id="riskText">安全</small>
            </div>
        </div>
    </div>
</div>

<!-- 图表区域 -->
<div class="row mb-4">
    <!-- 价格图表 -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>价格走势
                </h5>
                <div class="btn-group btn-group-sm" role="group">
                    <input type="radio" class="btn-check" name="timeframe" id="tf1m" value="1m">
                    <label class="btn btn-outline-primary" for="tf1m">1分</label>
                    
                    <input type="radio" class="btn-check" name="timeframe" id="tf5m" value="5m">
                    <label class="btn btn-outline-primary" for="tf5m">5分</label>
                    
                    <input type="radio" class="btn-check" name="timeframe" id="tf30m" value="30m" checked>
                    <label class="btn btn-outline-primary" for="tf30m">30分</label>
                    
                    <input type="radio" class="btn-check" name="timeframe" id="tf1h" value="1h">
                    <label class="btn btn-outline-primary" for="tf1h">1小时</label>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="priceChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- MACD指标 -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-wave-square me-2"></i>MACD指标
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="height: 300px;">
                    <canvas id="macdChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 持仓信息和风险监控 -->
<div class="row mb-4">
    <!-- 持仓信息 -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-coins me-2"></i>持仓信息
                </h5>
            </div>
            <div class="card-body">
                <div id="positionInfo">
                    <div class="text-center text-muted">
                        <i class="fas fa-spinner fa-spin fa-2x mb-2"></i>
                        <p>加载持仓信息...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 风险监控 -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>风险监控
                </h5>
            </div>
            <div class="card-body">
                <div id="riskMonitor">
                    <div class="text-center text-muted">
                        <i class="fas fa-spinner fa-spin fa-2x mb-2"></i>
                        <p>加载风险数据...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 最近交易记录 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>最近交易记录
                </h5>
                <button class="btn btn-sm btn-outline-primary" onclick="refreshTrades()">
                    <i class="fas fa-sync-alt"></i> 刷新
                </button>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="tradesTable">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>交易对</th>
                                <th>方向</th>
                                <th>数量</th>
                                <th>价格</th>
                                <th>盈亏</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="7" class="text-center text-muted">
                                    <i class="fas fa-spinner fa-spin me-2"></i>加载交易记录...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- WebSocket连接状态 -->
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 1050;">
    <div class="toast" id="wsStatusToast" role="alert">
        <div class="toast-header">
            <span class="status-indicator me-2" id="wsStatusIndicator"></span>
            <strong class="me-auto">WebSocket连接</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body" id="wsStatusText">
            正在连接...
        </div>
    </div>
</div>

<script>
// 全局变量
let priceChart = null;
let macdChart = null;
let wsConnection = null;
let refreshInterval = null;
let currentSymbol = 'BTC-USDT-SWAP';
let currentTimeframe = '30m';

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
    initializeWebSocket();
    loadInitialData();
    startAutoRefresh();
    
    // 绑定时间周期切换事件
    document.querySelectorAll('input[name="timeframe"]').forEach(radio => {
        radio.addEventListener('change', function() {
            currentTimeframe = this.value;
            updatePriceChart();
            updateMACDChart();
        });
    });
});

// 初始化图表
function initializeCharts() {
    // 价格图表
    const priceCtx = document.getElementById('priceChart').getContext('2d');
    priceChart = new Chart(priceCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: '价格',
                data: [],
                borderColor: '#2563eb',
                backgroundColor: 'rgba(37, 99, 235, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                x: {
                    display: true,
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                y: {
                    display: true,
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        }
    });

    // MACD图表
    const macdCtx = document.getElementById('macdChart').getContext('2d');
    macdChart = new Chart(macdCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [
                {
                    label: 'DIF',
                    data: [],
                    borderColor: '#059669',
                    backgroundColor: 'transparent',
                    borderWidth: 2
                },
                {
                    label: 'DEA',
                    data: [],
                    borderColor: '#dc2626',
                    backgroundColor: 'transparent',
                    borderWidth: 2
                },
                {
                    label: 'MACD',
                    data: [],
                    type: 'bar',
                    backgroundColor: 'rgba(37, 99, 235, 0.5)',
                    borderColor: '#2563eb',
                    borderWidth: 1
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                }
            },
            scales: {
                x: {
                    display: false
                },
                y: {
                    display: true,
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                }
            }
        }
    });
}

// 初始化WebSocket连接
function initializeWebSocket() {
    try {
        const wsUrl = '<?php echo PYTHON_WS_BASE; ?>';
        wsConnection = new WebSocket(wsUrl);
        
        wsConnection.onopen = function(event) {
            updateWSStatus('connected', '连接已建立');
            // 订阅实时数据
            wsConnection.send(JSON.stringify({
                action: 'subscribe',
                symbol: currentSymbol,
                channels: ['price', 'position', 'risk']
            }));
        };
        
        wsConnection.onmessage = function(event) {
            const data = JSON.parse(event.data);
            handleWebSocketMessage(data);
        };
        
        wsConnection.onclose = function(event) {
            updateWSStatus('disconnected', '连接已断开');
            // 5秒后重连
            setTimeout(initializeWebSocket, 5000);
        };
        
        wsConnection.onerror = function(error) {
            updateWSStatus('error', '连接错误');
        };
        
    } catch (error) {
        updateWSStatus('error', '无法建立WebSocket连接');
    }
}

// 处理WebSocket消息
function handleWebSocketMessage(data) {
    switch (data.type) {
        case 'price_update':
            updatePriceDisplay(data.data);
            break;
        case 'position_update':
            updatePositionDisplay(data.data);
            break;
        case 'risk_update':
            updateRiskDisplay(data.data);
            break;
        case 'trade_update':
            updateTradesTable(data.data);
            break;
    }
}

// 更新WebSocket状态
function updateWSStatus(status, message) {
    const indicator = document.getElementById('wsStatusIndicator');
    const text = document.getElementById('wsStatusText');
    const toast = document.getElementById('wsStatusToast');
    
    indicator.className = `status-indicator status-${status === 'connected' ? 'online' : 'offline'}`;
    text.textContent = message;
    
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
}

// 加载初始数据
function loadInitialData() {
    updatePriceChart();
    updateMACDChart();
    updatePositionInfo();
    updateRiskMonitor();
    updateTradesTable();
}

// 更新价格图表
function updatePriceChart() {
    fetch(`<?php echo Router::url('dashboard/price-data'); ?>?symbol=${currentSymbol}&timeframe=${currentTimeframe}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                priceChart.data.labels = data.data.labels;
                priceChart.data.datasets[0].data = data.data.prices;
                priceChart.update('none');
            }
        })
        .catch(error => console.error('更新价格图表失败:', error));
}

// 更新MACD图表
function updateMACDChart() {
    fetch(`<?php echo Router::url('dashboard/macd-data'); ?>?symbol=${currentSymbol}&timeframe=${currentTimeframe}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                macdChart.data.labels = data.data.labels;
                macdChart.data.datasets[0].data = data.data.dif;
                macdChart.data.datasets[1].data = data.data.dea;
                macdChart.data.datasets[2].data = data.data.histogram;
                macdChart.update('none');
            }
        })
        .catch(error => console.error('更新MACD图表失败:', error));
}

// 更新价格显示
function updatePriceDisplay(priceData) {
    const currentPriceEl = document.getElementById('currentPrice');
    const priceChangeEl = document.getElementById('priceChange');
    
    if (priceData) {
        currentPriceEl.textContent = formatNumber(priceData.price, 2);
        
        const change = priceData.change || 0;
        const changePercent = priceData.changePercent || 0;
        
        priceChangeEl.innerHTML = `
            <span class="${change >= 0 ? 'text-success' : 'text-danger'}">
                ${change >= 0 ? '+' : ''}${formatNumber(change, 2)} 
                (${change >= 0 ? '+' : ''}${formatPercentage(changePercent)})
            </span>
        `;
    }
}

// 更新持仓信息
function updatePositionInfo() {
    fetch('<?php echo Router::url('dashboard/real-time-data'); ?>')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data.positions) {
                displayPositions(data.data.positions);
            }
        })
        .catch(error => console.error('更新持仓信息失败:', error));
}

// 显示持仓信息
function displayPositions(positions) {
    const container = document.getElementById('positionInfo');
    
    if (!positions || positions.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted">
                <i class="fas fa-inbox fa-2x mb-2"></i>
                <p>当前无持仓</p>
            </div>
        `;
        return;
    }
    
    let html = '';
    positions.forEach(position => {
        html += `
            <div class="mb-3 p-3 border rounded">
                <div class="row">
                    <div class="col-md-6">
                        <h6>${position.symbol}</h6>
                        <p class="mb-1"><strong>方向:</strong> 
                            <span class="badge bg-${position.side === 'long' ? 'success' : 'danger'}">
                                ${position.side === 'long' ? '多头' : '空头'}
                            </span>
                        </p>
                        <p class="mb-1"><strong>数量:</strong> ${formatNumber(position.size, 4)}</p>
                        <p class="mb-0"><strong>开仓价:</strong> ${formatNumber(position.entry_price, 2)}</p>
                    </div>
                    <div class="col-md-6">
                        <p class="mb-1"><strong>标记价:</strong> ${formatNumber(position.mark_price, 2)}</p>
                        <p class="mb-1"><strong>未实现盈亏:</strong> 
                            <span class="${position.unrealized_pnl >= 0 ? 'text-success' : 'text-danger'}">
                                ${formatNumber(position.unrealized_pnl, 2)} USDT
                            </span>
                        </p>
                        <p class="mb-0"><strong>保证金:</strong> ${formatNumber(position.margin, 2)} USDT</p>
                    </div>
                </div>
            </div>
        `;
    });
    
    container.innerHTML = html;
}

// 开始自动刷新
function startAutoRefresh() {
    refreshInterval = setInterval(() => {
        updatePositionInfo();
        updateRiskMonitor();
    }, <?php echo $refresh_interval ?? 5000; ?>);
}

// 页面卸载时清理资源
window.addEventListener('beforeunload', function() {
    if (wsConnection) {
        wsConnection.close();
    }
    if (refreshInterval) {
        clearInterval(refreshInterval);
    }
});

// 工具函数
function formatNumber(num, decimals = 4) {
    return parseFloat(num).toFixed(decimals);
}

function formatPercentage(num, decimals = 2) {
    return (parseFloat(num) * 100).toFixed(decimals) + '%';
}

function refreshTrades() {
    updateTradesTable();
}
</script>
