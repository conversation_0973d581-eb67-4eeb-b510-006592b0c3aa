<template>
  <div class="dashboard">
    <!-- 系统状态概览 -->
    <div class="status-overview">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-card">
            <el-icon class="stat-icon" :class="systemStore.apiStatus ? 'success' : 'danger'">
              <Connection />
            </el-icon>
            <div class="stat-value">{{ systemStore.apiStatus ? '正常' : '断开' }}</div>
            <div class="stat-label">Python后端</div>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="stat-card">
            <el-icon class="stat-icon" :class="wsStore.connected ? 'success' : 'warning'">
              <Wifi />
            </el-icon>
            <div class="stat-value">{{ wsStore.connected ? '已连接' : '未连接' }}</div>
            <div class="stat-label">WebSocket</div>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="stat-card">
            <el-icon class="stat-icon" :class="tradingStore.isRunning ? 'success' : 'info'">
              <VideoPlay />
            </el-icon>
            <div class="stat-value">{{ tradingStore.isRunning ? '运行中' : '已停止' }}</div>
            <div class="stat-label">交易状态</div>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="stat-card">
            <el-icon class="stat-icon primary">
              <Money />
            </el-icon>
            <div class="stat-value">{{ formatNumber(tradingStore.totalPnl) }}</div>
            <div class="stat-label">总盈亏 (USDT)</div>
            <div class="stat-change" :class="tradingStore.totalPnl >= 0 ? 'positive' : 'negative'">
              {{ tradingStore.totalPnl >= 0 ? '+' : '' }}{{ formatPercent(tradingStore.totalPnl / 1000) }}
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 主要内容区域 -->
    <el-row :gutter="20" class="main-content">
      <!-- 左侧：价格图表和交易信息 -->
      <el-col :span="16">
        <!-- 价格图表 -->
        <div class="chart-container">
          <div class="chart-header">
            <h3 class="chart-title">价格走势图</h3>
            <div class="chart-controls">
              <el-select v-model="selectedSymbol" size="small" style="width: 120px">
                <el-option label="BTC/USDT" value="BTC-USDT" />
                <el-option label="ETH/USDT" value="ETH-USDT" />
                <el-option label="BNB/USDT" value="BNB-USDT" />
              </el-select>
              <el-select v-model="selectedTimeframe" size="small" style="width: 80px">
                <el-option label="1分钟" value="1m" />
                <el-option label="5分钟" value="5m" />
                <el-option label="15分钟" value="15m" />
                <el-option label="30分钟" value="30m" />
                <el-option label="1小时" value="1h" />
                <el-option label="4小时" value="4h" />
              </el-select>
            </div>
          </div>
          <div class="chart-content">
            <!-- 价格图表占位符 -->
            <div class="chart-placeholder">
              <el-icon size="48" color="#c0c4cc"><TrendCharts /></el-icon>
              <p style="color: #909399; margin-top: 16px;">价格图表开发中...</p>
            </div>
          </div>
        </div>

        <!-- 当前持仓 -->
        <div class="card mt-md">
          <h3 class="mb-md">当前持仓</h3>
          <el-table :data="tradingStore.positions" class="data-table">
            <el-table-column prop="symbol" label="交易对" width="120" />
            <el-table-column prop="side" label="方向" width="80">
              <template #default="{ row }">
                <el-tag :type="row.side === 'long' ? 'success' : 'danger'" size="small">
                  {{ row.side === 'long' ? '多头' : '空头' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="size" label="数量" width="100">
              <template #default="{ row }">
                {{ formatNumber(row.size, 4) }}
              </template>
            </el-table-column>
            <el-table-column prop="entry_price" label="开仓价格" width="120">
              <template #default="{ row }">
                {{ formatNumber(row.entry_price, 2) }}
              </template>
            </el-table-column>
            <el-table-column prop="mark_price" label="标记价格" width="120">
              <template #default="{ row }">
                {{ formatNumber(row.mark_price, 2) }}
              </template>
            </el-table-column>
            <el-table-column prop="unrealized_pnl" label="未实现盈亏" width="120">
              <template #default="{ row }">
                <span :class="row.unrealized_pnl >= 0 ? 'text-success' : 'text-danger'">
                  {{ formatNumber(row.unrealized_pnl, 2) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="{ row }">
                <el-button 
                  type="danger" 
                  size="small" 
                  @click="closePosition(row.symbol)"
                  :loading="tradingStore.loading"
                >
                  平仓
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <el-empty v-if="!tradingStore.positions.length" description="暂无持仓" />
        </div>
      </el-col>

      <!-- 右侧：交易控制和监控 -->
      <el-col :span="8">
        <!-- 交易控制面板 -->
        <div class="card">
          <h3 class="mb-md">交易控制</h3>
          
          <div class="trading-controls">
            <el-button 
              v-if="!tradingStore.isRunning"
              type="success" 
              size="large" 
              @click="startTrading"
              :loading="tradingStore.loading"
              class="w-full mb-sm"
            >
              <el-icon><VideoPlay /></el-icon>
              启动交易
            </el-button>
            
            <el-button 
              v-else
              type="warning" 
              size="large" 
              @click="stopTrading"
              :loading="tradingStore.loading"
              class="w-full mb-sm"
            >
              <el-icon><VideoPause /></el-icon>
              停止交易
            </el-button>
            
            <el-button 
              type="danger" 
              size="large" 
              @click="emergencyStop"
              :loading="tradingStore.loading"
              class="w-full"
            >
              <el-icon><Warning /></el-icon>
              紧急停止
            </el-button>
          </div>
          
          <!-- 交易状态信息 -->
          <div class="trading-status mt-md">
            <el-descriptions :column="1" size="small">
              <el-descriptions-item label="交易状态">
                <span class="status-indicator" :class="`status-${getStatusType(tradingStore.tradingState)}`">
                  {{ getTradingStateText(tradingStore.tradingState) }}
                </span>
              </el-descriptions-item>
              <el-descriptions-item label="活跃会话">
                {{ tradingStore.activeSessions }}
              </el-descriptions-item>
              <el-descriptions-item label="总会话数">
                {{ tradingStore.totalSessions }}
              </el-descriptions-item>
              <el-descriptions-item label="紧急停止">
                <el-tag :type="tradingStore.emergencyStop ? 'danger' : 'success'" size="small">
                  {{ tradingStore.emergencyStop ? '已激活' : '未激活' }}
                </el-tag>
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </div>

        <!-- 风险监控 -->
        <div class="card mt-md">
          <h3 class="mb-md">风险监控</h3>
          
          <div class="risk-metrics">
            <div class="risk-item">
              <span class="risk-label">风险等级</span>
              <el-tag 
                :type="getRiskLevelType(tradingStore.riskMetrics.riskLevel)" 
                size="small"
              >
                {{ getRiskLevelText(tradingStore.riskMetrics.riskLevel) }}
              </el-tag>
            </div>
            
            <div class="risk-item">
              <span class="risk-label">保证金比例</span>
              <span class="risk-value">{{ formatPercent(tradingStore.riskMetrics.marginRatio) }}</span>
            </div>
            
            <div class="risk-item">
              <span class="risk-label">最大回撤</span>
              <span class="risk-value text-danger">{{ formatPercent(tradingStore.riskMetrics.maxDrawdown) }}</span>
            </div>
            
            <div class="risk-item">
              <span class="risk-label">强平距离</span>
              <span class="risk-value">{{ formatPercent(tradingStore.riskMetrics.liquidationDistance) }}</span>
            </div>
          </div>
        </div>

        <!-- 实时日志 -->
        <div class="card mt-md">
          <h3 class="mb-md">实时日志</h3>
          
          <div class="log-container">
            <div 
              v-for="log in recentLogs" 
              :key="log.id"
              class="log-item"
              :class="`log-${log.level}`"
            >
              <span class="log-time">{{ formatTime(log.timestamp) }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
            
            <el-empty v-if="!recentLogs.length" description="暂无日志" />
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useSystemStore } from '@/stores/system'
import { useWebSocketStore } from '@/stores/websocket'
import { useTradingStore } from '@/stores/trading'
import {
  Connection,
  Wifi,
  VideoPlay,
  VideoPause,
  Money,
  Warning,
  TrendCharts
} from '@element-plus/icons-vue'

const systemStore = useSystemStore()
const wsStore = useWebSocketStore()
const tradingStore = useTradingStore()

// 响应式数据
const selectedSymbol = ref('BTC-USDT')
const selectedTimeframe = ref('30m')

// 计算属性
const recentLogs = computed(() => {
  return wsStore.realtimeData.logs.slice(0, 10)
})

// 方法
const formatNumber = (value, decimals = 2) => {
  if (value === null || value === undefined) return '0.00'
  return Number(value).toFixed(decimals)
}

const formatPercent = (value) => {
  if (value === null || value === undefined) return '0.00%'
  return (Number(value) * 100).toFixed(2) + '%'
}

const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleTimeString()
}

const getTradingStateText = (state) => {
  const stateMap = {
    'idle': '空闲',
    'monitoring': '监控中',
    'opening': '开仓中',
    'adding': '加仓中',
    'closing': '平仓中',
    'error': '错误'
  }
  return stateMap[state] || state
}

const getStatusType = (state) => {
  const typeMap = {
    'idle': 'info',
    'monitoring': 'warning',
    'opening': 'success',
    'adding': 'success',
    'closing': 'warning',
    'error': 'danger'
  }
  return typeMap[state] || 'info'
}

const getRiskLevelText = (level) => {
  const levelMap = {
    'safe': '安全',
    'low': '低风险',
    'medium': '中风险',
    'high': '高风险',
    'critical': '极高风险'
  }
  return levelMap[level] || level
}

const getRiskLevelType = (level) => {
  const typeMap = {
    'safe': 'success',
    'low': 'success',
    'medium': 'warning',
    'high': 'danger',
    'critical': 'danger'
  }
  return typeMap[level] || 'info'
}

const startTrading = async () => {
  const success = await tradingStore.startTrading({})
  if (success) {
    ElMessage.success('交易启动成功')
  }
}

const stopTrading = async () => {
  const success = await tradingStore.stopTrading()
  if (success) {
    ElMessage.success('交易停止成功')
  }
}

const emergencyStop = async () => {
  ElMessageBox.confirm(
    '确定要执行紧急停止吗？这将立即停止所有交易活动。',
    '紧急停止确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    const success = await tradingStore.emergencyStopTrading()
    if (success) {
      ElMessage.warning('紧急停止执行成功')
    }
  })
}

const closePosition = async (symbol) => {
  ElMessageBox.confirm(
    `确定要平仓 ${symbol} 吗？`,
    '平仓确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    const success = await tradingStore.closePosition(symbol)
    if (success) {
      ElMessage.success('平仓操作成功')
    }
  })
}

// 生命周期
onMounted(async () => {
  // 获取初始数据
  await tradingStore.getTradingStatus()
  await tradingStore.getPositions()
  await tradingStore.getRiskMetrics()
  
  // 监听WebSocket更新
  wsStore.on('trading_update', tradingStore.updateTradingStatus)
  wsStore.on('position_update', tradingStore.updatePositions)
  wsStore.on('risk_update', tradingStore.updateRiskMetrics)
})

onUnmounted(() => {
  // 清理事件监听
  wsStore.off('trading_update', tradingStore.updateTradingStatus)
  wsStore.off('position_update', tradingStore.updatePositions)
  wsStore.off('risk_update', tradingStore.updateRiskMetrics)
})
</script>

<style lang="scss" scoped>
.dashboard {
  .status-overview {
    margin-bottom: var(--spacing-lg);
  }
  
  .main-content {
    min-height: calc(100vh - 200px);
  }

  .chart-placeholder {
    height: 400px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #fafbfc;
    border: 2px dashed #e4e7ed;
    border-radius: 8px;
  }
  
  .trading-controls {
    .el-button {
      height: 48px;
      font-size: 16px;
      font-weight: 600;
    }
  }
  
  .trading-status {
    border-top: 1px solid var(--border-color-lighter);
    padding-top: var(--spacing-md);
  }
  
  .risk-metrics {
    .risk-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: var(--spacing-sm) 0;
      border-bottom: 1px solid var(--border-color-lighter);
      
      &:last-child {
        border-bottom: none;
      }
      
      .risk-label {
        color: var(--text-color-secondary);
        font-size: 14px;
      }
      
      .risk-value {
        font-weight: 600;
      }
    }
  }
  
  .log-container {
    max-height: 300px;
    overflow-y: auto;
    
    .log-item {
      display: flex;
      gap: var(--spacing-sm);
      padding: var(--spacing-xs) 0;
      border-bottom: 1px solid var(--border-color-lighter);
      font-size: 12px;
      
      &:last-child {
        border-bottom: none;
      }
      
      .log-time {
        color: var(--text-color-placeholder);
        white-space: nowrap;
        min-width: 80px;
      }
      
      .log-message {
        flex: 1;
        word-break: break-all;
      }
      
      &.log-error {
        .log-message {
          color: var(--danger-color);
        }
      }
      
      &.log-warning {
        .log-message {
          color: var(--warning-color);
        }
      }
      
      &.log-info {
        .log-message {
          color: var(--info-color);
        }
      }
    }
  }
}

.text-success {
  color: var(--success-color);
}

.text-danger {
  color: var(--danger-color);
}
</style>
