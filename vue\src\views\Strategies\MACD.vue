<template>
  <div class="macd-strategy">
    <div class="page-header">
      <h2>MACD策略配置</h2>
      <p>配置MACD技术指标参数和交易策略</p>
    </div>

    <el-row :gutter="20">
      <!-- 左侧：配置表单 -->
      <el-col :span="16">
        <div class="card">
          <h3 class="mb-md">MACD参数配置</h3>
          
          <el-form :model="macdConfig" :rules="rules" ref="formRef" label-width="120px" class="form-container">
            <div class="form-section">
              <h4 class="section-title">基础参数</h4>
              
              <el-form-item label="快速EMA" prop="fast_period">
                <el-input-number 
                  v-model="macdConfig.fast_period" 
                  :min="1" 
                  :max="50" 
                  style="width: 200px"
                />
                <span class="param-desc">快速指数移动平均线周期</span>
              </el-form-item>
              
              <el-form-item label="慢速EMA" prop="slow_period">
                <el-input-number 
                  v-model="macdConfig.slow_period" 
                  :min="1" 
                  :max="100" 
                  style="width: 200px"
                />
                <span class="param-desc">慢速指数移动平均线周期</span>
              </el-form-item>
              
              <el-form-item label="信号线" prop="signal_period">
                <el-input-number 
                  v-model="macdConfig.signal_period" 
                  :min="1" 
                  :max="50" 
                  style="width: 200px"
                />
                <span class="param-desc">MACD信号线周期</span>
              </el-form-item>
            </div>

            <div class="form-section">
              <h4 class="section-title">开仓条件</h4>
              
              <el-form-item label="开仓信号" prop="entry_signal">
                <el-select v-model="macdConfig.entry_signal" style="width: 200px">
                  <el-option label="MACD上穿信号线" value="macd_cross_up" />
                  <el-option label="MACD下穿信号线" value="macd_cross_down" />
                  <el-option label="MACD上穿零轴" value="macd_cross_zero_up" />
                  <el-option label="MACD下穿零轴" value="macd_cross_zero_down" />
                  <el-option label="柱状图转正" value="histogram_positive" />
                  <el-option label="柱状图转负" value="histogram_negative" />
                </el-select>
              </el-form-item>
              
              <el-form-item label="确认周期" prop="confirmation_periods">
                <el-input-number 
                  v-model="macdConfig.confirmation_periods" 
                  :min="1" 
                  :max="10" 
                  style="width: 200px"
                />
                <span class="param-desc">信号确认所需的K线周期数</span>
              </el-form-item>
              
              <el-form-item label="最小强度" prop="min_strength">
                <el-input-number 
                  v-model="macdConfig.min_strength" 
                  :min="0" 
                  :max="1" 
                  :step="0.01"
                  :precision="2"
                  style="width: 200px"
                />
                <span class="param-desc">信号最小强度阈值</span>
              </el-form-item>
            </div>

            <div class="form-section">
              <h4 class="section-title">加仓策略</h4>
              
              <el-form-item label="启用加仓" prop="enable_add_position">
                <el-switch v-model="macdConfig.enable_add_position" />
              </el-form-item>
              
              <el-form-item label="加仓条件" prop="add_position_signal" v-if="macdConfig.enable_add_position">
                <el-select v-model="macdConfig.add_position_signal" style="width: 200px">
                  <el-option label="MACD继续发散" value="macd_divergence" />
                  <el-option label="柱状图增强" value="histogram_strengthen" />
                  <el-option label="价格回调" value="price_pullback" />
                </el-select>
              </el-form-item>
              
              <el-form-item label="最大加仓次数" prop="max_add_times" v-if="macdConfig.enable_add_position">
                <el-input-number 
                  v-model="macdConfig.max_add_times" 
                  :min="1" 
                  :max="10" 
                  style="width: 200px"
                />
              </el-form-item>
              
              <el-form-item label="加仓间隔" prop="add_position_interval" v-if="macdConfig.enable_add_position">
                <el-input-number 
                  v-model="macdConfig.add_position_interval" 
                  :min="1" 
                  :max="100" 
                  style="width: 200px"
                />
                <span class="param-desc">加仓之间的最小K线间隔</span>
              </el-form-item>
            </div>

            <div class="form-section">
              <h4 class="section-title">平仓条件</h4>
              
              <el-form-item label="平仓信号" prop="exit_signal">
                <el-select v-model="macdConfig.exit_signal" style="width: 200px">
                  <el-option label="MACD反向穿越" value="macd_reverse_cross" />
                  <el-option label="柱状图收敛" value="histogram_converge" />
                  <el-option label="信号线反转" value="signal_reverse" />
                  <el-option label="止盈止损" value="stop_profit_loss" />
                </el-select>
              </el-form-item>
              
              <el-form-item label="止盈比例" prop="take_profit_percent">
                <el-input-number 
                  v-model="macdConfig.take_profit_percent" 
                  :min="0.1" 
                  :max="50" 
                  :step="0.1"
                  :precision="1"
                  style="width: 200px"
                />
                <span class="param-desc">%</span>
              </el-form-item>
              
              <el-form-item label="止损比例" prop="stop_loss_percent">
                <el-input-number 
                  v-model="macdConfig.stop_loss_percent" 
                  :min="0.1" 
                  :max="50" 
                  :step="0.1"
                  :precision="1"
                  style="width: 200px"
                />
                <span class="param-desc">%</span>
              </el-form-item>
            </div>

            <div class="form-section">
              <h4 class="section-title">风险控制</h4>
              
              <el-form-item label="最大持仓时间" prop="max_hold_time">
                <el-input-number 
                  v-model="macdConfig.max_hold_time" 
                  :min="1" 
                  :max="1440" 
                  style="width: 200px"
                />
                <span class="param-desc">分钟</span>
              </el-form-item>
              
              <el-form-item label="最大回撤" prop="max_drawdown">
                <el-input-number 
                  v-model="macdConfig.max_drawdown" 
                  :min="1" 
                  :max="50" 
                  :step="0.1"
                  :precision="1"
                  style="width: 200px"
                />
                <span class="param-desc">%</span>
              </el-form-item>
              
              <el-form-item label="启用动态止损" prop="enable_trailing_stop">
                <el-switch v-model="macdConfig.enable_trailing_stop" />
              </el-form-item>
              
              <el-form-item label="追踪距离" prop="trailing_distance" v-if="macdConfig.enable_trailing_stop">
                <el-input-number 
                  v-model="macdConfig.trailing_distance" 
                  :min="0.1" 
                  :max="10" 
                  :step="0.1"
                  :precision="1"
                  style="width: 200px"
                />
                <span class="param-desc">%</span>
              </el-form-item>
            </div>

            <div class="form-actions">
              <el-button type="primary" @click="saveConfig" :loading="saving">
                保存配置
              </el-button>
              <el-button @click="testStrategy" :loading="testing">
                策略回测
              </el-button>
              <el-button @click="resetConfig">
                重置配置
              </el-button>
            </div>
          </el-form>
        </div>
      </el-col>

      <!-- 右侧：预览和说明 -->
      <el-col :span="8">
        <!-- 策略说明 -->
        <div class="card">
          <h3 class="mb-md">策略说明</h3>
          
          <div class="strategy-description">
            <h4>MACD指标原理</h4>
            <p>MACD（Moving Average Convergence Divergence）是一种趋势跟踪动量指标，通过计算两条不同周期的指数移动平均线的差值来判断趋势变化。</p>
            
            <h4>信号类型</h4>
            <ul>
              <li><strong>金叉信号：</strong>MACD线上穿信号线，通常表示上涨趋势开始</li>
              <li><strong>死叉信号：</strong>MACD线下穿信号线，通常表示下跌趋势开始</li>
              <li><strong>零轴穿越：</strong>MACD线穿越零轴，表示趋势强度变化</li>
              <li><strong>柱状图：</strong>反映MACD与信号线的距离，表示趋势强度</li>
            </ul>
            
            <h4>使用建议</h4>
            <ul>
              <li>在趋势市场中效果较好，震荡市场中容易产生假信号</li>
              <li>建议结合其他指标使用，提高信号准确性</li>
              <li>注意参数设置，过小容易产生噪音，过大反应迟钝</li>
            </ul>
          </div>
        </div>

        <!-- 当前配置预览 -->
        <div class="card mt-md">
          <h3 class="mb-md">配置预览</h3>
          
          <div class="config-preview">
            <el-descriptions :column="1" size="small">
              <el-descriptions-item label="快速EMA">{{ macdConfig.fast_period }}</el-descriptions-item>
              <el-descriptions-item label="慢速EMA">{{ macdConfig.slow_period }}</el-descriptions-item>
              <el-descriptions-item label="信号线">{{ macdConfig.signal_period }}</el-descriptions-item>
              <el-descriptions-item label="开仓信号">{{ getSignalText(macdConfig.entry_signal) }}</el-descriptions-item>
              <el-descriptions-item label="止盈比例">{{ macdConfig.take_profit_percent }}%</el-descriptions-item>
              <el-descriptions-item label="止损比例">{{ macdConfig.stop_loss_percent }}%</el-descriptions-item>
              <el-descriptions-item label="加仓功能">
                <el-tag :type="macdConfig.enable_add_position ? 'success' : 'info'" size="small">
                  {{ macdConfig.enable_add_position ? '启用' : '禁用' }}
                </el-tag>
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </div>

        <!-- 风险提示 -->
        <div class="card mt-md">
          <h3 class="mb-md">风险提示</h3>
          
          <el-alert
            title="策略风险提醒"
            type="warning"
            :closable="false"
            show-icon
          >
            <template #default>
              <ul style="margin: 0; padding-left: 20px;">
                <li>MACD策略在震荡市场中可能产生频繁的假信号</li>
                <li>建议在明确趋势的市场环境中使用</li>
                <li>请充分测试策略参数后再进行实盘交易</li>
                <li>合理设置止损止盈，控制单笔交易风险</li>
              </ul>
            </template>
          </el-alert>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import api from '@/utils/api'

// 响应式数据
const saving = ref(false)
const testing = ref(false)
const formRef = ref()

// MACD配置
const macdConfig = reactive({
  // 基础参数
  fast_period: 12,
  slow_period: 26,
  signal_period: 9,
  
  // 开仓条件
  entry_signal: 'macd_cross_up',
  confirmation_periods: 2,
  min_strength: 0.1,
  
  // 加仓策略
  enable_add_position: true,
  add_position_signal: 'macd_divergence',
  max_add_times: 3,
  add_position_interval: 5,
  
  // 平仓条件
  exit_signal: 'macd_reverse_cross',
  take_profit_percent: 8.0,
  stop_loss_percent: 4.0,
  
  // 风险控制
  max_hold_time: 240,
  max_drawdown: 10.0,
  enable_trailing_stop: false,
  trailing_distance: 2.0
})

// 验证规则
const rules = {
  fast_period: [{ required: true, message: '请输入快速EMA周期', trigger: 'blur' }],
  slow_period: [{ required: true, message: '请输入慢速EMA周期', trigger: 'blur' }],
  signal_period: [{ required: true, message: '请输入信号线周期', trigger: 'blur' }],
  entry_signal: [{ required: true, message: '请选择开仓信号', trigger: 'change' }],
  exit_signal: [{ required: true, message: '请选择平仓信号', trigger: 'change' }]
}

// 方法
const getSignalText = (signal) => {
  const signalMap = {
    'macd_cross_up': 'MACD上穿信号线',
    'macd_cross_down': 'MACD下穿信号线',
    'macd_cross_zero_up': 'MACD上穿零轴',
    'macd_cross_zero_down': 'MACD下穿零轴',
    'histogram_positive': '柱状图转正',
    'histogram_negative': '柱状图转负',
    'macd_reverse_cross': 'MACD反向穿越',
    'histogram_converge': '柱状图收敛',
    'signal_reverse': '信号线反转',
    'stop_profit_loss': '止盈止损'
  }
  return signalMap[signal] || signal
}

const saveConfig = async () => {
  if (!formRef.value) return
  
  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return
  
  saving.value = true
  
  try {
    await api.post('/api/strategy/macd/config', macdConfig)
    ElMessage.success('MACD策略配置保存成功')
  } catch (error) {
    ElMessage.error(`保存失败: ${error.message}`)
  } finally {
    saving.value = false
  }
}

const testStrategy = async () => {
  if (!formRef.value) return
  
  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return
  
  testing.value = true
  
  try {
    const response = await api.post('/api/strategy/macd/test', macdConfig)
    if (response.data.success) {
      ElMessage.success('策略回测完成，请查看结果')
      // 这里可以显示回测结果
    }
  } catch (error) {
    ElMessage.error(`回测失败: ${error.message}`)
  } finally {
    testing.value = false
  }
}

const resetConfig = () => {
  Object.assign(macdConfig, {
    fast_period: 12,
    slow_period: 26,
    signal_period: 9,
    entry_signal: 'macd_cross_up',
    confirmation_periods: 2,
    min_strength: 0.1,
    enable_add_position: true,
    add_position_signal: 'macd_divergence',
    max_add_times: 3,
    add_position_interval: 5,
    exit_signal: 'macd_reverse_cross',
    take_profit_percent: 8.0,
    stop_loss_percent: 4.0,
    max_hold_time: 240,
    max_drawdown: 10.0,
    enable_trailing_stop: false,
    trailing_distance: 2.0
  })
}

const loadConfig = async () => {
  try {
    const response = await api.get('/api/strategy/macd/config')
    if (response.data.success && response.data.data) {
      Object.assign(macdConfig, response.data.data)
    }
  } catch (error) {
    console.error('加载MACD配置失败:', error)
  }
}

// 生命周期
onMounted(() => {
  loadConfig()
})
</script>

<style lang="scss" scoped>
.macd-strategy {
  .page-header {
    margin-bottom: var(--spacing-lg);
    
    h2 {
      margin-bottom: var(--spacing-xs);
      color: var(--text-color-primary);
    }
    
    p {
      color: var(--text-color-secondary);
    }
  }
  
  .param-desc {
    margin-left: var(--spacing-sm);
    color: var(--text-color-placeholder);
    font-size: 12px;
  }
  
  .form-actions {
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color-lighter);
    
    .el-button {
      margin-right: var(--spacing-sm);
    }
  }
  
  .strategy-description {
    h4 {
      color: var(--text-color-primary);
      margin: var(--spacing-md) 0 var(--spacing-sm) 0;
      font-size: 14px;
      
      &:first-child {
        margin-top: 0;
      }
    }
    
    p {
      color: var(--text-color-regular);
      font-size: 13px;
      line-height: 1.6;
      margin-bottom: var(--spacing-sm);
    }
    
    ul {
      margin: 0;
      padding-left: 20px;
      
      li {
        color: var(--text-color-regular);
        font-size: 13px;
        line-height: 1.6;
        margin-bottom: var(--spacing-xs);
        
        strong {
          color: var(--text-color-primary);
        }
      }
    }
  }
  
  .config-preview {
    .el-descriptions {
      :deep(.el-descriptions__label) {
        font-weight: 500;
      }
    }
  }
}
</style>
