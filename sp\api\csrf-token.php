<?php
/**
 * BitV MACD智能加仓交易系统 - CSRF令牌API
 * 实盘交易版本 - 安全令牌管理
 */

// 设置访问标识
define('BITV_ACCESS', true);

// 加载配置和安全模块
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/security.php';

// 设置响应头
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'error' => '不支持的请求方法'
    ]);
    exit;
}

try {
    // 启动会话（如果尚未启动）
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    // 生成新的CSRF令牌
    $token = generateCSRFToken();
    
    // 记录令牌生成日志
    writeLog('INFO', 'CSRF令牌已生成', [
        'ip_address' => getClientIP(),
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
        'session_id' => session_id()
    ]);
    
    // 返回成功响应
    echo json_encode([
        'success' => true,
        'token' => $token,
        'expires_at' => time() + SESSION_TIMEOUT
    ]);
    
} catch (Exception $e) {
    // 记录错误日志
    writeLog('ERROR', 'CSRF令牌生成失败: ' . $e->getMessage(), [
        'ip_address' => getClientIP(),
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ]);
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => '令牌生成失败'
    ]);
}
?>
