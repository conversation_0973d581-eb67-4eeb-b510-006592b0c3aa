<?php
/**
 * 宝塔面板环境测试页面
 */

echo "<h1>🚀 BitV系统 - 宝塔环境测试</h1>";

// 基本信息
echo "<h2>📊 环境信息</h2>";
echo "<p><strong>PHP版本:</strong> " . PHP_VERSION . "</p>";
echo "<p><strong>服务器软件:</strong> " . $_SERVER['SERVER_SOFTWARE'] . "</p>";
echo "<p><strong>文档根目录:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p><strong>脚本路径:</strong> " . $_SERVER['SCRIPT_NAME'] . "</p>";
echo "<p><strong>请求URI:</strong> " . $_SERVER['REQUEST_URI'] . "</p>";

// URL测试
echo "<h2>🔗 URL路由测试</h2>";
$test_urls = [
    '/' => '首页',
    '/dashboard' => '仪表板',
    '/config' => '配置页面',
    '/trading' => '交易控制',
    '/history' => '历史数据'
];

foreach ($test_urls as $url => $name) {
    echo "<p><a href='{$url}' target='_blank'>{$name}</a> - {$url}</p>";
}

// 文件权限检查
echo "<h2>📁 文件权限检查</h2>";
$dirs = [
    '.' => '当前目录',
    './config' => '配置目录',
    './logs' => '日志目录',
    './assets' => '资源目录'
];

foreach ($dirs as $dir => $name) {
    $exists = is_dir($dir);
    $readable = is_readable($dir);
    $writable = is_writable($dir);
    
    echo "<p><strong>{$name}:</strong> ";
    echo "存在(" . ($exists ? '✅' : '❌') . ") ";
    echo "可读(" . ($readable ? '✅' : '❌') . ") ";
    echo "可写(" . ($writable ? '✅' : '❌') . ")";
    echo "</p>";
}

// PHP扩展检查
echo "<h2>🔧 PHP扩展检查</h2>";
$extensions = ['mysqli', 'pdo', 'pdo_mysql', 'curl', 'json', 'openssl', 'mbstring'];
foreach ($extensions as $ext) {
    $loaded = extension_loaded($ext);
    echo "<p><strong>{$ext}:</strong> " . ($loaded ? '✅ 已加载' : '❌ 未加载') . "</p>";
}

// 配置文件检查
echo "<h2>📄 配置文件检查</h2>";
$config_files = [
    './config/config.php' => '系统配置',
    './.env' => '环境变量',
    './index.php' => '入口文件'
];

foreach ($config_files as $file => $name) {
    $exists = file_exists($file);
    echo "<p><strong>{$name}:</strong> " . ($exists ? '✅ 存在' : '❌ 不存在') . "</p>";
}

// 数据库连接测试
echo "<h2>🗄️ 数据库连接测试</h2>";
if (file_exists('./config/config.php')) {
    try {
        require_once './config/config.php';
        require_once './includes/Database.php';
        
        $db = Database::getInstance();
        $result = $db->fetch('SELECT 1 as test');
        
        if ($result && $result['test'] == 1) {
            echo "<p>✅ 数据库连接成功</p>";
        } else {
            echo "<p>❌ 数据库查询失败</p>";
        }
    } catch (Exception $e) {
        echo "<p>❌ 数据库连接失败: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
} else {
    echo "<p>⚠️ 配置文件不存在，无法测试数据库连接</p>";
}

// Python API连接测试
echo "<h2>🐍 Python API连接测试</h2>";
if (defined('PYTHON_API_BASE')) {
    $api_url = PYTHON_API_BASE . '/health';
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $api_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 3);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code === 200) {
        echo "<p>✅ Python API连接成功</p>";
        echo "<p>响应: " . htmlspecialchars($response) . "</p>";
    } else {
        echo "<p>❌ Python API连接失败 (HTTP {$http_code})</p>";
    }
} else {
    echo "<p>⚠️ Python API地址未配置</p>";
}

echo "<hr>";
echo "<p><a href='index.php'>返回首页</a> | <a href='install.php'>安装向导</a> | <a href='debug.php'>详细调试</a></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
h1 { color: #333; background: #fff; padding: 20px; border-radius: 5px; }
h2 { color: #666; border-bottom: 2px solid #ddd; padding-bottom: 5px; }
p { background: #fff; padding: 10px; margin: 5px 0; border-radius: 3px; }
a { color: #007cba; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
