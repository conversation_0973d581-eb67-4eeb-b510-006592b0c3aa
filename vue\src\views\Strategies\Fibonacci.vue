<template>
  <div class="fibonacci-strategy">
    <div class="page-header">
      <h2>斐波那契回撤策略配置</h2>
      <p>配置斐波那契回撤技术指标参数和交易策略</p>
    </div>

    <div class="card">
      <h3 class="mb-md">斐波那契策略开发中...</h3>
      <el-empty description="斐波那契策略配置页面正在开发中">
        <template #image>
          <el-icon size="60" color="#c0c4cc"><Grid /></el-icon>
        </template>
      </el-empty>
    </div>
  </div>
</template>

<script setup>
import { Grid } from '@element-plus/icons-vue'
</script>

<style lang="scss" scoped>
.fibonacci-strategy {
  .page-header {
    margin-bottom: var(--spacing-lg);
    
    h2 {
      margin-bottom: var(--spacing-xs);
      color: var(--text-color-primary);
    }
    
    p {
      color: var(--text-color-secondary);
    }
  }
}
</style>
