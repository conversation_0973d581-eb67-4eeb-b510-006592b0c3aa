<?php
/**
 * BitV MACD智能加仓交易系统 - 交易历史页面
 * 实盘交易版本 - 完整的交易记录和分析界面
 */

session_start();

// 加载配置
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/security.php';

// 检查认证
requireAuth();
requirePermission('view');

$pageTitle = '交易历史';
$currentPage = 'history';

// 获取查询参数
$page = max(1, (int)($_GET['page'] ?? 1));
$limit = max(10, min(100, (int)($_GET['limit'] ?? 50)));
$symbol = sanitizeInput($_GET['symbol'] ?? '');
$side = sanitizeInput($_GET['side'] ?? '');
$status = sanitizeInput($_GET['status'] ?? '');
$date_from = sanitizeInput($_GET['date_from'] ?? '');
$date_to = sanitizeInput($_GET['date_to'] ?? '');
$order_type = sanitizeInput($_GET['order_type'] ?? '');

// 构建查询条件
$filters = [
    'page' => $page,
    'limit' => $limit,
    'symbol' => $symbol,
    'side' => $side,
    'status' => $status,
    'date_from' => $date_from,
    'date_to' => $date_to,
    'order_type' => $order_type
];

// 获取交易历史数据
$tradesData = getTradingHistory($filters);
$tradingStats = getTradingStatistics($filters);
$performanceMetrics = getPerformanceMetrics($filters);

include 'includes/header.php';
?>

<div class="container-fluid">
    <!-- 页面标题和控制按钮 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">
            <i class="fas fa-history text-primary me-2"></i>
            交易历史分析
        </h1>
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-info" onclick="refreshHistory()">
                <i class="fas fa-sync-alt me-2"></i>
                刷新数据
            </button>
            <button type="button" class="btn btn-outline-success" onclick="showAdvancedFilters()">
                <i class="fas fa-filter me-2"></i>
                高级筛选
            </button>
            <button type="button" class="btn btn-outline-primary" onclick="showPerformanceAnalysis()">
                <i class="fas fa-chart-bar me-2"></i>
                绩效分析
            </button>
            <button type="button" class="btn btn-outline-warning" onclick="exportTradingData()">
                <i class="fas fa-download me-2"></i>
                导出数据
            </button>
        </div>
    </div>

    <!-- 交易统计概览 -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                总交易次数
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo $tradingStats['success'] ? formatNumber($tradingStats['data']['total_trades'], 0) : '---'; ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exchange-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                总盈亏 (USDT)
                            </div>
                            <div class="h5 mb-0 font-weight-bold">
                                <span class="<?php 
                                    if ($tradingStats['success']) {
                                        echo $tradingStats['data']['total_pnl'] >= 0 ? 'text-success' : 'text-danger';
                                    } else {
                                        echo 'text-gray-800';
                                    }
                                ?>">
                                    <?php echo $tradingStats['success'] ? formatNumber($tradingStats['data']['total_pnl'], 2) : '---'; ?>
                                </span>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                胜率
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo $tradingStats['success'] ? formatPercentage($tradingStats['data']['win_rate']) : '---'; ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-percentage fa-2x text-gray-300"></i>
                        </div>
                    </div>
                    <div class="row no-gutters align-items-center">
                        <div class="col">
                            <div class="progress progress-sm mr-2">
                                <div class="progress-bar bg-info" role="progressbar" 
                                     style="width: <?php echo $tradingStats['success'] ? $tradingStats['data']['win_rate'] : 0; ?>%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                平均收益率
                            </div>
                            <div class="h5 mb-0 font-weight-bold">
                                <span class="<?php 
                                    if ($tradingStats['success']) {
                                        echo $tradingStats['data']['avg_return'] >= 0 ? 'text-success' : 'text-danger';
                                    } else {
                                        echo 'text-gray-800';
                                    }
                                ?>">
                                    <?php echo $tradingStats['success'] ? formatPercentage($tradingStats['data']['avg_return']) : '---'; ?>
                                </span>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 筛选器 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-filter me-2"></i>
                        交易记录筛选
                    </h6>
                </div>
                <div class="card-body">
                    <form method="GET" action="" id="historyFilterForm">
                        <div class="row">
                            <div class="col-md-2 mb-3">
                                <label for="symbol" class="form-label">交易对</label>
                                <select class="form-select" id="symbol" name="symbol">
                                    <option value="">全部交易对</option>
                                    <?php foreach (SUPPORTED_SYMBOLS as $sym => $info): ?>
                                    <option value="<?php echo $sym; ?>" <?php echo $symbol === $sym ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($info['display_name']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-2 mb-3">
                                <label for="side" class="form-label">交易方向</label>
                                <select class="form-select" id="side" name="side">
                                    <option value="">全部方向</option>
                                    <option value="buy" <?php echo $side === 'buy' ? 'selected' : ''; ?>>买入</option>
                                    <option value="sell" <?php echo $side === 'sell' ? 'selected' : ''; ?>>卖出</option>
                                </select>
                            </div>
                            
                            <div class="col-md-2 mb-3">
                                <label for="order_type" class="form-label">订单类型</label>
                                <select class="form-select" id="order_type" name="order_type">
                                    <option value="">全部类型</option>
                                    <option value="market" <?php echo $order_type === 'market' ? 'selected' : ''; ?>>市价单</option>
                                    <option value="limit" <?php echo $order_type === 'limit' ? 'selected' : ''; ?>>限价单</option>
                                    <option value="stop" <?php echo $order_type === 'stop' ? 'selected' : ''; ?>>止损单</option>
                                    <option value="stop_limit" <?php echo $order_type === 'stop_limit' ? 'selected' : ''; ?>>止损限价单</option>
                                </select>
                            </div>
                            
                            <div class="col-md-2 mb-3">
                                <label for="status" class="form-label">订单状态</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="">全部状态</option>
                                    <option value="filled" <?php echo $status === 'filled' ? 'selected' : ''; ?>>已成交</option>
                                    <option value="partial" <?php echo $status === 'partial' ? 'selected' : ''; ?>>部分成交</option>
                                    <option value="cancelled" <?php echo $status === 'cancelled' ? 'selected' : ''; ?>>已取消</option>
                                    <option value="rejected" <?php echo $status === 'rejected' ? 'selected' : ''; ?>>已拒绝</option>
                                </select>
                            </div>
                            
                            <div class="col-md-2 mb-3">
                                <label for="date_from" class="form-label">开始日期</label>
                                <input type="date" class="form-control" id="date_from" name="date_from" 
                                       value="<?php echo htmlspecialchars($date_from); ?>">
                            </div>
                            
                            <div class="col-md-2 mb-3">
                                <label for="date_to" class="form-label">结束日期</label>
                                <input type="date" class="form-control" id="date_to" name="date_to" 
                                       value="<?php echo htmlspecialchars($date_to); ?>">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-2 mb-3">
                                <label for="limit" class="form-label">每页显示</label>
                                <select class="form-select" id="limit" name="limit">
                                    <option value="25" <?php echo $limit === 25 ? 'selected' : ''; ?>>25条</option>
                                    <option value="50" <?php echo $limit === 50 ? 'selected' : ''; ?>>50条</option>
                                    <option value="100" <?php echo $limit === 100 ? 'selected' : ''; ?>>100条</option>
                                </select>
                            </div>
                            
                            <div class="col-md-10 mb-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search me-2"></i>
                                    筛选
                                </button>
                                <button type="button" class="btn btn-outline-secondary me-2" onclick="resetFilters()">
                                    <i class="fas fa-undo me-2"></i>
                                    重置
                                </button>
                                <button type="button" class="btn btn-outline-info" onclick="saveFilterPreset()">
                                    <i class="fas fa-save me-2"></i>
                                    保存筛选
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 交易记录表格 -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-list me-2"></i>
                        交易记录列表
                        <?php if ($tradesData['success']): ?>
                            <span class="badge bg-primary ms-2">
                                共 <?php echo formatNumber($tradesData['data']['total'], 0); ?> 条记录
                            </span>
                        <?php endif; ?>
                    </h6>
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-primary" onclick="toggleTableView('detailed')" id="detailedViewBtn">
                            详细视图
                        </button>
                        <button type="button" class="btn btn-outline-primary active" onclick="toggleTableView('compact')" id="compactViewBtn">
                            紧凑视图
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <?php if ($tradesData['success'] && !empty($tradesData['data']['trades'])): ?>
                        <div class="table-responsive">
                            <table class="table table-hover" id="tradesTable">
                                <thead>
                                    <tr>
                                        <th>时间</th>
                                        <th>交易对</th>
                                        <th>方向</th>
                                        <th>类型</th>
                                        <th>数量</th>
                                        <th>价格</th>
                                        <th>成交金额</th>
                                        <th>手续费</th>
                                        <th>状态</th>
                                        <th class="detailed-column" style="display: none;">订单ID</th>
                                        <th class="detailed-column" style="display: none;">会话ID</th>
                                        <th class="detailed-column" style="display: none;">备注</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($tradesData['data']['trades'] as $trade): ?>
                                        <tr class="trade-row" data-trade-id="<?php echo $trade['trade_id']; ?>">
                                            <td>
                                                <div class="fw-bold"><?php echo formatTime($trade['timestamp'], 'Y-m-d'); ?></div>
                                                <small class="text-muted"><?php echo formatTime($trade['timestamp'], 'H:i:s'); ?></small>
                                            </td>
                                            <td>
                                                <strong><?php echo htmlspecialchars($trade['symbol']); ?></strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo $trade['side'] === 'buy' ? 'success' : 'danger'; ?>">
                                                    <?php echo strtoupper($trade['side']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">
                                                    <?php echo strtoupper($trade['type']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php echo formatNumber($trade['amount'], 6); ?>
                                                <br>
                                                <small class="text-muted"><?php echo $trade['base_currency']; ?></small>
                                            </td>
                                            <td>
                                                $<?php echo formatNumber($trade['price'], 2); ?>
                                            </td>
                                            <td>
                                                $<?php echo formatNumber($trade['total'], 2); ?>
                                            </td>
                                            <td>
                                                $<?php echo formatNumber($trade['fee'], 4); ?>
                                                <br>
                                                <small class="text-muted"><?php echo formatPercentage($trade['fee_rate']); ?></small>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo getTradeStatusColor($trade['status']); ?>">
                                                    <?php echo getTradeStatusText($trade['status']); ?>
                                                </span>
                                            </td>
                                            <td class="detailed-column" style="display: none;">
                                                <small class="font-monospace"><?php echo htmlspecialchars($trade['order_id']); ?></small>
                                            </td>
                                            <td class="detailed-column" style="display: none;">
                                                <?php if ($trade['session_id']): ?>
                                                    <a href="trading.php?session=<?php echo $trade['session_id']; ?>" class="text-decoration-none">
                                                        <small class="font-monospace"><?php echo substr($trade['session_id'], 0, 8); ?>...</small>
                                                    </a>
                                                <?php else: ?>
                                                    <small class="text-muted">---</small>
                                                <?php endif; ?>
                                            </td>
                                            <td class="detailed-column" style="display: none;">
                                                <small class="text-muted"><?php echo htmlspecialchars($trade['notes'] ?? '---'); ?></small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <button type="button" class="btn btn-outline-info" 
                                                            onclick="viewTradeDetails('<?php echo $trade['trade_id']; ?>')"
                                                            title="查看详情">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <?php if ($trade['status'] === 'filled'): ?>
                                                    <button type="button" class="btn btn-outline-success" 
                                                            onclick="downloadTradeReceipt('<?php echo $trade['trade_id']; ?>')"
                                                            title="下载凭证">
                                                        <i class="fas fa-receipt"></i>
                                                    </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        <?php if ($tradesData['data']['total_pages'] > 1): ?>
                        <nav aria-label="交易记录分页">
                            <ul class="pagination justify-content-center">
                                <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?<?php echo http_build_query(array_merge($filters, ['page' => $page - 1])); ?>">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                </li>
                                <?php endif; ?>

                                <?php
                                $start = max(1, $page - 2);
                                $end = min($tradesData['data']['total_pages'], $page + 2);
                                
                                for ($i = $start; $i <= $end; $i++):
                                ?>
                                <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                    <a class="page-link" href="?<?php echo http_build_query(array_merge($filters, ['page' => $i])); ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                                <?php endfor; ?>

                                <?php if ($page < $tradesData['data']['total_pages']): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?<?php echo http_build_query(array_merge($filters, ['page' => $page + 1])); ?>">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                        <?php endif; ?>

                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-history fa-3x text-muted mb-3"></i>
                            <h5>暂无交易记录</h5>
                            <p class="text-muted">
                                <?php if (array_filter($filters)): ?>
                                    当前筛选条件下没有找到交易记录，请尝试调整筛选条件。
                                <?php else: ?>
                                    您还没有进行过任何交易。
                                <?php endif; ?>
                            </p>
                            <?php if (!array_filter($filters)): ?>
                            <a href="trading.php" class="btn btn-primary">
                                <i class="fas fa-rocket me-2"></i>
                                开始交易
                            </a>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 交易详情模态框 -->
<div class="modal fade" id="tradeDetailsModal" tabindex="-1" aria-labelledby="tradeDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="tradeDetailsModalLabel">
                    <i class="fas fa-exchange-alt me-2"></i>
                    交易详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="tradeDetailsContent">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <div class="mt-2">正在加载交易详情...</div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="downloadTradeReceipt()">
                    <i class="fas fa-download me-2"></i>
                    下载凭证
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 绩效分析模态框 -->
<div class="modal fade" id="performanceAnalysisModal" tabindex="-1" aria-labelledby="performanceAnalysisModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="performanceAnalysisModalLabel">
                    <i class="fas fa-chart-bar me-2"></i>
                    绩效分析报告
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-3">
                        <h6>分析参数</h6>
                        <form id="performanceAnalysisForm">
                            <div class="mb-3">
                                <label for="analysis_period" class="form-label">分析周期</label>
                                <select class="form-select" id="analysis_period" name="analysis_period">
                                    <option value="7d">最近7天</option>
                                    <option value="30d" selected>最近30天</option>
                                    <option value="90d">最近90天</option>
                                    <option value="1y">最近1年</option>
                                    <option value="all">全部时间</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="analysis_symbol" class="form-label">交易对</label>
                                <select class="form-select" id="analysis_symbol" name="analysis_symbol">
                                    <option value="">全部交易对</option>
                                    <?php foreach (SUPPORTED_SYMBOLS as $sym => $info): ?>
                                    <option value="<?php echo $sym; ?>">
                                        <?php echo htmlspecialchars($info['display_name']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="analysis_strategy" class="form-label">策略类型</label>
                                <select class="form-select" id="analysis_strategy" name="analysis_strategy">
                                    <option value="">全部策略</option>
                                    <option value="macd">MACD策略</option>
                                    <option value="manual">手动交易</option>
                                </select>
                            </div>
                            <button type="button" class="btn btn-primary" onclick="generatePerformanceReport()">
                                <i class="fas fa-chart-line me-2"></i>
                                生成报告
                            </button>
                        </form>
                    </div>
                    <div class="col-md-9">
                        <div id="performanceReportContent">
                            <div class="text-center py-5">
                                <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                                <h6>绩效分析报告</h6>
                                <p class="text-muted">选择分析参数后点击"生成报告"查看详细绩效分析</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 高级筛选模态框 -->
<div class="modal fade" id="advancedFiltersModal" tabindex="-1" aria-labelledby="advancedFiltersModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="advancedFiltersModalLabel">
                    <i class="fas fa-filter me-2"></i>
                    高级筛选条件
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="advancedFiltersForm">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>金额范围</h6>
                            <div class="mb-3">
                                <label for="min_amount" class="form-label">最小交易金额 (USDT)</label>
                                <input type="number" class="form-control" id="min_amount" name="min_amount"
                                       min="0" step="0.01" placeholder="0.00">
                            </div>
                            <div class="mb-3">
                                <label for="max_amount" class="form-label">最大交易金额 (USDT)</label>
                                <input type="number" class="form-control" id="max_amount" name="max_amount"
                                       min="0" step="0.01" placeholder="无限制">
                            </div>
                        </div>

                        <div class="col-md-6">
                            <h6>手续费范围</h6>
                            <div class="mb-3">
                                <label for="min_fee" class="form-label">最小手续费 (USDT)</label>
                                <input type="number" class="form-control" id="min_fee" name="min_fee"
                                       min="0" step="0.0001" placeholder="0.0000">
                            </div>
                            <div class="mb-3">
                                <label for="max_fee" class="form-label">最大手续费 (USDT)</label>
                                <input type="number" class="form-control" id="max_fee" name="max_fee"
                                       min="0" step="0.0001" placeholder="无限制">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <h6>时间范围</h6>
                            <div class="mb-3">
                                <label for="time_from" class="form-label">开始时间</label>
                                <input type="datetime-local" class="form-control" id="time_from" name="time_from">
                            </div>
                            <div class="mb-3">
                                <label for="time_to" class="form-label">结束时间</label>
                                <input type="datetime-local" class="form-control" id="time_to" name="time_to">
                            </div>
                        </div>

                        <div class="col-md-6">
                            <h6>其他条件</h6>
                            <div class="mb-3">
                                <label for="exchange" class="form-label">交易所</label>
                                <select class="form-select" id="exchange" name="exchange">
                                    <option value="">全部交易所</option>
                                    <option value="okx">OKX</option>
                                    <option value="gateio">Gate.io</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="only_profitable" name="only_profitable">
                                    <label class="form-check-label" for="only_profitable">
                                        仅显示盈利交易
                                    </label>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="only_losses" name="only_losses">
                                    <label class="form-check-label" for="only_losses">
                                        仅显示亏损交易
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-outline-warning" onclick="resetAdvancedFilters()">
                    <i class="fas fa-undo me-2"></i>
                    重置
                </button>
                <button type="button" class="btn btn-primary" onclick="applyAdvancedFilters()">
                    <i class="fas fa-filter me-2"></i>
                    应用筛选
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// 全局变量
let currentTradeId = null;
let filterPresets = {};

// 页面加载完成后执行
$(document).ready(function() {
    // 初始化页面
    initializeHistoryPage();

    // 绑定事件
    bindHistoryEvents();

    // 加载筛选预设
    loadFilterPresets();

    // 设置默认日期范围
    setDefaultDateRange();
});

// 初始化历史页面
function initializeHistoryPage() {
    // 设置表格排序
    setupTableSorting();

    // 初始化图表
    initializeCharts();

    // 设置自动刷新
    setupAutoRefresh();
}

// 绑定历史页面事件
function bindHistoryEvents() {
    // 筛选表单提交
    $('#historyFilterForm').on('submit', function(e) {
        e.preventDefault();
        applyFilters();
    });

    // 表格视图切换
    $('.btn-group button').on('click', function() {
        $(this).addClass('active').siblings().removeClass('active');
    });

    // 日期范围快速选择
    setupDateRangeShortcuts();
}

// 设置表格排序
function setupTableSorting() {
    $('#tradesTable th').each(function(index) {
        if (index < 9) { // 前9列可排序
            $(this).addClass('sortable').css('cursor', 'pointer');
            $(this).append(' <i class="fas fa-sort text-muted"></i>');
        }
    });

    $('#tradesTable th.sortable').on('click', function() {
        const column = $(this).index();
        const currentSort = $(this).data('sort') || 'asc';
        const newSort = currentSort === 'asc' ? 'desc' : 'asc';

        // 更新排序图标
        $('#tradesTable th i').removeClass('fa-sort-up fa-sort-down').addClass('fa-sort');
        $(this).find('i').removeClass('fa-sort').addClass(newSort === 'asc' ? 'fa-sort-up' : 'fa-sort-down');
        $(this).data('sort', newSort);

        // 执行排序
        sortTable(column, newSort);
    });
}

// 表格排序功能
function sortTable(column, direction) {
    const table = document.getElementById('tradesTable');
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));

    rows.sort((a, b) => {
        const aValue = a.cells[column].textContent.trim();
        const bValue = b.cells[column].textContent.trim();

        // 数字排序
        if (!isNaN(aValue) && !isNaN(bValue)) {
            return direction === 'asc' ? aValue - bValue : bValue - aValue;
        }

        // 文本排序
        return direction === 'asc' ?
            aValue.localeCompare(bValue) :
            bValue.localeCompare(aValue);
    });

    // 重新插入排序后的行
    rows.forEach(row => tbody.appendChild(row));
}

// 切换表格视图
function toggleTableView(viewType) {
    const detailedColumns = document.querySelectorAll('.detailed-column');

    if (viewType === 'detailed') {
        detailedColumns.forEach(col => col.style.display = '');
        $('#detailedViewBtn').addClass('active');
        $('#compactViewBtn').removeClass('active');
    } else {
        detailedColumns.forEach(col => col.style.display = 'none');
        $('#compactViewBtn').addClass('active');
        $('#detailedViewBtn').removeClass('active');
    }

    // 保存视图偏好
    localStorage.setItem('bitv_table_view', viewType);
}

// 查看交易详情
function viewTradeDetails(tradeId) {
    currentTradeId = tradeId;
    showLoading('正在加载交易详情...');

    fetch(window.BITV.API_BASE + '/api/trades/' + tradeId, {
        headers: {
            'Authorization': 'Bearer ' + window.BITV.API_TOKEN
        }
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        if (data.success) {
            displayTradeDetails(data.data);
            $('#tradeDetailsModal').modal('show');
        } else {
            showNotification('获取交易详情失败: ' + (data.error || '未知错误'), 'error');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('获取交易详情失败:', error);
        showNotification('网络错误，请重试', 'error');
    });
}

// 显示交易详情
function displayTradeDetails(tradeData) {
    const container = document.getElementById('tradeDetailsContent');

    const html = `
        <div class="row">
            <div class="col-md-6">
                <h6>基本信息</h6>
                <table class="table table-sm">
                    <tr><td>交易ID:</td><td class="font-monospace">${tradeData.trade_id}</td></tr>
                    <tr><td>订单ID:</td><td class="font-monospace">${tradeData.order_id}</td></tr>
                    <tr><td>交易对:</td><td>${tradeData.symbol}</td></tr>
                    <tr><td>交易方向:</td><td><span class="badge bg-${tradeData.side === 'buy' ? 'success' : 'danger'}">${tradeData.side.toUpperCase()}</span></td></tr>
                    <tr><td>订单类型:</td><td><span class="badge bg-secondary">${tradeData.type.toUpperCase()}</span></td></tr>
                    <tr><td>交易时间:</td><td>${formatTime(tradeData.timestamp)}</td></tr>
                    <tr><td>交易所:</td><td>${tradeData.exchange}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>交易详情</h6>
                <table class="table table-sm">
                    <tr><td>交易数量:</td><td>${formatNumber(tradeData.amount, 6)} ${tradeData.base_currency}</td></tr>
                    <tr><td>交易价格:</td><td>$${formatNumber(tradeData.price, 2)}</td></tr>
                    <tr><td>成交金额:</td><td>$${formatNumber(tradeData.total, 2)}</td></tr>
                    <tr><td>手续费:</td><td>$${formatNumber(tradeData.fee, 4)} (${formatPercentage(tradeData.fee_rate)})</td></tr>
                    <tr><td>状态:</td><td><span class="badge bg-${getTradeStatusColor(tradeData.status)}">${getTradeStatusText(tradeData.status)}</span></td></tr>
                    <tr><td>流动性:</td><td>${tradeData.liquidity === 'maker' ? 'Maker' : 'Taker'}</td></tr>
                </table>
            </div>
        </div>

        ${tradeData.session_id ? `
        <div class="row mt-4">
            <div class="col-12">
                <h6>关联会话信息</h6>
                <table class="table table-sm">
                    <tr><td>会话ID:</td><td class="font-monospace">${tradeData.session_id}</td></tr>
                    <tr><td>策略类型:</td><td>${tradeData.strategy_type || 'MACD'}</td></tr>
                    <tr><td>会话状态:</td><td><span class="badge bg-${getStatusColor(tradeData.session_status)}">${getStatusText(tradeData.session_status)}</span></td></tr>
                    <tr><td>操作类型:</td><td>${tradeData.operation_type || '开仓'}</td></tr>
                </table>
            </div>
        </div>
        ` : ''}

        ${tradeData.market_data ? `
        <div class="row mt-4">
            <div class="col-12">
                <h6>市场数据</h6>
                <table class="table table-sm">
                    <tr><td>市场价格:</td><td>$${formatNumber(tradeData.market_data.market_price, 2)}</td></tr>
                    <tr><td>买一价:</td><td>$${formatNumber(tradeData.market_data.bid_price, 2)}</td></tr>
                    <tr><td>卖一价:</td><td>$${formatNumber(tradeData.market_data.ask_price, 2)}</td></tr>
                    <tr><td>价差:</td><td>${formatPercentage(tradeData.market_data.spread)}</td></tr>
                    <tr><td>24h成交量:</td><td>${formatNumber(tradeData.market_data.volume_24h, 0)}</td></tr>
                </table>
            </div>
        </div>
        ` : ''}

        ${tradeData.notes ? `
        <div class="row mt-4">
            <div class="col-12">
                <h6>备注信息</h6>
                <div class="alert alert-info">
                    ${tradeData.notes}
                </div>
            </div>
        </div>
        ` : ''}
    `;

    container.innerHTML = html;
}

// 下载交易凭证
function downloadTradeReceipt(tradeId = null) {
    const id = tradeId || currentTradeId;
    if (!id) return;

    showLoading('正在生成交易凭证...');

    fetch(window.BITV.API_BASE + '/api/trades/' + id + '/receipt', {
        method: 'POST',
        headers: {
            'Authorization': 'Bearer ' + window.BITV.API_TOKEN
        }
    })
    .then(response => response.blob())
    .then(blob => {
        hideLoading();

        // 创建下载链接
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `trade_receipt_${id}.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        showNotification('交易凭证下载成功', 'success');
    })
    .catch(error => {
        hideLoading();
        console.error('下载交易凭证失败:', error);
        showNotification('下载失败，请重试', 'error');
    });
}

// 显示绩效分析
function showPerformanceAnalysis() {
    $('#performanceAnalysisModal').modal('show');
}

// 生成绩效报告
function generatePerformanceReport() {
    const formData = new FormData(document.getElementById('performanceAnalysisForm'));
    const params = Object.fromEntries(formData);

    showLoading('正在生成绩效报告...');

    fetch(window.BITV.API_BASE + '/api/analysis/performance', {
        method: 'POST',
        headers: {
            'Authorization': 'Bearer ' + window.BITV.API_TOKEN,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(params)
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        if (data.success) {
            displayPerformanceReport(data.data);
        } else {
            showNotification('生成绩效报告失败: ' + (data.error || '未知错误'), 'error');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('生成绩效报告失败:', error);
        showNotification('网络错误，请重试', 'error');
    });
}

// 显示绩效报告
function displayPerformanceReport(reportData) {
    const container = document.getElementById('performanceReportContent');

    const html = `
        <div class="row">
            <div class="col-md-6">
                <h6>总体表现</h6>
                <table class="table table-sm">
                    <tr><td>总交易次数:</td><td>${formatNumber(reportData.total_trades, 0)}</td></tr>
                    <tr><td>盈利交易:</td><td class="text-success">${formatNumber(reportData.winning_trades, 0)}</td></tr>
                    <tr><td>亏损交易:</td><td class="text-danger">${formatNumber(reportData.losing_trades, 0)}</td></tr>
                    <tr><td>胜率:</td><td>${formatPercentage(reportData.win_rate)}</td></tr>
                    <tr><td>总盈亏:</td><td class="${reportData.total_pnl >= 0 ? 'text-success' : 'text-danger'}">${formatNumber(reportData.total_pnl, 2)} USDT</td></tr>
                    <tr><td>平均收益:</td><td class="${reportData.avg_return >= 0 ? 'text-success' : 'text-danger'}">${formatPercentage(reportData.avg_return)}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>风险指标</h6>
                <table class="table table-sm">
                    <tr><td>最大回撤:</td><td class="text-danger">${formatPercentage(reportData.max_drawdown)}</td></tr>
                    <tr><td>夏普比率:</td><td>${formatNumber(reportData.sharpe_ratio, 2)}</td></tr>
                    <tr><td>盈亏比:</td><td>${formatNumber(reportData.profit_loss_ratio, 2)}</td></tr>
                    <tr><td>最大连续盈利:</td><td class="text-success">${reportData.max_consecutive_wins}</td></tr>
                    <tr><td>最大连续亏损:</td><td class="text-danger">${reportData.max_consecutive_losses}</td></tr>
                    <tr><td>平均持仓时间:</td><td>${reportData.avg_holding_time}</td></tr>
                </table>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <h6>收益曲线</h6>
                <canvas id="performanceChart" width="400" height="200"></canvas>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-md-6">
                <h6>交易对表现</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr><th>交易对</th><th>交易次数</th><th>胜率</th><th>总盈亏</th></tr>
                        </thead>
                        <tbody>
                            ${reportData.symbol_performance.map(item => `
                                <tr>
                                    <td>${item.symbol}</td>
                                    <td>${item.trades}</td>
                                    <td>${formatPercentage(item.win_rate)}</td>
                                    <td class="${item.pnl >= 0 ? 'text-success' : 'text-danger'}">${formatNumber(item.pnl, 2)}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="col-md-6">
                <h6>月度表现</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr><th>月份</th><th>交易次数</th><th>胜率</th><th>月收益</th></tr>
                        </thead>
                        <tbody>
                            ${reportData.monthly_performance.map(item => `
                                <tr>
                                    <td>${item.month}</td>
                                    <td>${item.trades}</td>
                                    <td>${formatPercentage(item.win_rate)}</td>
                                    <td class="${item.return >= 0 ? 'text-success' : 'text-danger'}">${formatPercentage(item.return)}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    `;

    container.innerHTML = html;

    // 绘制收益曲线图
    drawPerformanceChart(reportData.equity_curve);
}

// 绘制绩效图表
function drawPerformanceChart(equityCurve) {
    const ctx = document.getElementById('performanceChart').getContext('2d');

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: equityCurve.map(point => formatTime(point.timestamp, 'M-d')),
            datasets: [{
                label: '账户权益',
                data: equityCurve.map(point => point.equity),
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.1)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: false,
                    ticks: {
                        callback: function(value) {
                            return '$' + formatNumber(value, 2);
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return '权益: $' + formatNumber(context.parsed.y, 2);
                        }
                    }
                }
            }
        }
    });
}

// 工具函数
function getTradeStatusColor(status) {
    const colors = {
        'filled': 'success',
        'partial': 'warning',
        'cancelled': 'secondary',
        'rejected': 'danger',
        'pending': 'info'
    };
    return colors[status] || 'secondary';
}

function getTradeStatusText(status) {
    const texts = {
        'filled': '已成交',
        'partial': '部分成交',
        'cancelled': '已取消',
        'rejected': '已拒绝',
        'pending': '待成交'
    };
    return texts[status] || '未知';
}

// 页面卸载时清理
window.addEventListener('beforeunload', function() {
    // 清理定时器等资源
});
</script>

<?php include 'includes/footer.php'; ?>
