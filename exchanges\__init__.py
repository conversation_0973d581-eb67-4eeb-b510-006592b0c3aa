"""
交易所模块
严格遵守交易所完全分离原则
提供统一的异步交易所接口
"""

from .base_exchange import (
    BaseExchange,
    KlineData,
    PositionInfo,
    OrderInfo,
    OrderSide,
    OrderType,
    PositionSide
)

from .okx_exchange import OKXExchange
from .gateio_exchange import GateIOExchange
from .exchange_factory import ExchangeFactory, ExchangeManager, exchange_manager

__all__ = [
    # 基础类和数据结构
    'BaseExchange',
    'KlineData',
    'PositionInfo',
    'OrderInfo',
    'OrderSide',
    'OrderType',
    'PositionSide',
    
    # 交易所实现
    'OKXExchange',
    'GateIOExchange',
    
    # 工厂和管理器
    'ExchangeFactory',
    'ExchangeManager',
    'exchange_manager'
]

# 版本信息
__version__ = '1.0.0'

# 支持的交易所列表
SUPPORTED_EXCHANGES = ['okx', 'gateio']
