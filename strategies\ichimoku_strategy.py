"""
一目均衡表（Ichimoku）交易策略
基于一目均衡表指标的完整交易策略实现
"""

import asyncio
import logging
from typing import List, Optional, Dict, Any, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import time

from indicators.ichimoku_calculator import IchimokuCalculator, IchimokuData, IchimokuSignal
from exchanges.base_exchange import BaseExchange, OrderSide, PositionSide
from utils.concurrency_monitor import monitor_task, TaskType

logger = logging.getLogger(__name__)

@dataclass
class IchimokuConfig:
    """一目均衡表策略配置"""
    
    # 一目均衡表指标参数
    tenkan_period: int = 9              # 转换线周期
    kijun_period: int = 26              # 基准线周期
    senkou_b_period: int = 52           # 先行带B周期
    displacement: int = 26              # 位移周期
    
    # 交易参数
    custom_symbol: str = "BTC-USDT-SWAP"    # 自定义交易对
    custom_leverage: int = 10               # 自定义杠杆倍数
    initial_margin: float = 500.0           # 初始保证金 (USDT)
    quantity_precision: int = 3             # 数量精度
    
    # 信号过滤参数
    min_signal_strength: float = 0.5        # 最小信号强度
    min_cloud_thickness: float = 0.1        # 最小云层厚度（%）
    require_price_above_cloud: bool = True   # 做多时要求价格在云层上方
    require_price_below_cloud: bool = True   # 做空时要求价格在云层下方
    
    # 风险控制参数
    take_profit_percent: float = 2.0        # 止盈百分比
    stop_loss_percent: float = 1.5          # 止损百分比
    max_position_time: int = 24             # 最大持仓时间（小时）
    
    # 立即开仓功能
    immediate_open_enabled: bool = False    # 启用立即开仓
    immediate_open_interval: int = 30       # 检测间隔（秒）
    immediate_market_order: bool = True     # 立即市价下单
    
    # 时间参数
    position_check_interval: int = 10       # 持仓检查间隔（秒）
    order_cooldown: int = 60               # 下单冷却时间（秒）
    
    # 交易所配置
    exchange_name: str = "okx"             # 交易所名称
    
    def get_required_data_count(self) -> int:
        """获取所需的最小数据点数"""
        return max(self.senkou_b_period, self.kijun_period) + self.displacement
    
    def __str__(self) -> str:
        """配置信息的字符串表示"""
        return f"""一目均衡表策略配置:
        指标参数: 转换线={self.tenkan_period}, 基准线={self.kijun_period}, 先行带B={self.senkou_b_period}
        交易参数: 保证金={self.initial_margin}, 杠杆={self.custom_leverage}x
        风险控制: 止盈={self.take_profit_percent}%, 止损={self.stop_loss_percent}%
        信号过滤: 最小强度={self.min_signal_strength}, 最小云层厚度={self.min_cloud_thickness}%
        立即开仓: {'启用' if self.immediate_open_enabled else '禁用'}
        交易对: {self.custom_symbol} ({self.exchange_name})"""

class IchimokuStrategy:
    """一目均衡表交易策略"""
    
    def __init__(self, config: IchimokuConfig):
        """
        初始化一目均衡表策略
        
        Args:
            config: 策略配置
        """
        self.config = config
        self.calculator = IchimokuCalculator(
            tenkan_period=config.tenkan_period,
            kijun_period=config.kijun_period,
            senkou_b_period=config.senkou_b_period,
            displacement=config.displacement
        )
        
        # 交易所管理器
        self.exchange_manager: Optional[BaseExchange] = None
        
        # 策略状态
        self.is_running = False
        self.current_position = None
        self.last_order_time = 0
        self.last_signal_time = 0
        self.total_trades = 0
        
        # 监控任务
        self._position_monitor_task = None
        self._immediate_open_task = None
        
        logger.info(f"一目均衡表策略初始化完成: {config}")
    
    async def start(self, exchange_manager: BaseExchange):
        """启动策略"""
        try:
            self.exchange_manager = exchange_manager
            self.is_running = True
            
            logger.info("🚀 启动一目均衡表策略")
            
            # 启动持仓监控
            self._position_monitor_task = asyncio.create_task(self._start_position_monitor())
            
            # 启动立即开仓监控（如果启用）
            if self.config.immediate_open_enabled:
                self._immediate_open_task = asyncio.create_task(self._start_immediate_open_monitor())
            
            logger.info("✅ 一目均衡表策略启动成功")
            
        except Exception as e:
            logger.error(f"❌ 启动一目均衡表策略失败: {e}")
            await self.stop()
            raise
    
    async def stop(self):
        """停止策略"""
        try:
            logger.info("🛑 停止一目均衡表策略")
            
            self.is_running = False
            
            # 停止监控任务
            if self._position_monitor_task:
                self._position_monitor_task.cancel()
                try:
                    await self._position_monitor_task
                except asyncio.CancelledError:
                    pass
            
            if self._immediate_open_task:
                self._immediate_open_task.cancel()
                try:
                    await self._immediate_open_task
                except asyncio.CancelledError:
                    pass
            
            logger.info("✅ 一目均衡表策略已停止")
            
        except Exception as e:
            logger.error(f"❌ 停止一目均衡表策略异常: {e}")
    
    async def _start_position_monitor(self):
        """启动持仓监控"""
        try:
            logger.info("👁️ 启动一目均衡表持仓监控")

            while self.is_running:
                try:
                    await self._check_position_status()
                    # 检查平仓机会
                    await self._check_close_position_opportunity()
                    await asyncio.sleep(self.config.position_check_interval)
                except Exception as e:
                    logger.error(f"❌ 持仓监控异常: {e}")
                    await asyncio.sleep(5)

        except Exception as e:
            logger.error(f"❌ 持仓监控启动失败: {e}")
    
    async def _start_immediate_open_monitor(self):
        """启动立即开仓监控"""
        try:
            logger.info("🎯 启动一目均衡表立即开仓监控")
            
            while self.is_running and self.config.immediate_open_enabled:
                try:
                    await self._check_immediate_open_opportunity()
                    await asyncio.sleep(self.config.immediate_open_interval)
                except Exception as e:
                    logger.error(f"❌ 立即开仓监控异常: {e}")
                    await asyncio.sleep(5)
                    
        except Exception as e:
            logger.error(f"❌ 立即开仓监控启动失败: {e}")
    
    @monitor_task(TaskType.OPENING_CHECK)
    async def _check_immediate_open_opportunity(self):
        """检查立即开仓机会"""
        try:
            # 检查是否有持仓
            if await self._has_position():
                logger.debug("已有持仓，跳过开仓检查")
                return

            # 检查冷却时间
            if not self._check_order_cooldown():
                logger.debug("冷却时间未到，跳过开仓检查")
                return

            # 获取当前价格数据
            price_data = await self._get_price_data()
            if (not price_data or
                len(price_data['closes']) < self.config.get_required_data_count() or
                len(price_data['highs']) < self.config.get_required_data_count() or
                len(price_data['lows']) < self.config.get_required_data_count()):
                logger.warning("⚠️ 价格数据不足，无法进行一目均衡表分析")
                return

            # 检测一目均衡表信号
            has_signal, signal_type, signal_strength = self.calculator.detect_ichimoku_signal(
                price_data['highs'], price_data['lows'], price_data['closes'])

            if not has_signal:
                logger.debug("未检测到一目均衡表信号")
                return

            # 验证信号强度
            if signal_strength < self.config.min_signal_strength:
                logger.debug(f"信号强度不足: {signal_strength:.3f} < {self.config.min_signal_strength}")
                return

            # 获取详细的一目均衡表数据进行额外验证
            ichimoku_data = await self.calculator.calculate_ichimoku(
                price_data['highs'], price_data['lows'], price_data['closes'])
            
            if not ichimoku_data:
                logger.warning("无法获取详细一目均衡表数据")
                return

            # 验证云层厚度
            if ichimoku_data.cloud_thickness < self.config.min_cloud_thickness:
                logger.debug(f"云层厚度不足: {ichimoku_data.cloud_thickness:.3f}% < {self.config.min_cloud_thickness}%")
                return

            # 验证价格与云层关系
            if signal_type == "long" and self.config.require_price_above_cloud:
                if ichimoku_data.price_vs_cloud != "云层上方":
                    logger.debug(f"做多信号但价格不在云层上方: {ichimoku_data.price_vs_cloud}")
                    return
            elif signal_type == "short" and self.config.require_price_below_cloud:
                if ichimoku_data.price_vs_cloud != "云层下方":
                    logger.debug(f"做空信号但价格不在云层下方: {ichimoku_data.price_vs_cloud}")
                    return

            logger.info(f"🎯 [一目均衡表立即开仓] 检测到{signal_type}信号，强度: {signal_strength:.3f}")
            logger.info(f"📊 [一目均衡表数据] {ichimoku_data.trend_direction}, {ichimoku_data.price_vs_cloud}")

            # 执行立即开仓
            await self._execute_immediate_open(signal_type, signal_strength, ichimoku_data)

        except Exception as e:
            logger.error(f"❌ 检查一目均衡表立即开仓机会异常: {e}")
    
    async def _execute_immediate_open(self, direction: str, signal_strength: float, ichimoku_data: IchimokuData):
        """执行立即开仓"""
        try:
            logger.info(f"🚀 [一目均衡表立即开仓] 开始执行{direction}开仓")

            # 获取当前价格
            current_price = await self._get_current_price()
            if not current_price:
                logger.error("❌ 无法获取当前价格")
                return

            # 计算开仓数量
            quantity = self._calculate_position_size(current_price)
            if quantity <= 0:
                logger.error("❌ 计算的开仓数量无效")
                return

            # 计算止损价格
            if direction == "long":
                stop_loss_price = current_price * (1 - self.config.stop_loss_percent / 100)
            else:
                stop_loss_price = current_price * (1 + self.config.stop_loss_percent / 100)

            logger.info(f"📊 [开仓参数] 方向: {direction}, 数量: {quantity}, 价格: {current_price:.4f}")
            logger.info(f"📊 [风险控制] 止损价: {stop_loss_price:.4f}")

            # 执行开仓
            order_result = await self._place_market_order(direction, quantity, current_price)
            if order_result:
                logger.info(f"✅ [一目均衡表立即开仓] 开仓成功: {direction}仓位，价格: {current_price:.4f}，数量: {quantity}")

                # 设置止损订单
                await self._place_stop_loss_order(direction, quantity, stop_loss_price)

                # 更新策略状态
                self.last_order_time = time.time()
                self.last_signal_time = time.time()
                self.total_trades += 1

                # 记录信号强度用于后续分析
                if not hasattr(self, 'signal_history'):
                    self.signal_history = []
                self.signal_history.append({
                    'timestamp': datetime.now(),
                    'direction': direction,
                    'signal_strength': signal_strength,
                    'price': current_price,
                    'quantity': quantity,
                    'ichimoku_data': ichimoku_data
                })

                # 禁用立即开仓功能（开仓后自动禁用）
                self.config.immediate_open_enabled = False
                logger.info("🔒 [一目均衡表立即开仓] 开仓后自动禁用立即开仓功能")

            else:
                logger.error("❌ [一目均衡表立即开仓] 开仓失败")

        except Exception as e:
            logger.error(f"❌ 执行一目均衡表立即开仓异常: {e}")
    
    async def _place_market_order(self, direction: str, quantity: float, price: float) -> bool:
        """下市价单"""
        try:
            if not self.exchange_manager:
                logger.error("❌ 交易所管理器未设置")
                return False

            logger.info(f"📈 [一目均衡表市价下单] 方向: {direction}, 数量: {quantity}, 参考价格: {price:.4f}")
            logger.info(f"📊 [交易对] 使用自定义交易对: {self.config.custom_symbol}")
            logger.info(f"📊 [杠杆] 使用自定义杠杆: {self.config.custom_leverage}x")

            # 确定订单方向
            if direction == "long":
                order_side = OrderSide.BUY
            elif direction == "short":
                order_side = OrderSide.SELL
            else:
                logger.error(f"❌ 未知的开仓方向: {direction}")
                return False

            # 调用交易所API下单
            order_result = await self.exchange_manager.place_market_order(
                symbol=self.config.custom_symbol,
                side=order_side,
                amount=quantity,
                reduce_only=False  # 开仓单，不是平仓单
            )

            if order_result and hasattr(order_result, 'order_id') and order_result.order_id:
                logger.info(f"✅ [一目均衡表开仓] 开仓订单已提交: 订单ID {order_result.order_id}")
                
                # 等待一段时间让订单执行
                await asyncio.sleep(1)
                
                # 验证开仓是否成功
                positions_after = await self.exchange_manager.get_positions(self.config.custom_symbol)
                if positions_after and any(p.size > 0 for p in positions_after):
                    logger.info(f"✅ [一目均衡表开仓] 开仓验证成功：已建立持仓")
                    return True
                else:
                    logger.warning(f"⚠️ [一目均衡表开仓] 开仓可能未成功，未检测到持仓")
                    return False
            else:
                logger.error(f"❌ [一目均衡表开仓] 开仓订单提交失败")
                return False

        except Exception as e:
            logger.error(f"❌ 一目均衡表市价下单异常: {e}")
            return False

    async def _place_stop_loss_order(self, direction: str, quantity: float, stop_loss_price: float):
        """设置止损订单"""
        try:
            if not self.exchange_manager:
                logger.error("❌ 交易所管理器未设置")
                return

            # 确定止损订单方向（与开仓方向相反）
            stop_side = "sell" if direction == "long" else "buy"

            logger.info(f"🛡️ [止损订单] 设置止损: 方向={stop_side}, 数量={quantity}, 止损价={stop_loss_price:.4f}")

            # 根据交易所类型选择止损订单方式
            exchange_name = self.config.exchange_name.lower()

            if exchange_name == "okx":
                await self._place_okx_algo_stop_loss_order(stop_side, quantity, stop_loss_price)
            elif exchange_name == "gate":
                await self._place_gate_stop_loss_order(stop_side, quantity, stop_loss_price)
            else:
                # 默认使用限价单作为止损
                await self._place_limit_stop_loss_order(stop_side, quantity, stop_loss_price)

        except Exception as e:
            logger.error(f"❌ 设置止损订单异常: {e}")

    async def _place_okx_algo_stop_loss_order(self, side: str, quantity: float, stop_loss_price: float):
        """使用OKX策略委托接口设置止损订单"""
        try:
            if hasattr(self.exchange_manager.exchange, 'place_algo_order'):
                # 使用OKX策略委托接口
                order_result = await self.exchange_manager.exchange.place_algo_order(
                    symbol=self.config.custom_symbol,
                    side=side,
                    order_type="conditional",
                    quantity=quantity,
                    trigger_price=stop_loss_price,
                    order_price=-1,  # 市价执行
                    reduce_only=True
                )

                if order_result:
                    logger.info(f"✅ [OKX止损] 策略委托止损订单设置成功: {order_result}")
                else:
                    logger.error("❌ [OKX止损] 策略委托止损订单设置失败")
            else:
                logger.warning("⚠️ [OKX止损] 交易所不支持策略委托，使用限价单替代")
                await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)

        except Exception as e:
            logger.error(f"❌ OKX策略委托止损订单失败: {e}")
            await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)

    async def _place_gate_stop_loss_order(self, side: str, quantity: float, stop_loss_price: float):
        """使用Gate.io止损订单"""
        try:
            # Gate.io的止损订单实现
            logger.info(f"🔧 [Gate.io止损] 设置止损订单: {side}, {quantity}, {stop_loss_price}")

            # 暂时使用限价单作为备选
            await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)

        except Exception as e:
            logger.error(f"❌ Gate.io止损订单失败: {e}")
            await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)

    async def _place_limit_stop_loss_order(self, side: str, quantity: float, stop_loss_price: float):
        """使用限价单作为止损（备选方案）"""
        try:
            logger.info(f"📋 [限价止损] 设置限价止损单: {side}, {quantity}, {stop_loss_price}")

            # 这里可以实现限价单止损逻辑
            # 注意：限价单不是真正的止损，需要持续监控价格

        except Exception as e:
            logger.error(f"❌ 限价止损订单失败: {e}")

    async def _check_close_position_opportunity(self):
        """检查平仓机会"""
        try:
            # 检查是否有持仓
            if not await self._has_position():
                return

            # 获取当前持仓信息
            positions = await self._get_current_positions()
            if not positions:
                return

            current_position = positions[0]

            # 获取当前价格数据
            price_data = await self._get_price_data()
            if (not price_data or
                len(price_data['closes']) < self.config.get_required_data_count() or
                len(price_data['highs']) < self.config.get_required_data_count() or
                len(price_data['lows']) < self.config.get_required_data_count()):
                logger.debug("价格数据不足，无法进行一目均衡表平仓分析")
                return

            # 检测一目均衡表平仓信号
            should_close, close_reason = await self._detect_ichimoku_close_signal(
                price_data, current_position
            )

            if should_close:
                logger.info(f"🎯 [一目均衡表平仓] 检测到平仓信号: {close_reason}")
                await self._execute_close_position(current_position, close_reason)

        except Exception as e:
            logger.error(f"❌ 检查一目均衡表平仓机会异常: {e}")

    async def _detect_ichimoku_close_signal(self, price_data: Dict, position) -> Tuple[bool, str]:
        """
        检测一目均衡表平仓信号

        Args:
            price_data: 价格数据
            position: 当前持仓信息

        Returns:
            Tuple[bool, str]: (是否应该平仓, 平仓原因)
        """
        try:
            # 计算当前一目均衡表数据
            ichimoku_data = await self.calculator.calculate_ichimoku(
                price_data['highs'], price_data['lows'], price_data['closes']
            )

            if not ichimoku_data:
                return False, "无法计算一目均衡表数据"

            # 获取持仓方向
            position_side = position.side.value if hasattr(position.side, 'value') else str(position.side)

            # 平仓逻辑：
            # 多仓 + 看跌信号 = 平仓
            # 空仓 + 看涨信号 = 平仓

            if position_side == "long":
                # 多仓平仓条件
                if ichimoku_data.signal in [IchimokuSignal.STRONG_BEARISH, IchimokuSignal.WEAK_BEARISH]:
                    return True, f"多仓遇到看跌信号: {ichimoku_data.signal.value}, 强度: {ichimoku_data.signal_strength:.3f}"

                # 价格跌破云层
                if ichimoku_data.price_vs_cloud == "云层下方":
                    return True, f"多仓价格跌破云层: {ichimoku_data.price_vs_cloud}"

                # 转换线跌破基准线
                if ichimoku_data.tenkan_sen < ichimoku_data.kijun_sen and ichimoku_data.current_price < ichimoku_data.tenkan_sen:
                    return True, f"多仓转换线跌破基准线且价格跌破转换线"

            elif position_side == "short":
                # 空仓平仓条件
                if ichimoku_data.signal in [IchimokuSignal.STRONG_BULLISH, IchimokuSignal.WEAK_BULLISH]:
                    return True, f"空仓遇到看涨信号: {ichimoku_data.signal.value}, 强度: {ichimoku_data.signal_strength:.3f}"

                # 价格突破云层
                if ichimoku_data.price_vs_cloud == "云层上方":
                    return True, f"空仓价格突破云层: {ichimoku_data.price_vs_cloud}"

                # 转换线突破基准线
                if ichimoku_data.tenkan_sen > ichimoku_data.kijun_sen and ichimoku_data.current_price > ichimoku_data.tenkan_sen:
                    return True, f"空仓转换线突破基准线且价格突破转换线"

            # 检查云层厚度变化（云层变薄可能意味着趋势减弱）
            if ichimoku_data.cloud_thickness < self.config.min_cloud_thickness * 0.5:
                return True, f"云层厚度过薄，趋势减弱: {ichimoku_data.cloud_thickness:.3f}%"

            return False, f"一目均衡表信号与持仓方向匹配，继续持有: 持仓={position_side}, 信号={ichimoku_data.signal.value}"

        except Exception as e:
            logger.error(f"❌ 检测一目均衡表平仓信号异常: {e}")
            return False, f"检测异常: {str(e)}"

    async def _execute_close_position(self, position, reason: str):
        """
        执行平仓操作

        Args:
            position: 持仓信息
            reason: 平仓原因
        """
        try:
            logger.info(f"🚀 [一目均衡表平仓] 开始执行平仓: {reason}")

            if not self.exchange_manager:
                logger.error("❌ 交易所管理器未设置")
                return False

            # 确定平仓方向
            position_side = position.side.value if hasattr(position.side, 'value') else str(position.side)

            if position_side == "long":
                close_side_enum = OrderSide.SELL
            elif position_side == "short":
                close_side_enum = OrderSide.BUY
            else:
                logger.error(f"❌ 未知的持仓方向: {position_side}")
                return False

            # 调用真实的交易所API进行平仓
            order_result = await self.exchange_manager.place_market_order(
                symbol=self.config.custom_symbol,
                side=close_side_enum,
                amount=position.size,
                reduce_only=True  # 关键：标记为平仓单
            )

            if order_result and hasattr(order_result, 'order_id') and order_result.order_id:
                logger.info(f"✅ [一目均衡表平仓] 平仓订单已提交: 订单ID {order_result.order_id}")

                # 等待一段时间让订单执行
                await asyncio.sleep(1)

                # 验证平仓是否成功
                positions_after = await self.exchange_manager.get_positions(self.config.custom_symbol)
                if not positions_after or all(p.size == 0 for p in positions_after):
                    logger.info(f"✅ [一目均衡表平仓] 平仓验证成功：持仓已清零")
                else:
                    logger.warning(f"⚠️ [一目均衡表平仓] 平仓可能未完全成功，剩余持仓: {[p.size for p in positions_after]}")
            else:
                logger.error(f"❌ [一目均衡表平仓] 平仓订单提交失败")
                return False

            # 重置策略状态
            self.current_position = None

            # 重新启用立即开仓功能（平仓后重新启用）
            self.config.immediate_open_enabled = True
            logger.info("🔓 [一目均衡表平仓] 平仓后重新启用立即开仓功能")

            return True

        except Exception as e:
            logger.error(f"❌ [一目均衡表平仓] 执行平仓异常: {e}")
            return False

    # ==================== 辅助方法 ====================

    async def _has_position(self) -> bool:
        """检查是否有持仓"""
        try:
            if not self.exchange_manager:
                return False

            positions = await self.exchange_manager.get_positions(self.config.custom_symbol)
            return bool(positions and any(p.size != 0 for p in positions))

        except Exception as e:
            logger.error(f"❌ 检查持仓状态异常: {e}")
            return False

    async def _get_current_positions(self):
        """获取当前持仓"""
        try:
            if not self.exchange_manager:
                return []

            return await self.exchange_manager.get_positions(self.config.custom_symbol)

        except Exception as e:
            logger.error(f"❌ 获取当前持仓异常: {e}")
            return []

    async def _check_position_status(self):
        """检查持仓状态"""
        try:
            positions = await self._get_current_positions()
            if positions:
                for position in positions:
                    logger.debug(f"持仓状态: {position.side.value}仓, 数量: {position.size}")
            else:
                logger.debug("当前无持仓")

        except Exception as e:
            logger.error(f"❌ 检查持仓状态异常: {e}")

    async def _get_price_data(self) -> Optional[Dict[str, List[float]]]:
        """获取价格数据"""
        try:
            if not self.exchange_manager:
                logger.error("交易所管理器未设置")
                return None

            # 获取K线数据
            klines = await self.exchange_manager.get_klines(
                symbol=self.config.custom_symbol,
                timeframe="1h",  # 使用1小时K线
                limit=self.config.get_required_data_count() + 10  # 多获取一些数据确保充足
            )

            if not klines:
                logger.error("无法获取K线数据")
                return None

            # 提取价格数据
            highs = [float(kline.high) for kline in klines]
            lows = [float(kline.low) for kline in klines]
            closes = [float(kline.close) for kline in klines]

            return {
                'highs': highs,
                'lows': lows,
                'closes': closes
            }

        except Exception as e:
            logger.error(f"❌ 获取价格数据异常: {e}")
            return None

    async def _get_current_price(self) -> Optional[float]:
        """获取当前价格"""
        try:
            if not self.exchange_manager:
                return None

            # 获取最新的ticker数据
            ticker = await self.exchange_manager.get_ticker(self.config.custom_symbol)
            if ticker and hasattr(ticker, 'last_price'):
                return float(ticker.last_price)

            # 如果ticker不可用，从K线获取最新价格
            price_data = await self._get_price_data()
            if price_data and price_data['closes']:
                return price_data['closes'][-1]

            return None

        except Exception as e:
            logger.error(f"❌ 获取当前价格异常: {e}")
            return None

    def _calculate_position_size(self, price: float) -> float:
        """计算开仓数量"""
        try:
            # 基于初始保证金和自定义杠杆计算开仓数量
            position_value = self.config.initial_margin * self.config.custom_leverage
            quantity = position_value / price

            # 应用精度
            quantity = round(quantity, self.config.quantity_precision)

            logger.info(f"💰 [仓位计算] 价格: {price:.4f}, 保证金: {self.config.initial_margin}, "
                       f"杠杆: {self.config.custom_leverage}x, 数量: {quantity}")
            return quantity

        except Exception as e:
            logger.error(f"❌ 计算开仓数量异常: {e}")
            return 0.0

    def _check_order_cooldown(self) -> bool:
        """检查下单冷却时间"""
        current_time = time.time()
        if current_time - self.last_order_time < self.config.order_cooldown:
            return False
        return True

    # ==================== 策略分析方法 ====================

    async def analyze_opening_opportunity(self) -> Dict[str, Any]:
        """分析开仓机会"""
        try:
            # 检查是否有持仓
            if await self._has_position():
                return {
                    'has_opportunity': False,
                    'reason': '已有持仓',
                    'direction': None,
                    'strength': 0.0
                }

            # 获取价格数据
            price_data = await self._get_price_data()
            if not price_data:
                return {
                    'has_opportunity': False,
                    'reason': '无法获取价格数据',
                    'direction': None,
                    'strength': 0.0
                }

            # 检测信号
            has_signal, signal_type, signal_strength = self.calculator.detect_ichimoku_signal(
                price_data['highs'], price_data['lows'], price_data['closes']
            )

            if not has_signal:
                return {
                    'has_opportunity': False,
                    'reason': '无一目均衡表信号',
                    'direction': None,
                    'strength': signal_strength
                }

            # 获取详细数据
            ichimoku_data = await self.calculator.calculate_ichimoku(
                price_data['highs'], price_data['lows'], price_data['closes']
            )

            return {
                'has_opportunity': True,
                'reason': f'检测到{signal_type}信号',
                'direction': signal_type,
                'strength': signal_strength,
                'ichimoku_data': ichimoku_data
            }

        except Exception as e:
            logger.error(f"❌ 分析开仓机会异常: {e}")
            return {
                'has_opportunity': False,
                'reason': f'分析异常: {str(e)}',
                'direction': None,
                'strength': 0.0
            }

    async def get_current_ichimoku_data(self) -> Optional[IchimokuData]:
        """获取当前一目均衡表数据"""
        try:
            price_data = await self._get_price_data()
            if not price_data:
                return None

            return await self.calculator.calculate_ichimoku(
                price_data['highs'], price_data['lows'], price_data['closes']
            )

        except Exception as e:
            logger.error(f"❌ 获取一目均衡表数据异常: {e}")
            return None

    def get_strategy_status(self) -> Dict[str, Any]:
        """获取策略状态"""
        return {
            'is_running': self.is_running,
            'immediate_open_enabled': self.config.immediate_open_enabled,
            'total_trades': self.total_trades,
            'last_order_time': self.last_order_time,
            'last_signal_time': self.last_signal_time,
            'config': {
                'custom_symbol': self.config.custom_symbol,
                'custom_leverage': self.config.custom_leverage,
                'initial_margin': self.config.initial_margin,
                'min_signal_strength': self.config.min_signal_strength,
                'min_cloud_thickness': self.config.min_cloud_thickness
            }
        }
