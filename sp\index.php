<?php
/**
 * BitV MACD智能加仓交易系统 - 实盘版本主页
 * 专业级量化交易前端界面
 */

// 安全配置
session_start();
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');

// 加载配置
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/security.php';

// 检查认证
if (!isAuthenticated()) {
    header('Location: login.php');
    exit;
}

$pageTitle = '实盘交易仪表板';
$currentPage = 'dashboard';

// 获取系统状态
$systemStatus = getSystemStatus();
$accountBalance = getAccountBalance();
$activePositions = getActivePositions();
$tradingSessions = getTradingSessions();

include 'includes/header.php';
?>

<div class="container-fluid">
    <!-- 系统状态警告 -->
    <?php if (!$systemStatus['success'] || !$systemStatus['data']['exchange_connected']): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <strong>系统警告:</strong> 
        <?php if (!$systemStatus['success']): ?>
            交易引擎未连接，请检查后端服务状态。
        <?php elseif (!$systemStatus['data']['exchange_connected']): ?>
            交易所未连接，请先配置交易所API。
        <?php endif; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <!-- 紧急停止按钮 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h3 mb-0">
                    <i class="fas fa-chart-line text-primary me-2"></i>
                    实盘交易仪表板
                </h1>
                <div>
                    <button type="button" class="btn btn-danger btn-lg" id="emergencyStopBtn" 
                            onclick="emergencyStop()" 
                            <?php echo (!$systemStatus['success'] || !$systemStatus['data']['is_trading']) ? 'disabled' : ''; ?>>
                        <i class="fas fa-stop-circle me-2"></i>
                        紧急停止
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 系统状态卡片 -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                系统状态
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo $systemStatus['success'] ? '运行中' : '离线'; ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-server fa-2x <?php echo $systemStatus['success'] ? 'text-success' : 'text-danger'; ?>"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                账户余额 (USDT)
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php 
                                if ($accountBalance['success']) {
                                    $usdtBalance = $accountBalance['data']['balance']['USDT']['free'] ?? 0;
                                    echo number_format($usdtBalance, 2);
                                } else {
                                    echo '---';
                                }
                                ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                活跃持仓
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo $activePositions['success'] ? count($activePositions['data']['positions']) : 0; ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-pie fa-2x text-info"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                交易会话
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo $tradingSessions['success'] ? count($tradingSessions['data']['sessions']) : 0; ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-cogs fa-2x text-warning"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="row">
        <!-- 左侧：交易控制面板 -->
        <div class="col-xl-8 col-lg-7">
            <!-- 交易控制卡片 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-robot me-2"></i>
                        智能交易控制
                    </h6>
                    <div class="dropdown no-arrow">
                        <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink"
                            data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in"
                            aria-labelledby="dropdownMenuLink">
                            <div class="dropdown-header">交易操作:</div>
                            <a class="dropdown-item" href="trading.php">
                                <i class="fas fa-play fa-sm fa-fw mr-2 text-gray-400"></i>
                                启动新交易
                            </a>
                            <a class="dropdown-item" href="positions.php">
                                <i class="fas fa-chart-area fa-sm fa-fw mr-2 text-gray-400"></i>
                                持仓管理
                            </a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item" href="settings.php">
                                <i class="fas fa-cog fa-sm fa-fw mr-2 text-gray-400"></i>
                                系统设置
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <?php if ($systemStatus['success'] && $systemStatus['data']['exchange_connected']): ?>
                        <!-- 快速启动交易表单 -->
                        <form id="quickTradingForm" class="row g-3">
                            <div class="col-md-4">
                                <label for="symbol" class="form-label">交易对</label>
                                <select class="form-select" id="symbol" name="symbol" required>
                                    <option value="BTC-USDT-SWAP">BTC/USDT 永续</option>
                                    <option value="ETH-USDT-SWAP">ETH/USDT 永续</option>
                                    <option value="LTC-USDT-SWAP">LTC/USDT 永续</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="leverage" class="form-label">杠杆倍数</label>
                                <select class="form-select" id="leverage" name="leverage" required>
                                    <option value="5">5x</option>
                                    <option value="10" selected>10x</option>
                                    <option value="20">20x</option>
                                    <option value="50">50x</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="initial_margin" class="form-label">初始保证金 (USDT)</label>
                                <input type="number" class="form-control" id="initial_margin" name="initial_margin" 
                                       value="100" min="10" max="10000" step="10" required>
                            </div>
                            <div class="col-md-2">
                                <label for="max_add_times" class="form-label">最大加仓次数</label>
                                <select class="form-select" id="max_add_times" name="max_add_times" required>
                                    <option value="1">1次</option>
                                    <option value="2">2次</option>
                                    <option value="3" selected>3次</option>
                                    <option value="5">5次</option>
                                </select>
                            </div>
                            <div class="col-12">
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <button type="button" class="btn btn-success btn-lg" onclick="startTrading()">
                                        <i class="fas fa-play me-2"></i>
                                        启动智能交易
                                    </button>
                                </div>
                            </div>
                        </form>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                            <h5>系统未就绪</h5>
                            <p class="text-muted">请先配置交易所连接后再启动交易</p>
                            <a href="settings.php" class="btn btn-primary">
                                <i class="fas fa-cog me-2"></i>
                                前往设置
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- 实时价格图表 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-line me-2"></i>
                        实时价格监控
                    </h6>
                </div>
                <div class="card-body">
                    <div class="chart-area">
                        <canvas id="priceChart" width="100%" height="40"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧：持仓和会话信息 -->
        <div class="col-xl-4 col-lg-5">
            <!-- 活跃持仓 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-pie me-2"></i>
                        活跃持仓
                    </h6>
                </div>
                <div class="card-body">
                    <div id="positionsContainer">
                        <?php if ($activePositions['success'] && !empty($activePositions['data']['positions'])): ?>
                            <?php foreach ($activePositions['data']['positions'] as $position): ?>
                                <div class="position-item mb-3 p-3 border rounded">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1"><?php echo htmlspecialchars($position['symbol']); ?></h6>
                                            <small class="text-muted">
                                                <?php echo strtoupper($position['side']); ?> | 
                                                <?php echo number_format($position['size'], 6); ?>
                                            </small>
                                        </div>
                                        <div class="text-end">
                                            <div class="<?php echo $position['unrealized_pnl'] >= 0 ? 'text-success' : 'text-danger'; ?>">
                                                <?php echo ($position['unrealized_pnl'] >= 0 ? '+' : '') . number_format($position['unrealized_pnl'], 2); ?> USDT
                                            </div>
                                            <small class="text-muted">
                                                入场: $<?php echo number_format($position['entry_price'], 2); ?>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-inbox fa-2x text-muted mb-3"></i>
                                <p class="text-muted">暂无活跃持仓</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- 交易会话 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-cogs me-2"></i>
                        交易会话
                    </h6>
                </div>
                <div class="card-body">
                    <div id="sessionsContainer">
                        <?php if ($tradingSessions['success'] && !empty($tradingSessions['data']['sessions'])): ?>
                            <?php foreach ($tradingSessions['data']['sessions'] as $session): ?>
                                <div class="session-item mb-3 p-3 border rounded">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1"><?php echo htmlspecialchars($session['symbol']); ?></h6>
                                            <small class="text-muted">
                                                <?php echo $session['leverage']; ?>x | 
                                                <?php echo number_format($session['initial_margin'], 2); ?> USDT
                                            </small>
                                        </div>
                                        <div class="text-end">
                                            <span class="badge bg-<?php echo $session['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                                <?php echo $session['status'] === 'active' ? '运行中' : '已停止'; ?>
                                            </span>
                                            <div class="mt-1">
                                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                                        onclick="stopSession('<?php echo $session['session_id']; ?>')"
                                                        <?php echo $session['status'] !== 'active' ? 'disabled' : ''; ?>>
                                                    停止
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-robot fa-2x text-muted mb-3"></i>
                                <p class="text-muted">暂无交易会话</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 确认对话框 -->
<div class="modal fade" id="confirmModal" tabindex="-1" aria-labelledby="confirmModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmModalLabel">确认操作</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="confirmModalBody">
                <!-- 动态内容 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmActionBtn">确认</button>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>

<script>
// 实时数据更新
let ws = null;
let priceChart = null;

// 初始化
$(document).ready(function() {
    initWebSocket();
    initPriceChart();
    
    // 每30秒刷新一次数据
    setInterval(refreshData, 30000);
});

// WebSocket连接
function initWebSocket() {
    const wsUrl = 'ws://<?php echo PYTHON_API_HOST; ?>:<?php echo PYTHON_API_PORT; ?>/ws';
    
    try {
        ws = new WebSocket(wsUrl);
        
        ws.onopen = function(event) {
            console.log('WebSocket连接已建立');
            showNotification('WebSocket连接成功', 'success');
        };
        
        ws.onmessage = function(event) {
            const data = JSON.parse(event.data);
            handleWebSocketMessage(data);
        };
        
        ws.onclose = function(event) {
            console.log('WebSocket连接已断开');
            showNotification('WebSocket连接断开，尝试重连...', 'warning');
            
            // 5秒后重连
            setTimeout(initWebSocket, 5000);
        };
        
        ws.onerror = function(error) {
            console.error('WebSocket错误:', error);
            showNotification('WebSocket连接错误', 'error');
        };
    } catch (error) {
        console.error('WebSocket初始化失败:', error);
    }
}

// 处理WebSocket消息
function handleWebSocketMessage(data) {
    switch(data.type) {
        case 'real_price_update':
            updatePriceChart(data.data);
            break;
        case 'real_data_snapshot':
            updateDashboard(data.data);
            break;
    }
}

// 启动交易
function startTrading() {
    const formData = new FormData(document.getElementById('quickTradingForm'));
    const config = Object.fromEntries(formData);
    
    // 转换数据类型
    config.leverage = parseInt(config.leverage);
    config.initial_margin = parseFloat(config.initial_margin);
    config.max_add_times = parseInt(config.max_add_times);
    config.strategy = 'macd';
    
    // 显示确认对话框
    showConfirmDialog(
        '启动实盘交易',
        `确认启动 ${config.symbol} 的实盘交易？<br>
         杠杆: ${config.leverage}x<br>
         保证金: ${config.initial_margin} USDT<br>
         <strong class="text-danger">这是真实交易，可能产生实际损失！</strong>`,
        function() {
            executeStartTrading(config);
        }
    );
}

// 执行启动交易
function executeStartTrading(config) {
    showLoading('正在启动交易...');
    
    fetch('/api/trading/start', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer <?php echo API_TOKEN; ?>'
        },
        body: JSON.stringify(config)
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        
        if (data.success) {
            showNotification('交易启动成功！', 'success');
            setTimeout(() => location.reload(), 2000);
        } else {
            showNotification('交易启动失败: ' + (data.error || '未知错误'), 'error');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('启动交易失败:', error);
        showNotification('启动交易失败: ' + error.message, 'error');
    });
}

// 紧急停止
function emergencyStop() {
    showConfirmDialog(
        '紧急停止所有交易',
        '<strong class="text-danger">确认紧急停止所有交易？</strong><br>这将立即平仓所有持仓并停止所有交易会话。',
        function() {
            executeEmergencyStop();
        }
    );
}

// 执行紧急停止
function executeEmergencyStop() {
    showLoading('正在执行紧急停止...');
    
    fetch('/api/emergency/stop', {
        method: 'POST',
        headers: {
            'Authorization': 'Bearer <?php echo API_TOKEN; ?>'
        }
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        
        if (data.success) {
            showNotification('紧急停止执行成功！', 'success');
            setTimeout(() => location.reload(), 2000);
        } else {
            showNotification('紧急停止失败: ' + (data.error || '未知错误'), 'error');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('紧急停止失败:', error);
        showNotification('紧急停止失败: ' + error.message, 'error');
    });
}

// 停止会话
function stopSession(sessionId) {
    showConfirmDialog(
        '停止交易会话',
        '确认停止此交易会话？这将平仓相关持仓。',
        function() {
            executeStopSession(sessionId);
        }
    );
}

// 执行停止会话
function executeStopSession(sessionId) {
    showLoading('正在停止会话...');
    
    fetch('/api/trading/stop', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer <?php echo API_TOKEN; ?>'
        },
        body: JSON.stringify({session_id: sessionId})
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        
        if (data.success) {
            showNotification('会话停止成功！', 'success');
            setTimeout(() => location.reload(), 2000);
        } else {
            showNotification('会话停止失败: ' + (data.error || '未知错误'), 'error');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('停止会话失败:', error);
        showNotification('停止会话失败: ' + error.message, 'error');
    });
}

// 刷新数据
function refreshData() {
    // 这里可以添加定期刷新逻辑
    console.log('刷新数据...');
}

// 初始化价格图表
function initPriceChart() {
    const ctx = document.getElementById('priceChart').getContext('2d');
    
    priceChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: 'BTC/USDT',
                data: [],
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.1)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: false
                }
            }
        }
    });
}

// 更新价格图表
function updatePriceChart(priceData) {
    if (!priceChart || !priceData) return;
    
    const time = new Date().toLocaleTimeString();
    const price = priceData.price;
    
    // 添加新数据点
    priceChart.data.labels.push(time);
    priceChart.data.datasets[0].data.push(price);
    
    // 保持最多50个数据点
    if (priceChart.data.labels.length > 50) {
        priceChart.data.labels.shift();
        priceChart.data.datasets[0].data.shift();
    }
    
    priceChart.update('none');
}

// 工具函数
function showConfirmDialog(title, message, onConfirm) {
    document.getElementById('confirmModalLabel').textContent = title;
    document.getElementById('confirmModalBody').innerHTML = message;
    
    const confirmBtn = document.getElementById('confirmActionBtn');
    confirmBtn.onclick = function() {
        $('#confirmModal').modal('hide');
        onConfirm();
    };
    
    $('#confirmModal').modal('show');
}

function showNotification(message, type) {
    // 实现通知显示逻辑
    console.log(`${type.toUpperCase()}: ${message}`);
    
    // 可以使用 toastr 或其他通知库
    if (typeof toastr !== 'undefined') {
        toastr[type](message);
    } else {
        alert(message);
    }
}

function showLoading(message) {
    // 实现加载状态显示
    console.log('Loading:', message);
}

function hideLoading() {
    // 隐藏加载状态
    console.log('Loading hidden');
}
</script>

</body>
</html>
