// 批量创建策略页面的脚本
const fs = require('fs')
const path = require('path')

const strategies = [
  { name: 'KDJ', title: 'KDJ策略配置', desc: '配置随机指标(KDJ)技术指标参数和交易策略', icon: 'DataBoard' },
  { name: 'MA', title: '移动平均线策略配置', desc: '配置移动平均线技术指标参数和交易策略', icon: 'LineChart' },
  { name: 'MFI', title: '资金流量指标策略配置', desc: '配置资金流量指标(MFI)技术指标参数和交易策略', icon: 'Money' },
  { name: 'ADX', title: '平均方向指数策略配置', desc: '配置平均方向指数(ADX)技术指标参数和交易策略', icon: 'Compass' },
  { name: 'OBV', title: '成交量平衡指标策略配置', desc: '配置成交量平衡指标(OBV)技术指标参数和交易策略', icon: 'PieChart' },
  { name: '<PERSON><PERSON><PERSON><PERSON>', title: '斐波那契回撤策略配置', desc: '配置斐波那契回撤技术指标参数和交易策略', icon: 'Grid' },
  { name: 'Williams', title: '威廉指标策略配置', desc: '配置威廉指标(Williams %R)技术指标参数和交易策略', icon: 'ScaleToOriginal' },
  { name: 'Ichimoku', title: '一目均衡表策略配置', desc: '配置一目均衡表技术指标参数和交易策略', icon: 'Histogram' },
  { name: 'Pinbar', title: '插针策略配置', desc: '配置插针(Pin Bar)形态识别参数和交易策略', icon: 'Position' }
]

const template = (strategy) => `<template>
  <div class="${strategy.name.toLowerCase()}-strategy">
    <div class="page-header">
      <h2>${strategy.title}</h2>
      <p>${strategy.desc}</p>
    </div>

    <div class="card">
      <h3 class="mb-md">${strategy.name}策略开发中...</h3>
      <el-empty description="${strategy.name}策略配置页面正在开发中">
        <template #image>
          <el-icon size="60" color="#c0c4cc"><${strategy.icon} /></el-icon>
        </template>
      </el-empty>
    </div>
  </div>
</template>

<script setup>
import { ${strategy.icon} } from '@element-plus/icons-vue'
</script>

<style lang="scss" scoped>
.${strategy.name.toLowerCase()}-strategy {
  .page-header {
    margin-bottom: var(--spacing-lg);
    
    h2 {
      margin-bottom: var(--spacing-xs);
      color: var(--text-color-primary);
    }
    
    p {
      color: var(--text-color-secondary);
    }
  }
}
</style>`

// 创建策略页面文件
strategies.forEach(strategy => {
  const filePath = path.join(__dirname, 'src', 'views', 'Strategies', `${strategy.name}.vue`)
  const content = template(strategy)
  
  fs.writeFileSync(filePath, content, 'utf8')
  console.log(`✅ 创建文件: ${strategy.name}.vue`)
})

console.log('🎉 所有策略页面创建完成！')
