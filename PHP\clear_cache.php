<?php
/**
 * 清除缓存页面
 */

echo "<h1>🧹 清除缓存</h1>";

// 清除操作码缓存
if (function_exists('opcache_reset')) {
    if (opcache_reset()) {
        echo "<p>✅ OPcache 缓存已清除</p>";
    } else {
        echo "<p>❌ OPcache 缓存清除失败</p>";
    }
} else {
    echo "<p>ℹ️ OPcache 未启用</p>";
}

// 清除APCu缓存
if (function_exists('apcu_clear_cache')) {
    if (apcu_clear_cache()) {
        echo "<p>✅ APCu 缓存已清除</p>";
    } else {
        echo "<p>❌ APCu 缓存清除失败</p>";
    }
} else {
    echo "<p>ℹ️ APCu 未启用</p>";
}

// 清除会话
session_start();
session_destroy();
echo "<p>✅ 会话已清除</p>";

echo "<hr>";
echo "<p><a href='index.php'>返回首页</a></p>";

// 自动跳转到首页
echo "<script>setTimeout(function(){ window.location.href = 'index.php'; }, 2000);</script>";
?>
