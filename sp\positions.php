<?php
/**
 * BitV MACD智能加仓交易系统 - 持仓管理页面
 * 实盘交易版本 - 完整的持仓监控和管理界面
 */

session_start();

// 加载配置
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/security.php';

// 检查认证
requireAuth();
requirePermission('trade');

$pageTitle = '持仓管理';
$currentPage = 'positions';

$success = '';
$error = '';

// 处理持仓操作
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // 验证CSRF令牌
        if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
            throw new Exception('安全验证失败，请刷新页面重试');
        }
        
        $action = $_POST['action'] ?? '';
        
        if ($action === 'close_position') {
            // 平仓操作
            $position_id = sanitizeInput($_POST['position_id'] ?? '');
            $close_ratio = (float)($_POST['close_ratio'] ?? 100);
            $order_type = sanitizeInput($_POST['order_type'] ?? 'market');
            $limit_price = (float)($_POST['limit_price'] ?? 0);
            
            $result = closePosition($position_id, $close_ratio, $order_type, $limit_price);
            
            if ($result['success']) {
                $success = '平仓操作提交成功！订单ID: ' . $result['data']['order_id'];
                
                // 记录操作日志
                logUserAction('CLOSE_POSITION', [
                    'position_id' => $position_id,
                    'close_ratio' => $close_ratio,
                    'order_type' => $order_type,
                    'order_id' => $result['data']['order_id']
                ]);
            } else {
                throw new Exception($result['error'] ?? '平仓操作失败');
            }
            
        } elseif ($action === 'add_position') {
            // 加仓操作
            $position_id = sanitizeInput($_POST['position_id'] ?? '');
            $add_margin = (float)($_POST['add_margin'] ?? 0);
            $order_type = sanitizeInput($_POST['order_type'] ?? 'market');
            $limit_price = (float)($_POST['limit_price'] ?? 0);
            
            $result = addPosition($position_id, $add_margin, $order_type, $limit_price);
            
            if ($result['success']) {
                $success = '加仓操作提交成功！订单ID: ' . $result['data']['order_id'];
                
                // 记录操作日志
                logUserAction('ADD_POSITION', [
                    'position_id' => $position_id,
                    'add_margin' => $add_margin,
                    'order_type' => $order_type,
                    'order_id' => $result['data']['order_id']
                ]);
            } else {
                throw new Exception($result['error'] ?? '加仓操作失败');
            }
            
        } elseif ($action === 'set_stop_loss') {
            // 设置止损
            $position_id = sanitizeInput($_POST['position_id'] ?? '');
            $stop_loss_price = (float)($_POST['stop_loss_price'] ?? 0);
            $stop_loss_type = sanitizeInput($_POST['stop_loss_type'] ?? 'stop_market');
            
            $result = setStopLoss($position_id, $stop_loss_price, $stop_loss_type);
            
            if ($result['success']) {
                $success = '止损设置成功！';
                
                // 记录操作日志
                logUserAction('SET_STOP_LOSS', [
                    'position_id' => $position_id,
                    'stop_loss_price' => $stop_loss_price,
                    'stop_loss_type' => $stop_loss_type
                ]);
            } else {
                throw new Exception($result['error'] ?? '止损设置失败');
            }
            
        } elseif ($action === 'set_take_profit') {
            // 设置止盈
            $position_id = sanitizeInput($_POST['position_id'] ?? '');
            $take_profit_price = (float)($_POST['take_profit_price'] ?? 0);
            $take_profit_type = sanitizeInput($_POST['take_profit_type'] ?? 'limit');
            
            $result = setTakeProfit($position_id, $take_profit_price, $take_profit_type);
            
            if ($result['success']) {
                $success = '止盈设置成功！';
                
                // 记录操作日志
                logUserAction('SET_TAKE_PROFIT', [
                    'position_id' => $position_id,
                    'take_profit_price' => $take_profit_price,
                    'take_profit_type' => $take_profit_type
                ]);
            } else {
                throw new Exception($result['error'] ?? '止盈设置失败');
            }
            
        } elseif ($action === 'emergency_close_all') {
            // 紧急平仓所有持仓
            $result = emergencyCloseAllPositions();
            
            if ($result['success']) {
                $success = '紧急平仓操作执行成功！';
                
                // 记录操作日志
                logUserAction('EMERGENCY_CLOSE_ALL', [
                    'timestamp' => date('Y-m-d H:i:s'),
                    'closed_positions' => count($result['data']['orders'] ?? [])
                ]);
            } else {
                throw new Exception($result['error'] ?? '紧急平仓失败');
            }
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
        
        // 记录错误日志
        logUserAction('POSITION_ACTION_FAILED', [
            'action' => $action ?? 'unknown',
            'error' => $error
        ]);
    }
}

// 获取持仓数据
$positionsData = getActivePositions();
$accountBalance = getAccountBalance();
$riskMetrics = getRiskMetrics();

include 'includes/header.php';
?>

<div class="container-fluid">
    <!-- 页面标题和控制按钮 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">
            <i class="fas fa-layer-group text-primary me-2"></i>
            持仓管理中心
        </h1>
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-info" onclick="refreshPositions()">
                <i class="fas fa-sync-alt me-2"></i>
                刷新数据
            </button>
            <button type="button" class="btn btn-outline-success" onclick="showBatchOperations()">
                <i class="fas fa-tasks me-2"></i>
                批量操作
            </button>
            <button type="button" class="btn btn-outline-warning" onclick="exportPositions()">
                <i class="fas fa-download me-2"></i>
                导出数据
            </button>
            <button type="button" class="btn btn-danger" onclick="showEmergencyCloseAll()">
                <i class="fas fa-exclamation-triangle me-2"></i>
                紧急平仓
            </button>
        </div>
    </div>

    <!-- 成功/错误消息 -->
    <?php if ($success): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>
        <?php echo htmlspecialchars($success); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <?php echo htmlspecialchars($error); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <!-- 账户概览 -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                账户总资产
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalAssets">
                                <?php echo $accountBalance['success'] ? formatNumber($accountBalance['data']['total'], 2) : '---'; ?> USDT
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-wallet fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                可用余额
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="availableBalance">
                                <?php echo $accountBalance['success'] ? formatNumber($accountBalance['data']['available'], 2) : '---'; ?> USDT
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                已用保证金
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="usedMargin">
                                <?php echo $accountBalance['success'] ? formatNumber($accountBalance['data']['margin_used'], 2) : '---'; ?> USDT
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-pie fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                未实现盈亏
                            </div>
                            <div class="h5 mb-0 font-weight-bold" id="unrealizedPnl">
                                <span class="<?php 
                                    if ($accountBalance['success']) {
                                        echo $accountBalance['data']['unrealized_pnl'] >= 0 ? 'text-success' : 'text-danger';
                                    } else {
                                        echo 'text-gray-800';
                                    }
                                ?>">
                                    <?php echo $accountBalance['success'] ? formatNumber($accountBalance['data']['unrealized_pnl'], 2) : '---'; ?> USDT
                                </span>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 风险监控面板 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-shield-alt me-2"></i>
                        风险监控面板
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center">
                            <div class="mb-2">
                                <span class="text-xs font-weight-bold text-primary text-uppercase">总风险等级</span>
                            </div>
                            <div class="h4 mb-0" id="overallRiskLevel">
                                <?php if ($riskMetrics['success']): ?>
                                    <span class="badge bg-<?php echo getRiskLevelColor($riskMetrics['data']['overall_risk']); ?> badge-lg">
                                        <?php echo getRiskLevelText($riskMetrics['data']['overall_risk']); ?>
                                    </span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">未知</span>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="col-md-3 text-center">
                            <div class="mb-2">
                                <span class="text-xs font-weight-bold text-info text-uppercase">保证金比率</span>
                            </div>
                            <div class="h4 mb-0" id="marginRatio">
                                <?php echo $riskMetrics['success'] ? formatPercentage($riskMetrics['data']['margin_ratio']) : '---'; ?>
                            </div>
                        </div>
                        
                        <div class="col-md-3 text-center">
                            <div class="mb-2">
                                <span class="text-xs font-weight-bold text-warning text-uppercase">持仓数量</span>
                            </div>
                            <div class="h4 mb-0" id="positionCount">
                                <?php echo $positionsData['success'] ? count($positionsData['data']['positions']) : 0; ?>
                            </div>
                        </div>
                        
                        <div class="col-md-3 text-center">
                            <div class="mb-2">
                                <span class="text-xs font-weight-bold text-success text-uppercase">平均杠杆</span>
                            </div>
                            <div class="h4 mb-0" id="averageLeverage">
                                <?php echo $riskMetrics['success'] ? formatNumber($riskMetrics['data']['average_leverage'], 1) . 'x' : '---'; ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 风险警报 -->
                    <div class="mt-3" id="riskAlerts">
                        <?php if ($riskMetrics['success'] && !empty($riskMetrics['data']['alerts'])): ?>
                            <?php foreach ($riskMetrics['data']['alerts'] as $alert): ?>
                                <div class="alert alert-<?php echo getAlertClass($alert['level']); ?> alert-sm">
                                    <i class="fas fa-<?php echo getAlertIcon($alert['level']); ?> me-2"></i>
                                    <?php echo htmlspecialchars($alert['message']); ?>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="alert alert-success alert-sm">
                                <i class="fas fa-check-circle me-2"></i>
                                当前风险状态正常
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 持仓列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-layer-group me-2"></i>
                        活跃持仓列表
                    </h6>
                    <div class="btn-group btn-group-sm" role="group">
                        <input type="radio" class="btn-check" name="positionFilter" id="filterAll" autocomplete="off" checked>
                        <label class="btn btn-outline-primary" for="filterAll">全部</label>

                        <input type="radio" class="btn-check" name="positionFilter" id="filterLong" autocomplete="off">
                        <label class="btn btn-outline-success" for="filterLong">多头</label>

                        <input type="radio" class="btn-check" name="positionFilter" id="filterShort" autocomplete="off">
                        <label class="btn btn-outline-danger" for="filterShort">空头</label>

                        <input type="radio" class="btn-check" name="positionFilter" id="filterProfit" autocomplete="off">
                        <label class="btn btn-outline-info" for="filterProfit">盈利</label>

                        <input type="radio" class="btn-check" name="positionFilter" id="filterLoss" autocomplete="off">
                        <label class="btn btn-outline-warning" for="filterLoss">亏损</label>
                    </div>
                </div>
                <div class="card-body">
                    <div id="positionsTableContainer">
                        <?php if ($positionsData['success'] && !empty($positionsData['data']['positions'])): ?>
                            <div class="table-responsive">
                                <table class="table table-hover" id="positionsTable">
                                    <thead>
                                        <tr>
                                            <th>
                                                <input type="checkbox" id="selectAllPositions" onchange="toggleAllPositions()">
                                            </th>
                                            <th>交易对</th>
                                            <th>方向</th>
                                            <th>数量</th>
                                            <th>入场价</th>
                                            <th>当前价</th>
                                            <th>强平价</th>
                                            <th>保证金</th>
                                            <th>杠杆</th>
                                            <th>未实现盈亏</th>
                                            <th>盈亏比例</th>
                                            <th>风险等级</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($positionsData['data']['positions'] as $position): ?>
                                            <tr class="position-row" 
                                                data-position-id="<?php echo $position['position_id']; ?>"
                                                data-side="<?php echo $position['side']; ?>"
                                                data-pnl="<?php echo $position['unrealized_pnl']; ?>">
                                                <td>
                                                    <input type="checkbox" class="position-checkbox" 
                                                           value="<?php echo $position['position_id']; ?>">
                                                </td>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($position['symbol']); ?></strong>
                                                    <br>
                                                    <small class="text-muted">
                                                        <?php echo formatTime($position['open_time'], 'H:i:s'); ?>
                                                    </small>
                                                </td>
                                                <td>
                                                    <span class="badge bg-<?php echo $position['side'] === 'long' ? 'success' : 'danger'; ?>">
                                                        <?php echo strtoupper($position['side']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php echo formatNumber($position['size'], 6); ?>
                                                    <br>
                                                    <small class="text-muted">
                                                        <?php echo $position['base_currency']; ?>
                                                    </small>
                                                </td>
                                                <td>
                                                    $<?php echo formatNumber($position['entry_price'], 2); ?>
                                                </td>
                                                <td>
                                                    <span id="currentPrice_<?php echo $position['position_id']; ?>">
                                                        $<?php echo formatNumber($position['current_price'], 2); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="text-<?php echo getRiskColor($position['liquidation_distance']); ?>">
                                                        $<?php echo formatNumber($position['liquidation_price'], 2); ?>
                                                    </span>
                                                    <br>
                                                    <small class="text-muted">
                                                        距离: <?php echo formatPercentage($position['liquidation_distance']); ?>
                                                    </small>
                                                </td>
                                                <td>
                                                    $<?php echo formatNumber($position['margin'], 2); ?>
                                                </td>
                                                <td>
                                                    <?php echo $position['leverage']; ?>x
                                                </td>
                                                <td>
                                                    <span class="<?php echo $position['unrealized_pnl'] >= 0 ? 'text-success' : 'text-danger'; ?>" 
                                                          id="unrealizedPnl_<?php echo $position['position_id']; ?>">
                                                        $<?php echo formatNumber($position['unrealized_pnl'], 2); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="<?php echo $position['pnl_percentage'] >= 0 ? 'text-success' : 'text-danger'; ?>" 
                                                          id="pnlPercentage_<?php echo $position['position_id']; ?>">
                                                        <?php echo formatPercentage($position['pnl_percentage']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-<?php echo getRiskLevelColor($position['risk_level']); ?>">
                                                        <?php echo getRiskLevelText($position['risk_level']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm" role="group">
                                                        <button type="button" class="btn btn-outline-info" 
                                                                onclick="viewPositionDetails('<?php echo $position['position_id']; ?>')"
                                                                title="查看详情">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-outline-success" 
                                                                onclick="showAddPosition('<?php echo $position['position_id']; ?>')"
                                                                title="加仓">
                                                            <i class="fas fa-plus"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-outline-warning" 
                                                                onclick="showSetStopLoss('<?php echo $position['position_id']; ?>')"
                                                                title="设置止损">
                                                            <i class="fas fa-shield-alt"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-outline-primary" 
                                                                onclick="showSetTakeProfit('<?php echo $position['position_id']; ?>')"
                                                                title="设置止盈">
                                                            <i class="fas fa-bullseye"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-outline-danger" 
                                                                onclick="showClosePosition('<?php echo $position['position_id']; ?>')"
                                                                title="平仓">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-5">
                                <i class="fas fa-layer-group fa-3x text-muted mb-3"></i>
                                <h5>暂无活跃持仓</h5>
                                <p class="text-muted">当前账户没有活跃的持仓</p>
                                <a href="trading.php" class="btn btn-primary">
                                    <i class="fas fa-rocket me-2"></i>
                                    开始交易
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 持仓详情模态框 -->
<div class="modal fade" id="positionDetailsModal" tabindex="-1" aria-labelledby="positionDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="positionDetailsModalLabel">
                    <i class="fas fa-layer-group me-2"></i>
                    持仓详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="positionDetailsContent">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <div class="mt-2">正在加载持仓详情...</div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="exportPositionData()">
                    <i class="fas fa-download me-2"></i>
                    导出数据
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 平仓操作模态框 -->
<div class="modal fade" id="closePositionModal" tabindex="-1" aria-labelledby="closePositionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="closePositionModalLabel">
                    <i class="fas fa-times me-2"></i>
                    平仓操作
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="closePositionForm" method="POST" action="">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="action" value="close_position">
                    <input type="hidden" name="position_id" id="closePositionId">

                    <div class="mb-3">
                        <label for="close_ratio" class="form-label">平仓比例 (%)</label>
                        <input type="range" class="form-range" id="close_ratio" name="close_ratio"
                               min="10" max="100" step="10" value="100"
                               oninput="updateCloseRatioDisplay()">
                        <div class="d-flex justify-content-between">
                            <small>10%</small>
                            <small id="closeRatioValue">100%</small>
                            <small>100%</small>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="close_order_type" class="form-label">订单类型</label>
                        <select class="form-select" id="close_order_type" name="order_type" onchange="toggleLimitPrice('close')">
                            <option value="market">市价单 (立即执行)</option>
                            <option value="limit">限价单 (指定价格)</option>
                        </select>
                    </div>

                    <div class="mb-3" id="closeLimitPriceGroup" style="display: none;">
                        <label for="close_limit_price" class="form-label">限价价格</label>
                        <input type="number" class="form-control" id="close_limit_price" name="limit_price"
                               step="0.01" placeholder="输入限价价格">
                        <div class="form-text">
                            当前市价: <span id="closeCurrentPrice">---</span>
                        </div>
                    </div>

                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>平仓确认：</h6>
                        <p class="mb-0">
                            您即将平仓 <strong id="closePositionSymbol">---</strong>
                            <span id="closePositionSide">---</span> 持仓的
                            <strong id="closePositionRatio">100%</strong>
                        </p>
                        <p class="mb-0 mt-2">
                            预计平仓数量: <strong id="closePositionAmount">---</strong><br>
                            预计收益: <span id="closePositionPnl" class="fw-bold">---</span>
                        </p>
                    </div>

                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="closePositionConfirm" required>
                        <label class="form-check-label fw-bold" for="closePositionConfirm">
                            我确认要执行此平仓操作
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" onclick="submitClosePosition()">
                    <i class="fas fa-times me-2"></i>
                    确认平仓
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 加仓操作模态框 -->
<div class="modal fade" id="addPositionModal" tabindex="-1" aria-labelledby="addPositionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addPositionModalLabel">
                    <i class="fas fa-plus me-2"></i>
                    加仓操作
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addPositionForm" method="POST" action="">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="action" value="add_position">
                    <input type="hidden" name="position_id" id="addPositionId">

                    <div class="mb-3">
                        <label for="add_margin" class="form-label">加仓保证金 (USDT)</label>
                        <input type="number" class="form-control" id="add_margin" name="add_margin"
                               min="10" max="10000" step="10" value="100" required
                               oninput="updateAddPositionCalculation()">
                        <div class="form-text">
                            可用余额: <span id="addAvailableBalance">---</span> USDT
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="add_order_type" class="form-label">订单类型</label>
                        <select class="form-select" id="add_order_type" name="order_type" onchange="toggleLimitPrice('add')">
                            <option value="market">市价单 (立即执行)</option>
                            <option value="limit">限价单 (指定价格)</option>
                        </select>
                    </div>

                    <div class="mb-3" id="addLimitPriceGroup" style="display: none;">
                        <label for="add_limit_price" class="form-label">限价价格</label>
                        <input type="number" class="form-control" id="add_limit_price" name="limit_price"
                               step="0.01" placeholder="输入限价价格">
                        <div class="form-text">
                            当前市价: <span id="addCurrentPrice">---</span>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>加仓计算：</h6>
                        <p class="mb-0">
                            当前持仓: <strong id="addCurrentPosition">---</strong><br>
                            加仓数量: <strong id="addPositionAmount">---</strong><br>
                            加仓后总持仓: <strong id="addTotalPosition">---</strong><br>
                            平均成本: <strong id="addAverageCost">---</strong>
                        </p>
                    </div>

                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="addPositionConfirm" required>
                        <label class="form-check-label fw-bold" for="addPositionConfirm">
                            我确认要执行此加仓操作
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-success" onclick="submitAddPosition()">
                    <i class="fas fa-plus me-2"></i>
                    确认加仓
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 设置止损模态框 -->
<div class="modal fade" id="setStopLossModal" tabindex="-1" aria-labelledby="setStopLossModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="setStopLossModalLabel">
                    <i class="fas fa-shield-alt me-2"></i>
                    设置止损
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="setStopLossForm" method="POST" action="">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="action" value="set_stop_loss">
                    <input type="hidden" name="position_id" id="stopLossPositionId">

                    <div class="mb-3">
                        <label for="stop_loss_price" class="form-label">止损价格</label>
                        <input type="number" class="form-control" id="stop_loss_price" name="stop_loss_price"
                               step="0.01" required oninput="updateStopLossCalculation()">
                        <div class="form-text">
                            当前价格: <span id="stopLossCurrentPrice">---</span><br>
                            入场价格: <span id="stopLossEntryPrice">---</span><br>
                            强平价格: <span id="stopLossLiquidationPrice">---</span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="stop_loss_type" class="form-label">止损类型</label>
                        <select class="form-select" id="stop_loss_type" name="stop_loss_type">
                            <option value="stop_market">止损市价单 (推荐)</option>
                            <option value="stop_limit">止损限价单</option>
                        </select>
                        <div class="form-text">
                            止损市价单在触发时以市价立即执行，止损限价单可能因流动性不足而无法完全成交
                        </div>
                    </div>

                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>止损确认：</h6>
                        <p class="mb-0">
                            止损价格: <strong id="stopLossConfirmPrice">---</strong><br>
                            预计亏损: <span id="stopLossConfirmLoss" class="text-danger fw-bold">---</span><br>
                            亏损比例: <span id="stopLossConfirmRatio" class="text-danger fw-bold">---</span>
                        </p>
                    </div>

                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="stopLossConfirm" required>
                        <label class="form-check-label fw-bold" for="stopLossConfirm">
                            我确认要设置此止损价格
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-warning" onclick="submitSetStopLoss()">
                    <i class="fas fa-shield-alt me-2"></i>
                    确认设置
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 设置止盈模态框 -->
<div class="modal fade" id="setTakeProfitModal" tabindex="-1" aria-labelledby="setTakeProfitModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="setTakeProfitModalLabel">
                    <i class="fas fa-bullseye me-2"></i>
                    设置止盈
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="setTakeProfitForm" method="POST" action="">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="action" value="set_take_profit">
                    <input type="hidden" name="position_id" id="takeProfitPositionId">

                    <div class="mb-3">
                        <label for="take_profit_price" class="form-label">止盈价格</label>
                        <input type="number" class="form-control" id="take_profit_price" name="take_profit_price"
                               step="0.01" required oninput="updateTakeProfitCalculation()">
                        <div class="form-text">
                            当前价格: <span id="takeProfitCurrentPrice">---</span><br>
                            入场价格: <span id="takeProfitEntryPrice">---</span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="take_profit_type" class="form-label">止盈类型</label>
                        <select class="form-select" id="take_profit_type" name="take_profit_type">
                            <option value="limit">限价单 (推荐)</option>
                            <option value="market">市价单</option>
                        </select>
                        <div class="form-text">
                            限价单可以确保在指定价格成交，市价单会立即以当前市价成交
                        </div>
                    </div>

                    <div class="alert alert-success">
                        <h6><i class="fas fa-bullseye me-2"></i>止盈确认：</h6>
                        <p class="mb-0">
                            止盈价格: <strong id="takeProfitConfirmPrice">---</strong><br>
                            预计盈利: <span id="takeProfitConfirmProfit" class="text-success fw-bold">---</span><br>
                            盈利比例: <span id="takeProfitConfirmRatio" class="text-success fw-bold">---</span>
                        </p>
                    </div>

                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="takeProfitConfirm" required>
                        <label class="form-check-label fw-bold" for="takeProfitConfirm">
                            我确认要设置此止盈价格
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="submitSetTakeProfit()">
                    <i class="fas fa-bullseye me-2"></i>
                    确认设置
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 批量操作模态框 -->
<div class="modal fade" id="batchOperationsModal" tabindex="-1" aria-labelledby="batchOperationsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchOperationsModalLabel">
                    <i class="fas fa-tasks me-2"></i>
                    批量操作
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>选择操作类型</h6>
                        <div class="list-group">
                            <button type="button" class="list-group-item list-group-item-action"
                                    onclick="setBatchOperation('close_all')">
                                <i class="fas fa-times text-danger me-2"></i>
                                批量平仓
                            </button>
                            <button type="button" class="list-group-item list-group-item-action"
                                    onclick="setBatchOperation('set_stop_loss')">
                                <i class="fas fa-shield-alt text-warning me-2"></i>
                                批量设置止损
                            </button>
                            <button type="button" class="list-group-item list-group-item-action"
                                    onclick="setBatchOperation('set_take_profit')">
                                <i class="fas fa-bullseye text-success me-2"></i>
                                批量设置止盈
                            </button>
                            <button type="button" class="list-group-item list-group-item-action"
                                    onclick="setBatchOperation('adjust_leverage')">
                                <i class="fas fa-expand-arrows-alt text-info me-2"></i>
                                批量调整杠杆
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>已选择的持仓 (<span id="selectedPositionsCount">0</span>)</h6>
                        <div id="selectedPositionsList" style="max-height: 300px; overflow-y: auto;">
                            <div class="text-center py-3 text-muted">
                                请先选择要操作的持仓
                            </div>
                        </div>
                    </div>
                </div>

                <div id="batchOperationForm" style="display: none;">
                    <hr>
                    <div id="batchFormContent">
                        <!-- 动态生成的表单内容 -->
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="executeBatchOperation"
                        style="display: none;" onclick="executeBatchOperation()">
                    <i class="fas fa-play me-2"></i>
                    执行批量操作
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 紧急平仓确认模态框 -->
<div class="modal fade" id="emergencyCloseAllModal" tabindex="-1" aria-labelledby="emergencyCloseAllModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="emergencyCloseAllModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    紧急平仓确认
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <h6><i class="fas fa-warning me-2"></i>危险操作警告：</h6>
                    <p class="mb-0">此操作将立即平仓所有活跃持仓！这是不可撤销的操作。</p>
                </div>

                <div class="mb-3">
                    <h6>将要平仓的持仓：</h6>
                    <div id="emergencyClosePositionsList" style="max-height: 200px; overflow-y: auto;">
                        <!-- 动态生成持仓列表 -->
                    </div>
                </div>

                <div class="form-check mb-3">
                    <input class="form-check-input" type="checkbox" id="emergencyCloseConfirm">
                    <label class="form-check-label fw-bold" for="emergencyCloseConfirm">
                        我确认要执行紧急平仓操作，并承担相应后果
                    </label>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmEmergencyCloseBtn" disabled
                        onclick="executeEmergencyCloseAll()">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    确认紧急平仓
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// 全局变量
let positionUpdateInterval = null;
let priceUpdateInterval = null;
let selectedPositions = [];
let currentBatchOperation = null;
let positionsData = {};

// 页面加载完成后执行
$(document).ready(function() {
    // 初始化页面
    initializePositionsPage();

    // 启动实时更新
    startPositionUpdates();

    // 绑定事件
    bindPositionEvents();

    // 加载初始数据
    loadPositionsData();
});

// 初始化持仓页面
function initializePositionsPage() {
    // 设置持仓过滤器
    setupPositionFilters();

    // 初始化批量操作
    setupBatchOperations();

    // 设置实时价格更新
    setupPriceUpdates();
}

// 绑定持仓相关事件
function bindPositionEvents() {
    // 持仓过滤器
    $('input[name="positionFilter"]').on('change', function() {
        filterPositions($(this).attr('id'));
    });

    // 全选/取消全选
    $('#selectAllPositions').on('change', function() {
        toggleAllPositions();
    });

    // 单个持仓选择
    $(document).on('change', '.position-checkbox', function() {
        updateSelectedPositions();
    });

    // 紧急平仓确认
    $('#emergencyCloseConfirm').on('change', function() {
        $('#confirmEmergencyCloseBtn').prop('disabled', !this.checked);
    });
}

// 启动持仓更新
function startPositionUpdates() {
    // 持仓数据更新 - 每10秒
    positionUpdateInterval = setInterval(updatePositionsData, 10000);

    // 价格更新 - 每5秒
    priceUpdateInterval = setInterval(updatePositionPrices, 5000);

    // 立即执行一次更新
    updatePositionsData();
    updatePositionPrices();
}

// 更新持仓数据
function updatePositionsData() {
    fetch(window.BITV.API_BASE + '/api/positions', {
        headers: {
            'Authorization': 'Bearer ' + window.BITV.API_TOKEN
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            positionsData = data.data;
            updatePositionsDisplay(data.data);
            updateAccountSummary(data.data.account_summary);
            updateRiskMetrics(data.data.risk_metrics);
        }
    })
    .catch(error => {
        console.error('获取持仓数据失败:', error);
    });
}

// 更新持仓价格
function updatePositionPrices() {
    if (!positionsData.positions || positionsData.positions.length === 0) return;

    positionsData.positions.forEach(position => {
        // 更新当前价格显示
        const currentPriceElement = document.getElementById(`currentPrice_${position.position_id}`);
        const unrealizedPnlElement = document.getElementById(`unrealizedPnl_${position.position_id}`);
        const pnlPercentageElement = document.getElementById(`pnlPercentage_${position.position_id}`);

        if (currentPriceElement && position.current_price) {
            currentPriceElement.textContent = `$${formatNumber(position.current_price, 2)}`;
        }

        if (unrealizedPnlElement && position.unrealized_pnl !== undefined) {
            const pnlClass = position.unrealized_pnl >= 0 ? 'text-success' : 'text-danger';
            unrealizedPnlElement.className = pnlClass;
            unrealizedPnlElement.textContent = `$${formatNumber(position.unrealized_pnl, 2)}`;
        }

        if (pnlPercentageElement && position.pnl_percentage !== undefined) {
            const pnlClass = position.pnl_percentage >= 0 ? 'text-success' : 'text-danger';
            pnlPercentageElement.className = pnlClass;
            pnlPercentageElement.textContent = formatPercentage(position.pnl_percentage);
        }
    });
}

// 持仓过滤功能
function setupPositionFilters() {
    window.positionFilters = {
        all: () => true,
        long: (position) => position.side === 'long',
        short: (position) => position.side === 'short',
        profit: (position) => position.unrealized_pnl > 0,
        loss: (position) => position.unrealized_pnl < 0
    };
}

function filterPositions(filterType) {
    const filter = filterType.replace('filter', '').toLowerCase();
    const filterFunction = window.positionFilters[filter] || window.positionFilters.all;

    $('.position-row').each(function() {
        const positionId = $(this).data('position-id');
        const position = positionsData.positions.find(p => p.position_id === positionId);

        if (position && filterFunction(position)) {
            $(this).show();
        } else {
            $(this).hide();
        }
    });

    // 更新显示的持仓数量
    const visibleCount = $('.position-row:visible').length;
    showNotification(`已筛选出 ${visibleCount} 个持仓`, 'info');
}

// 全选/取消全选持仓
function toggleAllPositions() {
    const isChecked = $('#selectAllPositions').is(':checked');
    $('.position-checkbox:visible').prop('checked', isChecked);
    updateSelectedPositions();
}

// 更新选中的持仓
function updateSelectedPositions() {
    selectedPositions = [];
    $('.position-checkbox:checked').each(function() {
        selectedPositions.push($(this).val());
    });

    // 更新批量操作按钮状态
    const batchBtn = $('button[onclick="showBatchOperations()"]');
    if (selectedPositions.length > 0) {
        batchBtn.removeClass('btn-outline-success').addClass('btn-success');
        batchBtn.html(`<i class="fas fa-tasks me-2"></i>批量操作 (${selectedPositions.length})`);
    } else {
        batchBtn.removeClass('btn-success').addClass('btn-outline-success');
        batchBtn.html('<i class="fas fa-tasks me-2"></i>批量操作');
    }
}

// 查看持仓详情
function viewPositionDetails(positionId) {
    showLoading('正在加载持仓详情...');

    fetch(window.BITV.API_BASE + '/api/positions/' + positionId, {
        headers: {
            'Authorization': 'Bearer ' + window.BITV.API_TOKEN
        }
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        if (data.success) {
            displayPositionDetails(data.data);
            $('#positionDetailsModal').modal('show');
        } else {
            showNotification('获取持仓详情失败: ' + (data.error || '未知错误'), 'error');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('获取持仓详情失败:', error);
        showNotification('网络错误，请重试', 'error');
    });
}

// 显示持仓详情
function displayPositionDetails(positionData) {
    const container = document.getElementById('positionDetailsContent');

    const html = `
        <div class="row">
            <div class="col-md-6">
                <h6>基本信息</h6>
                <table class="table table-sm">
                    <tr><td>持仓ID:</td><td>${positionData.position_id}</td></tr>
                    <tr><td>交易对:</td><td>${positionData.symbol}</td></tr>
                    <tr><td>方向:</td><td><span class="badge bg-${positionData.side === 'long' ? 'success' : 'danger'}">${positionData.side.toUpperCase()}</span></td></tr>
                    <tr><td>数量:</td><td>${formatNumber(positionData.size, 6)} ${positionData.base_currency}</td></tr>
                    <tr><td>杠杆:</td><td>${positionData.leverage}x</td></tr>
                    <tr><td>开仓时间:</td><td>${formatTime(positionData.open_time)}</td></tr>
                    <tr><td>持仓时长:</td><td>${calculateDuration(positionData.open_time)}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>价格信息</h6>
                <table class="table table-sm">
                    <tr><td>入场价:</td><td>$${formatNumber(positionData.entry_price, 2)}</td></tr>
                    <tr><td>当前价:</td><td>$${formatNumber(positionData.current_price, 2)}</td></tr>
                    <tr><td>强平价:</td><td class="text-danger">$${formatNumber(positionData.liquidation_price, 2)}</td></tr>
                    <tr><td>止损价:</td><td>${positionData.stop_loss_price ? '$' + formatNumber(positionData.stop_loss_price, 2) : '未设置'}</td></tr>
                    <tr><td>止盈价:</td><td>${positionData.take_profit_price ? '$' + formatNumber(positionData.take_profit_price, 2) : '未设置'}</td></tr>
                    <tr><td>价格变化:</td><td class="${positionData.price_change >= 0 ? 'text-success' : 'text-danger'}">${formatPercentage(positionData.price_change)}</td></tr>
                </table>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-md-6">
                <h6>盈亏统计</h6>
                <table class="table table-sm">
                    <tr><td>未实现盈亏:</td><td class="${positionData.unrealized_pnl >= 0 ? 'text-success' : 'text-danger'}">${formatNumber(positionData.unrealized_pnl, 2)} USDT</td></tr>
                    <tr><td>盈亏比例:</td><td class="${positionData.pnl_percentage >= 0 ? 'text-success' : 'text-danger'}">${formatPercentage(positionData.pnl_percentage)}</td></tr>
                    <tr><td>已实现盈亏:</td><td>${formatNumber(positionData.realized_pnl || 0, 2)} USDT</td></tr>
                    <tr><td>手续费:</td><td>${formatNumber(positionData.total_fees || 0, 4)} USDT</td></tr>
                    <tr><td>资金费用:</td><td>${formatNumber(positionData.funding_fees || 0, 4)} USDT</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>风险指标</h6>
                <table class="table table-sm">
                    <tr><td>保证金:</td><td>${formatNumber(positionData.margin, 2)} USDT</td></tr>
                    <tr><td>保证金比率:</td><td>${formatPercentage(positionData.margin_ratio)}</td></tr>
                    <tr><td>风险等级:</td><td><span class="badge bg-${getRiskLevelColor(positionData.risk_level)}">${getRiskLevelText(positionData.risk_level)}</span></td></tr>
                    <tr><td>距离强平:</td><td class="text-${getRiskColor(positionData.liquidation_distance)}">${formatPercentage(positionData.liquidation_distance)}</td></tr>
                    <tr><td>最大回撤:</td><td class="text-danger">${formatPercentage(positionData.max_drawdown || 0)}</td></tr>
                </table>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <h6>交易历史</h6>
                <div style="max-height: 300px; overflow-y: auto;">
                    ${displayPositionTrades(positionData.trades || [])}
                </div>
            </div>
        </div>
    `;

    container.innerHTML = html;
}

// 显示持仓交易历史
function displayPositionTrades(trades) {
    if (!trades || trades.length === 0) {
        return '<div class="text-center py-3 text-muted">暂无交易记录</div>';
    }

    let html = '<div class="table-responsive"><table class="table table-sm">';
    html += '<thead><tr><th>时间</th><th>类型</th><th>方向</th><th>数量</th><th>价格</th><th>手续费</th><th>状态</th></tr></thead><tbody>';

    trades.forEach(trade => {
        const sideClass = trade.side === 'buy' ? 'text-success' : 'text-danger';
        html += `
            <tr>
                <td>${formatTime(trade.timestamp, 'H:i:s')}</td>
                <td>${trade.type}</td>
                <td class="${sideClass}">${trade.side.toUpperCase()}</td>
                <td>${formatNumber(trade.amount, 6)}</td>
                <td>$${formatNumber(trade.price, 2)}</td>
                <td>$${formatNumber(trade.fee || 0, 4)}</td>
                <td><span class="badge bg-${trade.status === 'filled' ? 'success' : 'warning'}">${trade.status}</span></td>
            </tr>
        `;
    });

    html += '</tbody></table></div>';
    return html;
}

// 页面卸载时清理
window.addEventListener('beforeunload', function() {
    if (positionUpdateInterval) clearInterval(positionUpdateInterval);
    if (priceUpdateInterval) clearInterval(priceUpdateInterval);
});
</script>

<?php include 'includes/footer.php'; ?>
