# BitV MACD智能加仓交易系统 - Apache重写规则

# 开启重写引擎
RewriteEngine On

# 设置基础目录（如果在子目录中，请修改此行）
# RewriteBase /bitV/PHP/

# 阻止直接访问敏感文件
<Files "config.php">
    Order Allow,Deny
    Deny from all
</Files>

<Files ".env">
    Order Allow,Deny
    Deny from all
</Files>

<Files "*.log">
    Order Allow,Deny
    Deny from all
</Files>

# 允许访问静态资源
RewriteCond %{REQUEST_FILENAME} -f
RewriteCond %{REQUEST_URI} \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ [NC]
RewriteRule ^(.*)$ - [L]

# 允许访问特殊文件
RewriteCond %{REQUEST_URI} ^/(install\.php|test\.php|debug\.php)$ [NC]
RewriteRule ^(.*)$ - [L]

# 重定向所有请求到index.php
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# 安全设置
<IfModule mod_headers.c>
    # 防止点击劫持
    Header always append X-Frame-Options SAMEORIGIN
    
    # 防止MIME类型嗅探
    Header set X-Content-Type-Options nosniff
    
    # XSS保护
    Header set X-XSS-Protection "1; mode=block"
    
    # 引用策略
    Header set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# 压缩设置
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# 缓存设置
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/ico "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
</IfModule>

# 错误页面
ErrorDocument 404 /index.php
ErrorDocument 403 /index.php
ErrorDocument 500 /index.php
