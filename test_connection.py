#!/usr/bin/env python3
"""
测试Python后端连接
"""

import requests
import json
import time

def test_endpoint(url, name, headers=None):
    """测试API端点"""
    print(f"\n🧪 测试 {name}")
    print(f"URL: {url}")
    
    try:
        start_time = time.time()
        response = requests.get(url, headers=headers, timeout=10)
        end_time = time.time()
        
        print(f"状态码: {response.status_code}")
        print(f"响应时间: {(end_time - start_time)*1000:.2f}ms")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                return True
            except:
                print(f"响应内容: {response.text}")
                return True
        else:
            print(f"错误响应: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError as e:
        print(f"❌ 连接错误: {e}")
        return False
    except requests.exceptions.Timeout as e:
        print(f"❌ 超时错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def main():
    """主函数"""
    print("🔧 BitV API连接测试")
    print("=" * 50)
    
    # 测试基本连接
    base_url = "http://localhost:8000"
    
    # 测试健康检查
    health_result = test_endpoint(f"{base_url}/health", "健康检查")
    
    # 测试根路径
    root_result = test_endpoint(f"{base_url}/", "根路径")
    
    # 测试需要认证的端点
    headers = {
        'Authorization': 'Bearer bitv_real_trading_token',
        'Content-Type': 'application/json'
    }
    
    status_result = test_endpoint(f"{base_url}/api/status", "系统状态 (需认证)", headers)
    trading_result = test_endpoint(f"{base_url}/api/trading/status", "交易状态 (需认证)", headers)
    
    # 总结
    print("\n📋 测试总结")
    print("=" * 30)
    results = {
        "健康检查": health_result,
        "根路径": root_result,
        "系统状态": status_result,
        "交易状态": trading_result
    }
    
    success_count = sum(results.values())
    total_count = len(results)
    
    for name, result in results.items():
        status = "✅ 成功" if result else "❌ 失败"
        print(f"{name}: {status}")
    
    print(f"\n总体结果: {success_count}/{total_count} 成功")
    
    if success_count == total_count:
        print("🎉 所有测试通过！API连接正常！")
    else:
        print("⚠️ 部分测试失败，请检查配置。")

if __name__ == "__main__":
    main()
