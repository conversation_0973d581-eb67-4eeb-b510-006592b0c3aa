"""
异步运行器工具
用于在GUI环境中安全地运行异步操作
"""

import asyncio
import threading
import logging
import time
from typing import Any, Coroutine, Optional, Callable
from concurrent.futures import ThreadPoolExecutor, Future

logger = logging.getLogger(__name__)


class AsyncRunner:
    """异步运行器，用于在GUI线程中安全执行异步操作"""
    
    def __init__(self, max_workers: int = 5):
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self._shutdown = False
    
    def run_async(self, coro: Coroutine, timeout: float = 30.0, 
                  callback: Optional[Callable] = None) -> Future:
        """
        在后台线程中运行异步协程
        
        Args:
            coro: 异步协程
            timeout: 超时时间（秒）
            callback: 完成后的回调函数
            
        Returns:
            Future对象
        """
        if self._shutdown:
            future = Future()
            future.set_exception(RuntimeError("AsyncRunner已关闭"))
            return future
        
        def run_in_thread():
            """在线程中运行异步操作"""
            result = None
            exception = None
            
            try:
                # 创建新的事件循环
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                try:
                    # 运行协程
                    result = loop.run_until_complete(
                        asyncio.wait_for(coro, timeout=timeout)
                    )
                except asyncio.TimeoutError:
                    exception = TimeoutError(f"异步操作超时 ({timeout}秒)")
                except Exception as e:
                    exception = e
                finally:
                    # 清理事件循环
                    try:
                        # 取消所有未完成的任务
                        pending = asyncio.all_tasks(loop)
                        for task in pending:
                            task.cancel()
                        
                        # 等待所有任务完成
                        if pending:
                            loop.run_until_complete(
                                asyncio.gather(*pending, return_exceptions=True)
                            )
                    except Exception as cleanup_error:
                        logger.warning(f"清理事件循环时出错: {cleanup_error}")
                    finally:
                        loop.close()
                        
            except Exception as e:
                exception = e
            
            return result, exception
        
        # 提交到线程池
        future = self.executor.submit(run_in_thread)
        
        # 添加回调处理
        if callback:
            def handle_result(fut):
                try:
                    result, exception = fut.result()
                    if exception:
                        callback(None, exception)
                    else:
                        callback(result, None)
                except Exception as e:
                    callback(None, e)
            
            future.add_done_callback(handle_result)
        
        return future
    
    def run_async_sync(self, coro: Coroutine, timeout: float = 30.0) -> Any:
        """
        同步运行异步协程（阻塞直到完成）
        
        Args:
            coro: 异步协程
            timeout: 超时时间（秒）
            
        Returns:
            协程的返回值
            
        Raises:
            异步操作中的异常
        """
        future = self.run_async(coro, timeout)
        
        try:
            result, exception = future.result(timeout=timeout + 5)  # 额外5秒缓冲
            if exception:
                raise exception
            return result
        except Exception as e:
            logger.error(f"同步异步操作失败: {e}")
            raise
    
    def shutdown(self, wait: bool = True):
        """关闭异步运行器"""
        self._shutdown = True
        self.executor.shutdown(wait=wait)
        logger.info("AsyncRunner已关闭")


class SafeAsyncRunner:
    """安全的异步运行器，带有更多错误处理和重试机制"""
    
    def __init__(self, max_retries: int = 3, retry_delay: float = 1.0):
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.runner = AsyncRunner()
    
    def run_with_retry(self, coro_factory: Callable[[], Coroutine], 
                      timeout: float = 30.0) -> Any:
        """
        带重试机制的异步运行
        
        Args:
            coro_factory: 创建协程的工厂函数
            timeout: 超时时间（秒）
            
        Returns:
            协程的返回值
        """
        last_exception = None
        
        for attempt in range(self.max_retries):
            try:
                coro = coro_factory()
                return self.runner.run_async_sync(coro, timeout)
                
            except Exception as e:
                last_exception = e
                logger.warning(f"异步操作失败 (尝试 {attempt + 1}/{self.max_retries}): {e}")
                
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay * (2 ** attempt))  # 指数退避
                    continue
                else:
                    break
        
        # 所有重试都失败了
        logger.error(f"异步操作最终失败: {last_exception}")
        raise last_exception
    
    def shutdown(self):
        """关闭安全异步运行器"""
        self.runner.shutdown()


# 全局异步运行器实例
_global_runner: Optional[AsyncRunner] = None


def get_async_runner() -> AsyncRunner:
    """获取全局异步运行器实例"""
    global _global_runner
    if _global_runner is None or _global_runner._shutdown:
        _global_runner = AsyncRunner()
    return _global_runner


def run_async_safe(coro: Coroutine, timeout: float = 30.0) -> Any:
    """
    安全运行异步协程的便捷函数
    
    Args:
        coro: 异步协程
        timeout: 超时时间（秒）
        
    Returns:
        协程的返回值
    """
    runner = get_async_runner()
    return runner.run_async_sync(coro, timeout)


def cleanup_async_runner():
    """清理全局异步运行器"""
    global _global_runner
    if _global_runner:
        _global_runner.shutdown()
        _global_runner = None
