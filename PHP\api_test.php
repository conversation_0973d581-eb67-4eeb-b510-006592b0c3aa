<?php
/**
 * Python API连接测试工具
 */

// 加载配置
require_once 'config/config.php';

echo "<h1>🐍 Python API连接测试</h1>";

// 显示配置信息
echo "<h2>📊 当前配置</h2>";
echo "<p><strong>API主机:</strong> " . PYTHON_API_HOST . "</p>";
echo "<p><strong>API端口:</strong> " . PYTHON_API_PORT . "</p>";
echo "<p><strong>API地址:</strong> " . PYTHON_API_BASE . "</p>";
echo "<p><strong>WebSocket地址:</strong> " . PYTHON_WS_BASE . "</p>";

// 测试函数
function testConnection($url, $name, $timeout = 5) {
    echo "<h3>🔗 测试 {$name}</h3>";
    echo "<p>URL: {$url}</p>";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 3);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'BitV-PHP-Client/1.0');
    
    $start_time = microtime(true);
    $response = curl_exec($ch);
    $end_time = microtime(true);
    
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    $response_time = round(($end_time - $start_time) * 1000, 2);
    
    curl_close($ch);
    
    echo "<div style='background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
    
    if ($curl_error) {
        echo "<p style='color: red;'>❌ <strong>连接错误:</strong> {$curl_error}</p>";
        return false;
    }
    
    echo "<p><strong>HTTP状态码:</strong> {$http_code}</p>";
    echo "<p><strong>响应时间:</strong> {$response_time}ms</p>";
    
    if ($http_code === 200) {
        echo "<p style='color: green;'>✅ <strong>连接成功!</strong></p>";
        echo "<p><strong>响应内容:</strong></p>";
        echo "<pre style='background: #fff; padding: 10px; border: 1px solid #ddd; max-height: 200px; overflow-y: auto;'>";
        echo htmlspecialchars($response);
        echo "</pre>";
        return true;
    } else {
        echo "<p style='color: red;'>❌ <strong>连接失败</strong></p>";
        if ($response) {
            echo "<p><strong>错误响应:</strong></p>";
            echo "<pre style='background: #fff; padding: 10px; border: 1px solid #ddd;'>";
            echo htmlspecialchars($response);
            echo "</pre>";
        }
        return false;
    }
    
    echo "</div>";
}

// 端口检测函数
function checkPort($host, $port, $timeout = 3) {
    $connection = @fsockopen($host, $port, $errno, $errstr, $timeout);
    if ($connection) {
        fclose($connection);
        return true;
    }
    return false;
}

// 检查端口是否开放
echo "<h2>🔌 端口检测</h2>";
$ports_to_check = [
    8000 => 'Python API端口',
    8001 => 'WebSocket端口'
];

foreach ($ports_to_check as $port => $description) {
    $is_open = checkPort(PYTHON_API_HOST, $port);
    $status = $is_open ? '✅ 开放' : '❌ 关闭';
    echo "<p><strong>{$description} ({$port}):</strong> {$status}</p>";
}

// 测试不同的API端点
echo "<h2>🧪 API端点测试</h2>";

$endpoints = [
    '/' => '根路径',
    '/health' => '健康检查',
    '/api/status' => '系统状态',
    '/api/trading/status' => '交易状态'
];

$results = [];
foreach ($endpoints as $endpoint => $name) {
    $url = PYTHON_API_BASE . $endpoint;
    $results[$endpoint] = testConnection($url, $name);
}

// 总结
echo "<h2>📋 测试总结</h2>";
$success_count = count(array_filter($results));
$total_count = count($results);

echo "<p><strong>成功连接:</strong> {$success_count}/{$total_count}</p>";

if ($success_count === 0) {
    echo "<div style='background: #ffebee; border: 1px solid #f44336; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3 style='color: #d32f2f; margin-top: 0;'>🚨 Python后端未运行</h3>";
    echo "<p><strong>可能的原因:</strong></p>";
    echo "<ul>";
    echo "<li>Python后端程序没有启动</li>";
    echo "<li>端口8000被其他程序占用</li>";
    echo "<li>防火墙阻止了连接</li>";
    echo "<li>Python程序启动时出现错误</li>";
    echo "</ul>";
    
    echo "<p><strong>解决步骤:</strong></p>";
    echo "<ol>";
    echo "<li>打开命令行，进入Python目录</li>";
    echo "<li>运行: <code>cd C:\\Users\\<USER>\\Desktop\\bitV</code></li>";
    echo "<li>运行: <code>python main.py</code></li>";
    echo "<li>检查是否有错误信息</li>";
    echo "<li>确认看到 '交易控制器组件初始化成功' 消息</li>";
    echo "</ol>";
    echo "</div>";
} else if ($success_count < $total_count) {
    echo "<div style='background: #fff3e0; border: 1px solid #ff9800; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3 style='color: #f57c00; margin-top: 0;'>⚠️ 部分连接成功</h3>";
    echo "<p>Python后端正在运行，但某些API端点可能还未完全初始化。</p>";
    echo "</div>";
} else {
    echo "<div style='background: #e8f5e8; border: 1px solid #4caf50; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3 style='color: #388e3c; margin-top: 0;'>🎉 所有连接成功!</h3>";
    echo "<p>Python后端运行正常，PHP前端应该可以正常工作了。</p>";
    echo "</div>";
}

// 实时测试按钮
echo "<h2>🔄 实时测试</h2>";
echo "<button onclick='location.reload()' style='background: #2196f3; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;'>刷新测试</button>";
echo " ";
echo "<button onclick='testQuickConnection()' style='background: #4caf50; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;'>快速测试</button>";

?>

<script>
function testQuickConnection() {
    const apiUrl = '<?php echo PYTHON_API_BASE; ?>/health';
    
    fetch(apiUrl)
        .then(response => {
            if (response.ok) {
                alert('✅ Python API连接成功!');
            } else {
                alert('❌ Python API连接失败 (HTTP ' + response.status + ')');
            }
        })
        .catch(error => {
            alert('❌ 连接错误: ' + error.message);
        });
}
</script>

<style>
body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
h1, h2, h3 { color: #333; }
h1 { background: #fff; padding: 20px; border-radius: 5px; }
h2 { border-bottom: 2px solid #ddd; padding-bottom: 5px; }
p { background: #fff; padding: 10px; margin: 5px 0; border-radius: 3px; }
code { background: #f0f0f0; padding: 2px 5px; border-radius: 3px; font-family: monospace; }
pre { font-family: monospace; font-size: 12px; }
button:hover { opacity: 0.8; }
</style>

<hr>
<p><a href="index.php">返回首页</a> | <a href="bt_test.php">环境测试</a> | <a href="debug.php">系统调试</a></p>
