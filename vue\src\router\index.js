import { createRouter, createWebHistory } from 'vue-router'
import Layout from '@/layout/index.vue'

const routes = [
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard/index.vue'),
        meta: { title: '仪表板', icon: 'Monitor' }
      },
      {
        path: 'config',
        name: 'Config',
        component: () => import('@/views/Config/index.vue'),
        meta: { title: '配置设置', icon: 'Setting' }
      },
      {
        path: 'strategy',
        name: 'Strategy',
        component: () => import('@/views/Strategy/index.vue'),
        meta: { title: '策略配置', icon: 'TrendCharts' }
      },
      {
        path: 'bollinger',
        name: 'Bolling<PERSON>',
        component: () => import('@/views/Strategies/Bollinger.vue'),
        meta: { title: '布林带策略', icon: 'DataLine' }
      },
      {
        path: 'macd',
        name: 'MACD',
        component: () => import('@/views/Strategies/MACD.vue'),
        meta: { title: 'MACD策略', icon: 'TrendCharts' }
      },
      {
        path: 'rsi',
        name: 'RSI',
        component: () => import('@/views/Strategies/RSI.vue'),
        meta: { title: 'RSI策略', icon: 'DataAnalysis' }
      },
      {
        path: 'kdj',
        name: 'KDJ',
        component: () => import('@/views/Strategies/KDJ.vue'),
        meta: { title: 'KDJ策略', icon: 'DataBoard' }
      },
      {
        path: 'ma',
        name: 'MA',
        component: () => import('@/views/Strategies/MA.vue'),
        meta: { title: '移动平均线策略', icon: 'LineChart' }
      },
      {
        path: 'mfi',
        name: 'MFI',
        component: () => import('@/views/Strategies/MFI.vue'),
        meta: { title: '资金流量指标策略', icon: 'Money' }
      },
      {
        path: 'adx',
        name: 'ADX',
        component: () => import('@/views/Strategies/ADX.vue'),
        meta: { title: '平均方向指数策略', icon: 'Compass' }
      },
      {
        path: 'obv',
        name: 'OBV',
        component: () => import('@/views/Strategies/OBV.vue'),
        meta: { title: '成交量平衡指标策略', icon: 'PieChart' }
      },
      {
        path: 'fibonacci',
        name: 'Fibonacci',
        component: () => import('@/views/Strategies/Fibonacci.vue'),
        meta: { title: '斐波那契回撤策略', icon: 'Grid' }
      },
      {
        path: 'williams',
        name: 'Williams',
        component: () => import('@/views/Strategies/Williams.vue'),
        meta: { title: '威廉指标策略', icon: 'ScaleToOriginal' }
      },
      {
        path: 'ichimoku',
        name: 'Ichimoku',
        component: () => import('@/views/Strategies/Ichimoku.vue'),
        meta: { title: '一目均衡表策略', icon: 'Histogram' }
      },
      {
        path: 'pinbar',
        name: 'Pinbar',
        component: () => import('@/views/Strategies/Pinbar.vue'),
        meta: { title: '插针策略', icon: 'Position' }
      },
      {
        path: 'risk',
        name: 'Risk',
        component: () => import('@/views/Risk/index.vue'),
        meta: { title: '资金设置', icon: 'Shield' }
      },
      {
        path: 'trading',
        name: 'Trading',
        component: () => import('@/views/Trading/index.vue'),
        meta: { title: '交易控制', icon: 'VideoPlay' }
      },
      {
        path: 'monitor',
        name: 'Monitor',
        component: () => import('@/views/Monitor/index.vue'),
        meta: { title: '实时监控', icon: 'View' }
      },
      {
        path: 'logs',
        name: 'Logs',
        component: () => import('@/views/Logs/index.vue'),
        meta: { title: '系统日志', icon: 'Document' }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
