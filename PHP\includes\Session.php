<?php
/**
 * 会话管理类
 * 
 * @description 安全的会话管理和用户认证
 */

class Session {
    private static $started = false;
    private static $db;

    /**
     * 启动会话
     */
    public static function start() {
        if (!self::$started) {
            // 配置会话参数
            ini_set('session.cookie_httponly', 1);
            ini_set('session.cookie_secure', isset($_SERVER['HTTPS']));
            ini_set('session.use_strict_mode', 1);
            ini_set('session.cookie_samesite', 'Strict');
            
            session_name(SESSION_NAME);
            session_set_cookie_params([
                'lifetime' => SESSION_LIFETIME,
                'path' => '/',
                'domain' => '',
                'secure' => isset($_SERVER['HTTPS']),
                'httponly' => true,
                'samesite' => 'Strict'
            ]);

            session_start();
            self::$started = true;
            self::$db = Database::getInstance();

            // 会话安全检查
            self::validateSession();
            
            // 记录会话活动
            self::logActivity();
        }
    }

    /**
     * 验证会话安全性
     */
    private static function validateSession() {
        // 检查会话劫持
        if (isset($_SESSION['user_ip']) && $_SESSION['user_ip'] !== self::getClientIP()) {
            self::destroy();
            throw new Exception('会话安全验证失败');
        }

        // 检查会话过期
        if (isset($_SESSION['last_activity']) && 
            (time() - $_SESSION['last_activity']) > SESSION_LIFETIME) {
            self::destroy();
            throw new Exception('会话已过期');
        }

        // 更新最后活动时间
        $_SESSION['last_activity'] = time();
        
        // 设置用户IP（首次访问）
        if (!isset($_SESSION['user_ip'])) {
            $_SESSION['user_ip'] = self::getClientIP();
        }

        // 生成CSRF令牌
        if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
            $_SESSION[CSRF_TOKEN_NAME] = self::generateCSRFToken();
        }
    }

    /**
     * 记录会话活动
     */
    private static function logActivity() {
        try {
            $userId = self::get('user_id', 'anonymous');
            $ip = self::getClientIP();
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
            
            self::$db->insert('system_logs', [
                'level' => 'INFO',
                'message' => '用户会话活动',
                'context' => json_encode([
                    'action' => 'session_activity',
                    'session_id' => session_id(),
                    'page' => $_SERVER['REQUEST_URI'] ?? ''
                ]),
                'user_id' => $userId,
                'ip_address' => $ip,
                'user_agent' => $userAgent
            ]);
        } catch (Exception $e) {
            // 静默处理日志错误
            error_log('会话活动记录失败: ' . $e->getMessage());
        }
    }

    /**
     * 设置会话变量
     */
    public static function set($key, $value) {
        self::ensureStarted();
        $_SESSION[$key] = $value;
    }

    /**
     * 获取会话变量
     */
    public static function get($key, $default = null) {
        self::ensureStarted();
        return $_SESSION[$key] ?? $default;
    }

    /**
     * 检查会话变量是否存在
     */
    public static function has($key) {
        self::ensureStarted();
        return isset($_SESSION[$key]);
    }

    /**
     * 删除会话变量
     */
    public static function remove($key) {
        self::ensureStarted();
        unset($_SESSION[$key]);
    }

    /**
     * 销毁会话
     */
    public static function destroy() {
        self::ensureStarted();
        
        // 记录会话销毁
        try {
            $userId = self::get('user_id', 'anonymous');
            self::$db->insert('system_logs', [
                'level' => 'INFO',
                'message' => '用户会话销毁',
                'context' => json_encode([
                    'action' => 'session_destroy',
                    'session_id' => session_id()
                ]),
                'user_id' => $userId,
                'ip_address' => self::getClientIP()
            ]);
        } catch (Exception $e) {
            error_log('会话销毁记录失败: ' . $e->getMessage());
        }

        $_SESSION = [];
        
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params["path"], $params["domain"],
                $params["secure"], $params["httponly"]
            );
        }
        
        session_destroy();
        self::$started = false;
    }

    /**
     * 重新生成会话ID
     */
    public static function regenerateId() {
        self::ensureStarted();
        session_regenerate_id(true);
    }

    /**
     * 生成CSRF令牌
     */
    public static function generateCSRFToken() {
        return bin2hex(random_bytes(32));
    }

    /**
     * 验证CSRF令牌
     */
    public static function validateCSRFToken($token) {
        $sessionToken = self::get(CSRF_TOKEN_NAME);
        return $sessionToken && hash_equals($sessionToken, $token);
    }

    /**
     * 获取CSRF令牌
     */
    public static function getCSRFToken() {
        return self::get(CSRF_TOKEN_NAME);
    }

    /**
     * 用户登录
     */
    public static function login($userId, $userData = []) {
        self::ensureStarted();
        
        // 重新生成会话ID防止会话固定攻击
        self::regenerateId();
        
        // 设置用户信息
        self::set('user_id', $userId);
        self::set('user_data', $userData);
        self::set('login_time', time());
        self::set('is_authenticated', true);
        
        // 记录登录
        try {
            self::$db->insert('system_logs', [
                'level' => 'INFO',
                'message' => '用户登录成功',
                'context' => json_encode([
                    'action' => 'user_login',
                    'session_id' => session_id()
                ]),
                'user_id' => $userId,
                'ip_address' => self::getClientIP(),
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]);
        } catch (Exception $e) {
            error_log('登录记录失败: ' . $e->getMessage());
        }
    }

    /**
     * 用户登出
     */
    public static function logout() {
        $userId = self::get('user_id', 'anonymous');
        
        // 记录登出
        try {
            self::$db->insert('system_logs', [
                'level' => 'INFO',
                'message' => '用户登出',
                'context' => json_encode([
                    'action' => 'user_logout',
                    'session_id' => session_id()
                ]),
                'user_id' => $userId,
                'ip_address' => self::getClientIP()
            ]);
        } catch (Exception $e) {
            error_log('登出记录失败: ' . $e->getMessage());
        }
        
        self::destroy();
    }

    /**
     * 检查用户是否已认证
     */
    public static function isAuthenticated() {
        return self::get('is_authenticated', false) === true;
    }

    /**
     * 获取当前用户ID
     */
    public static function getUserId() {
        return self::get('user_id');
    }

    /**
     * 获取用户数据
     */
    public static function getUserData($key = null) {
        $userData = self::get('user_data', []);
        return $key ? ($userData[$key] ?? null) : $userData;
    }

    /**
     * 获取客户端IP地址
     */
    private static function getClientIP() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                // 处理多个IP的情况（X-Forwarded-For可能包含多个IP）
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                // 验证IP格式
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }

    /**
     * 确保会话已启动
     */
    private static function ensureStarted() {
        if (!self::$started) {
            self::start();
        }
    }

    /**
     * 获取会话信息
     */
    public static function getSessionInfo() {
        self::ensureStarted();
        
        return [
            'session_id' => session_id(),
            'user_id' => self::getUserId(),
            'is_authenticated' => self::isAuthenticated(),
            'login_time' => self::get('login_time'),
            'last_activity' => self::get('last_activity'),
            'user_ip' => self::get('user_ip'),
            'csrf_token' => self::getCSRFToken()
        ];
    }
}
?>
