<template>
  <div class="layout-container">
    <!-- 顶部导航栏 -->
    <el-header class="layout-header">
      <div class="header-left">
        <el-button
          :icon="Expand"
          circle
          @click="toggleSidebar"
          class="sidebar-toggle"
        />
        <h1 class="system-title">
          <el-icon><TrendCharts /></el-icon>
          BitV MACD智能加仓交易系统
        </h1>
      </div>
      
      <div class="header-right">
        <!-- 系统状态指示器 -->
        <div class="status-indicators">
          <el-tooltip content="Python后端连接状态">
            <el-badge
              :value="systemStore.apiStatus ? '正常' : '断开'"
              :type="systemStore.apiStatus ? 'success' : 'danger'"
              class="status-badge"
            >
              <el-icon :class="['status-icon', systemStore.apiStatus ? 'online' : 'offline']">
                <Connection />
              </el-icon>
            </el-badge>
          </el-tooltip>
          
          <el-tooltip content="WebSocket连接状态">
            <el-badge
              :value="wsStore.connected ? '已连接' : '未连接'"
              :type="wsStore.connected ? 'success' : 'warning'"
              class="status-badge"
            >
              <el-icon :class="['status-icon', wsStore.connected ? 'online' : 'offline']">
                <Wifi />
              </el-icon>
            </el-badge>
          </el-tooltip>
          
          <el-tooltip content="交易状态">
            <el-badge
              :value="tradingStore.isRunning ? '运行中' : '已停止'"
              :type="tradingStore.isRunning ? 'success' : 'info'"
              class="status-badge"
            >
              <el-icon :class="['status-icon', tradingStore.isRunning ? 'running' : 'stopped']">
                <VideoPlay />
              </el-icon>
            </el-badge>
          </el-tooltip>
        </div>
        
        <!-- 用户菜单 -->
        <el-dropdown>
          <span class="user-dropdown">
            <el-avatar :size="32" :icon="UserFilled" />
            <span class="username">管理员</span>
            <el-icon><ArrowDown /></el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="toggleTheme">
                <el-icon><Moon /></el-icon>
                切换主题
              </el-dropdown-item>
              <el-dropdown-item @click="showAbout">
                <el-icon><InfoFilled /></el-icon>
                关于系统
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </el-header>

    <el-container class="layout-body">
      <!-- 侧边栏 -->
      <el-aside :width="sidebarWidth" class="layout-sidebar">
        <el-scrollbar>
          <el-menu
            :default-active="$route.path"
            :collapse="isCollapse"
            router
            class="sidebar-menu"
          >
            <!-- 基础功能 -->
            <el-menu-item index="/dashboard">
              <el-icon><Monitor /></el-icon>
              <template #title>仪表板</template>
            </el-menu-item>
            
            <el-menu-item index="/config">
              <el-icon><Setting /></el-icon>
              <template #title>配置设置</template>
            </el-menu-item>
            
            <!-- 策略配置分组 -->
            <el-sub-menu index="strategies">
              <template #title>
                <el-icon><TrendCharts /></el-icon>
                <span>策略配置</span>
              </template>
              
              <el-menu-item index="/strategy">
                <el-icon><Setting /></el-icon>
                <template #title>基础策略</template>
              </el-menu-item>
              
              <el-menu-item index="/bollinger">
                <el-icon><DataLine /></el-icon>
                <template #title>布林带策略</template>
              </el-menu-item>
              
              <el-menu-item index="/macd">
                <el-icon><TrendCharts /></el-icon>
                <template #title>MACD策略</template>
              </el-menu-item>
              
              <el-menu-item index="/rsi">
                <el-icon><DataAnalysis /></el-icon>
                <template #title>RSI策略</template>
              </el-menu-item>
              
              <el-menu-item index="/kdj">
                <el-icon><DataBoard /></el-icon>
                <template #title>KDJ策略</template>
              </el-menu-item>
              
              <el-menu-item index="/ma">
                <el-icon><LineChart /></el-icon>
                <template #title>移动平均线</template>
              </el-menu-item>
              
              <el-menu-item index="/mfi">
                <el-icon><Money /></el-icon>
                <template #title>资金流量指标</template>
              </el-menu-item>
              
              <el-menu-item index="/adx">
                <el-icon><Compass /></el-icon>
                <template #title>平均方向指数</template>
              </el-menu-item>
              
              <el-menu-item index="/obv">
                <el-icon><PieChart /></el-icon>
                <template #title>成交量平衡指标</template>
              </el-menu-item>
              
              <el-menu-item index="/fibonacci">
                <el-icon><Grid /></el-icon>
                <template #title>斐波那契回撤</template>
              </el-menu-item>
              
              <el-menu-item index="/williams">
                <el-icon><ScaleToOriginal /></el-icon>
                <template #title>威廉指标</template>
              </el-menu-item>
              
              <el-menu-item index="/ichimoku">
                <el-icon><Histogram /></el-icon>
                <template #title>一目均衡表</template>
              </el-menu-item>
              
              <el-menu-item index="/pinbar">
                <el-icon><Position /></el-icon>
                <template #title>插针策略</template>
              </el-menu-item>
            </el-sub-menu>
            
            <!-- 交易管理 -->
            <el-menu-item index="/risk">
              <el-icon><Shield /></el-icon>
              <template #title>资金设置</template>
            </el-menu-item>
            
            <el-menu-item index="/trading">
              <el-icon><VideoPlay /></el-icon>
              <template #title>交易控制</template>
            </el-menu-item>
            
            <el-menu-item index="/monitor">
              <el-icon><View /></el-icon>
              <template #title>实时监控</template>
            </el-menu-item>
            
            <el-menu-item index="/logs">
              <el-icon><Document /></el-icon>
              <template #title>系统日志</template>
            </el-menu-item>
          </el-menu>
        </el-scrollbar>
      </el-aside>

      <!-- 主内容区域 -->
      <el-main class="layout-main">
        <div class="main-content">
          <router-view />
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useSystemStore } from '@/stores/system'
import { useWebSocketStore } from '@/stores/websocket'
import { useTradingStore } from '@/stores/trading'
import {
  Expand,
  Fold,
  TrendCharts,
  Connection,
  Wifi,
  VideoPlay,
  UserFilled,
  ArrowDown,
  Moon,
  InfoFilled,
  Monitor,
  Setting,
  DataLine,
  DataAnalysis,
  DataBoard,
  LineChart,
  Money,
  Compass,
  PieChart,
  Grid,
  ScaleToOriginal,
  Histogram,
  Position,
  Shield,
  View,
  Document
} from '@element-plus/icons-vue'

const systemStore = useSystemStore()
const wsStore = useWebSocketStore()
const tradingStore = useTradingStore()

const isCollapse = ref(false)

const sidebarWidth = computed(() => {
  return isCollapse.value ? '64px' : '240px'
})

const toggleSidebar = () => {
  isCollapse.value = !isCollapse.value
}

const toggleTheme = () => {
  // 主题切换逻辑
  ElMessage.info('主题切换功能开发中...')
}

const showAbout = () => {
  ElMessageBox.alert(
    'BitV MACD智能加仓交易系统 v1.0.0\n\n基于Vue 3 + Python FastAPI的现代化交易系统\n支持多种技术指标策略和智能风险管理',
    '关于系统',
    {
      confirmButtonText: '确定'
    }
  )
}
</script>

<style lang="scss" scoped>
.layout-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.layout-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;
    
    .sidebar-toggle {
      background: rgba(255, 255, 255, 0.1);
      border: none;
      color: white;
      
      &:hover {
        background: rgba(255, 255, 255, 0.2);
      }
    }
    
    .system-title {
      font-size: 20px;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0;
    }
  }
  
  .header-right {
    display: flex;
    align-items: center;
    gap: 20px;
    
    .status-indicators {
      display: flex;
      gap: 12px;
      
      .status-badge {
        .status-icon {
          font-size: 18px;
          
          &.online, &.running {
            color: #67c23a;
          }
          
          &.offline, &.stopped {
            color: #f56c6c;
          }
        }
      }
    }
    
    .user-dropdown {
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;
      padding: 8px;
      border-radius: 6px;
      transition: background-color 0.3s;
      
      &:hover {
        background: rgba(255, 255, 255, 0.1);
      }
      
      .username {
        font-size: 14px;
      }
    }
  }
}

.layout-body {
  flex: 1;
  overflow: hidden;
}

.layout-sidebar {
  background: #f8f9fa;
  border-right: 1px solid #e9ecef;
  transition: width 0.3s;
  
  .sidebar-menu {
    border: none;
    height: 100%;
    
    .el-menu-item, .el-sub-menu__title {
      height: 48px;
      line-height: 48px;
      
      &:hover {
        background: #e3f2fd;
      }
      
      &.is-active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        
        .el-icon {
          color: white;
        }
      }
    }
  }
}

.layout-main {
  background: #f5f7fa;
  padding: 0;
  overflow: hidden;
  
  .main-content {
    height: 100%;
    padding: 20px;
    overflow-y: auto;
  }
}
</style>
