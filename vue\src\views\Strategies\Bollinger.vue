<template>
  <div class="bollinger-strategy">
    <div class="page-header">
      <h2>布林带策略配置</h2>
      <p>配置布林带技术指标参数和交易策略</p>
    </div>

    <el-row :gutter="20">
      <el-col :span="16">
        <div class="card">
          <h3 class="mb-md">布林带参数配置</h3>
          
          <el-form :model="config" :rules="rules" ref="formRef" label-width="120px" class="form-container">
            <div class="form-section">
              <h4 class="section-title">基础参数</h4>
              
              <el-form-item label="周期" prop="period">
                <el-input-number v-model="config.period" :min="5" :max="100" style="width: 200px" />
                <span class="param-desc">移动平均线周期</span>
              </el-form-item>
              
              <el-form-item label="标准差倍数" prop="std_multiplier">
                <el-input-number v-model="config.std_multiplier" :min="1" :max="5" :step="0.1" :precision="1" style="width: 200px" />
                <span class="param-desc">布林带宽度控制</span>
              </el-form-item>
            </div>

            <div class="form-section">
              <h4 class="section-title">交易信号</h4>
              
              <el-form-item label="开仓信号" prop="entry_signal">
                <el-select v-model="config.entry_signal" style="width: 200px">
                  <el-option label="价格突破上轨" value="break_upper" />
                  <el-option label="价格突破下轨" value="break_lower" />
                  <el-option label="价格回归中轨" value="return_middle" />
                  <el-option label="布林带收缩" value="squeeze" />
                </el-select>
              </el-form-item>
              
              <el-form-item label="平仓信号" prop="exit_signal">
                <el-select v-model="config.exit_signal" style="width: 200px">
                  <el-option label="价格回归中轨" value="return_middle" />
                  <el-option label="反向突破" value="reverse_break" />
                  <el-option label="止盈止损" value="stop_profit_loss" />
                </el-select>
              </el-form-item>
            </div>

            <div class="form-actions">
              <el-button type="primary" @click="saveConfig" :loading="saving">保存配置</el-button>
              <el-button @click="testStrategy" :loading="testing">策略回测</el-button>
              <el-button @click="resetConfig">重置配置</el-button>
            </div>
          </el-form>
        </div>
      </el-col>

      <el-col :span="8">
        <div class="card">
          <h3 class="mb-md">策略说明</h3>
          <div class="strategy-description">
            <h4>布林带指标原理</h4>
            <p>布林带由三条线组成：中轨（移动平均线）、上轨（中轨+标准差）、下轨（中轨-标准差）。</p>
            
            <h4>交易策略</h4>
            <ul>
              <li><strong>突破策略：</strong>价格突破上轨做多，突破下轨做空</li>
              <li><strong>回归策略：</strong>价格偏离后回归中轨</li>
              <li><strong>收缩策略：</strong>布林带收缩后的突破</li>
            </ul>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import api from '@/utils/api'

const saving = ref(false)
const testing = ref(false)
const formRef = ref()

const config = reactive({
  period: 20,
  std_multiplier: 2.0,
  entry_signal: 'break_upper',
  exit_signal: 'return_middle'
})

const rules = {
  period: [{ required: true, message: '请输入周期', trigger: 'blur' }],
  std_multiplier: [{ required: true, message: '请输入标准差倍数', trigger: 'blur' }]
}

const saveConfig = async () => {
  if (!formRef.value) return
  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return
  
  saving.value = true
  try {
    await api.post('/api/strategy/bollinger/config', config)
    ElMessage.success('布林带策略配置保存成功')
  } catch (error) {
    ElMessage.error(`保存失败: ${error.message}`)
  } finally {
    saving.value = false
  }
}

const testStrategy = async () => {
  testing.value = true
  try {
    await api.post('/api/strategy/bollinger/test', config)
    ElMessage.success('策略回测完成')
  } catch (error) {
    ElMessage.error(`回测失败: ${error.message}`)
  } finally {
    testing.value = false
  }
}

const resetConfig = () => {
  Object.assign(config, {
    period: 20,
    std_multiplier: 2.0,
    entry_signal: 'break_upper',
    exit_signal: 'return_middle'
  })
}

onMounted(async () => {
  try {
    const response = await api.get('/api/strategy/bollinger/config')
    if (response.data.success && response.data.data) {
      Object.assign(config, response.data.data)
    }
  } catch (error) {
    console.error('加载配置失败:', error)
  }
})
</script>

<style lang="scss" scoped>
.bollinger-strategy {
  .page-header {
    margin-bottom: var(--spacing-lg);
    
    h2 {
      margin-bottom: var(--spacing-xs);
      color: var(--text-color-primary);
    }
    
    p {
      color: var(--text-color-secondary);
    }
  }
  
  .param-desc {
    margin-left: var(--spacing-sm);
    color: var(--text-color-placeholder);
    font-size: 12px;
  }
  
  .form-actions {
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color-lighter);
    
    .el-button {
      margin-right: var(--spacing-sm);
    }
  }
  
  .strategy-description {
    h4 {
      color: var(--text-color-primary);
      margin: var(--spacing-md) 0 var(--spacing-sm) 0;
      font-size: 14px;
      
      &:first-child {
        margin-top: 0;
      }
    }
    
    p {
      color: var(--text-color-regular);
      font-size: 13px;
      line-height: 1.6;
      margin-bottom: var(--spacing-sm);
    }
    
    ul {
      margin: 0;
      padding-left: 20px;
      
      li {
        color: var(--text-color-regular);
        font-size: 13px;
        line-height: 1.6;
        margin-bottom: var(--spacing-xs);
        
        strong {
          color: var(--text-color-primary);
        }
      }
    }
  }
}
</style>
