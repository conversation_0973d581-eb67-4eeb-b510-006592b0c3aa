<?php
/**
 * 直接测试HomeController
 */

// 错误报告设置
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 定义常量
define('ROOT_PATH', __DIR__);
define('CONFIG_PATH', ROOT_PATH . '/config');
define('INCLUDES_PATH', ROOT_PATH . '/includes');
define('TEMPLATES_PATH', ROOT_PATH . '/templates');

// 加载核心配置
require_once CONFIG_PATH . '/config.php';
require_once INCLUDES_PATH . '/functions.php';
require_once INCLUDES_PATH . '/Database.php';
require_once INCLUDES_PATH . '/ApiClient.php';
require_once INCLUDES_PATH . '/Session.php';
require_once 'controllers/BaseController.php';
require_once 'controllers/HomeController.php';

echo "<h1>🧪 HomeController 直接测试</h1>";

try {
    // 启动会话
    Session::start();
    echo "<p>✅ 会话启动成功</p>";
    
    // 创建控制器实例
    $controller = new HomeController();
    echo "<p>✅ HomeController 创建成功</p>";
    
    // 捕获输出
    ob_start();
    
    // 调用index方法
    echo "<h2>调用 index() 方法...</h2>";
    $controller->index();
    
    // 获取输出
    $output = ob_get_clean();
    
    echo "<h2>输出结果:</h2>";
    echo "<div style='border: 1px solid #ddd; padding: 15px; background: #f9f9f9;'>";
    echo $output;
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p>❌ 错误: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>堆栈跟踪:</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><a href='index.php'>返回首页</a></p>";
?>
