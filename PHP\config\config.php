<?php
/**
 * BitV MACD智能加仓交易系统 - 核心配置
 *
 * @description 基于Python后端系统的完整配置映射
 * <AUTHOR> Trading Team
 * @version 1.0.0
 */

// 系统基础配置
define('SYSTEM_NAME', 'BitV MACD智能加仓交易系统');
define('SYSTEM_VERSION', '1.0.0');
define('DEBUG_MODE', true);
define('TIMEZONE', 'Asia/Shanghai');

// 设置时区
date_default_timezone_set(TIMEZONE);

// Python后端API配置
define('PYTHON_API_HOST', 'localhost');
define('PYTHON_API_PORT', 8000);
define('PYTHON_WS_PORT', 8001);
define('PYTHON_API_BASE', 'http://' . PYTHON_API_HOST . ':' . PYTHON_API_PORT);
define('PYTHON_WS_BASE', 'ws://' . PYTHON_API_HOST . ':' . PYTHON_WS_PORT);

// 数据库配置
define('DB_HOST', 'localhost');
define('DB_NAME', 'php');
define('DB_USER', 'php');
define('DB_PASS', 'php');
define('DB_CHARSET', 'utf8mb4');

// 会话和安全配置
define('SESSION_LIFETIME', 86400); // 24小时
define('SESSION_NAME', 'BITV_SESSION');
define('CSRF_TOKEN_NAME', '_token');
define('API_RATE_LIMIT', 100); // 每分钟请求限制
define('ENCRYPTION_KEY', 'BitV_Trading_System_2024_Secure_Key');

// 支持的交易所配置 (对应Python config.py)
$SUPPORTED_EXCHANGES = [
    'okx' => [
        'name' => 'OKX',
        'display_name' => 'OKX交易所',
        'api_version' => 'v5',
        'requires_passphrase' => true,
        'base_url' => 'https://www.okx.com',
        'ws_url' => 'wss://ws.okx.com:8443/ws/v5',
        'supported_symbols' => [
            'BTC-USDT-SWAP' => 'BTC永续合约',
            'ETH-USDT-SWAP' => 'ETH永续合约',
            'BNB-USDT-SWAP' => 'BNB永续合约',
            'SOL-USDT-SWAP' => 'SOL永续合约',
            'DOGE-USDT-SWAP' => 'DOGE永续合约'
        ],
        'timeframes' => ['1m', '5m', '15m', '30m', '1h', '4h', '1d'],
        'max_leverage' => 100
    ],
    'gateio' => [
        'name' => 'Gate.io',
        'display_name' => 'Gate.io交易所',
        'api_version' => 'v4',
        'requires_passphrase' => false,
        'base_url' => 'https://api.gateio.ws',
        'ws_url' => 'wss://fx-ws.gateio.ws/v4/ws',
        'supported_symbols' => [
            'BTC_USDT' => 'BTC永续合约',
            'ETH_USDT' => 'ETH永续合约',
            'BNB_USDT' => 'BNB永续合约'
        ],
        'timeframes' => ['1m', '5m', '15m', '30m', '1h', '4h', '1d'],
        'max_leverage' => 100
    ]
];

// 交易策略配置 (对应Python strategies)
$SUPPORTED_STRATEGIES = [
    'macd' => [
        'name' => 'MACD策略',
        'description' => '基于MACD指标的趋势跟踪策略',
        'class' => 'MACDStrategy',
        'enabled' => true,
        'parameters' => [
            'fast_period' => [
                'type' => 'integer',
                'default' => 12,
                'min' => 5,
                'max' => 50,
                'description' => 'MACD快线周期'
            ],
            'slow_period' => [
                'type' => 'integer',
                'default' => 26,
                'min' => 10,
                'max' => 100,
                'description' => 'MACD慢线周期'
            ],
            'signal_period' => [
                'type' => 'integer',
                'default' => 9,
                'min' => 3,
                'max' => 30,
                'description' => 'MACD信号线周期'
            ],
            'min_signal_strength' => [
                'type' => 'float',
                'default' => 0.3,
                'min' => 0.1,
                'max' => 1.0,
                'description' => '最小信号强度阈值'
            ]
        ]
    ],
    'bollinger' => [
        'name' => '布林带策略',
        'description' => '基于布林带的均值回归策略',
        'class' => 'BollingerStrategy',
        'enabled' => true,
        'parameters' => [
            'period' => [
                'type' => 'integer',
                'default' => 20,
                'min' => 10,
                'max' => 50,
                'description' => '布林带周期'
            ],
            'std_dev' => [
                'type' => 'float',
                'default' => 2.0,
                'min' => 1.0,
                'max' => 3.0,
                'description' => '标准差倍数'
            ],
            'trigger_distance_points' => [
                'type' => 'float',
                'default' => 2.0,
                'min' => 0.5,
                'max' => 10.0,
                'description' => '触发距离(点数)'
            ]
        ]
    ],
    'rsi' => [
        'name' => 'RSI策略',
        'description' => '基于相对强弱指标的超买超卖策略',
        'class' => 'RSIStrategy',
        'enabled' => false,
        'parameters' => [
            'period' => [
                'type' => 'integer',
                'default' => 14,
                'min' => 5,
                'max' => 30,
                'description' => 'RSI计算周期'
            ],
            'overbought' => [
                'type' => 'integer',
                'default' => 70,
                'min' => 60,
                'max' => 90,
                'description' => '超买阈值'
            ],
            'oversold' => [
                'type' => 'integer',
                'default' => 30,
                'min' => 10,
                'max' => 40,
                'description' => '超卖阈值'
            ]
        ]
    ]
];

// 风险管理配置 (对应Python config.py)
$RISK_MANAGEMENT = [
    'max_leverage' => 100,
    'min_margin' => 10.0,
    'max_margin' => 50000.0,
    'max_add_times' => 10,
    'min_add_interval' => 60, // 秒
    'max_daily_loss_percent' => 50.0,
    'emergency_stop_loss_percent' => 80.0,
    'liquidation_warning_distance' => 0.1, // 10%
    'position_size_limits' => [
        'min_position_size' => 0.001,
        'max_position_size' => 1000.0,
        'max_position_ratio' => 0.5 // 最大持仓比例
    ]
];

// 时间周期配置
$TIMEFRAMES = [
    '1m' => '1分钟',
    '5m' => '5分钟',
    '15m' => '15分钟',
    '30m' => '30分钟',
    '1h' => '1小时',
    '4h' => '4小时',
    '1d' => '1天'
];

// 加仓类型配置 (对应Python add_position_strategy.py)
$ADD_POSITION_TYPES = [
    'equal' => [
        'name' => '等量加仓',
        'description' => '每次加仓使用相同的保证金数量',
        'ratio' => 1.0
    ],
    'half' => [
        'name' => '半量加仓',
        'description' => '每次加仓使用初始保证金的一半',
        'ratio' => 0.5
    ],
    'custom' => [
        'name' => '自定义比例',
        'description' => '使用自定义的加仓比例',
        'ratio' => 0.3
    ]
];

// 交易状态枚举 (对应Python TradingState)
$TRADING_STATES = [
    'idle' => '空闲',
    'analyzing' => '分析中',
    'opening' => '开仓中',
    'monitoring' => '监控中',
    'adding' => '加仓中',
    'closing' => '平仓中',
    'error' => '错误状态'
];

// 风险等级 (对应Python RiskLevel)
$RISK_LEVELS = [
    'safe' => [
        'name' => '安全',
        'color' => '#28a745',
        'threshold' => 0.5
    ],
    'warning' => [
        'name' => '警告',
        'color' => '#ffc107',
        'threshold' => 0.3
    ],
    'danger' => [
        'name' => '危险',
        'color' => '#fd7e14',
        'threshold' => 0.1
    ],
    'critical' => [
        'name' => '极危险',
        'color' => '#dc3545',
        'threshold' => 0.05
    ]
];

// 日志配置
$LOG_CONFIG = [
    'enabled' => true,
    'level' => DEBUG_MODE ? 'DEBUG' : 'INFO',
    'file' => 'logs/trading.log',
    'max_size' => 10 * 1024 * 1024, // 10MB
    'max_files' => 5,
    'auto_cleanup' => true,
    'cleanup_interval' => 3600, // 1小时
    'retention_hours' => 24
];

// 默认用户配置 (对应user_config.json)
$DEFAULT_USER_CONFIG = [
    'exchange' => 'okx',
    'symbol' => 'BTC-USDT-SWAP',
    'leverage' => 10,
    'initial_margin' => 100.0,
    'timeframe' => '30m',
    'macd_enabled' => true,
    'macd_fast' => 12,
    'macd_slow' => 26,
    'macd_signal' => 9,
    'min_signal_strength' => 0.3,
    'alert_points' => 1.5,
    'add_position_types' => ['half'],
    'max_add_times' => 3,
    'price_check_interval' => 5,
    'position_check_interval' => 10,
    'risk_control_enabled' => true,
    'emergency_stop' => false,
    'bollinger_enabled' => false,
    'auto_start' => false
];

// 导出全局配置
$GLOBALS['SUPPORTED_EXCHANGES'] = $SUPPORTED_EXCHANGES;
$GLOBALS['SUPPORTED_STRATEGIES'] = $SUPPORTED_STRATEGIES;
$GLOBALS['RISK_MANAGEMENT'] = $RISK_MANAGEMENT;
$GLOBALS['TIMEFRAMES'] = $TIMEFRAMES;
$GLOBALS['ADD_POSITION_TYPES'] = $ADD_POSITION_TYPES;
$GLOBALS['TRADING_STATES'] = $TRADING_STATES;
$GLOBALS['RISK_LEVELS'] = $RISK_LEVELS;
$GLOBALS['LOG_CONFIG'] = $LOG_CONFIG;
$GLOBALS['DEFAULT_USER_CONFIG'] = $DEFAULT_USER_CONFIG;
?>
