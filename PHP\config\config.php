<?php
/**
 * 系统配置文件
 * 
 * @description 定义系统的基础配置参数
 */

// 调试模式
define('DEBUG_MODE', true);

// 数据库配置
define('DB_HOST', 'localhost');
define('DB_NAME', 'bitv_trading');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// Python后端API配置
define('PYTHON_API_BASE', 'http://localhost:8000');
define('PYTHON_WS_BASE', 'ws://localhost:8001');

// 会话配置
define('SESSION_LIFETIME', 3600 * 24); // 24小时
define('SESSION_NAME', 'BITV_TRADING_SESSION');

// 安全配置
define('CSRF_TOKEN_NAME', '_token');
define('API_RATE_LIMIT', 100); // 每分钟请求限制

// 系统配置
define('SYSTEM_NAME', 'BitV MACD智能加仓交易系统');
define('SYSTEM_VERSION', '1.0.0');
define('SYSTEM_AUTHOR', 'BitV Trading Team');

// 交易所配置
$SUPPORTED_EXCHANGES = [
    'okx' => [
        'name' => 'OKX',
        'display_name' => 'OKX交易所',
        'api_version' => 'v5',
        'requires_passphrase' => true,
        'supported_symbols' => ['BTC-USDT-SWAP', 'ETH-USDT-SWAP', 'BNB-USDT-SWAP'],
        'timeframes' => ['1m', '5m', '15m', '30m', '1h', '4h', '1d']
    ],
    'gateio' => [
        'name' => 'Gate.io',
        'display_name' => 'Gate.io交易所',
        'api_version' => 'v4',
        'requires_passphrase' => false,
        'supported_symbols' => ['BTC_USDT', 'ETH_USDT', 'BNB_USDT'],
        'timeframes' => ['1m', '5m', '15m', '30m', '1h', '4h', '1d']
    ]
];

// 策略配置
$SUPPORTED_STRATEGIES = [
    'macd' => [
        'name' => 'MACD策略',
        'description' => '基于MACD指标的趋势跟踪策略',
        'parameters' => [
            'fast_period' => ['type' => 'int', 'default' => 12, 'min' => 5, 'max' => 50],
            'slow_period' => ['type' => 'int', 'default' => 26, 'min' => 10, 'max' => 100],
            'signal_period' => ['type' => 'int', 'default' => 9, 'min' => 3, 'max' => 30],
            'min_signal_strength' => ['type' => 'float', 'default' => 0.3, 'min' => 0.1, 'max' => 1.0]
        ]
    ],
    'bollinger' => [
        'name' => '布林带策略',
        'description' => '基于布林带的均值回归策略',
        'parameters' => [
            'period' => ['type' => 'int', 'default' => 20, 'min' => 10, 'max' => 50],
            'std_dev' => ['type' => 'float', 'default' => 2.0, 'min' => 1.0, 'max' => 3.0],
            'trigger_distance' => ['type' => 'float', 'default' => 1.0, 'min' => 0.5, 'max' => 5.0]
        ]
    ],
    'rsi' => [
        'name' => 'RSI策略',
        'description' => '基于相对强弱指标的超买超卖策略',
        'parameters' => [
            'period' => ['type' => 'int', 'default' => 14, 'min' => 5, 'max' => 30],
            'overbought' => ['type' => 'int', 'default' => 70, 'min' => 60, 'max' => 90],
            'oversold' => ['type' => 'int', 'default' => 30, 'min' => 10, 'max' => 40]
        ]
    ]
];

// 风险管理配置
$RISK_MANAGEMENT = [
    'max_leverage' => 100,
    'min_margin' => 10.0,
    'max_margin' => 10000.0,
    'max_add_times' => 10,
    'min_add_interval' => 60, // 秒
    'max_daily_loss' => 0.5, // 50%
    'emergency_stop_loss' => 0.8 // 80%
];

// 时间周期配置
$TIMEFRAMES = [
    '1m' => '1分钟',
    '5m' => '5分钟',
    '15m' => '15分钟',
    '30m' => '30分钟',
    '1h' => '1小时',
    '4h' => '4小时',
    '1d' => '1天'
];

// 加仓类型配置
$ADD_POSITION_TYPES = [
    'equal' => '等量加仓',
    'half' => '半量加仓',
    'custom' => '自定义比例'
];

// 日志级别
$LOG_LEVELS = [
    'DEBUG' => 'debug',
    'INFO' => 'info',
    'WARNING' => 'warning',
    'ERROR' => 'error',
    'CRITICAL' => 'critical'
];

// 导出全局变量
$GLOBALS['SUPPORTED_EXCHANGES'] = $SUPPORTED_EXCHANGES;
$GLOBALS['SUPPORTED_STRATEGIES'] = $SUPPORTED_STRATEGIES;
$GLOBALS['RISK_MANAGEMENT'] = $RISK_MANAGEMENT;
$GLOBALS['TIMEFRAMES'] = $TIMEFRAMES;
$GLOBALS['ADD_POSITION_TYPES'] = $ADD_POSITION_TYPES;
$GLOBALS['LOG_LEVELS'] = $LOG_LEVELS;
?>
