<?php
/**
 * BitV MACD智能加仓交易系统 - 系统日志页面
 * 实盘交易版本 - 完整的系统日志监控和分析界面
 */

session_start();

// 加载配置
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/security.php';

// 检查认证和权限
requireAuth();
requirePermission('admin'); // 只有管理员可以查看系统日志

$pageTitle = '系统日志';
$currentPage = 'logs';

// 获取查询参数
$page = max(1, (int)($_GET['page'] ?? 1));
$limit = max(10, min(100, (int)($_GET['limit'] ?? 50)));
$level = sanitizeInput($_GET['level'] ?? '');
$category = sanitizeInput($_GET['category'] ?? '');
$date_from = sanitizeInput($_GET['date_from'] ?? '');
$date_to = sanitizeInput($_GET['date_to'] ?? '');
$search = sanitizeInput($_GET['search'] ?? '');

// 构建查询条件
$filters = [
    'page' => $page,
    'limit' => $limit,
    'level' => $level,
    'category' => $category,
    'date_from' => $date_from,
    'date_to' => $date_to,
    'search' => $search
];

// 获取日志数据
$logsData = getSystemLogs($filters);
$logStats = getLogStatistics($filters);
$systemHealth = getSystemHealthMetrics();

include 'includes/header.php';
?>

<div class="container-fluid">
    <!-- 页面标题和控制按钮 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">
            <i class="fas fa-file-alt text-primary me-2"></i>
            系统日志监控
        </h1>
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-info" onclick="refreshLogs()">
                <i class="fas fa-sync-alt me-2"></i>
                刷新日志
            </button>
            <button type="button" class="btn btn-outline-success" onclick="showLogAnalysis()">
                <i class="fas fa-chart-pie me-2"></i>
                日志分析
            </button>
            <button type="button" class="btn btn-outline-warning" onclick="showSystemHealth()">
                <i class="fas fa-heartbeat me-2"></i>
                系统健康
            </button>
            <button type="button" class="btn btn-outline-primary" onclick="exportLogs()">
                <i class="fas fa-download me-2"></i>
                导出日志
            </button>
            <button type="button" class="btn btn-outline-danger" onclick="showLogCleanup()">
                <i class="fas fa-trash me-2"></i>
                清理日志
            </button>
        </div>
    </div>

    <!-- 系统健康状态 -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                系统状态
                            </div>
                            <div class="h5 mb-0 font-weight-bold">
                                <?php if ($systemHealth['success']): ?>
                                    <span class="badge bg-<?php echo getHealthStatusColor($systemHealth['data']['status']); ?> badge-lg">
                                        <?php echo getHealthStatusText($systemHealth['data']['status']); ?>
                                    </span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">未知</span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-server fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                今日日志条数
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo $logStats['success'] ? formatNumber($logStats['data']['today_count'], 0) : '---'; ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-list fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                错误日志数
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo $logStats['success'] ? formatNumber($logStats['data']['error_count'], 0) : '---'; ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                系统运行时间
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="systemUptime">
                                <?php echo $systemHealth['success'] ? $systemHealth['data']['uptime'] : '---'; ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 日志级别统计 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-bar me-2"></i>
                        日志级别分布 (最近24小时)
                    </h6>
                </div>
                <div class="card-body">
                    <?php if ($logStats['success']): ?>
                    <div class="row text-center">
                        <div class="col-md-2">
                            <div class="mb-2">
                                <span class="text-xs font-weight-bold text-info text-uppercase">DEBUG</span>
                            </div>
                            <div class="h4 mb-0 font-weight-bold text-info">
                                <?php echo formatNumber($logStats['data']['level_stats']['debug'] ?? 0, 0); ?>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="mb-2">
                                <span class="text-xs font-weight-bold text-primary text-uppercase">INFO</span>
                            </div>
                            <div class="h4 mb-0 font-weight-bold text-primary">
                                <?php echo formatNumber($logStats['data']['level_stats']['info'] ?? 0, 0); ?>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="mb-2">
                                <span class="text-xs font-weight-bold text-warning text-uppercase">WARNING</span>
                            </div>
                            <div class="h4 mb-0 font-weight-bold text-warning">
                                <?php echo formatNumber($logStats['data']['level_stats']['warning'] ?? 0, 0); ?>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="mb-2">
                                <span class="text-xs font-weight-bold text-danger text-uppercase">ERROR</span>
                            </div>
                            <div class="h4 mb-0 font-weight-bold text-danger">
                                <?php echo formatNumber($logStats['data']['level_stats']['error'] ?? 0, 0); ?>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="mb-2">
                                <span class="text-xs font-weight-bold text-dark text-uppercase">CRITICAL</span>
                            </div>
                            <div class="h4 mb-0 font-weight-bold text-dark">
                                <?php echo formatNumber($logStats['data']['level_stats']['critical'] ?? 0, 0); ?>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="mb-2">
                                <span class="text-xs font-weight-bold text-success text-uppercase">AUDIT</span>
                            </div>
                            <div class="h4 mb-0 font-weight-bold text-success">
                                <?php echo formatNumber($logStats['data']['level_stats']['audit'] ?? 0, 0); ?>
                            </div>
                        </div>
                    </div>
                    <?php else: ?>
                    <div class="text-center py-3">
                        <i class="fas fa-exclamation-triangle text-warning"></i>
                        <span class="text-muted">无法获取日志统计数据</span>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- 日志筛选器 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-filter me-2"></i>
                        日志筛选条件
                    </h6>
                </div>
                <div class="card-body">
                    <form method="GET" action="" id="logFilterForm">
                        <div class="row">
                            <div class="col-md-2 mb-3">
                                <label for="level" class="form-label">日志级别</label>
                                <select class="form-select" id="level" name="level">
                                    <option value="">全部级别</option>
                                    <option value="debug" <?php echo $level === 'debug' ? 'selected' : ''; ?>>DEBUG</option>
                                    <option value="info" <?php echo $level === 'info' ? 'selected' : ''; ?>>INFO</option>
                                    <option value="warning" <?php echo $level === 'warning' ? 'selected' : ''; ?>>WARNING</option>
                                    <option value="error" <?php echo $level === 'error' ? 'selected' : ''; ?>>ERROR</option>
                                    <option value="critical" <?php echo $level === 'critical' ? 'selected' : ''; ?>>CRITICAL</option>
                                    <option value="audit" <?php echo $level === 'audit' ? 'selected' : ''; ?>>AUDIT</option>
                                </select>
                            </div>
                            
                            <div class="col-md-2 mb-3">
                                <label for="category" class="form-label">日志分类</label>
                                <select class="form-select" id="category" name="category">
                                    <option value="">全部分类</option>
                                    <option value="system" <?php echo $category === 'system' ? 'selected' : ''; ?>>系统</option>
                                    <option value="trading" <?php echo $category === 'trading' ? 'selected' : ''; ?>>交易</option>
                                    <option value="security" <?php echo $category === 'security' ? 'selected' : ''; ?>>安全</option>
                                    <option value="api" <?php echo $category === 'api' ? 'selected' : ''; ?>>API</option>
                                    <option value="database" <?php echo $category === 'database' ? 'selected' : ''; ?>>数据库</option>
                                    <option value="exchange" <?php echo $category === 'exchange' ? 'selected' : ''; ?>>交易所</option>
                                </select>
                            </div>
                            
                            <div class="col-md-2 mb-3">
                                <label for="date_from" class="form-label">开始日期</label>
                                <input type="date" class="form-control" id="date_from" name="date_from" 
                                       value="<?php echo htmlspecialchars($date_from); ?>">
                            </div>
                            
                            <div class="col-md-2 mb-3">
                                <label for="date_to" class="form-label">结束日期</label>
                                <input type="date" class="form-control" id="date_to" name="date_to" 
                                       value="<?php echo htmlspecialchars($date_to); ?>">
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <label for="search" class="form-label">关键词搜索</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       value="<?php echo htmlspecialchars($search); ?>" 
                                       placeholder="搜索日志内容...">
                            </div>
                            
                            <div class="col-md-1 mb-3">
                                <label for="limit" class="form-label">每页</label>
                                <select class="form-select" id="limit" name="limit">
                                    <option value="25" <?php echo $limit === 25 ? 'selected' : ''; ?>>25</option>
                                    <option value="50" <?php echo $limit === 50 ? 'selected' : ''; ?>>50</option>
                                    <option value="100" <?php echo $limit === 100 ? 'selected' : ''; ?>>100</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search me-2"></i>
                                    筛选日志
                                </button>
                                <button type="button" class="btn btn-outline-secondary me-2" onclick="resetLogFilters()">
                                    <i class="fas fa-undo me-2"></i>
                                    重置筛选
                                </button>
                                <button type="button" class="btn btn-outline-info me-2" onclick="showRealTimeLog()">
                                    <i class="fas fa-play me-2"></i>
                                    实时日志
                                </button>
                                <button type="button" class="btn btn-outline-warning" onclick="showLogPresets()">
                                    <i class="fas fa-bookmark me-2"></i>
                                    快速筛选
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 日志列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-list me-2"></i>
                        系统日志列表
                        <?php if ($logsData['success']): ?>
                            <span class="badge bg-primary ms-2">
                                共 <?php echo formatNumber($logsData['data']['total'], 0); ?> 条记录
                            </span>
                        <?php endif; ?>
                    </h6>
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-primary active" onclick="toggleLogView('table')" id="tableViewBtn">
                            表格视图
                        </button>
                        <button type="button" class="btn btn-outline-primary" onclick="toggleLogView('raw')" id="rawViewBtn">
                            原始日志
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <?php if ($logsData['success'] && !empty($logsData['data']['logs'])): ?>
                        <!-- 表格视图 -->
                        <div id="tableView">
                            <div class="table-responsive">
                                <table class="table table-hover table-sm" id="logsTable">
                                    <thead>
                                        <tr>
                                            <th width="140">时间</th>
                                            <th width="80">级别</th>
                                            <th width="100">分类</th>
                                            <th width="120">来源</th>
                                            <th>消息</th>
                                            <th width="100">用户</th>
                                            <th width="120">IP地址</th>
                                            <th width="80">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($logsData['data']['logs'] as $log): ?>
                                            <tr class="log-row log-level-<?php echo $log['level']; ?>" 
                                                data-log-id="<?php echo $log['id']; ?>">
                                                <td>
                                                    <div class="fw-bold"><?php echo formatTime($log['timestamp'], 'H:i:s'); ?></div>
                                                    <small class="text-muted"><?php echo formatTime($log['timestamp'], 'm-d'); ?></small>
                                                </td>
                                                <td>
                                                    <span class="badge bg-<?php echo getLogLevelColor($log['level']); ?>">
                                                        <?php echo strtoupper($log['level']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-secondary">
                                                        <?php echo strtoupper($log['category'] ?? 'SYSTEM'); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <small class="font-monospace">
                                                        <?php echo htmlspecialchars($log['source'] ?? 'system'); ?>
                                                    </small>
                                                </td>
                                                <td>
                                                    <div class="log-message" style="max-width: 400px; overflow: hidden; text-overflow: ellipsis;">
                                                        <?php echo htmlspecialchars($log['message']); ?>
                                                    </div>
                                                    <?php if (!empty($log['context'])): ?>
                                                    <small class="text-muted">
                                                        <i class="fas fa-info-circle me-1"></i>
                                                        包含上下文数据
                                                    </small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($log['user_id']): ?>
                                                        <small><?php echo htmlspecialchars($log['username'] ?? 'ID:' . $log['user_id']); ?></small>
                                                    <?php else: ?>
                                                        <small class="text-muted">系统</small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <small class="font-monospace">
                                                        <?php echo htmlspecialchars($log['ip_address'] ?? '---'); ?>
                                                    </small>
                                                </td>
                                                <td>
                                                    <button type="button" class="btn btn-outline-info btn-sm" 
                                                            onclick="viewLogDetails('<?php echo $log['id']; ?>')"
                                                            title="查看详情">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- 原始日志视图 -->
                        <div id="rawView" style="display: none;">
                            <div class="bg-dark text-light p-3 rounded" style="font-family: 'Courier New', monospace; font-size: 12px; max-height: 600px; overflow-y: auto;">
                                <?php foreach ($logsData['data']['logs'] as $log): ?>
                                    <div class="log-line mb-1">
                                        <span class="text-<?php echo getLogLevelColor($log['level']); ?>">
                                            [<?php echo $log['timestamp']; ?>]
                                        </span>
                                        <span class="text-warning">
                                            <?php echo strtoupper($log['level']); ?>:
                                        </span>
                                        <span class="text-info">
                                            <?php echo $log['category'] ?? 'SYSTEM'; ?>:
                                        </span>
                                        <span class="text-light">
                                            <?php echo htmlspecialchars($log['message']); ?>
                                        </span>
                                        <?php if ($log['context']): ?>
                                        <span class="text-muted">
                                            <?php echo htmlspecialchars(json_encode($log['context'], JSON_UNESCAPED_UNICODE)); ?>
                                        </span>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>

                        <!-- 分页 -->
                        <?php if ($logsData['data']['total_pages'] > 1): ?>
                        <nav aria-label="日志分页" class="mt-3">
                            <ul class="pagination justify-content-center">
                                <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?<?php echo http_build_query(array_merge($filters, ['page' => $page - 1])); ?>">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                </li>
                                <?php endif; ?>

                                <?php
                                $start = max(1, $page - 2);
                                $end = min($logsData['data']['total_pages'], $page + 2);
                                
                                for ($i = $start; $i <= $end; $i++):
                                ?>
                                <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                    <a class="page-link" href="?<?php echo http_build_query(array_merge($filters, ['page' => $i])); ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                                <?php endfor; ?>

                                <?php if ($page < $logsData['data']['total_pages']): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?<?php echo http_build_query(array_merge($filters, ['page' => $page + 1])); ?>">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                        <?php endif; ?>

                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                            <h5>暂无日志记录</h5>
                            <p class="text-muted">
                                <?php if (array_filter($filters)): ?>
                                    当前筛选条件下没有找到日志记录，请尝试调整筛选条件。
                                <?php else: ?>
                                    系统还没有生成任何日志记录。
                                <?php endif; ?>
                            </p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 日志详情模态框 -->
<div class="modal fade" id="logDetailsModal" tabindex="-1" aria-labelledby="logDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="logDetailsModalLabel">
                    <i class="fas fa-file-alt me-2"></i>
                    日志详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="logDetailsContent">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <div class="mt-2">正在加载日志详情...</div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="copyLogDetails()">
                    <i class="fas fa-copy me-2"></i>
                    复制详情
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 实时日志模态框 -->
<div class="modal fade" id="realTimeLogModal" tabindex="-1" aria-labelledby="realTimeLogModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="realTimeLogModalLabel">
                    <i class="fas fa-play me-2"></i>
                    实时日志监控
                    <span class="badge bg-success ms-2" id="realTimeStatus">已连接</span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="realTimeLevel" class="form-label">监控级别</label>
                        <select class="form-select" id="realTimeLevel">
                            <option value="">全部级别</option>
                            <option value="debug">DEBUG</option>
                            <option value="info">INFO</option>
                            <option value="warning">WARNING</option>
                            <option value="error">ERROR</option>
                            <option value="critical">CRITICAL</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label for="realTimeCategory" class="form-label">监控分类</label>
                        <select class="form-select" id="realTimeCategory">
                            <option value="">全部分类</option>
                            <option value="system">系统</option>
                            <option value="trading">交易</option>
                            <option value="security">安全</option>
                            <option value="api">API</option>
                        </select>
                    </div>
                </div>

                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <button type="button" class="btn btn-success btn-sm" id="startRealTimeBtn" onclick="startRealTimeLog()">
                            <i class="fas fa-play me-2"></i>
                            开始监控
                        </button>
                        <button type="button" class="btn btn-danger btn-sm" id="stopRealTimeBtn" onclick="stopRealTimeLog()" style="display: none;">
                            <i class="fas fa-stop me-2"></i>
                            停止监控
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearRealTimeLog()">
                            <i class="fas fa-trash me-2"></i>
                            清空日志
                        </button>
                    </div>
                    <div>
                        <small class="text-muted">
                            日志条数: <span id="realTimeLogCount">0</span> |
                            自动滚动: <input type="checkbox" id="autoScroll" checked>
                        </small>
                    </div>
                </div>

                <div id="realTimeLogContainer" class="bg-dark text-light p-3 rounded"
                     style="font-family: 'Courier New', monospace; font-size: 12px; height: 400px; overflow-y: auto;">
                    <div class="text-center py-5 text-muted">
                        点击"开始监控"查看实时日志
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 日志分析模态框 -->
<div class="modal fade" id="logAnalysisModal" tabindex="-1" aria-labelledby="logAnalysisModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="logAnalysisModalLabel">
                    <i class="fas fa-chart-pie me-2"></i>
                    日志分析报告
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-3">
                        <h6>分析参数</h6>
                        <form id="logAnalysisForm">
                            <div class="mb-3">
                                <label for="analysis_period" class="form-label">分析周期</label>
                                <select class="form-select" id="analysis_period" name="analysis_period">
                                    <option value="1h">最近1小时</option>
                                    <option value="6h">最近6小时</option>
                                    <option value="24h" selected>最近24小时</option>
                                    <option value="7d">最近7天</option>
                                    <option value="30d">最近30天</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="analysis_level" class="form-label">日志级别</label>
                                <select class="form-select" id="analysis_level" name="analysis_level">
                                    <option value="">全部级别</option>
                                    <option value="error">仅错误日志</option>
                                    <option value="warning">警告及以上</option>
                                    <option value="info">信息及以上</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="analysis_category" class="form-label">分析分类</label>
                                <select class="form-select" id="analysis_category" name="analysis_category">
                                    <option value="">全部分类</option>
                                    <option value="system">系统日志</option>
                                    <option value="trading">交易日志</option>
                                    <option value="security">安全日志</option>
                                </select>
                            </div>
                            <button type="button" class="btn btn-primary" onclick="generateLogAnalysis()">
                                <i class="fas fa-chart-line me-2"></i>
                                生成分析
                            </button>
                        </form>
                    </div>
                    <div class="col-md-9">
                        <div id="logAnalysisContent">
                            <div class="text-center py-5">
                                <i class="fas fa-chart-pie fa-3x text-muted mb-3"></i>
                                <h6>日志分析报告</h6>
                                <p class="text-muted">选择分析参数后点击"生成分析"查看详细报告</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 系统健康模态框 -->
<div class="modal fade" id="systemHealthModal" tabindex="-1" aria-labelledby="systemHealthModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="systemHealthModalLabel">
                    <i class="fas fa-heartbeat me-2"></i>
                    系统健康检查
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="systemHealthContent">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">检查中...</span>
                        </div>
                        <div class="mt-2">正在进行系统健康检查...</div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="refreshSystemHealth()">
                    <i class="fas fa-sync-alt me-2"></i>
                    重新检查
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 日志清理模态框 -->
<div class="modal fade" id="logCleanupModal" tabindex="-1" aria-labelledby="logCleanupModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title" id="logCleanupModalLabel">
                    <i class="fas fa-trash me-2"></i>
                    日志清理
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>注意：</h6>
                    <p class="mb-0">日志清理操作不可撤销，请谨慎选择清理条件。</p>
                </div>

                <form id="logCleanupForm">
                    <div class="mb-3">
                        <label for="cleanup_period" class="form-label">清理周期</label>
                        <select class="form-select" id="cleanup_period" name="cleanup_period" required>
                            <option value="">请选择清理周期</option>
                            <option value="7d">7天前的日志</option>
                            <option value="30d">30天前的日志</option>
                            <option value="90d">90天前的日志</option>
                            <option value="1y">1年前的日志</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="cleanup_level" class="form-label">清理级别</label>
                        <select class="form-select" id="cleanup_level" name="cleanup_level">
                            <option value="">全部级别</option>
                            <option value="debug">仅DEBUG日志</option>
                            <option value="info">INFO及以下</option>
                            <option value="warning">WARNING及以下</option>
                        </select>
                        <div class="form-text">
                            选择要清理的日志级别，ERROR和CRITICAL级别的日志建议保留
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="backup_before_cleanup">
                            <label class="form-check-label" for="backup_before_cleanup">
                                清理前创建备份
                            </label>
                        </div>
                    </div>

                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="cleanup_confirm" required>
                        <label class="form-check-label fw-bold" for="cleanup_confirm">
                            我确认要执行日志清理操作
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-warning" onclick="executeLogCleanup()">
                    <i class="fas fa-trash me-2"></i>
                    执行清理
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// 全局变量
let realTimeLogWebSocket = null;
let realTimeLogCount = 0;
let currentLogId = null;

// 页面加载完成后执行
$(document).ready(function() {
    // 初始化页面
    initializeLogsPage();

    // 绑定事件
    bindLogEvents();

    // 设置自动刷新
    setupAutoRefresh();

    // 加载保存的视图偏好
    loadViewPreferences();
});

// 初始化日志页面
function initializeLogsPage() {
    // 设置日志级别颜色
    setupLogLevelColors();

    // 初始化表格功能
    setupTableFeatures();

    // 设置快捷键
    setupKeyboardShortcuts();
}

// 绑定日志页面事件
function bindLogEvents() {
    // 筛选表单提交
    $('#logFilterForm').on('submit', function(e) {
        e.preventDefault();
        applyLogFilters();
    });

    // 实时日志级别和分类变化
    $('#realTimeLevel, #realTimeCategory').on('change', function() {
        if (realTimeLogWebSocket) {
            updateRealTimeFilters();
        }
    });

    // 自动滚动切换
    $('#autoScroll').on('change', function() {
        localStorage.setItem('bitv_auto_scroll', this.checked);
    });
}

// 设置日志级别颜色
function setupLogLevelColors() {
    const style = document.createElement('style');
    style.textContent = `
        .log-level-debug { background-color: rgba(108, 117, 125, 0.1); }
        .log-level-info { background-color: rgba(13, 110, 253, 0.1); }
        .log-level-warning { background-color: rgba(255, 193, 7, 0.1); }
        .log-level-error { background-color: rgba(220, 53, 69, 0.1); }
        .log-level-critical { background-color: rgba(220, 53, 69, 0.2); }
        .log-level-audit { background-color: rgba(25, 135, 84, 0.1); }
    `;
    document.head.appendChild(style);
}

// 切换日志视图
function toggleLogView(viewType) {
    if (viewType === 'table') {
        $('#tableView').show();
        $('#rawView').hide();
        $('#tableViewBtn').addClass('active');
        $('#rawViewBtn').removeClass('active');
    } else {
        $('#tableView').hide();
        $('#rawView').show();
        $('#rawViewBtn').addClass('active');
        $('#tableViewBtn').removeClass('active');
    }

    // 保存视图偏好
    localStorage.setItem('bitv_log_view', viewType);
}

// 查看日志详情
function viewLogDetails(logId) {
    currentLogId = logId;
    showLoading('正在加载日志详情...');

    fetch(window.BITV.API_BASE + '/api/logs/' + logId, {
        headers: {
            'Authorization': 'Bearer ' + window.BITV.API_TOKEN
        }
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        if (data.success) {
            displayLogDetails(data.data);
            $('#logDetailsModal').modal('show');
        } else {
            showNotification('获取日志详情失败: ' + (data.error || '未知错误'), 'error');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('获取日志详情失败:', error);
        showNotification('网络错误，请重试', 'error');
    });
}

// 显示日志详情
function displayLogDetails(logData) {
    const container = document.getElementById('logDetailsContent');

    const html = `
        <div class="row">
            <div class="col-md-6">
                <h6>基本信息</h6>
                <table class="table table-sm">
                    <tr><td>日志ID:</td><td class="font-monospace">${logData.id}</td></tr>
                    <tr><td>时间戳:</td><td>${formatTime(logData.timestamp)}</td></tr>
                    <tr><td>级别:</td><td><span class="badge bg-${getLogLevelColor(logData.level)}">${logData.level.toUpperCase()}</span></td></tr>
                    <tr><td>分类:</td><td><span class="badge bg-secondary">${logData.category || 'SYSTEM'}</span></td></tr>
                    <tr><td>来源:</td><td class="font-monospace">${logData.source || 'system'}</td></tr>
                    <tr><td>用户:</td><td>${logData.username || (logData.user_id ? 'ID:' + logData.user_id : '系统')}</td></tr>
                    <tr><td>IP地址:</td><td class="font-monospace">${logData.ip_address || '---'}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>请求信息</h6>
                <table class="table table-sm">
                    <tr><td>请求方法:</td><td>${logData.request_method || '---'}</td></tr>
                    <tr><td>请求URI:</td><td class="font-monospace small">${logData.request_uri || '---'}</td></tr>
                    <tr><td>用户代理:</td><td class="small">${logData.user_agent || '---'}</td></tr>
                    <tr><td>会话ID:</td><td class="font-monospace small">${logData.session_id || '---'}</td></tr>
                    <tr><td>进程ID:</td><td>${logData.process_id || '---'}</td></tr>
                    <tr><td>内存使用:</td><td>${logData.memory_usage ? formatBytes(logData.memory_usage) : '---'}</td></tr>
                </table>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <h6>日志消息</h6>
                <div class="alert alert-${getLogLevelColor(logData.level)}">
                    <pre class="mb-0">${logData.message}</pre>
                </div>
            </div>
        </div>

        ${logData.context ? `
        <div class="row mt-4">
            <div class="col-12">
                <h6>上下文数据</h6>
                <div class="bg-light p-3 rounded">
                    <pre class="mb-0"><code>${JSON.stringify(logData.context, null, 2)}</code></pre>
                </div>
            </div>
        </div>
        ` : ''}

        ${logData.stack_trace ? `
        <div class="row mt-4">
            <div class="col-12">
                <h6>堆栈跟踪</h6>
                <div class="bg-dark text-light p-3 rounded">
                    <pre class="mb-0 text-light"><code>${logData.stack_trace}</code></pre>
                </div>
            </div>
        </div>
        ` : ''}
    `;

    container.innerHTML = html;
}

// 复制日志详情
function copyLogDetails() {
    if (!currentLogId) return;

    const content = document.getElementById('logDetailsContent').textContent;

    if (navigator.clipboard) {
        navigator.clipboard.writeText(content).then(() => {
            showNotification('日志详情已复制到剪贴板', 'success');
        }).catch(err => {
            console.error('复制失败:', err);
            showNotification('复制失败', 'error');
        });
    } else {
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = content;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showNotification('日志详情已复制到剪贴板', 'success');
    }
}

// 显示实时日志
function showRealTimeLog() {
    $('#realTimeLogModal').modal('show');
}

// 开始实时日志监控
function startRealTimeLog() {
    if (realTimeLogWebSocket) {
        realTimeLogWebSocket.close();
    }

    const wsUrl = (window.BITV.WS_BASE || 'ws://localhost:8000') + '/ws/logs';
    realTimeLogWebSocket = new WebSocket(wsUrl);

    realTimeLogWebSocket.onopen = function(event) {
        $('#realTimeStatus').removeClass('bg-danger').addClass('bg-success').text('已连接');
        $('#startRealTimeBtn').hide();
        $('#stopRealTimeBtn').show();

        // 发送筛选条件
        updateRealTimeFilters();

        showNotification('实时日志监控已启动', 'success');
    };

    realTimeLogWebSocket.onmessage = function(event) {
        try {
            const logData = JSON.parse(event.data);
            appendRealTimeLog(logData);
        } catch (e) {
            console.error('解析实时日志失败:', e);
        }
    };

    realTimeLogWebSocket.onclose = function(event) {
        $('#realTimeStatus').removeClass('bg-success').addClass('bg-danger').text('已断开');
        $('#startRealTimeBtn').show();
        $('#stopRealTimeBtn').hide();
    };

    realTimeLogWebSocket.onerror = function(error) {
        console.error('实时日志WebSocket错误:', error);
        showNotification('实时日志连接失败', 'error');
    };
}

// 停止实时日志监控
function stopRealTimeLog() {
    if (realTimeLogWebSocket) {
        realTimeLogWebSocket.close();
        realTimeLogWebSocket = null;
    }
}

// 更新实时日志筛选条件
function updateRealTimeFilters() {
    if (!realTimeLogWebSocket || realTimeLogWebSocket.readyState !== WebSocket.OPEN) return;

    const filters = {
        level: $('#realTimeLevel').val(),
        category: $('#realTimeCategory').val()
    };

    realTimeLogWebSocket.send(JSON.stringify({
        type: 'filter',
        data: filters
    }));
}

// 添加实时日志条目
function appendRealTimeLog(logData) {
    const container = document.getElementById('realTimeLogContainer');
    const logLine = document.createElement('div');
    logLine.className = 'log-line mb-1';

    const levelColor = getLogLevelColor(logData.level);
    logLine.innerHTML = `
        <span class="text-${levelColor}">
            [${formatTime(logData.timestamp, 'H:i:s')}]
        </span>
        <span class="text-warning">
            ${logData.level.toUpperCase()}:
        </span>
        <span class="text-info">
            ${logData.category || 'SYSTEM'}:
        </span>
        <span class="text-light">
            ${logData.message}
        </span>
    `;

    container.appendChild(logLine);
    realTimeLogCount++;

    // 更新计数
    document.getElementById('realTimeLogCount').textContent = realTimeLogCount;

    // 自动滚动
    if (document.getElementById('autoScroll').checked) {
        container.scrollTop = container.scrollHeight;
    }

    // 限制日志条数
    const maxLines = 1000;
    while (container.children.length > maxLines) {
        container.removeChild(container.firstChild);
        realTimeLogCount--;
    }
}

// 清空实时日志
function clearRealTimeLog() {
    document.getElementById('realTimeLogContainer').innerHTML = '';
    realTimeLogCount = 0;
    document.getElementById('realTimeLogCount').textContent = '0';
}

// 显示日志分析
function showLogAnalysis() {
    $('#logAnalysisModal').modal('show');
}

// 生成日志分析
function generateLogAnalysis() {
    const formData = new FormData(document.getElementById('logAnalysisForm'));
    const params = Object.fromEntries(formData);

    showLoading('正在生成日志分析...');

    fetch(window.BITV.API_BASE + '/api/logs/analysis', {
        method: 'POST',
        headers: {
            'Authorization': 'Bearer ' + window.BITV.API_TOKEN,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(params)
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        if (data.success) {
            displayLogAnalysis(data.data);
        } else {
            showNotification('生成日志分析失败: ' + (data.error || '未知错误'), 'error');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('生成日志分析失败:', error);
        showNotification('网络错误，请重试', 'error');
    });
}

// 显示日志分析结果
function displayLogAnalysis(analysisData) {
    const container = document.getElementById('logAnalysisContent');

    const html = `
        <div class="row">
            <div class="col-md-6">
                <h6>日志统计</h6>
                <table class="table table-sm">
                    <tr><td>总日志条数:</td><td>${formatNumber(analysisData.total_logs, 0)}</td></tr>
                    <tr><td>错误日志:</td><td class="text-danger">${formatNumber(analysisData.error_logs, 0)}</td></tr>
                    <tr><td>警告日志:</td><td class="text-warning">${formatNumber(analysisData.warning_logs, 0)}</td></tr>
                    <tr><td>信息日志:</td><td class="text-info">${formatNumber(analysisData.info_logs, 0)}</td></tr>
                    <tr><td>错误率:</td><td class="text-danger">${formatPercentage(analysisData.error_rate)}</td></tr>
                    <tr><td>平均日志/小时:</td><td>${formatNumber(analysisData.avg_logs_per_hour, 1)}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>系统指标</h6>
                <table class="table table-sm">
                    <tr><td>系统可用性:</td><td class="text-success">${formatPercentage(analysisData.system_availability)}</td></tr>
                    <tr><td>平均响应时间:</td><td>${analysisData.avg_response_time}ms</td></tr>
                    <tr><td>内存使用峰值:</td><td>${formatBytes(analysisData.peak_memory_usage)}</td></tr>
                    <tr><td>活跃用户数:</td><td>${formatNumber(analysisData.active_users, 0)}</td></tr>
                    <tr><td>API调用次数:</td><td>${formatNumber(analysisData.api_calls, 0)}</td></tr>
                    <tr><td>数据库查询:</td><td>${formatNumber(analysisData.db_queries, 0)}</td></tr>
                </table>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <h6>日志趋势图</h6>
                <canvas id="logTrendChart" width="400" height="200"></canvas>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-md-6">
                <h6>热门错误</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr><th>错误消息</th><th>次数</th><th>最后发生</th></tr>
                        </thead>
                        <tbody>
                            ${analysisData.top_errors.map(error => `
                                <tr>
                                    <td class="small">${error.message.substring(0, 50)}...</td>
                                    <td><span class="badge bg-danger">${error.count}</span></td>
                                    <td class="small">${formatTime(error.last_occurrence, 'H:i')}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="col-md-6">
                <h6>活跃IP地址</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr><th>IP地址</th><th>请求数</th><th>错误数</th></tr>
                        </thead>
                        <tbody>
                            ${analysisData.top_ips.map(ip => `
                                <tr>
                                    <td class="font-monospace">${ip.ip_address}</td>
                                    <td>${ip.request_count}</td>
                                    <td class="text-danger">${ip.error_count}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    `;

    container.innerHTML = html;

    // 绘制趋势图
    drawLogTrendChart(analysisData.trend_data);
}

// 工具函数
function getLogLevelColor(level) {
    const colors = {
        'debug': 'secondary',
        'info': 'primary',
        'warning': 'warning',
        'error': 'danger',
        'critical': 'danger',
        'audit': 'success'
    };
    return colors[level] || 'secondary';
}

function getHealthStatusColor(status) {
    const colors = {
        'healthy': 'success',
        'warning': 'warning',
        'critical': 'danger',
        'unknown': 'secondary'
    };
    return colors[status] || 'secondary';
}

function getHealthStatusText(status) {
    const texts = {
        'healthy': '健康',
        'warning': '警告',
        'critical': '严重',
        'unknown': '未知'
    };
    return texts[status] || '未知';
}

function formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 页面卸载时清理
window.addEventListener('beforeunload', function() {
    if (realTimeLogWebSocket) {
        realTimeLogWebSocket.close();
    }
});
</script>

<?php include 'includes/footer.php'; ?>
