﻿Microsoft Windows [版本 10.0.14393]
(c) 2016 Microsoft Corporation。保留所有权利。

C:\Users\<USER>\Desktop\bitV>pip install fastapi uvicorn ccxt cryptography
Requirement already satisfied: fastapi in c:\program files\python\lib\site-packages (0.116.1)
Requirement already satisfied: uvicorn in c:\program files\python\lib\site-packages (0.33.0)
Requirement already satisfied: cryptography in c:\program files\python\lib\site-packages (3.0)
Collecting ccxt
  Downloading ccxt-4.4.98-py2.py3-none-any.whl (5.7 MB)
     |████████████████████████████████| 5.7 MB 3.3 MB/s
Requirement already satisfied: requests>=2.18.4 in c:\program files\python\lib\site-packages (from ccxt) (2.24.0)
Requirement already satisfied: cryptography in c:\program files\python\lib\site-packages (3.0)
Requirement already satisfied: typing-extensions>=4.4.0 in c:\program files\python\lib\site-packages (from ccxt) (4.13.2)
Requirement already satisfied: certifi>=2018.1.18 in c:\program files\python\lib\site-packages (from ccxt) (2020.6.20)
Requirement already satisfied: cffi!=1.11.3,>=1.8 in c:\program files\python\lib\site-packages (from cryptography) (1.14.1)
Requirement already satisfied: six>=1.4.1 in c:\program files\python\lib\site-packages (from cryptography) (1.15.0)
Requirement already satisfied: typing-extensions>=4.4.0 in c:\program files\python\lib\site-packages (from ccxt) (4.13.2)
Requirement already satisfied: pydantic!=1.8,!=1.8.1,!=2.0.0,!=2.0.1,!=2.1.0,<3.0.0,>=1.7.4 in c:\program files\python\lib\site-packages (from fastapi) (2.10.6)
Requirement already satisfied: starlette<0.48.0,>=0.40.0 in c:\program files\python\lib\site-packages (from fastapi) (0.44.0)
Requirement already satisfied: click>=7.0 in c:\program files\python\lib\site-packages (from uvicorn) (7.1.2)
Requirement already satisfied: typing-extensions>=4.4.0 in c:\program files\python\lib\site-packages (from ccxt) (4.13.2)
Requirement already satisfied: h11>=0.8 in c:\program files\python\lib\site-packages (from uvicorn) (0.16.0)
Collecting aiodns>=1.1.1
  Downloading aiodns-3.2.0-py3-none-any.whl (5.7 kB)
Collecting aiohttp>=3.10.11
  Downloading aiohttp-3.10.11-cp38-cp38-win_amd64.whl (384 kB)
     |████████████████████████████████| 384 kB 6.4 MB/s
Collecting aiohappyeyeballs>=2.3.0
  Downloading aiohappyeyeballs-2.4.4-py3-none-any.whl (14 kB)
Collecting aiosignal>=1.1.2
  Downloading aiosignal-1.3.1-py3-none-any.whl (7.6 kB)
Collecting async-timeout<6.0,>=4.0
  Downloading async_timeout-5.0.1-py3-none-any.whl (6.2 kB)
Collecting attrs>=17.3.0
  Downloading attrs-25.3.0-py3-none-any.whl (63 kB)
     |████████████████████████████████| 63 kB 4.8 MB/s
Requirement already satisfied: pycparser in c:\program files\python\lib\site-packages (from cffi!=1.11.3,>=1.8->cryptography) (2.20)
Collecting frozenlist>=1.1.1
  Downloading frozenlist-1.5.0-cp38-cp38-win_amd64.whl (51 kB)
     |████████████████████████████████| 51 kB ...
Collecting multidict<7.0,>=4.5
  Downloading multidict-6.1.0-cp38-cp38-win_amd64.whl (28 kB)
Requirement already satisfied: typing-extensions>=4.4.0 in c:\program files\python\lib\site-packages (from ccxt) (4.13.2)
Collecting pycares>=4.0.0
  Downloading pycares-4.4.0-cp38-cp38-win_amd64.whl (76 kB)
     |████████████████████████████████| 76 kB 2.3 MB/s
Requirement already satisfied: cffi!=1.11.3,>=1.8 in c:\program files\python\lib\site-packages (from cryptography) (1.14.1)
Requirement already satisfied: pydantic-core==2.27.2 in c:\program files\python\lib\site-packages (from pydantic!=1.8,!=1.8.1,!=2.0.0,!=2.0.1,!=2.1.0,<3.0.0,>=1.7.4->fastapi) (2.27.2)
Requirement already satisfied: annotated-types>=0.6.0 in c:\program files\python\lib\site-packages (from pydantic!=1.8,!=1.8.1,!=2.0.0,!=2.0.1,!=2.1.0,<3.0.0,>=1.7.4->fastapi) (0.7.0)
Requirement already satisfied: typing-extensions>=4.4.0 in c:\program files\python\lib\site-packages (from ccxt) (4.13.2)
Requirement already satisfied: typing-extensions>=4.4.0 in c:\program files\python\lib\site-packages (from ccxt) (4.13.2)
Requirement already satisfied: typing-extensions>=4.4.0 in c:\program files\python\lib\site-packages (from ccxt) (4.13.2)
Requirement already satisfied: urllib3!=1.25.0,!=1.25.1,<1.26,>=1.21.1 in c:\program files\python\lib\site-packages (from requests>=2.18.4->ccxt) (1.25.10)
Requirement already satisfied: chardet<4,>=3.0.2 in c:\program files\python\lib\site-packages (from requests>=2.18.4->ccxt) (3.0.4)
Requirement already satisfied: idna<3,>=2.5 in c:\program files\python\lib\site-packages (from requests>=2.18.4->ccxt) (2.10)
Requirement already satisfied: certifi>=2018.1.18 in c:\program files\python\lib\site-packages (from ccxt) (2020.6.20)
Collecting setuptools>=60.9.0
  Downloading setuptools-75.3.2-py3-none-any.whl (1.3 MB)
     |████████████████████████████████| 1.3 MB ...
Requirement already satisfied: typing-extensions>=4.4.0 in c:\program files\python\lib\site-packages (from ccxt) (4.13.2)
Requirement already satisfied: anyio<5,>=3.4.0 in c:\program files\python\lib\site-packages (from starlette<0.48.0,>=0.40.0->fastapi) (4.5.2)
Requirement already satisfied: typing-extensions>=4.4.0 in c:\program files\python\lib\site-packages (from ccxt) (4.13.2)
Requirement already satisfied: exceptiongroup>=1.0.2 in c:\program files\python\lib\site-packages (from anyio<5,>=3.4.0->starlette<0.48.0,>=0.40.0->fastapi) (1.3.0)
Requirement already satisfied: idna<3,>=2.5 in c:\program files\python\lib\site-packages (from requests>=2.18.4->ccxt) (2.10)
Requirement already satisfied: sniffio>=1.1 in c:\program files\python\lib\site-packages (from anyio<5,>=3.4.0->starlette<0.48.0,>=0.40.0->fastapi) (1.3.1)
Requirement already satisfied: typing-extensions>=4.4.0 in c:\program files\python\lib\site-packages (from ccxt) (4.13.2)
Collecting yarl>=1.7.2
  Downloading yarl-1.15.2-cp38-cp38-win_amd64.whl (84 kB)
     |████████████████████████████████| 84 kB 893 kB/s
Requirement already satisfied: idna<3,>=2.5 in c:\program files\python\lib\site-packages (from requests>=2.18.4->ccxt) (2.10)
Collecting propcache>=0.2.0
  Downloading propcache-0.2.0-cp38-cp38-win_amd64.whl (45 kB)
     |████████████████████████████████| 45 kB 3.4 MB/s
Installing collected packages: propcache, multidict, frozenlist, yarl, pycares, attrs, async-timeout, aiosignal, aiohappyeyeballs, setuptools, aiohttp, aiodns, ccxt
  Attempting uninstall: setuptools
    Found existing installation: setuptools 49.2.1
    Uninstalling setuptools-49.2.1:
      Successfully uninstalled setuptools-49.2.1
Successfully installed aiodns-3.2.0 aiohappyeyeballs-2.4.4 aiohttp-3.10.11 aiosignal-1.3.1 async-timeout-5.0.1 attrs-25.3.0 ccxt-4.4.98 frozenlist-1.5.0 multidict-6.1.0 propcache-0.2.0 pycares-4.4.0 setuptools-75.3.2 yarl-1.15.2
WARNING: You are using pip version 20.3.1; however, version 25.0.1 is available.
You should consider upgrading via the 'c:\program files\python\python.exe -m pip install --upgrade pip' command.

C:\Users\<USER>\Desktop\bitV>
C:\Users\<USER>\Desktop\bitV>python real_trading_system.py
🚀 启动BitV MACD智能加仓交易系统 - 实盘版本
================================================================================
🔥 实盘交易特性:
   💰 真实交易所API集成 (OKX/Gate.io)
   📈 完整MACD策略分析
   ⚡ 智能加仓管理
   🛡️ 实时风险监控
   🔐 安全认证和审计
   🚨 紧急停止功能
   📊 实时数据推送
================================================================================
⚠️  重要安全提示:
   🔑 请妥善保管API密钥
   💸 建议先在沙盒环境测试
   📊 密切监控风险指标
   🚨 设置合理的止损点
================================================================================
🌐 服务地址:
   - API服务: http://localhost:8000
   - API文档: http://localhost:8000/docs
   - 健康检查: http://localhost:8000/health
   - WebSocket: ws://localhost:8000/ws
================================================================================
🔧 使用步骤:
   1. 访问 /docs 查看API文档
   2. 使用 /api/configure-exchange 配置交易所
   3. 使用 /api/trading/start 启动交易
   4. 监控 /api/trading/positions 持仓状态
   5. 必要时使用 /api/emergency/stop 紧急停止
================================================================================
🔐 访问令牌: bitv_real_trading_token
================================================================================
[32mINFO[0m:     Started server process [[36m2688[0m]
[32mINFO[0m:     Waiting for application startup.
--- Logging error ---
Traceback (most recent call last):
  File "C:\Program Files\python\lib\logging\__init__.py", line 1084, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f680' in position 44: illegal multibyte sequence
Call stack:
  File "real_trading_system.py", line 1750, in <module>
    main()
  File "real_trading_system.py", line 1736, in main
    uvicorn.run(
  File "C:\Program Files\python\lib\site-packages\uvicorn\main.py", line 579, in run
    server.run()
  File "C:\Program Files\python\lib\site-packages\uvicorn\server.py", line 65, in run
    return asyncio.run(self.serve(sockets=sockets))
  File "C:\Program Files\python\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Program Files\python\lib\asyncio\base_events.py", line 603, in run_until_complete
    self.run_forever()
  File "C:\Program Files\python\lib\asyncio\windows_events.py", line 316, in run_forever
    super().run_forever()
  File "C:\Program Files\python\lib\asyncio\base_events.py", line 570, in run_forever
    self._run_once()
  File "C:\Program Files\python\lib\asyncio\base_events.py", line 1859, in _run_once
    handle._run()
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Program Files\python\lib\site-packages\uvicorn\lifespan\on.py", line 86, in main
    await app(scope, self.receive, self.send)
  File "C:\Program Files\python\lib\site-packages\uvicorn\middleware\proxy_headers.py", line 29, in __call__
    return await self.app(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\middleware\errors.py", line 152, in __call__
    await self.app(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\middleware\cors.py", line 77, in __call__
    await self.app(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\middleware\exceptions.py", line 48, in __call__
    await self.app(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\routing.py", line 724, in app
    await self.lifespan(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\routing.py", line 693, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "C:\Program Files\python\lib\contextlib.py", line 171, in __aenter__
    return await self.gen.__anext__()
  File "real_trading_system.py", line 1261, in lifespan
    logger.info("🚀 BitV MACD智能加仓交易系统 - 实盘版本启动中...")
Message: '🚀 BitV MACD智能加仓交易系统 - 实盘版本启动中...'
Arguments: ()
2025-08-04 00:14:23,310 | INFO | __main__ | 🚀 BitV MACD智能加仓交易系统 - 实盘版本启动中...
--- Logging error ---
Traceback (most recent call last):
  File "C:\Program Files\python\lib\logging\__init__.py", line 1084, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f4ca' in position 44: illegal multibyte sequence
Call stack:
  File "real_trading_system.py", line 1750, in <module>
    main()
  File "real_trading_system.py", line 1736, in main
    uvicorn.run(
  File "C:\Program Files\python\lib\site-packages\uvicorn\main.py", line 579, in run
    server.run()
  File "C:\Program Files\python\lib\site-packages\uvicorn\server.py", line 65, in run
    return asyncio.run(self.serve(sockets=sockets))
  File "C:\Program Files\python\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Program Files\python\lib\asyncio\base_events.py", line 603, in run_until_complete
    self.run_forever()
  File "C:\Program Files\python\lib\asyncio\windows_events.py", line 316, in run_forever
    super().run_forever()
  File "C:\Program Files\python\lib\asyncio\base_events.py", line 570, in run_forever
    self._run_once()
  File "C:\Program Files\python\lib\asyncio\base_events.py", line 1859, in _run_once
    handle._run()
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Program Files\python\lib\site-packages\uvicorn\lifespan\on.py", line 86, in main
    await app(scope, self.receive, self.send)
  File "C:\Program Files\python\lib\site-packages\uvicorn\middleware\proxy_headers.py", line 29, in __call__
    return await self.app(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\middleware\errors.py", line 152, in __call__
    await self.app(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\middleware\cors.py", line 77, in __call__
    await self.app(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\middleware\exceptions.py", line 48, in __call__
    await self.app(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\routing.py", line 724, in app
    await self.lifespan(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\routing.py", line 693, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "C:\Program Files\python\lib\contextlib.py", line 171, in __aenter__
    return await self.gen.__anext__()
  File "real_trading_system.py", line 1262, in lifespan
    logger.info("📊 系统组件初始化:")
Message: '📊 系统组件初始化:'
Arguments: ()
2025-08-04 00:14:23,350 | INFO | __main__ | 📊 系统组件初始化:
--- Logging error ---
Traceback (most recent call last):
  File "C:\Program Files\python\lib\logging\__init__.py", line 1084, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\u2705' in position 47: illegal multibyte sequence
Call stack:
  File "real_trading_system.py", line 1750, in <module>
    main()
  File "real_trading_system.py", line 1736, in main
    uvicorn.run(
  File "C:\Program Files\python\lib\site-packages\uvicorn\main.py", line 579, in run
    server.run()
  File "C:\Program Files\python\lib\site-packages\uvicorn\server.py", line 65, in run
    return asyncio.run(self.serve(sockets=sockets))
  File "C:\Program Files\python\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Program Files\python\lib\asyncio\base_events.py", line 603, in run_until_complete
    self.run_forever()
  File "C:\Program Files\python\lib\asyncio\windows_events.py", line 316, in run_forever
    super().run_forever()
  File "C:\Program Files\python\lib\asyncio\base_events.py", line 570, in run_forever
    self._run_once()
  File "C:\Program Files\python\lib\asyncio\base_events.py", line 1859, in _run_once
    handle._run()
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Program Files\python\lib\site-packages\uvicorn\lifespan\on.py", line 86, in main
    await app(scope, self.receive, self.send)
  File "C:\Program Files\python\lib\site-packages\uvicorn\middleware\proxy_headers.py", line 29, in __call__
    return await self.app(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\middleware\errors.py", line 152, in __call__
    await self.app(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\middleware\cors.py", line 77, in __call__
    await self.app(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\middleware\exceptions.py", line 48, in __call__
    await self.app(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\routing.py", line 724, in app
    await self.lifespan(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\routing.py", line 693, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "C:\Program Files\python\lib\contextlib.py", line 171, in __aenter__
    return await self.gen.__anext__()
  File "real_trading_system.py", line 1263, in lifespan
    logger.info("   ✅ 安全管理器")
Message: '   ✅ 安全管理器'
Arguments: ()
2025-08-04 00:14:23,366 | INFO | __main__ |    ✅ 安全管理器
--- Logging error ---
Traceback (most recent call last):
  File "C:\Program Files\python\lib\logging\__init__.py", line 1084, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\u2705' in position 47: illegal multibyte sequence
Call stack:
  File "real_trading_system.py", line 1750, in <module>
    main()
  File "real_trading_system.py", line 1736, in main
    uvicorn.run(
  File "C:\Program Files\python\lib\site-packages\uvicorn\main.py", line 579, in run
    server.run()
  File "C:\Program Files\python\lib\site-packages\uvicorn\server.py", line 65, in run
    return asyncio.run(self.serve(sockets=sockets))
  File "C:\Program Files\python\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Program Files\python\lib\asyncio\base_events.py", line 603, in run_until_complete
    self.run_forever()
  File "C:\Program Files\python\lib\asyncio\windows_events.py", line 316, in run_forever
    super().run_forever()
  File "C:\Program Files\python\lib\asyncio\base_events.py", line 570, in run_forever
    self._run_once()
  File "C:\Program Files\python\lib\asyncio\base_events.py", line 1859, in _run_once
    handle._run()
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Program Files\python\lib\site-packages\uvicorn\lifespan\on.py", line 86, in main
    await app(scope, self.receive, self.send)
  File "C:\Program Files\python\lib\site-packages\uvicorn\middleware\proxy_headers.py", line 29, in __call__
    return await self.app(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\middleware\errors.py", line 152, in __call__
    await self.app(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\middleware\cors.py", line 77, in __call__
    await self.app(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\middleware\exceptions.py", line 48, in __call__
    await self.app(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\routing.py", line 724, in app
    await self.lifespan(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\routing.py", line 693, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "C:\Program Files\python\lib\contextlib.py", line 171, in __aenter__
    return await self.gen.__anext__()
  File "real_trading_system.py", line 1264, in lifespan
    logger.info("   ✅ 审计日志记录器")
Message: '   ✅ 审计日志记录器'
Arguments: ()
2025-08-04 00:14:23,408 | INFO | __main__ |    ✅ 审计日志记录器
--- Logging error ---
Traceback (most recent call last):
  File "C:\Program Files\python\lib\logging\__init__.py", line 1084, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\u2705' in position 47: illegal multibyte sequence
Call stack:
  File "real_trading_system.py", line 1750, in <module>
    main()
  File "real_trading_system.py", line 1736, in main
    uvicorn.run(
  File "C:\Program Files\python\lib\site-packages\uvicorn\main.py", line 579, in run
    server.run()
  File "C:\Program Files\python\lib\site-packages\uvicorn\server.py", line 65, in run
    return asyncio.run(self.serve(sockets=sockets))
  File "C:\Program Files\python\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Program Files\python\lib\asyncio\base_events.py", line 603, in run_until_complete
    self.run_forever()
  File "C:\Program Files\python\lib\asyncio\windows_events.py", line 316, in run_forever
    super().run_forever()
  File "C:\Program Files\python\lib\asyncio\base_events.py", line 570, in run_forever
    self._run_once()
  File "C:\Program Files\python\lib\asyncio\base_events.py", line 1859, in _run_once
    handle._run()
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Program Files\python\lib\site-packages\uvicorn\lifespan\on.py", line 86, in main
    await app(scope, self.receive, self.send)
  File "C:\Program Files\python\lib\site-packages\uvicorn\middleware\proxy_headers.py", line 29, in __call__
    return await self.app(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\middleware\errors.py", line 152, in __call__
    await self.app(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\middleware\cors.py", line 77, in __call__
    await self.app(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\middleware\exceptions.py", line 48, in __call__
    await self.app(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\routing.py", line 724, in app
    await self.lifespan(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\routing.py", line 693, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "C:\Program Files\python\lib\contextlib.py", line 171, in __aenter__
    return await self.gen.__anext__()
  File "real_trading_system.py", line 1265, in lifespan
    logger.info("   ✅ 实盘交易状态管理")
Message: '   ✅ 实盘交易状态管理'
Arguments: ()
2025-08-04 00:14:23,439 | INFO | __main__ |    ✅ 实盘交易状态管理
--- Logging error ---
Traceback (most recent call last):
  File "C:\Program Files\python\lib\logging\__init__.py", line 1084, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\u26a0' in position 47: illegal multibyte sequence
Call stack:
  File "real_trading_system.py", line 1750, in <module>
    main()
  File "real_trading_system.py", line 1736, in main
    uvicorn.run(
  File "C:\Program Files\python\lib\site-packages\uvicorn\main.py", line 579, in run
    server.run()
  File "C:\Program Files\python\lib\site-packages\uvicorn\server.py", line 65, in run
    return asyncio.run(self.serve(sockets=sockets))
  File "C:\Program Files\python\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Program Files\python\lib\asyncio\base_events.py", line 603, in run_until_complete
    self.run_forever()
  File "C:\Program Files\python\lib\asyncio\windows_events.py", line 316, in run_forever
    super().run_forever()
  File "C:\Program Files\python\lib\asyncio\base_events.py", line 570, in run_forever
    self._run_once()
  File "C:\Program Files\python\lib\asyncio\base_events.py", line 1859, in _run_once
    handle._run()
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Program Files\python\lib\site-packages\uvicorn\lifespan\on.py", line 86, in main
    await app(scope, self.receive, self.send)
  File "C:\Program Files\python\lib\site-packages\uvicorn\middleware\proxy_headers.py", line 29, in __call__
    return await self.app(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\middleware\errors.py", line 152, in __call__
    await self.app(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\middleware\cors.py", line 77, in __call__
    await self.app(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\middleware\exceptions.py", line 48, in __call__
    await self.app(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\routing.py", line 724, in app
    await self.lifespan(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\routing.py", line 693, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "C:\Program Files\python\lib\contextlib.py", line 171, in __aenter__
    return await self.gen.__anext__()
  File "real_trading_system.py", line 1266, in lifespan
    logger.info("   ⚠️  等待交易所配置...")
Message: '   ⚠️  等待交易所配置...'
Arguments: ()
2025-08-04 00:14:23,479 | INFO | __main__ |    ⚠️  等待交易所配置...
--- Logging error ---
Traceback (most recent call last):
  File "C:\Program Files\python\lib\logging\__init__.py", line 1084, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f389' in position 44: illegal multibyte sequence
Call stack:
  File "real_trading_system.py", line 1750, in <module>
    main()
  File "real_trading_system.py", line 1736, in main
    uvicorn.run(
  File "C:\Program Files\python\lib\site-packages\uvicorn\main.py", line 579, in run
    server.run()
  File "C:\Program Files\python\lib\site-packages\uvicorn\server.py", line 65, in run
    return asyncio.run(self.serve(sockets=sockets))
  File "C:\Program Files\python\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Program Files\python\lib\asyncio\base_events.py", line 603, in run_until_complete
    self.run_forever()
  File "C:\Program Files\python\lib\asyncio\windows_events.py", line 316, in run_forever
    super().run_forever()
  File "C:\Program Files\python\lib\asyncio\base_events.py", line 570, in run_forever
    self._run_once()
  File "C:\Program Files\python\lib\asyncio\base_events.py", line 1859, in _run_once
    handle._run()
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Program Files\python\lib\site-packages\uvicorn\lifespan\on.py", line 86, in main
    await app(scope, self.receive, self.send)
  File "C:\Program Files\python\lib\site-packages\uvicorn\middleware\proxy_headers.py", line 29, in __call__
    return await self.app(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\middleware\errors.py", line 152, in __call__
    await self.app(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\middleware\cors.py", line 77, in __call__
    await self.app(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\middleware\exceptions.py", line 48, in __call__
    await self.app(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\routing.py", line 724, in app
    await self.lifespan(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\routing.py", line 693, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "C:\Program Files\python\lib\contextlib.py", line 171, in __aenter__
    return await self.gen.__anext__()
  File "real_trading_system.py", line 1268, in lifespan
    logger.info("🎉 实盘交易系统启动完成！")
Message: '🎉 实盘交易系统启动完成！'
Arguments: ()
2025-08-04 00:14:23,543 | INFO | __main__ | 🎉 实盘交易系统启动完成！
--- Logging error ---
Traceback (most recent call last):
  File "C:\Program Files\python\lib\logging\__init__.py", line 1084, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\u26a0' in position 47: illegal multibyte sequence
Call stack:
  File "real_trading_system.py", line 1750, in <module>
    main()
  File "real_trading_system.py", line 1736, in main
    uvicorn.run(
  File "C:\Program Files\python\lib\site-packages\uvicorn\main.py", line 579, in run
    server.run()
  File "C:\Program Files\python\lib\site-packages\uvicorn\server.py", line 65, in run
    return asyncio.run(self.serve(sockets=sockets))
  File "C:\Program Files\python\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Program Files\python\lib\asyncio\base_events.py", line 603, in run_until_complete
    self.run_forever()
  File "C:\Program Files\python\lib\asyncio\windows_events.py", line 316, in run_forever
    super().run_forever()
  File "C:\Program Files\python\lib\asyncio\base_events.py", line 570, in run_forever
    self._run_once()
  File "C:\Program Files\python\lib\asyncio\base_events.py", line 1859, in _run_once
    handle._run()
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Program Files\python\lib\site-packages\uvicorn\lifespan\on.py", line 86, in main
    await app(scope, self.receive, self.send)
  File "C:\Program Files\python\lib\site-packages\uvicorn\middleware\proxy_headers.py", line 29, in __call__
    return await self.app(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\middleware\errors.py", line 152, in __call__
    await self.app(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\middleware\cors.py", line 77, in __call__
    await self.app(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\middleware\exceptions.py", line 48, in __call__
    await self.app(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\routing.py", line 724, in app
    await self.lifespan(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\routing.py", line 693, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "C:\Program Files\python\lib\contextlib.py", line 171, in __aenter__
    return await self.gen.__anext__()
  File "real_trading_system.py", line 1269, in lifespan
    logger.warning("⚠️  请通过API配置交易所连接后开始交易")
Message: '⚠️  请通过API配置交易所连接后开始交易'
Arguments: ()
2025-08-04 00:14:23,568 | WARNING | __main__ | ⚠️  请通过API配置交易所连接后开始交易
[32mINFO[0m:     Application startup complete.
[31mERROR[0m:    [Errno 10048] error while attempting to bind on address ('0.0.0.0', 8000): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
[32mINFO[0m:     Waiting for application shutdown.
--- Logging error ---
Traceback (most recent call last):
  File "C:\Program Files\python\lib\logging\__init__.py", line 1084, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f6d1' in position 44: illegal multibyte sequence
Call stack:
  File "real_trading_system.py", line 1750, in <module>
    main()
  File "real_trading_system.py", line 1736, in main
    uvicorn.run(
  File "C:\Program Files\python\lib\site-packages\uvicorn\main.py", line 579, in run
    server.run()
  File "C:\Program Files\python\lib\site-packages\uvicorn\server.py", line 65, in run
    return asyncio.run(self.serve(sockets=sockets))
  File "C:\Program Files\python\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Program Files\python\lib\asyncio\base_events.py", line 603, in run_until_complete
    self.run_forever()
  File "C:\Program Files\python\lib\asyncio\windows_events.py", line 316, in run_forever
    super().run_forever()
  File "C:\Program Files\python\lib\asyncio\base_events.py", line 570, in run_forever
    self._run_once()
  File "C:\Program Files\python\lib\asyncio\base_events.py", line 1859, in _run_once
    handle._run()
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Program Files\python\lib\site-packages\uvicorn\lifespan\on.py", line 86, in main
    await app(scope, self.receive, self.send)
  File "C:\Program Files\python\lib\site-packages\uvicorn\middleware\proxy_headers.py", line 29, in __call__
    return await self.app(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\middleware\errors.py", line 152, in __call__
    await self.app(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\middleware\cors.py", line 77, in __call__
    await self.app(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\middleware\exceptions.py", line 48, in __call__
    await self.app(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\routing.py", line 724, in app
    await self.lifespan(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\routing.py", line 700, in lifespan
    await receive()
  File "C:\Program Files\python\lib\contextlib.py", line 178, in __aexit__
    await self.gen.__anext__()
  File "real_trading_system.py", line 1274, in lifespan
    logger.info("🛑 正在关闭实盘交易系统...")
Message: '🛑 正在关闭实盘交易系统...'
Arguments: ()
2025-08-04 00:14:23,611 | INFO | __main__ | 🛑 正在关闭实盘交易系统...
--- Logging error ---
Traceback (most recent call last):
  File "C:\Program Files\python\lib\logging\__init__.py", line 1084, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f44b' in position 44: illegal multibyte sequence
Call stack:
  File "real_trading_system.py", line 1750, in <module>
    main()
  File "real_trading_system.py", line 1736, in main
    uvicorn.run(
  File "C:\Program Files\python\lib\site-packages\uvicorn\main.py", line 579, in run
    server.run()
  File "C:\Program Files\python\lib\site-packages\uvicorn\server.py", line 65, in run
    return asyncio.run(self.serve(sockets=sockets))
  File "C:\Program Files\python\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Program Files\python\lib\asyncio\base_events.py", line 603, in run_until_complete
    self.run_forever()
  File "C:\Program Files\python\lib\asyncio\windows_events.py", line 316, in run_forever
    super().run_forever()
  File "C:\Program Files\python\lib\asyncio\base_events.py", line 570, in run_forever
    self._run_once()
  File "C:\Program Files\python\lib\asyncio\base_events.py", line 1859, in _run_once
    handle._run()
  File "C:\Program Files\python\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Program Files\python\lib\site-packages\uvicorn\lifespan\on.py", line 86, in main
    await app(scope, self.receive, self.send)
  File "C:\Program Files\python\lib\site-packages\uvicorn\middleware\proxy_headers.py", line 29, in __call__
    return await self.app(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\middleware\errors.py", line 152, in __call__
    await self.app(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\middleware\cors.py", line 77, in __call__
    await self.app(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\middleware\exceptions.py", line 48, in __call__
    await self.app(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\routing.py", line 724, in app
    await self.lifespan(scope, receive, send)
  File "C:\Program Files\python\lib\site-packages\starlette\routing.py", line 700, in lifespan
    await receive()
  File "C:\Program Files\python\lib\contextlib.py", line 178, in __aexit__
    await self.gen.__anext__()
  File "real_trading_system.py", line 1297, in lifespan
    logger.info("👋 实盘交易系统已关闭")
Message: '👋 实盘交易系统已关闭'
Arguments: ()
2025-08-04 00:14:23,627 | INFO | __main__ | 👋 实盘交易系统已关闭
[32mINFO[0m:     Application shutdown complete.

C:\Users\<USER>\Desktop\bitV>