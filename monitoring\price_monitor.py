"""
异步价格监控和预警系统
实现实时价格监控、价格预警、触发条件检测
严格遵守异步编程原则
"""
import asyncio
import time
from typing import Optional, Dict, List, Callable, Any, Set
from dataclasses import dataclass
from enum import Enum
import logging

from exchanges import BaseExchange

logger = logging.getLogger(__name__)

class PriceDirection(Enum):
    """价格方向"""
    UP = "up"
    DOWN = "down"
    STABLE = "stable"

class AlertType(Enum):
    """预警类型"""
    PRICE_REACHED = "price_reached"         # 价格到达
    PRICE_CROSSED = "price_crossed"         # 价格穿越
    VOLATILITY_HIGH = "volatility_high"     # 波动率过高
    TREND_CHANGE = "trend_change"           # 趋势变化

@dataclass
class PriceAlert:
    """价格预警"""
    symbol: str
    alert_type: AlertType
    trigger_price: float
    current_price: float
    direction: PriceDirection
    message: str
    timestamp: int
    metadata: Dict[str, Any] = None

@dataclass
class PriceTarget:
    """价格目标"""
    symbol: str
    target_price: float
    alert_type: AlertType
    direction: PriceDirection  # 触发方向：UP表示价格上涨到目标价，DOWN表示下跌到目标价
    callback: Optional[Callable] = None
    metadata: Dict[str, Any] = None
    created_time: int = 0
    triggered: bool = False

@dataclass
class PriceData:
    """价格数据"""
    symbol: str
    price: float
    timestamp: int
    change_24h: float = 0.0
    volume_24h: float = 0.0

class AsyncPriceMonitor:
    """
    异步价格监控器
    实现实时价格监控、预警触发、趋势分析
    """
    
    def __init__(self, exchange: BaseExchange):
        """
        初始化价格监控器
        
        Args:
            exchange: 交易所实例
        """
        self.exchange = exchange
        self._monitoring_tasks: Dict[str, asyncio.Task] = {}
        self._price_cache: Dict[str, PriceData] = {}
        self._price_history: Dict[str, List[float]] = {}
        self._price_targets: Dict[str, List[PriceTarget]] = {}
        self._alert_callbacks: List[Callable] = []
        self._is_monitoring = False

        # 并发安全锁
        self._cache_lock = asyncio.Lock()    # 保护价格缓存
        self._history_lock = asyncio.Lock()  # 保护价格历史
        self._targets_lock = asyncio.Lock()  # 保护价格目标
        
        # 价格历史长度限制
        self._max_history_length = 100
        
        # 波动率计算参数
        self._volatility_window = 20
        self._high_volatility_threshold = 0.05  # 5%
        
        logger.info("异步价格监控器初始化完成")
    
    async def monitor_single_price(self, symbol: str, check_interval: int = 5) -> None:
        """
        监控单个交易对价格
        
        Args:
            symbol: 交易对符号
            check_interval: 检查间隔（秒）
        """
        logger.info(f"开始监控价格: {symbol}, 检查间隔: {check_interval}秒")
        
        while self._is_monitoring:
            try:
                # 获取当前价格
                current_price = await self.exchange.get_current_price(symbol)
                if current_price <= 0:
                    logger.warning(f"无法获取{symbol}的价格")
                    await asyncio.sleep(check_interval)
                    continue
                
                # 更新价格数据
                await self._update_price_data(symbol, current_price)
                
                # 检查价格目标
                await self._check_price_targets(symbol, current_price)
                
                # 检查波动率
                await self._check_volatility(symbol)
                
                # 检查趋势变化
                await self._check_trend_change(symbol)
                
                await asyncio.sleep(check_interval)
                
            except asyncio.CancelledError:
                logger.info(f"停止监控价格: {symbol}")
                break
            except Exception as e:
                logger.error(f"监控价格{symbol}异常: {e}")
                await asyncio.sleep(check_interval)
    
    async def _update_price_data(self, symbol: str, price: float) -> None:
        """
        更新价格数据
        
        Args:
            symbol: 交易对符号
            price: 当前价格
        """
        try:
            timestamp = int(time.time() * 1000)
            
            # 更新价格缓存
            self._price_cache[symbol] = PriceData(
                symbol=symbol,
                price=price,
                timestamp=timestamp
            )
            
            # 更新价格历史
            if symbol not in self._price_history:
                self._price_history[symbol] = []
            
            self._price_history[symbol].append(price)
            
            # 限制历史长度
            if len(self._price_history[symbol]) > self._max_history_length:
                self._price_history[symbol] = self._price_history[symbol][-self._max_history_length:]
            
            logger.debug(f"更新价格: {symbol} = {price}")
            
        except Exception as e:
            logger.error(f"更新价格数据异常: {e}")
    
    async def _check_price_targets(self, symbol: str, current_price: float) -> None:
        """
        检查价格目标
        
        Args:
            symbol: 交易对符号
            current_price: 当前价格
        """
        try:
            if symbol not in self._price_targets:
                return
            
            targets_to_remove = []
            
            for i, target in enumerate(self._price_targets[symbol]):
                if target.triggered:
                    continue
                
                # 检查是否触发
                triggered = False
                
                if target.direction == PriceDirection.UP:
                    # 价格上涨到目标价
                    triggered = current_price >= target.target_price
                elif target.direction == PriceDirection.DOWN:
                    # 价格下跌到目标价
                    triggered = current_price <= target.target_price
                
                if triggered:
                    # 触发预警
                    alert = PriceAlert(
                        symbol=symbol,
                        alert_type=target.alert_type,
                        trigger_price=target.target_price,
                        current_price=current_price,
                        direction=target.direction,
                        message=f"{symbol}价格{target.direction.value}至{target.target_price}，当前价格{current_price}",
                        timestamp=int(time.time() * 1000),
                        metadata=target.metadata
                    )
                    
                    await self._send_price_alert(alert)
                    
                    # 调用回调函数
                    if target.callback:
                        try:
                            if asyncio.iscoroutinefunction(target.callback):
                                await target.callback(alert)
                            else:
                                target.callback(alert)
                        except Exception as e:
                            logger.error(f"价格目标回调异常: {e}")
                    
                    # 标记为已触发
                    target.triggered = True
                    targets_to_remove.append(i)
            
            # 移除已触发的目标
            for i in reversed(targets_to_remove):
                self._price_targets[symbol].pop(i)
                
        except Exception as e:
            logger.error(f"检查价格目标异常: {e}")
    
    async def _check_volatility(self, symbol: str) -> None:
        """
        检查价格波动率
        
        Args:
            symbol: 交易对符号
        """
        try:
            if symbol not in self._price_history:
                return
            
            history = self._price_history[symbol]
            if len(history) < self._volatility_window:
                return
            
            # 计算最近N个价格的标准差
            recent_prices = history[-self._volatility_window:]
            avg_price = sum(recent_prices) / len(recent_prices)
            variance = sum((p - avg_price) ** 2 for p in recent_prices) / len(recent_prices)
            std_dev = variance ** 0.5
            volatility = std_dev / avg_price if avg_price > 0 else 0
            
            # 检查是否超过高波动率阈值
            if volatility > self._high_volatility_threshold:
                alert = PriceAlert(
                    symbol=symbol,
                    alert_type=AlertType.VOLATILITY_HIGH,
                    trigger_price=0.0,
                    current_price=recent_prices[-1],
                    direction=PriceDirection.STABLE,
                    message=f"{symbol}波动率过高: {volatility*100:.2f}%",
                    timestamp=int(time.time() * 1000),
                    metadata={'volatility': volatility, 'threshold': self._high_volatility_threshold}
                )
                
                await self._send_price_alert(alert)
                
        except Exception as e:
            logger.error(f"检查波动率异常: {e}")
    
    async def _check_trend_change(self, symbol: str) -> None:
        """
        检查趋势变化
        
        Args:
            symbol: 交易对符号
        """
        try:
            if symbol not in self._price_history:
                return
            
            history = self._price_history[symbol]
            if len(history) < 10:  # 至少需要10个价格点
                return
            
            # 简单趋势检测：比较最近5个价格和前5个价格的平均值
            recent_5 = history[-5:]
            previous_5 = history[-10:-5]
            
            recent_avg = sum(recent_5) / len(recent_5)
            previous_avg = sum(previous_5) / len(previous_5)
            
            change_percent = (recent_avg - previous_avg) / previous_avg if previous_avg > 0 else 0
            
            # 检测显著趋势变化（超过2%）
            if abs(change_percent) > 0.02:
                direction = PriceDirection.UP if change_percent > 0 else PriceDirection.DOWN
                
                alert = PriceAlert(
                    symbol=symbol,
                    alert_type=AlertType.TREND_CHANGE,
                    trigger_price=0.0,
                    current_price=history[-1],
                    direction=direction,
                    message=f"{symbol}趋势变化: {direction.value}, 变化幅度{change_percent*100:.2f}%",
                    timestamp=int(time.time() * 1000),
                    metadata={'change_percent': change_percent}
                )
                
                await self._send_price_alert(alert)
                
        except Exception as e:
            logger.error(f"检查趋势变化异常: {e}")
    
    async def _send_price_alert(self, alert: PriceAlert) -> None:
        """
        发送价格预警
        
        Args:
            alert: 价格预警信息
        """
        try:
            logger.warning(f"价格预警: {alert.message}")
            
            # 调用所有注册的回调函数
            for callback in self._alert_callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(alert)
                    else:
                        callback(alert)
                except Exception as e:
                    logger.error(f"价格预警回调异常: {e}")
                    
        except Exception as e:
            logger.error(f"发送价格预警异常: {e}")
    
    async def start_monitoring(self, symbols: List[str], check_interval: int = 5) -> None:
        """
        开始监控多个交易对价格
        
        Args:
            symbols: 交易对列表
            check_interval: 检查间隔（秒）
        """
        if self._is_monitoring:
            logger.warning("价格监控已在运行中")
            return
        
        self._is_monitoring = True
        logger.info(f"开始监控价格: {symbols}")
        
        # 为每个交易对创建监控任务
        for symbol in symbols:
            task = asyncio.create_task(
                self.monitor_single_price(symbol, check_interval)
            )
            self._monitoring_tasks[symbol] = task
    
    async def stop_monitoring(self) -> None:
        """停止价格监控"""
        self._is_monitoring = False
        
        # 取消所有监控任务
        for symbol, task in self._monitoring_tasks.items():
            if not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
        
        self._monitoring_tasks.clear()
        logger.info("已停止所有价格监控")
    
    def add_price_target(self, target: PriceTarget) -> None:
        """
        添加价格目标
        
        Args:
            target: 价格目标
        """
        if target.symbol not in self._price_targets:
            self._price_targets[target.symbol] = []
        
        target.created_time = int(time.time())
        self._price_targets[target.symbol].append(target)
        
        logger.info(f"添加价格目标: {target.symbol} {target.direction.value} {target.target_price}")
    
    def remove_price_target(self, symbol: str, target_price: float, direction: PriceDirection) -> bool:
        """
        移除价格目标
        
        Args:
            symbol: 交易对符号
            target_price: 目标价格
            direction: 方向
            
        Returns:
            bool: 是否成功移除
        """
        if symbol not in self._price_targets:
            return False
        
        for i, target in enumerate(self._price_targets[symbol]):
            if target.target_price == target_price and target.direction == direction:
                self._price_targets[symbol].pop(i)
                logger.info(f"移除价格目标: {symbol} {direction.value} {target_price}")
                return True
        
        return False
    
    def get_price_targets(self, symbol: str) -> List[PriceTarget]:
        """获取价格目标列表"""
        return self._price_targets.get(symbol, []).copy()
    
    def get_current_price(self, symbol: str) -> Optional[float]:
        """获取当前价格"""
        price_data = self._price_cache.get(symbol)
        return price_data.price if price_data else None
    
    def get_price_history(self, symbol: str, limit: int = 50) -> List[float]:
        """获取价格历史"""
        history = self._price_history.get(symbol, [])
        return history[-limit:] if history else []
    
    def add_alert_callback(self, callback: Callable) -> None:
        """添加预警回调函数"""
        self._alert_callbacks.append(callback)
        logger.info("添加价格预警回调函数")
    
    def remove_alert_callback(self, callback: Callable) -> None:
        """移除预警回调函数"""
        if callback in self._alert_callbacks:
            self._alert_callbacks.remove(callback)
            logger.info("移除价格预警回调函数")
    
    async def set_alert_price(self, symbol: str, alert_price: float, direction: PriceDirection, 
                             callback: Optional[Callable] = None, metadata: Dict[str, Any] = None) -> None:
        """
        设置价格预警
        
        Args:
            symbol: 交易对符号
            alert_price: 预警价格
            direction: 触发方向
            callback: 回调函数
            metadata: 元数据
        """
        target = PriceTarget(
            symbol=symbol,
            target_price=alert_price,
            alert_type=AlertType.PRICE_REACHED,
            direction=direction,
            callback=callback,
            metadata=metadata or {}
        )
        
        self.add_price_target(target)
        logger.info(f"设置价格预警: {symbol} {direction.value} {alert_price}")
    
    def clear_all_targets(self, symbol: Optional[str] = None) -> None:
        """
        清除价格目标
        
        Args:
            symbol: 交易对符号，None表示清除所有
        """
        if symbol:
            if symbol in self._price_targets:
                del self._price_targets[symbol]

    # ==================== 线程安全的数据管理方法 ====================

    async def update_price_cache(self, symbol: str, price_data: PriceData) -> None:
        """线程安全地更新价格缓存"""
        async with self._cache_lock:
            self._price_cache[symbol] = price_data

    async def get_price_cache(self, symbol: str) -> Optional[PriceData]:
        """线程安全地获取价格缓存"""
        async with self._cache_lock:
            return self._price_cache.get(symbol)

    async def add_price_to_history(self, symbol: str, price: float, max_history: int = 1000) -> None:
        """线程安全地添加价格到历史记录"""
        async with self._history_lock:
            if symbol not in self._price_history:
                self._price_history[symbol] = []

            self._price_history[symbol].append(price)

            # 限制历史记录长度
            if len(self._price_history[symbol]) > max_history:
                self._price_history[symbol] = self._price_history[symbol][-max_history:]

    async def get_price_history(self, symbol: str, count: int = None) -> List[float]:
        """线程安全地获取价格历史"""
        async with self._history_lock:
            history = self._price_history.get(symbol, [])
            if count is None:
                return history.copy()
            return history[-count:] if count > 0 else []

    async def add_price_target(self, symbol: str, target: PriceTarget) -> None:
        """线程安全地添加价格目标"""
        async with self._targets_lock:
            if symbol not in self._price_targets:
                self._price_targets[symbol] = []
            self._price_targets[symbol].append(target)

    async def get_price_targets(self, symbol: str) -> List[PriceTarget]:
        """线程安全地获取价格目标"""
        async with self._targets_lock:
            return self._price_targets.get(symbol, []).copy()

    async def remove_triggered_targets(self, symbol: str) -> None:
        """线程安全地移除已触发的目标"""
        async with self._targets_lock:
            if symbol in self._price_targets:
                self._price_targets[symbol] = [
                    target for target in self._price_targets[symbol]
                    if not target.triggered
                ]
