<?php
/**
 * BitV MACD智能加仓交易系统 - 系统测试脚本
 * 实盘交易版本 - 全面的系统功能测试
 */

// 设置访问标识
define('BITV_ACCESS', true);

// 加载配置和核心文件
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/security.php';

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 测试结果存储
$testResults = [];
$totalTests = 0;
$passedTests = 0;

/**
 * 执行测试并记录结果
 */
function runTest($testName, $testFunction) {
    global $testResults, $totalTests, $passedTests;
    
    $totalTests++;
    echo "测试: {$testName} ... ";
    
    try {
        $result = $testFunction();
        if ($result) {
            echo "✅ 通过\n";
            $passedTests++;
            $testResults[$testName] = ['status' => 'PASS', 'message' => '测试通过'];
        } else {
            echo "❌ 失败\n";
            $testResults[$testName] = ['status' => 'FAIL', 'message' => '测试失败'];
        }
    } catch (Exception $e) {
        echo "❌ 异常: " . $e->getMessage() . "\n";
        $testResults[$testName] = ['status' => 'ERROR', 'message' => $e->getMessage()];
    }
}

/**
 * 测试配置文件加载
 */
function testConfigLoading() {
    return defined('SYSTEM_NAME') && 
           defined('PYTHON_API_BASE') && 
           defined('API_TOKEN') &&
           defined('DB_HOST');
}

/**
 * 测试数据库连接
 */
function testDatabaseConnection() {
    try {
        $pdo = getDBConnection();
        $stmt = $pdo->query('SELECT 1');
        return $stmt !== false;
    } catch (Exception $e) {
        return false;
    }
}

/**
 * 测试目录权限
 */
function testDirectoryPermissions() {
    $directories = [LOGS_PATH, UPLOADS_PATH, CACHE_PATH];
    
    foreach ($directories as $dir) {
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
        if (!is_writable($dir)) {
            return false;
        }
    }
    
    return true;
}

/**
 * 测试安全函数
 */
function testSecurityFunctions() {
    // 测试CSRF令牌生成
    $token1 = generateCSRFToken();
    $token2 = generateCSRFToken();
    
    if (empty($token1) || $token1 !== $token2) {
        return false;
    }
    
    // 测试输入清理
    $dirty = '<script>alert("xss")</script>';
    $clean = sanitizeInput($dirty);
    
    if (strpos($clean, '<script>') !== false) {
        return false;
    }
    
    // 测试邮箱验证
    if (!validateEmail('<EMAIL>') || validateEmail('invalid-email')) {
        return false;
    }
    
    return true;
}

/**
 * 测试API函数
 */
function testAPIFunctions() {
    // 测试sendAPIRequest函数存在
    if (!function_exists('sendAPIRequest')) {
        return false;
    }
    
    // 测试其他API函数存在
    $apiFunctions = [
        'getSystemStatus',
        'getMarketPrices',
        'getTradingSessions',
        'getActivePositions',
        'getAccountBalance'
    ];
    
    foreach ($apiFunctions as $func) {
        if (!function_exists($func)) {
            return false;
        }
    }
    
    return true;
}

/**
 * 测试格式化函数
 */
function testFormattingFunctions() {
    // 测试数字格式化
    if (formatNumber(1234.567, 2) !== '1,234.57') {
        return false;
    }
    
    // 测试价格格式化
    if (formatPrice(1234.56) !== '$1,234.56') {
        return false;
    }
    
    // 测试百分比格式化
    if (formatPercentage(12.345) !== '+12.35%') {
        return false;
    }
    
    return true;
}

/**
 * 测试用户认证系统
 */
function testUserAuthentication() {
    global $userAuth;
    
    if (!isset($userAuth) || !is_object($userAuth)) {
        return false;
    }
    
    // 测试权限检查函数
    if (!function_exists('hasPermission') || 
        !function_exists('requireAuth') || 
        !function_exists('requirePermission')) {
        return false;
    }
    
    return true;
}

/**
 * 测试常量定义
 */
function testConstants() {
    $requiredConstants = [
        'SYSTEM_NAME',
        'SYSTEM_VERSION',
        'PYTHON_API_BASE',
        'API_TOKEN',
        'DEFAULT_LEVERAGE',
        'MAX_LEVERAGE',
        'MIN_MARGIN',
        'MAX_MARGIN'
    ];
    
    foreach ($requiredConstants as $constant) {
        if (!defined($constant)) {
            return false;
        }
    }
    
    // 测试数组常量
    if (!isset($GLOBALS['SUPPORTED_EXCHANGES']) || 
        !isset($GLOBALS['SUPPORTED_SYMBOLS'])) {
        return false;
    }
    
    return true;
}

/**
 * 测试文件包含
 */
function testFileIncludes() {
    $requiredFiles = [
        'config/config.php',
        'includes/functions.php',
        'includes/security.php',
        'includes/header.php',
        'includes/footer.php'
    ];
    
    foreach ($requiredFiles as $file) {
        if (!file_exists($file)) {
            return false;
        }
    }
    
    return true;
}

/**
 * 测试CSS和JS资源
 */
function testAssets() {
    $requiredAssets = [
        'assets/css/style.css'
    ];
    
    foreach ($requiredAssets as $asset) {
        if (!file_exists($asset)) {
            return false;
        }
    }
    
    return true;
}

// 开始测试
echo "=" * 80 . "\n";
echo "BitV MACD智能加仓交易系统 - 系统测试\n";
echo "=" * 80 . "\n";

// 执行所有测试
runTest("配置文件加载", 'testConfigLoading');
runTest("数据库连接", 'testDatabaseConnection');
runTest("目录权限", 'testDirectoryPermissions');
runTest("安全函数", 'testSecurityFunctions');
runTest("API函数", 'testAPIFunctions');
runTest("格式化函数", 'testFormattingFunctions');
runTest("用户认证", 'testUserAuthentication');
runTest("常量定义", 'testConstants');
runTest("文件包含", 'testFileIncludes');
runTest("资源文件", 'testAssets');

// 显示测试结果
echo "\n" . "=" * 80 . "\n";
echo "测试结果汇总\n";
echo "=" * 80 . "\n";
echo "总测试数: {$totalTests}\n";
echo "通过测试: {$passedTests}\n";
echo "失败测试: " . ($totalTests - $passedTests) . "\n";
echo "成功率: " . round(($passedTests / $totalTests) * 100, 2) . "%\n";

if ($passedTests === $totalTests) {
    echo "\n🎉 所有测试通过！系统状态良好。\n";
} else {
    echo "\n⚠️  部分测试失败，请检查以下问题：\n";
    foreach ($testResults as $testName => $result) {
        if ($result['status'] !== 'PASS') {
            echo "- {$testName}: {$result['message']}\n";
        }
    }
}

echo "=" * 80 . "\n";
echo "测试完成时间: " . date('Y-m-d H:i:s') . "\n";
echo "=" * 80 . "\n";
?>
