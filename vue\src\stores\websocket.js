import { defineStore } from 'pinia'
import { ref, reactive } from 'vue'
import { io } from 'socket.io-client'
import mitt from 'mitt'

export const useWebSocketStore = defineStore('websocket', () => {
  // 状态
  const connected = ref(false)
  const socket = ref(null)
  const reconnectAttempts = ref(0)
  const maxReconnectAttempts = 5
  const reconnectInterval = ref(null)
  
  // 事件总线
  const eventBus = mitt()
  
  // 实时数据
  const realtimeData = reactive({
    price: null,
    position: null,
    risk: null,
    trading: null,
    logs: []
  })
  
  // 连接WebSocket
  const connect = () => {
    try {
      socket.value = io('ws://localhost:8000/ws', {
        transports: ['websocket'],
        autoConnect: true,
        reconnection: true,
        reconnectionAttempts: maxReconnectAttempts,
        reconnectionDelay: 1000
      })
      
      setupEventHandlers()
    } catch (error) {
      console.error('WebSocket连接失败:', error)
    }
  }
  
  // 设置事件处理器
  const setupEventHandlers = () => {
    if (!socket.value) return
    
    socket.value.on('connect', () => {
      connected.value = true
      reconnectAttempts.value = 0
      console.log('WebSocket连接成功')
      
      // 订阅实时数据
      subscribe('price_update')
      subscribe('position_update')
      subscribe('risk_update')
      subscribe('trading_update')
      subscribe('log_update')
    })
    
    socket.value.on('disconnect', () => {
      connected.value = false
      console.log('WebSocket连接断开')
    })
    
    socket.value.on('connect_error', (error) => {
      console.error('WebSocket连接错误:', error)
      reconnectAttempts.value++
      
      if (reconnectAttempts.value >= maxReconnectAttempts) {
        console.error('WebSocket重连次数超限，停止重连')
      }
    })
    
    // 价格更新
    socket.value.on('price_update', (data) => {
      realtimeData.price = data
      eventBus.emit('price_update', data)
    })
    
    // 持仓更新
    socket.value.on('position_update', (data) => {
      realtimeData.position = data
      eventBus.emit('position_update', data)
    })
    
    // 风险更新
    socket.value.on('risk_update', (data) => {
      realtimeData.risk = data
      eventBus.emit('risk_update', data)
    })
    
    // 交易状态更新
    socket.value.on('trading_update', (data) => {
      realtimeData.trading = data
      eventBus.emit('trading_update', data)
    })
    
    // 日志更新
    socket.value.on('log_update', (data) => {
      realtimeData.logs.unshift(data)
      // 保持最新1000条日志
      if (realtimeData.logs.length > 1000) {
        realtimeData.logs = realtimeData.logs.slice(0, 1000)
      }
      eventBus.emit('log_update', data)
    })
    
    // 系统通知
    socket.value.on('system_notification', (data) => {
      eventBus.emit('system_notification', data)
    })
  }
  
  // 订阅数据
  const subscribe = (channel) => {
    if (socket.value && connected.value) {
      socket.value.emit('subscribe', { channel })
    }
  }
  
  // 取消订阅
  const unsubscribe = (channel) => {
    if (socket.value && connected.value) {
      socket.value.emit('unsubscribe', { channel })
    }
  }
  
  // 发送消息
  const emit = (event, data) => {
    if (socket.value && connected.value) {
      socket.value.emit(event, data)
    }
  }
  
  // 断开连接
  const disconnect = () => {
    if (socket.value) {
      socket.value.disconnect()
      socket.value = null
    }
    connected.value = false
  }
  
  // 监听事件
  const on = (event, callback) => {
    eventBus.on(event, callback)
  }
  
  // 移除事件监听
  const off = (event, callback) => {
    eventBus.off(event, callback)
  }
  
  return {
    // 状态
    connected,
    reconnectAttempts,
    realtimeData,
    
    // 方法
    connect,
    disconnect,
    subscribe,
    unsubscribe,
    emit,
    on,
    off
  }
})
