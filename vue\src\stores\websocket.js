import { defineStore } from 'pinia'
import { ref, reactive } from 'vue'
import mitt from 'mitt'

export const useWebSocketStore = defineStore('websocket', () => {
  // 状态
  const connected = ref(false)
  const socket = ref(null)
  const reconnectAttempts = ref(0)
  const maxReconnectAttempts = 5
  const reconnectInterval = ref(null)

  // 事件总线
  const eventBus = mitt()

  // 实时数据
  const realtimeData = reactive({
    price: null,
    position: null,
    risk: null,
    trading: null,
    logs: []
  })

  // 连接WebSocket (修复: 使用原生WebSocket替代Socket.IO)
  const connect = () => {
    try {
      const wsUrl = 'ws://localhost:8000/ws'
      socket.value = new WebSocket(wsUrl)

      setupEventHandlers()
    } catch (error) {
      console.error('WebSocket连接失败:', error)
    }
  }
  
  // 设置事件处理器 (修复: 适配原生WebSocket事件)
  const setupEventHandlers = () => {
    if (!socket.value) return

    socket.value.onopen = () => {
      connected.value = true
      reconnectAttempts.value = 0
      console.log('WebSocket连接成功')

      // 发送订阅消息
      const subscribeMessage = {
        action: 'subscribe',
        channels: ['price_update', 'position_update', 'risk_update', 'trading_update', 'log_update']
      }
      socket.value.send(JSON.stringify(subscribeMessage))
    }

    socket.value.onclose = () => {
      connected.value = false
      console.log('WebSocket连接断开')

      // 自动重连
      if (reconnectAttempts.value < maxReconnectAttempts) {
        setTimeout(() => {
          reconnectAttempts.value++
          connect()
        }, 1000 * reconnectAttempts.value)
      }
    }

    socket.value.onerror = (error) => {
      console.error('WebSocket连接错误:', error)
      reconnectAttempts.value++

      if (reconnectAttempts.value >= maxReconnectAttempts) {
        console.error('WebSocket重连次数超限，停止重连')
      }
    }

    // 消息处理 (修复: 解析JSON消息)
    socket.value.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data)
        handleWebSocketMessage(message)
      } catch (error) {
        console.error('WebSocket消息解析失败:', error)
      }
    }
  }

  // 处理WebSocket消息 (新增: 统一消息处理)
  const handleWebSocketMessage = (message) => {
    const { type, data } = message

    switch (type) {
      case 'price_update':
        realtimeData.price = data
        eventBus.emit('price_update', data)
        break

      case 'position_update':
        realtimeData.position = data
        eventBus.emit('position_update', data)
        break

      case 'risk_update':
        realtimeData.risk = data
        eventBus.emit('risk_update', data)
        break

      case 'trading_update':
        realtimeData.trading = data
        eventBus.emit('trading_update', data)
        break

      case 'log_update':
        realtimeData.logs.unshift(data)
        // 保持最新1000条日志
        if (realtimeData.logs.length > 1000) {
          realtimeData.logs = realtimeData.logs.slice(0, 1000)
        }
        eventBus.emit('log_update', data)
        break

      case 'system_notification':
        eventBus.emit('system_notification', data)
        break

      case 'connection_established':
        console.log('WebSocket连接确认:', data.message)
        break

      default:
        console.log('未知消息类型:', type, data)
    }
  }

  // 订阅数据 (修复: 使用原生WebSocket发送)
  const subscribe = (channel) => {
    if (socket.value && connected.value) {
      const message = {
        action: 'subscribe',
        channel: channel
      }
      socket.value.send(JSON.stringify(message))
    }
  }

  // 取消订阅 (修复: 使用原生WebSocket发送)
  const unsubscribe = (channel) => {
    if (socket.value && connected.value) {
      const message = {
        action: 'unsubscribe',
        channel: channel
      }
      socket.value.send(JSON.stringify(message))
    }
  }

  // 发送消息 (修复: 使用原生WebSocket发送)
  const emit = (event, data) => {
    if (socket.value && connected.value) {
      const message = {
        action: event,
        data: data
      }
      socket.value.send(JSON.stringify(message))
    }
  }

  // 断开连接 (修复: 使用原生WebSocket关闭)
  const disconnect = () => {
    if (socket.value) {
      socket.value.close()
      socket.value = null
    }
    connected.value = false
  }
  
  // 监听事件
  const on = (event, callback) => {
    eventBus.on(event, callback)
  }
  
  // 移除事件监听
  const off = (event, callback) => {
    eventBus.off(event, callback)
  }
  
  return {
    // 状态
    connected,
    reconnectAttempts,
    realtimeData,
    
    // 方法
    connect,
    disconnect,
    subscribe,
    unsubscribe,
    emit,
    on,
    off
  }
})
