<div class="row mb-4">
    <div class="col-12">
        <h1 class="h2 mb-3">
            <i class="fas fa-cog text-primary me-2"></i>
            交易配置管理
        </h1>
        <p class="text-muted">配置交易所连接、策略参数和风险管理设置</p>
    </div>
</div>

<!-- 配置表单 -->
<form id="configForm" method="POST" action="<?php echo Router::url('config/save'); ?>">
    <input type="hidden" name="_token" value="<?php echo htmlspecialchars($csrf_token); ?>">
    
    <div class="row">
        <!-- 交易所配置 -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-exchange-alt me-2"></i>交易所配置
                    </h5>
                </div>
                <div class="card-body">
                    <!-- 交易所选择 -->
                    <div class="mb-3">
                        <label for="exchange" class="form-label">交易所 <span class="text-danger">*</span></label>
                        <select class="form-select" id="exchange" name="exchange" required>
                            <?php foreach ($supported_exchanges as $key => $exchange): ?>
                                <option value="<?php echo $key; ?>" 
                                    <?php echo ($config['exchange'] ?? '') === $key ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($exchange['display_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- API配置 -->
                    <div class="mb-3">
                        <label for="api_key" class="form-label">API Key <span class="text-danger">*</span></label>
                        <input type="password" class="form-control" id="api_key" name="api_key" 
                               value="<?php echo htmlspecialchars($config['api_key'] ?? ''); ?>" required>
                    </div>

                    <div class="mb-3">
                        <label for="api_secret" class="form-label">API Secret <span class="text-danger">*</span></label>
                        <input type="password" class="form-control" id="api_secret" name="api_secret" 
                               value="<?php echo htmlspecialchars($config['api_secret'] ?? ''); ?>" required>
                    </div>

                    <div class="mb-3" id="passphraseGroup" style="display: none;">
                        <label for="passphrase" class="form-label">Passphrase</label>
                        <input type="password" class="form-control" id="passphrase" name="passphrase" 
                               value="<?php echo htmlspecialchars($config['passphrase'] ?? ''); ?>">
                    </div>

                    <!-- 沙盒模式 -->
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="sandbox" name="sandbox" value="true"
                                   <?php echo isset($config['sandbox']) && $config['sandbox'] ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="sandbox">
                                沙盒模式 (测试环境)
                            </label>
                        </div>
                    </div>

                    <!-- 测试连接按钮 -->
                    <button type="button" class="btn btn-outline-primary" onclick="testConnection()">
                        <i class="fas fa-plug me-1"></i>测试连接
                    </button>
                </div>
            </div>
        </div>

        <!-- 交易参数 -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line me-2"></i>交易参数
                    </h5>
                </div>
                <div class="card-body">
                    <!-- 交易对 -->
                    <div class="mb-3">
                        <label for="symbol" class="form-label">交易对 <span class="text-danger">*</span></label>
                        <select class="form-select" id="symbol" name="symbol" required>
                            <!-- 动态加载交易对 -->
                        </select>
                    </div>

                    <!-- 杠杆倍数 -->
                    <div class="mb-3">
                        <label for="leverage" class="form-label">杠杆倍数 <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="leverage" name="leverage" 
                                   min="1" max="<?php echo $risk_management['max_leverage']; ?>" 
                                   value="<?php echo $config['leverage'] ?? 10; ?>" required>
                            <span class="input-group-text">x</span>
                        </div>
                        <div class="form-text">最大杠杆: <?php echo $risk_management['max_leverage']; ?>x</div>
                    </div>

                    <!-- 初始保证金 -->
                    <div class="mb-3">
                        <label for="initial_margin" class="form-label">初始保证金 <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="initial_margin" name="initial_margin" 
                                   min="<?php echo $risk_management['min_margin']; ?>" 
                                   max="<?php echo $risk_management['max_margin']; ?>" 
                                   step="0.01" value="<?php echo $config['initial_margin'] ?? 100; ?>" required>
                            <span class="input-group-text">USDT</span>
                        </div>
                        <div class="form-text">
                            范围: <?php echo $risk_management['min_margin']; ?> - <?php echo $risk_management['max_margin']; ?> USDT
                        </div>
                    </div>

                    <!-- 时间周期 -->
                    <div class="mb-3">
                        <label for="timeframe" class="form-label">时间周期 <span class="text-danger">*</span></label>
                        <select class="form-select" id="timeframe" name="timeframe" required>
                            <?php foreach ($timeframes as $key => $label): ?>
                                <option value="<?php echo $key; ?>" 
                                    <?php echo ($config['timeframe'] ?? '') === $key ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($label); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- MACD策略配置 -->
    <div class="row">
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-wave-square me-2"></i>MACD策略配置
                    </h5>
                </div>
                <div class="card-body">
                    <!-- 启用MACD -->
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="macd_enabled" name="macd_enabled" value="true"
                                   <?php echo isset($config['macd_enabled']) && $config['macd_enabled'] ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="macd_enabled">
                                启用MACD策略
                            </label>
                        </div>
                    </div>

                    <div id="macdParams">
                        <!-- MACD快线周期 -->
                        <div class="mb-3">
                            <label for="macd_fast" class="form-label">快线周期</label>
                            <input type="number" class="form-control" id="macd_fast" name="macd_fast" 
                                   min="5" max="50" value="<?php echo $config['macd_fast'] ?? 12; ?>">
                        </div>

                        <!-- MACD慢线周期 -->
                        <div class="mb-3">
                            <label for="macd_slow" class="form-label">慢线周期</label>
                            <input type="number" class="form-control" id="macd_slow" name="macd_slow" 
                                   min="10" max="100" value="<?php echo $config['macd_slow'] ?? 26; ?>">
                        </div>

                        <!-- MACD信号线周期 -->
                        <div class="mb-3">
                            <label for="macd_signal" class="form-label">信号线周期</label>
                            <input type="number" class="form-control" id="macd_signal" name="macd_signal" 
                                   min="3" max="30" value="<?php echo $config['macd_signal'] ?? 9; ?>">
                        </div>

                        <!-- 最小信号强度 -->
                        <div class="mb-3">
                            <label for="min_signal_strength" class="form-label">最小信号强度</label>
                            <input type="number" class="form-control" id="min_signal_strength" name="min_signal_strength" 
                                   min="0.1" max="1.0" step="0.1" value="<?php echo $config['min_signal_strength'] ?? 0.3; ?>">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 加仓策略配置 -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-plus-circle me-2"></i>加仓策略配置
                    </h5>
                </div>
                <div class="card-body">
                    <!-- 最大加仓次数 -->
                    <div class="mb-3">
                        <label for="max_add_times" class="form-label">最大加仓次数</label>
                        <input type="number" class="form-control" id="max_add_times" name="max_add_times" 
                               min="1" max="<?php echo $risk_management['max_add_times']; ?>" 
                               value="<?php echo $config['max_add_times'] ?? 3; ?>">
                    </div>

                    <!-- 加仓类型 -->
                    <div class="mb-3">
                        <label class="form-label">加仓类型</label>
                        <?php foreach ($add_position_types as $key => $type): ?>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" 
                                       id="add_type_<?php echo $key; ?>" 
                                       name="add_position_types[]" 
                                       value="<?php echo $key; ?>"
                                       <?php echo in_array($key, $config['add_position_types'] ?? []) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="add_type_<?php echo $key; ?>">
                                    <?php echo htmlspecialchars($type['name']); ?>
                                    <small class="text-muted d-block"><?php echo htmlspecialchars($type['description']); ?></small>
                                </label>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <!-- 预警点数 -->
                    <div class="mb-3">
                        <label for="alert_points" class="form-label">预警点数</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="alert_points" name="alert_points" 
                                   min="0.1" max="10.0" step="0.1" value="<?php echo $config['alert_points'] ?? 1.5; ?>">
                            <span class="input-group-text">点</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 操作按钮 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-save me-1"></i>保存配置
                            </button>
                            <button type="button" class="btn btn-outline-secondary me-2" onclick="resetConfig()">
                                <i class="fas fa-undo me-1"></i>重置默认
                            </button>
                        </div>
                        <div>
                            <button type="button" class="btn btn-outline-info me-2" onclick="exportConfig()">
                                <i class="fas fa-download me-1"></i>导出配置
                            </button>
                            <button type="button" class="btn btn-outline-warning" onclick="importConfig()">
                                <i class="fas fa-upload me-1"></i>导入配置
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

<!-- 导入配置模态框 -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">导入配置</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="importForm" method="POST" action="<?php echo Router::url('config/import'); ?>" enctype="multipart/form-data">
                <input type="hidden" name="_token" value="<?php echo htmlspecialchars($csrf_token); ?>">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="config_file" class="form-label">选择配置文件</label>
                        <input type="file" class="form-control" id="config_file" name="config_file" 
                               accept=".json" required>
                        <div class="form-text">只支持JSON格式的配置文件</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">导入</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化交易对选择
    updateSymbols();
    
    // 初始化Passphrase显示
    updatePassphraseVisibility();
    
    // 初始化MACD参数显示
    updateMACDParams();
    
    // 绑定事件
    document.getElementById('exchange').addEventListener('change', function() {
        updateSymbols();
        updatePassphraseVisibility();
    });
    
    document.getElementById('macd_enabled').addEventListener('change', updateMACDParams);
});

// 更新交易对列表
function updateSymbols() {
    const exchange = document.getElementById('exchange').value;
    const symbolSelect = document.getElementById('symbol');
    
    // 清空现有选项
    symbolSelect.innerHTML = '';
    
    // 获取支持的交易对
    const supportedExchanges = <?php echo json_encode($supported_exchanges); ?>;
    const symbols = supportedExchanges[exchange]?.supported_symbols || {};
    
    // 添加选项
    Object.entries(symbols).forEach(([value, label]) => {
        const option = document.createElement('option');
        option.value = value;
        option.textContent = label;
        
        // 设置默认选中
        const currentSymbol = '<?php echo $config['symbol'] ?? ''; ?>';
        if (value === currentSymbol) {
            option.selected = true;
        }
        
        symbolSelect.appendChild(option);
    });
}

// 更新Passphrase显示
function updatePassphraseVisibility() {
    const exchange = document.getElementById('exchange').value;
    const passphraseGroup = document.getElementById('passphraseGroup');
    const supportedExchanges = <?php echo json_encode($supported_exchanges); ?>;
    
    if (supportedExchanges[exchange]?.requires_passphrase) {
        passphraseGroup.style.display = 'block';
        document.getElementById('passphrase').required = true;
    } else {
        passphraseGroup.style.display = 'none';
        document.getElementById('passphrase').required = false;
    }
}

// 更新MACD参数显示
function updateMACDParams() {
    const enabled = document.getElementById('macd_enabled').checked;
    const params = document.getElementById('macdParams');
    
    if (enabled) {
        params.style.display = 'block';
    } else {
        params.style.display = 'none';
    }
}

// 测试连接
function testConnection() {
    const button = event.target;
    const originalHtml = button.innerHTML;
    button.innerHTML = '<span class="loading-spinner"></span> 测试中...';
    button.disabled = true;
    
    const formData = new FormData();
    formData.append('_token', '<?php echo $csrf_token; ?>');
    formData.append('exchange', document.getElementById('exchange').value);
    formData.append('api_key', document.getElementById('api_key').value);
    formData.append('api_secret', document.getElementById('api_secret').value);
    formData.append('passphrase', document.getElementById('passphrase').value);
    formData.append('sandbox', document.getElementById('sandbox').checked);
    
    fetch('<?php echo Router::url('config/test'); ?>', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', '连接测试成功！');
        } else {
            showAlert('danger', '连接测试失败: ' + data.message);
        }
    })
    .catch(error => {
        showAlert('danger', '连接测试失败: 网络错误');
    })
    .finally(() => {
        button.innerHTML = originalHtml;
        button.disabled = false;
    });
}

// 重置配置
function resetConfig() {
    if (confirm('确定要重置所有配置为默认值吗？此操作不可撤销。')) {
        const formData = new FormData();
        formData.append('_token', '<?php echo $csrf_token; ?>');
        
        fetch('<?php echo Router::url('config/reset'); ?>', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', '配置已重置为默认值');
                setTimeout(() => location.reload(), 1500);
            } else {
                showAlert('danger', '重置失败: ' + data.message);
            }
        })
        .catch(error => {
            showAlert('danger', '重置失败: 网络错误');
        });
    }
}

// 导出配置
function exportConfig() {
    window.location.href = '<?php echo Router::url('config/export'); ?>';
}

// 导入配置
function importConfig() {
    const modal = new bootstrap.Modal(document.getElementById('importModal'));
    modal.show();
}

// 表单提交处理
document.getElementById('configForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalHtml = submitBtn.innerHTML;
    
    submitBtn.innerHTML = '<span class="loading-spinner"></span> 保存中...';
    submitBtn.disabled = true;
    
    fetch(this.action, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', '配置保存成功！');
        } else {
            showAlert('danger', '保存失败: ' + data.message);
        }
    })
    .catch(error => {
        showAlert('danger', '保存失败: 网络错误');
    })
    .finally(() => {
        submitBtn.innerHTML = originalHtml;
        submitBtn.disabled = false;
    });
});

// 导入表单提交处理
document.getElementById('importForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalHtml = submitBtn.innerHTML;
    
    submitBtn.innerHTML = '<span class="loading-spinner"></span> 导入中...';
    submitBtn.disabled = true;
    
    fetch(this.action, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', '配置导入成功！');
            setTimeout(() => location.reload(), 1500);
        } else {
            showAlert('danger', '导入失败: ' + data.message);
        }
    })
    .catch(error => {
        showAlert('danger', '导入失败: 网络错误');
    })
    .finally(() => {
        submitBtn.innerHTML = originalHtml;
        submitBtn.disabled = false;
        bootstrap.Modal.getInstance(document.getElementById('importModal')).hide();
    });
});
</script>
