@echo off
chcp 65001 >nul
echo.
echo ========================================
echo   BitV Vue前端启动脚本
echo ========================================
echo.

:: 检查Node.js是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未检测到Node.js
    echo 请先安装Node.js: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js版本:
node --version

:: 检查是否在正确目录
if not exist "package.json" (
    echo ❌ 错误: 未找到package.json文件
    echo 请确保在vue目录下运行此脚本
    pause
    exit /b 1
)

:: 检查Python后端是否运行
echo.
echo 🔍 检查Python后端连接...
curl -s http://localhost:8000/health >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  警告: Python后端未运行或无法连接
    echo 请确保Python后端服务运行在 http://localhost:8000
    echo.
    echo 是否继续启动前端? (y/n)
    set /p continue=
    if /i not "%continue%"=="y" (
        echo 已取消启动
        pause
        exit /b 1
    )
) else (
    echo ✅ Python后端连接正常
)

:: 检查依赖是否安装
if not exist "node_modules" (
    echo.
    echo 📦 首次运行，正在安装依赖...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
    echo ✅ 依赖安装完成
)

:: 启动开发服务器
echo.
echo 🚀 启动Vue开发服务器...
echo.
echo 📝 访问地址:
echo    - 本地: http://localhost:3000
echo    - 网络: http://0.0.0.0:3000
echo.
echo 💡 提示:
echo    - 按 Ctrl+C 停止服务器
echo    - 修改代码会自动热重载
echo    - API请求会自动代理到Python后端
echo.

npm run dev

echo.
echo 👋 Vue前端服务已停止
pause
