"""
统一日志配置模块
提供标准化的日志格式和级别管理
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime
from typing import Optional, Dict, Any
from pathlib import Path
import json

class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""
    
    # ANSI颜色代码
    COLORS = {
        'DEBUG': '\033[36m',      # 青色
        'INFO': '\033[32m',       # 绿色
        'WARNING': '\033[33m',    # 黄色
        'ERROR': '\033[31m',      # 红色
        'CRITICAL': '\033[35m',   # 紫色
        'RESET': '\033[0m'        # 重置
    }
    
    def format(self, record):
        # 添加颜色
        if hasattr(record, 'levelname'):
            color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
            record.levelname = f"{color}{record.levelname}{self.COLORS['RESET']}"
        
        return super().format(record)

class StructuredFormatter(logging.Formatter):
    """结构化日志格式化器（JSON格式）"""
    
    def format(self, record):
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # 添加异常信息
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        # 添加自定义字段
        if hasattr(record, 'trading_session'):
            log_entry['trading_session'] = record.trading_session
        if hasattr(record, 'symbol'):
            log_entry['symbol'] = record.symbol
        if hasattr(record, 'strategy'):
            log_entry['strategy'] = record.strategy
        if hasattr(record, 'exchange'):
            log_entry['exchange'] = record.exchange
        
        return json.dumps(log_entry, ensure_ascii=False)

class TradingLoggerAdapter(logging.LoggerAdapter):
    """交易日志适配器，自动添加交易相关上下文"""
    
    def process(self, msg, kwargs):
        # 添加交易上下文信息
        extra = kwargs.get('extra', {})
        extra.update(self.extra)
        kwargs['extra'] = extra
        return msg, kwargs

class LoggingConfig:
    """日志配置管理器"""
    
    def __init__(self, 
                 log_dir: str = "logs",
                 log_level: str = "INFO",
                 max_file_size: int = 10 * 1024 * 1024,  # 10MB
                 backup_count: int = 5,
                 enable_console: bool = True,
                 enable_file: bool = True,
                 enable_structured: bool = False):
        """
        初始化日志配置
        
        Args:
            log_dir: 日志目录
            log_level: 日志级别
            max_file_size: 单个日志文件最大大小
            backup_count: 备份文件数量
            enable_console: 是否启用控制台输出
            enable_file: 是否启用文件输出
            enable_structured: 是否启用结构化日志
        """
        self.log_dir = Path(log_dir)
        self.log_level = getattr(logging, log_level.upper())
        self.max_file_size = max_file_size
        self.backup_count = backup_count
        self.enable_console = enable_console
        self.enable_file = enable_file
        self.enable_structured = enable_structured
        
        # 创建日志目录
        self.log_dir.mkdir(exist_ok=True)
        
        # 日志格式
        self.console_format = "%(asctime)s | %(levelname)-8s | %(name)-20s | %(message)s"
        self.file_format = "%(asctime)s | %(levelname)-8s | %(name)-20s | %(module)s:%(funcName)s:%(lineno)d | %(message)s"
        self.date_format = "%Y-%m-%d %H:%M:%S"
        
        # 已配置的记录器
        self.configured_loggers = set()
    
    def setup_root_logger(self):
        """设置根日志记录器"""
        root_logger = logging.getLogger()
        root_logger.setLevel(self.log_level)
        
        # 清除现有处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # 添加处理器
        if self.enable_console:
            self._add_console_handler(root_logger)
        
        if self.enable_file:
            self._add_file_handler(root_logger, "bit_trading.log")
        
        return root_logger
    
    def setup_module_logger(self, module_name: str, 
                           log_file: Optional[str] = None,
                           level: Optional[str] = None) -> logging.Logger:
        """
        设置模块日志记录器
        
        Args:
            module_name: 模块名称
            log_file: 专用日志文件名
            level: 日志级别
        
        Returns:
            配置好的日志记录器
        """
        logger = logging.getLogger(module_name)
        
        if module_name in self.configured_loggers:
            return logger
        
        # 设置日志级别
        if level:
            logger.setLevel(getattr(logging, level.upper()))
        else:
            logger.setLevel(self.log_level)
        
        # 防止重复日志
        logger.propagate = False
        
        # 添加控制台处理器
        if self.enable_console:
            self._add_console_handler(logger)
        
        # 添加文件处理器
        if self.enable_file:
            if log_file:
                self._add_file_handler(logger, log_file)
            else:
                self._add_file_handler(logger, f"{module_name}.log")
        
        self.configured_loggers.add(module_name)
        return logger
    
    def _add_console_handler(self, logger: logging.Logger):
        """添加控制台处理器"""
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(self.log_level)
        
        if self.enable_structured:
            formatter = StructuredFormatter()
        else:
            formatter = ColoredFormatter(self.console_format, self.date_format)
        
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
    
    def _add_file_handler(self, logger: logging.Logger, filename: str):
        """添加文件处理器"""
        file_path = self.log_dir / filename
        
        # 使用轮转文件处理器
        file_handler = logging.handlers.RotatingFileHandler(
            file_path,
            maxBytes=self.max_file_size,
            backupCount=self.backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(self.log_level)
        
        if self.enable_structured:
            formatter = StructuredFormatter()
        else:
            formatter = logging.Formatter(self.file_format, self.date_format)
        
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    def get_trading_logger(self, context: Dict[str, Any]) -> TradingLoggerAdapter:
        """
        获取交易日志适配器
        
        Args:
            context: 交易上下文信息
        
        Returns:
            交易日志适配器
        """
        logger = logging.getLogger("trading")
        return TradingLoggerAdapter(logger, context)
    
    def setup_trading_loggers(self):
        """设置交易相关的专用日志记录器"""
        # 核心模块日志
        self.setup_module_logger("core", "core.log", "INFO")
        
        # 交易所模块日志
        self.setup_module_logger("exchanges", "exchanges.log", "INFO")
        
        # 策略模块日志
        self.setup_module_logger("strategies", "strategies.log", "INFO")
        
        # 指标模块日志
        self.setup_module_logger("indicators", "indicators.log", "DEBUG")
        
        # 监控模块日志
        self.setup_module_logger("monitoring", "monitoring.log", "INFO")
        
        # GUI模块日志
        self.setup_module_logger("gui", "gui.log", "WARNING")
        
        # 性能监控日志
        self.setup_module_logger("utils.performance_monitor", "performance.log", "INFO")
        
        # 错误日志（只记录ERROR及以上级别）
        error_logger = logging.getLogger("errors")
        error_logger.setLevel(logging.ERROR)
        error_handler = logging.handlers.RotatingFileHandler(
            self.log_dir / "errors.log",
            maxBytes=self.max_file_size,
            backupCount=self.backup_count,
            encoding='utf-8'
        )
        error_handler.setFormatter(
            logging.Formatter(self.file_format, self.date_format)
        )
        error_logger.addHandler(error_handler)
        error_logger.propagate = False
    
    def set_log_level(self, level: str):
        """动态设置日志级别"""
        new_level = getattr(logging, level.upper())
        self.log_level = new_level
        
        # 更新所有已配置的记录器
        for logger_name in self.configured_loggers:
            logger = logging.getLogger(logger_name)
            logger.setLevel(new_level)
            
            # 更新处理器级别
            for handler in logger.handlers:
                handler.setLevel(new_level)
    
    def get_log_stats(self) -> Dict[str, Any]:
        """获取日志统计信息"""
        stats = {
            'log_dir': str(self.log_dir),
            'log_level': logging.getLevelName(self.log_level),
            'configured_loggers': list(self.configured_loggers),
            'log_files': []
        }
        
        # 统计日志文件
        for log_file in self.log_dir.glob("*.log"):
            file_stats = log_file.stat()
            stats['log_files'].append({
                'name': log_file.name,
                'size_mb': file_stats.st_size / 1024 / 1024,
                'modified': datetime.fromtimestamp(file_stats.st_mtime).isoformat()
            })
        
        return stats

# 全局日志配置实例
logging_config = LoggingConfig()

def setup_logging(log_level: str = "INFO", 
                 enable_structured: bool = False,
                 log_dir: str = "logs"):
    """
    快速设置日志配置
    
    Args:
        log_level: 日志级别
        enable_structured: 是否启用结构化日志
        log_dir: 日志目录
    """
    global logging_config
    logging_config = LoggingConfig(
        log_level=log_level,
        enable_structured=enable_structured,
        log_dir=log_dir
    )
    
    # 设置根日志记录器
    logging_config.setup_root_logger()
    
    # 设置交易相关日志记录器
    logging_config.setup_trading_loggers()
    
    return logging_config
