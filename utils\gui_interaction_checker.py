"""
GUI交互性检测工具
检测GUI界面与后端代码的交互性和一致性
"""

import ast
import os
import sys
import importlib
import inspect
from typing import Dict, List, Tuple, Any, Optional
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class GUIInteractionChecker:
    """GUI交互性检测器"""
    
    def __init__(self, project_root: str = "."):
        """
        初始化检测器
        
        Args:
            project_root: 项目根目录
        """
        self.project_root = Path(project_root)
        self.gui_files = []
        self.backend_files = []
        self.interaction_issues = []
        self.binding_issues = []
        self.async_issues = []
        
        # 扫描项目文件
        self._scan_project_files()
    
    def _scan_project_files(self):
        """扫描项目文件"""
        for root, dirs, files in os.walk(self.project_root):
            # 跳过测试目录和缓存目录
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'tests']]
            
            for file in files:
                if file.endswith('.py'):
                    file_path = Path(root) / file
                    relative_path = file_path.relative_to(self.project_root)
                    
                    if 'gui' in str(relative_path):
                        self.gui_files.append(file_path)
                    elif any(module in str(relative_path) for module in ['core', 'strategies', 'exchanges', 'monitoring']):
                        self.backend_files.append(file_path)
    
    def check_gui_backend_interaction(self) -> Dict[str, Any]:
        """检查GUI与后端的交互性"""
        logger.info("🔍 开始检查GUI与后端交互性...")
        
        results = {
            'gui_files_count': len(self.gui_files),
            'backend_files_count': len(self.backend_files),
            'interaction_issues': [],
            'binding_issues': [],
            'async_issues': [],
            'import_issues': [],
            'method_binding_issues': [],
            'data_flow_issues': []
        }
        
        # 检查各种交互问题
        self._check_import_consistency(results)
        self._check_method_bindings(results)
        self._check_async_consistency(results)
        self._check_data_flow(results)
        self._check_event_handling(results)
        
        return results
    
    def _check_import_consistency(self, results: Dict[str, Any]):
        """检查导入一致性"""
        logger.info("检查导入一致性...")
        
        for gui_file in self.gui_files:
            try:
                with open(gui_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 解析AST
                tree = ast.parse(content)
                
                # 检查导入语句
                for node in ast.walk(tree):
                    if isinstance(node, ast.Import):
                        for alias in node.names:
                            self._validate_import(alias.name, gui_file, results)
                    elif isinstance(node, ast.ImportFrom):
                        if node.module:
                            self._validate_import(node.module, gui_file, results)
            
            except Exception as e:
                results['import_issues'].append({
                    'file': str(gui_file),
                    'error': f"解析文件失败: {e}"
                })
    
    def _validate_import(self, module_name: str, file_path: Path, results: Dict[str, Any]):
        """验证导入模块"""
        # 检查是否导入了后端模块
        backend_modules = ['core', 'strategies', 'exchanges', 'monitoring', 'indicators']
        
        if any(backend_mod in module_name for backend_mod in backend_modules):
            try:
                # 尝试导入模块
                importlib.import_module(module_name)
            except ImportError as e:
                results['import_issues'].append({
                    'file': str(file_path),
                    'module': module_name,
                    'error': str(e)
                })
    
    def _check_method_bindings(self, results: Dict[str, Any]):
        """检查方法绑定"""
        logger.info("检查方法绑定...")
        
        for gui_file in self.gui_files:
            try:
                with open(gui_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 查找可能的方法绑定问题
                lines = content.split('\n')
                for i, line in enumerate(lines, 1):
                    # 检查异步方法调用
                    if 'await' in line and 'def ' in line and 'async' not in line:
                        results['method_binding_issues'].append({
                            'file': str(gui_file),
                            'line': i,
                            'issue': '在非异步方法中使用await',
                            'code': line.strip()
                        })
                    
                    # 检查回调函数绑定
                    if 'command=' in line or 'callback=' in line:
                        if 'lambda' not in line and 'self.' not in line:
                            results['method_binding_issues'].append({
                                'file': str(gui_file),
                                'line': i,
                                'issue': '可能的回调函数绑定问题',
                                'code': line.strip()
                            })
            
            except Exception as e:
                results['method_binding_issues'].append({
                    'file': str(gui_file),
                    'error': f"检查方法绑定失败: {e}"
                })
    
    def _check_async_consistency(self, results: Dict[str, Any]):
        """检查异步一致性"""
        logger.info("检查异步一致性...")
        
        for gui_file in self.gui_files:
            try:
                with open(gui_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查异步相关问题
                lines = content.split('\n')
                for i, line in enumerate(lines, 1):
                    # 检查异步调用
                    if 'asyncio.create_task' in line:
                        # 检查是否在正确的上下文中
                        if 'def ' in content[:content.find(line)] and 'async def' not in content[:content.find(line)]:
                            results['async_issues'].append({
                                'file': str(gui_file),
                                'line': i,
                                'issue': '在同步方法中创建异步任务',
                                'code': line.strip()
                            })
                    
                    # 检查事件循环使用
                    if 'asyncio.run' in line:
                        results['async_issues'].append({
                            'file': str(gui_file),
                            'line': i,
                            'issue': '在GUI中使用asyncio.run可能导致问题',
                            'code': line.strip()
                        })
            
            except Exception as e:
                results['async_issues'].append({
                    'file': str(gui_file),
                    'error': f"检查异步一致性失败: {e}"
                })
    
    def _check_data_flow(self, results: Dict[str, Any]):
        """检查数据流"""
        logger.info("检查数据流...")
        
        for gui_file in self.gui_files:
            try:
                with open(gui_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查数据流问题
                lines = content.split('\n')
                for i, line in enumerate(lines, 1):
                    # 检查变量绑定
                    if 'StringVar' in line or 'IntVar' in line or 'BooleanVar' in line:
                        # 检查是否有对应的get/set调用
                        var_name = self._extract_variable_name(line)
                        if var_name and not self._has_variable_usage(content, var_name):
                            results['data_flow_issues'].append({
                                'file': str(gui_file),
                                'line': i,
                                'issue': f'变量{var_name}定义但未使用',
                                'code': line.strip()
                            })
            
            except Exception as e:
                results['data_flow_issues'].append({
                    'file': str(gui_file),
                    'error': f"检查数据流失败: {e}"
                })
    
    def _check_event_handling(self, results: Dict[str, Any]):
        """检查事件处理"""
        logger.info("检查事件处理...")
        
        for gui_file in self.gui_files:
            try:
                with open(gui_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查事件处理问题
                if 'bind(' in content:
                    # 检查事件绑定
                    lines = content.split('\n')
                    for i, line in enumerate(lines, 1):
                        if 'bind(' in line:
                            # 检查事件处理函数是否存在
                            if 'lambda' not in line and 'self.' in line:
                                method_name = self._extract_method_name(line)
                                if method_name and not self._method_exists(content, method_name):
                                    results['binding_issues'].append({
                                        'file': str(gui_file),
                                        'line': i,
                                        'issue': f'事件处理方法{method_name}不存在',
                                        'code': line.strip()
                                    })
            
            except Exception as e:
                results['binding_issues'].append({
                    'file': str(gui_file),
                    'error': f"检查事件处理失败: {e}"
                })
    
    def _extract_variable_name(self, line: str) -> Optional[str]:
        """提取变量名"""
        try:
            if '=' in line:
                var_part = line.split('=')[0].strip()
                if 'self.' in var_part:
                    return var_part.split('self.')[-1]
                else:
                    return var_part
        except:
            pass
        return None
    
    def _has_variable_usage(self, content: str, var_name: str) -> bool:
        """检查变量是否被使用"""
        return f'{var_name}.get(' in content or f'{var_name}.set(' in content
    
    def _extract_method_name(self, line: str) -> Optional[str]:
        """提取方法名"""
        try:
            if 'self.' in line:
                parts = line.split('self.')
                for part in parts[1:]:
                    if '(' in part:
                        return part.split('(')[0]
        except:
            pass
        return None
    
    def _method_exists(self, content: str, method_name: str) -> bool:
        """检查方法是否存在"""
        return f'def {method_name}(' in content
    
    def generate_report(self, results: Dict[str, Any]) -> str:
        """生成检测报告"""
        report = []
        report.append("🔍 GUI交互性检测报告")
        report.append("=" * 50)
        report.append(f"GUI文件数量: {results['gui_files_count']}")
        report.append(f"后端文件数量: {results['backend_files_count']}")
        report.append("")
        
        # 导入问题
        if results['import_issues']:
            report.append("❌ 导入问题:")
            for issue in results['import_issues']:
                report.append(f"  - {issue['file']}: {issue.get('error', issue.get('module', 'Unknown'))}")
            report.append("")
        
        # 方法绑定问题
        if results['method_binding_issues']:
            report.append("⚠️ 方法绑定问题:")
            for issue in results['method_binding_issues']:
                report.append(f"  - {issue['file']}:{issue.get('line', '?')}: {issue['issue']}")
            report.append("")
        
        # 异步问题
        if results['async_issues']:
            report.append("🔄 异步问题:")
            for issue in results['async_issues']:
                report.append(f"  - {issue['file']}:{issue.get('line', '?')}: {issue['issue']}")
            report.append("")
        
        # 数据流问题
        if results['data_flow_issues']:
            report.append("📊 数据流问题:")
            for issue in results['data_flow_issues']:
                report.append(f"  - {issue['file']}:{issue.get('line', '?')}: {issue['issue']}")
            report.append("")
        
        # 事件绑定问题
        if results['binding_issues']:
            report.append("🎯 事件绑定问题:")
            for issue in results['binding_issues']:
                report.append(f"  - {issue['file']}:{issue.get('line', '?')}: {issue['issue']}")
            report.append("")
        
        # 总结
        total_issues = (len(results['import_issues']) + 
                       len(results['method_binding_issues']) + 
                       len(results['async_issues']) + 
                       len(results['data_flow_issues']) + 
                       len(results['binding_issues']))
        
        if total_issues == 0:
            report.append("✅ 未发现GUI交互性问题")
        else:
            report.append(f"📊 总计发现 {total_issues} 个问题")
        
        return "\n".join(report)

def check_gui_interaction(project_root: str = ".") -> str:
    """
    快速检查GUI交互性
    
    Args:
        project_root: 项目根目录
    
    Returns:
        检测报告
    """
    checker = GUIInteractionChecker(project_root)
    results = checker.check_gui_backend_interaction()
    return checker.generate_report(results)

if __name__ == '__main__':
    report = check_gui_interaction()
    print(report)
