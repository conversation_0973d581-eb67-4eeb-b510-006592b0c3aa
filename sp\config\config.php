<?php
/**
 * BitV MACD智能加仓交易系统 - 实盘版本配置文件
 * 专业级量化交易系统配置
 */

// 防止直接访问
if (!defined('BITV_ACCESS')) {
    define('BITV_ACCESS', true);
}

// 错误报告设置
error_reporting(E_ALL);
ini_set('display_errors', 0); // 生产环境设为0
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../logs/php_errors.log');

// 时区设置
date_default_timezone_set('Asia/Shanghai');

// 系统基本配置
define('SYSTEM_NAME', 'BitV MACD智能加仓交易系统');
define('SYSTEM_VERSION', '2.0.0');
define('SYSTEM_AUTHOR', 'BitV Team');

// 安全配置
define('SECURITY_SALT', 'bitv_real_trading_2024_secure_salt');
define('SESSION_TIMEOUT', 3600); // 1小时
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15分钟

// Python后端API配置
define('PYTHON_API_HOST', 'localhost');
define('PYTHON_API_PORT', 8000);
define('PYTHON_API_BASE', 'http://' . PYTHON_API_HOST . ':' . PYTHON_API_PORT);
define('PYTHON_WS_BASE', 'ws://' . PYTHON_API_HOST . ':' . PYTHON_API_PORT);

// API认证令牌
define('API_TOKEN', 'bitv_real_trading_token');

// 数据库配置
define('DB_HOST', 'localhost');
define('DB_NAME', 'bitv_real_trading');
define('DB_USER', 'bitv_user');
define('DB_PASS', 'bitv_secure_password_2024');
define('DB_CHARSET', 'utf8mb4');

// 文件路径配置
define('ROOT_PATH', dirname(__DIR__));
define('LOGS_PATH', ROOT_PATH . '/logs');
define('UPLOADS_PATH', ROOT_PATH . '/uploads');
define('CACHE_PATH', ROOT_PATH . '/cache');

// 创建必要目录
$directories = [LOGS_PATH, UPLOADS_PATH, CACHE_PATH];
foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// 交易配置
define('DEFAULT_LEVERAGE', 10);
define('MAX_LEVERAGE', 50);
define('MIN_MARGIN', 10);
define('MAX_MARGIN', 10000);
define('DEFAULT_MAX_ADD_TIMES', 3);

// 支持的交易所
if (!defined('SUPPORTED_EXCHANGES')) {
    $GLOBALS['SUPPORTED_EXCHANGES'] = [
        'okx' => [
            'name' => 'OKX',
            'display_name' => 'OKX交易所',
            'supports_futures' => true,
            'max_leverage' => 100
        ],
        'gateio' => [
            'name' => 'Gate.io',
            'display_name' => 'Gate.io交易所',
            'supports_futures' => true,
            'max_leverage' => 100
        ]
    ];
    define('SUPPORTED_EXCHANGES', $GLOBALS['SUPPORTED_EXCHANGES']);
}

// 支持的交易对
if (!defined('SUPPORTED_SYMBOLS')) {
    $GLOBALS['SUPPORTED_SYMBOLS'] = [
        'BTC-USDT-SWAP' => [
            'display_name' => 'BTC/USDT 永续',
            'base_currency' => 'BTC',
            'quote_currency' => 'USDT',
            'min_size' => 0.001,
            'tick_size' => 0.1
        ],
        'ETH-USDT-SWAP' => [
            'display_name' => 'ETH/USDT 永续',
            'base_currency' => 'ETH',
            'quote_currency' => 'USDT',
            'min_size' => 0.01,
            'tick_size' => 0.01
        ],
        'LTC-USDT-SWAP' => [
            'display_name' => 'LTC/USDT 永续',
            'base_currency' => 'LTC',
            'quote_currency' => 'USDT',
            'min_size' => 0.1,
            'tick_size' => 0.001
        ],
        'XRP-USDT-SWAP' => [
            'display_name' => 'XRP/USDT 永续',
            'base_currency' => 'XRP',
            'quote_currency' => 'USDT',
            'min_size' => 1,
            'tick_size' => 0.0001
        ],
        'ADA-USDT-SWAP' => [
            'display_name' => 'ADA/USDT 永续',
            'base_currency' => 'ADA',
            'quote_currency' => 'USDT',
            'min_size' => 1,
            'tick_size' => 0.0001
        ]
    ];
    define('SUPPORTED_SYMBOLS', $GLOBALS['SUPPORTED_SYMBOLS']);
}

// MACD策略配置
define('MACD_FAST_PERIOD', 12);
define('MACD_SLOW_PERIOD', 26);
define('MACD_SIGNAL_PERIOD', 9);

// 风险管理配置
define('MAX_POSITION_SIZE_RATIO', 0.8); // 最大仓位占总资金80%
define('LIQUIDATION_WARNING_RATIO', 0.2); // 距离强平20%时警告
define('EMERGENCY_STOP_LOSS_RATIO', 0.05); // 5%距离强平时紧急平仓

// 通知配置
define('ENABLE_EMAIL_NOTIFICATIONS', false);
define('ENABLE_SMS_NOTIFICATIONS', false);
define('ENABLE_WEBHOOK_NOTIFICATIONS', true);

// 邮件配置（如果启用）
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
define('SMTP_FROM_EMAIL', '');
define('SMTP_FROM_NAME', 'BitV Trading System');

// 短信配置（如果启用）
define('SMS_API_KEY', '');
define('SMS_API_SECRET', '');

// Webhook配置
define('WEBHOOK_URL', '');
define('WEBHOOK_SECRET', '');

// 缓存配置
define('CACHE_ENABLED', true);
define('CACHE_TTL', 300); // 5分钟

// 日志配置
define('LOG_LEVEL', 'INFO'); // DEBUG, INFO, WARNING, ERROR
define('LOG_MAX_FILES', 30); // 保留30天日志
define('LOG_MAX_SIZE', 10 * 1024 * 1024); // 10MB

// 安全头配置
define('SECURITY_HEADERS', [
    'X-Content-Type-Options' => 'nosniff',
    'X-Frame-Options' => 'DENY',
    'X-XSS-Protection' => '1; mode=block',
    'Strict-Transport-Security' => 'max-age=31536000; includeSubDomains',
    'Content-Security-Policy' => "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; img-src 'self' data: https:; connect-src 'self' ws: wss:; font-src 'self' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com;"
]);

// 应用安全头
foreach (SECURITY_HEADERS as $header => $value) {
    header($header . ': ' . $value);
}

// 数据库连接函数
function getDBConnection() {
    static $pdo = null;
    
    if ($pdo === null) {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . DB_CHARSET
            ];
            
            $pdo = new PDO($dsn, DB_USER, DB_PASS, $options);
        } catch (PDOException $e) {
            error_log("数据库连接失败: " . $e->getMessage());
            die("数据库连接失败");
        }
    }
    
    return $pdo;
}

// 日志记录函数
function writeLog($level, $message, $context = []) {
    $logFile = LOGS_PATH . '/app_' . date('Y-m-d') . '.log';
    $timestamp = date('Y-m-d H:i:s');
    $contextStr = !empty($context) ? ' | Context: ' . json_encode($context) : '';
    $logEntry = "[{$timestamp}] [{$level}] {$message}{$contextStr}" . PHP_EOL;
    
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

// 配置验证函数
function validateConfig() {
    $errors = [];
    
    // 检查Python API连接
    $apiUrl = PYTHON_API_BASE . '/health';
    $context = stream_context_create([
        'http' => [
            'timeout' => 5,
            'method' => 'GET'
        ]
    ]);
    
    $response = @file_get_contents($apiUrl, false, $context);
    if ($response === false) {
        $errors[] = "无法连接到Python后端API: " . $apiUrl;
    }
    
    // 检查必要目录权限
    $directories = [LOGS_PATH, UPLOADS_PATH, CACHE_PATH];
    foreach ($directories as $dir) {
        if (!is_writable($dir)) {
            $errors[] = "目录不可写: " . $dir;
        }
    }
    
    // 检查PHP扩展
    $requiredExtensions = ['pdo', 'pdo_mysql', 'curl', 'json', 'openssl'];
    foreach ($requiredExtensions as $ext) {
        if (!extension_loaded($ext)) {
            $errors[] = "缺少PHP扩展: " . $ext;
        }
    }
    
    return $errors;
}

// 环境检查
$configErrors = validateConfig();
if (!empty($configErrors)) {
    writeLog('ERROR', '配置验证失败', $configErrors);
    
    // 在开发环境显示错误
    if (ini_get('display_errors')) {
        echo "<h3>配置错误:</h3>";
        echo "<ul>";
        foreach ($configErrors as $error) {
            echo "<li>" . htmlspecialchars($error) . "</li>";
        }
        echo "</ul>";
    }
}

// 自动加载函数
spl_autoload_register(function ($className) {
    $classFile = ROOT_PATH . '/classes/' . $className . '.php';
    if (file_exists($classFile)) {
        require_once $classFile;
    }
});

// 全局异常处理
set_exception_handler(function ($exception) {
    writeLog('ERROR', '未捕获的异常: ' . $exception->getMessage(), [
        'file' => $exception->getFile(),
        'line' => $exception->getLine(),
        'trace' => $exception->getTraceAsString()
    ]);
    
    if (ini_get('display_errors')) {
        echo "<h3>系统错误:</h3>";
        echo "<p>" . htmlspecialchars($exception->getMessage()) . "</p>";
    } else {
        echo "<h3>系统暂时不可用</h3>";
        echo "<p>请稍后再试，或联系系统管理员。</p>";
    }
});

// 全局错误处理
set_error_handler(function ($severity, $message, $file, $line) {
    if (!(error_reporting() & $severity)) {
        return false;
    }
    
    $errorTypes = [
        E_ERROR => 'ERROR',
        E_WARNING => 'WARNING',
        E_NOTICE => 'NOTICE',
        E_USER_ERROR => 'USER_ERROR',
        E_USER_WARNING => 'USER_WARNING',
        E_USER_NOTICE => 'USER_NOTICE'
    ];
    
    $errorType = $errorTypes[$severity] ?? 'UNKNOWN';
    
    writeLog($errorType, $message, [
        'file' => $file,
        'line' => $line
    ]);
    
    return true;
});

// 初始化完成日志
writeLog('INFO', '系统配置加载完成', [
    'version' => SYSTEM_VERSION,
    'php_version' => PHP_VERSION,
    'memory_limit' => ini_get('memory_limit')
]);

?>
