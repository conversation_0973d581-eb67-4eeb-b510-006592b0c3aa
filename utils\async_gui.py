"""
异步GUI工具
将tkinter与asyncio集成，实现真正的异步GUI
"""

import asyncio
import tkinter as tk
from typing import Callable, Any, Optional
import logging
import threading
import time

logger = logging.getLogger(__name__)


class AsyncTkinter:
    """异步Tkinter包装器"""
    
    def __init__(self, root: tk.Tk):
        self.root = root
        self.loop = None
        self.running = False
        self._tasks = set()
        
    async def setup_async_loop(self):
        """设置异步事件循环"""
        try:
            self.loop = asyncio.get_running_loop()
            self.running = True
            logger.info("异步GUI循环已设置")
        except RuntimeError:
            # 如果没有运行中的循环，创建一个新的
            self.loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.loop)
            self.running = True
            logger.info("创建新的异步GUI循环")
    
    def schedule_coroutine(self, coro, callback: Optional[Callable] = None):
        """调度协程执行"""
        if not self.running or not self.loop:
            logger.error("异步循环未运行，无法调度协程")
            return
        
        task = self.loop.create_task(coro)
        self._tasks.add(task)
        
        def task_done(t):
            self._tasks.discard(t)
            if callback and not t.cancelled():
                try:
                    result = t.result()
                    # 在主线程中执行回调
                    self.root.after(0, lambda: callback(result, None))
                except Exception as e:
                    self.root.after(0, lambda: callback(None, e))
        
        task.add_done_callback(task_done)
        return task
    
    def run_coroutine_sync(self, coro, timeout: float = 30.0):
        """同步运行协程（阻塞）"""
        if not self.running or not self.loop:
            raise RuntimeError("异步循环未运行")
        
        future = asyncio.run_coroutine_threadsafe(coro, self.loop)
        try:
            return future.result(timeout=timeout)
        except asyncio.TimeoutError:
            future.cancel()
            raise TimeoutError(f"协程执行超时 ({timeout}秒)")
    
    async def periodic_task(self, coro_func: Callable, interval: float):
        """周期性执行协程"""
        while self.running:
            try:
                await coro_func()
            except Exception as e:
                logger.error(f"周期性任务执行失败: {e}")
            
            await asyncio.sleep(interval)
    
    def start_periodic_task(self, coro_func: Callable, interval: float):
        """启动周期性任务"""
        task = self.schedule_coroutine(self.periodic_task(coro_func, interval))
        return task
    
    async def shutdown(self):
        """关闭异步循环"""
        self.running = False
        
        # 取消所有任务
        for task in self._tasks.copy():
            task.cancel()
        
        # 等待所有任务完成
        if self._tasks:
            await asyncio.gather(*self._tasks, return_exceptions=True)
        
        logger.info("异步GUI循环已关闭")


class AsyncMainLoop:
    """异步主循环管理器"""
    
    def __init__(self, root: tk.Tk):
        self.root = root
        self.async_tk = AsyncTkinter(root)
        self.loop = None
        self.running = False
    
    async def run_forever(self):
        """运行异步主循环（旧版本，保留兼容性）"""
        try:
            await self.async_tk.setup_async_loop()
            self.loop = self.async_tk.loop
            self.running = True

            await self.run_main_loop()

        except Exception as e:
            logger.error(f"异步主循环失败: {e}")
        finally:
            await self.shutdown()

    async def run_main_loop(self):
        """运行主循环（不包含初始化）"""
        try:
            # 启动tkinter更新循环
            while self.running:
                try:
                    # 更新tkinter
                    self.root.update()

                    # 让出控制权给其他协程
                    await asyncio.sleep(0.01)  # 100 FPS

                except tk.TclError:
                    # 窗口已关闭
                    break
                except Exception as e:
                    logger.error(f"主循环异常: {e}")
                    break

        except Exception as e:
            logger.error(f"主循环运行失败: {e}")
        finally:
            await self.shutdown()
    
    async def shutdown(self):
        """关闭主循环"""
        self.running = False
        if self.async_tk:
            await self.async_tk.shutdown()
        logger.info("异步主循环已关闭")
    
    def get_async_tk(self) -> AsyncTkinter:
        """获取异步tkinter实例"""
        return self.async_tk


def run_async_gui(root: tk.Tk, setup_func: Callable[[AsyncTkinter], Any]):
    """运行异步GUI应用"""
    async def main():
        main_loop = AsyncMainLoop(root)
        async_tk = main_loop.get_async_tk()

        # 先设置异步循环
        await async_tk.setup_async_loop()
        main_loop.loop = async_tk.loop
        main_loop.running = True

        # 然后设置GUI
        if asyncio.iscoroutinefunction(setup_func):
            await setup_func(async_tk)
        else:
            setup_func(async_tk)

        # 运行主循环（不再调用setup_async_loop）
        await main_loop.run_main_loop()

    # 运行异步主循环
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("用户中断程序")
    except Exception as e:
        logger.error(f"异步GUI运行失败: {e}")


class AsyncTimer:
    """异步定时器"""
    
    def __init__(self, async_tk: AsyncTkinter):
        self.async_tk = async_tk
        self._timers = {}
    
    def set_timeout(self, callback: Callable, delay: float, *args, **kwargs):
        """设置超时回调"""
        async def timeout_task():
            await asyncio.sleep(delay)
            if asyncio.iscoroutinefunction(callback):
                await callback(*args, **kwargs)
            else:
                callback(*args, **kwargs)
        
        task = self.async_tk.schedule_coroutine(timeout_task())
        return task
    
    def set_interval(self, callback: Callable, interval: float, *args, **kwargs):
        """设置间隔回调"""
        async def interval_task():
            while self.async_tk.running:
                # 先等待间隔时间，再执行回调
                await asyncio.sleep(interval)

                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(*args, **kwargs)
                    else:
                        callback(*args, **kwargs)
                except Exception as e:
                    logger.error(f"间隔回调执行失败: {e}")
                    import traceback
                    logger.error(f"详细错误: {traceback.format_exc()}")

        task = self.async_tk.schedule_coroutine(interval_task())
        return task
    
    def clear_timer(self, task):
        """清除定时器"""
        if task and not task.done():
            task.cancel()

    def clear_interval(self, task):
        """清除间隔定时器（别名方法）"""
        return self.clear_timer(task)


# 全局异步tkinter实例
_global_async_tk: Optional[AsyncTkinter] = None


def get_async_tk() -> Optional[AsyncTkinter]:
    """获取全局异步tkinter实例"""
    return _global_async_tk


def set_async_tk(async_tk: AsyncTkinter):
    """设置全局异步tkinter实例"""
    global _global_async_tk
    _global_async_tk = async_tk
