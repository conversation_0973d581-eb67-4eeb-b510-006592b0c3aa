"""
威廉指标(Williams %R)策略实现
基于威廉指标的超买超卖和背离信号进行交易
"""
import asyncio
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import pandas as pd
import numpy as np
from datetime import datetime

# 导入并发监控工具
try:
    from utils.concurrency_monitor import monitor_task, TaskType
except ImportError:
    # 如果导入失败，使用fallback实现
    from utils.fallback_imports import monitor_task, TaskType

logger = logging.getLogger(__name__)

class AddPositionType(Enum):
    """加仓类型"""
    EQUAL = "equal"      # 等量加仓
    HALF = "half"        # 半量加仓
    QUARTER = "quarter"  # 四分之一量加仓

class WilliamsSignal(Enum):
    """威廉指标信号"""
    OVERSOLD_BOUNCE = "oversold_bounce"        # 超卖反弹 (从-80以下反弹)
    OVERBOUGHT_DECLINE = "overbought_decline"  # 超买回落 (从-20以上回落)
    BULLISH_DIVERGENCE = "bullish_divergence"  # 看涨背离 (价格新低但威廉指标不创新低)
    BEARISH_DIVERGENCE = "bearish_divergence"  # 看跌背离 (价格新高但威廉指标不创新高)
    TREND_CONTINUATION = "trend_continuation"  # 趋势延续
    REVERSAL_SIGNAL = "reversal_signal"       # 反转信号
    BREAKOUT_SIGNAL = "breakout_signal"       # 突破信号
    CONSOLIDATION = "consolidation"           # 整理信号
    NEUTRAL = "neutral"                       # 中性
    NO_SIGNAL = "no_signal"                  # 无信号

@dataclass
class AddPositionRecord:
    """加仓记录"""
    timestamp: datetime
    price: float
    quantity: float
    margin: float
    add_type: AddPositionType
    trigger_reason: str
    new_avg_cost: float
    total_margin: float
    add_count: int

@dataclass
class WilliamsData:
    """威廉指标数据（包含可靠性评估）"""
    williams_value: float         # 威廉指标值
    williams_change: float        # 威廉指标变化
    williams_trend: str           # 威廉指标趋势 ("up", "down", "sideways")
    price_trend: str              # 价格趋势 ("up", "down", "sideways")
    signal: WilliamsSignal        # 威廉指标信号
    signal_strength: float        # 信号强度 (0.0-1.0)
    divergence_strength: float    # 背离强度 (0.0-1.0)
    trend_strength: float         # 趋势强度 (0.0-1.0)
    overbought_level: float       # 超买水平 (默认-20)
    oversold_level: float         # 超卖水平 (默认-80)
    reliability: float = 1.0      # 可靠性评分 (0.0-1.0)
    data_count: int = 0           # 实际数据点数
    required_count: int = 14      # 需要的数据点数
    calculation_period: int = 14  # 实际计算周期

class WilliamsConfig:
    """威廉指标策略配置"""
    
    def __init__(self):
        # 威廉指标参数
        self.period = 14                 # 威廉指标计算周期
        self.overbought_threshold = -20  # 超买阈值
        self.oversold_threshold = -80    # 超卖阈值
        self.signal_confirmation_periods = 2  # 信号确认周期
        self.divergence_lookback = 20    # 背离检测回看周期
        self.trend_filter_threshold = 0.3  # 趋势过滤阈值
        
        # 自定义交易参数（不使用全局配置）
        self.custom_symbol = "BTC/USDT"  # 自定义交易对
        self.custom_leverage = 20        # 自定义杠杆倍数
        self.initial_margin = 1000.0     # 初始保证金 (USDT)
        self.take_profit_percent = 2.8   # 止盈百分比
        self.stop_loss_percent = 4.5     # 止损百分比
        
        # 加仓触发参数
        self.trigger_distance_points = 80    # 触发加仓的距离(点数)
        self.trigger_distance_percent = 1.2  # 触发加仓的距离(百分比)
        self.use_points_trigger = True       # 是否使用点数触发
        
        # 加仓类型和次数
        self.add_position_types = [AddPositionType.EQUAL]  # 加仓类型序列
        self.max_add_count = 4              # 最大加仓次数
        
        # 加仓间距设置(递增)
        self.add_intervals = [2.2, 3.8, 6.0, 9.5]  # 加仓间距百分比
        
        # 风险控制
        self.max_total_loss_percent = 15.0   # 最大总亏损百分比
        self.max_investment_ratio = 0.25     # 最大投入资金比例 (25%)
        self.enable_emergency_stop = True    # 启用紧急停止
        
        # 威廉指标信号过滤
        self.enable_divergence_filter = True    # 启用背离过滤
        self.enable_trend_filter = True         # 启用趋势过滤
        self.enable_overbought_oversold_filter = True  # 启用超买超卖过滤
        self.min_signal_strength = 0.6         # 最小信号强度
        
        # 立即开仓功能
        self.immediate_open_enabled = False   # 立即开仓功能开关
        self.immediate_check_interval = 30    # 立即开仓检测间隔(秒)
        
        # 紧急加仓保护
        self.enable_emergency_add = True      # 启用紧急加仓
        self.emergency_distance_threshold = 70.0  # 紧急距离阈值(%)
        
        # 冷却时间设置
        self.order_cooldown = 40             # 下单冷却时间(秒)
        self.position_check_interval = 18    # 持仓检查间隔(秒)
        
        # 交易所设置（使用自定义设置）
        self.exchange_name = "okx"          # 默认交易所
        
        # 数据获取设置
        self.kline_limit = 200              # K线数据获取数量
        self.price_precision = 4            # 价格精度
        self.quantity_precision = 8         # 数量精度
        
        # 日志和监控
        self.enable_detailed_logging = True  # 启用详细日志
        self.enable_performance_monitoring = True  # 启用性能监控
        
        # 模拟交易设置
        self.is_simulation = False          # 是否为模拟交易
        self.simulation_balance = 10000.0   # 模拟账户余额
        
    def validate(self) -> bool:
        """验证配置参数的有效性"""
        try:
            # 验证威廉指标参数
            if self.period <= 0 or self.period > 100:
                logger.error("威廉指标周期必须在1-100之间")
                return False
            
            if not (-100 <= self.overbought_threshold <= 0):
                logger.error("超买阈值必须在-100到0之间")
                return False
            
            if not (-100 <= self.oversold_threshold <= 0):
                logger.error("超卖阈值必须在-100到0之间")
                return False
            
            if self.overbought_threshold <= self.oversold_threshold:
                logger.error("超买阈值必须大于超卖阈值")
                return False
            
            if self.signal_confirmation_periods < 1 or self.signal_confirmation_periods > 10:
                logger.error("信号确认周期必须在1-10之间")
                return False
            
            if self.divergence_lookback < 5 or self.divergence_lookback > 50:
                logger.error("背离检测回看周期必须在5-50之间")
                return False
            
            # 验证自定义交易参数
            if not self.custom_symbol or "/" not in self.custom_symbol:
                logger.error("自定义交易对格式无效")
                return False
            
            if self.custom_leverage <= 0 or self.custom_leverage > 100:
                logger.error("自定义杠杆倍数必须在1-100之间")
                return False
            
            if self.initial_margin <= 0:
                logger.error("初始保证金必须大于0")
                return False
            
            # 验证风险控制参数
            if self.take_profit_percent <= 0:
                logger.error("止盈百分比必须大于0")
                return False
            
            if self.stop_loss_percent <= 0:
                logger.error("止损百分比必须大于0")
                return False
            
            if self.max_total_loss_percent <= 0:
                logger.error("最大总亏损百分比必须大于0")
                return False
            
            if self.max_investment_ratio <= 0 or self.max_investment_ratio > 1:
                logger.error("最大投入资金比例必须在0-1之间")
                return False
            
            # 验证加仓参数
            if self.max_add_count < 0:
                logger.error("最大加仓次数不能为负数")
                return False
            
            if len(self.add_intervals) < self.max_add_count:
                logger.error("加仓间距设置数量不足")
                return False
            
            # 验证信号过滤参数
            if not (0.0 <= self.min_signal_strength <= 1.0):
                logger.error("最小信号强度必须在0.0-1.0之间")
                return False
            
            logger.info("✅ 威廉指标策略配置验证通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 威廉指标策略配置验证失败: {e}")
            return False
    
    def get_required_data_count(self) -> int:
        """获取所需的最小数据点数"""
        # 威廉指标需要的最小数据点数
        return max(self.period, 14) + 10  # 额外10个点用于趋势分析
    
    def to_dict(self) -> Dict[str, Any]:
        """将配置转换为字典"""
        return {
            # 威廉指标参数
            "period": self.period,
            "overbought_threshold": self.overbought_threshold,
            "oversold_threshold": self.oversold_threshold,
            "signal_confirmation_periods": self.signal_confirmation_periods,
            "divergence_lookback": self.divergence_lookback,
            "trend_filter_threshold": self.trend_filter_threshold,
            
            # 自定义交易参数
            "custom_symbol": self.custom_symbol,
            "custom_leverage": self.custom_leverage,
            "initial_margin": self.initial_margin,
            "take_profit_percent": self.take_profit_percent,
            "stop_loss_percent": self.stop_loss_percent,
            
            # 风险控制
            "max_total_loss_percent": self.max_total_loss_percent,
            "max_investment_ratio": self.max_investment_ratio,
            "max_add_count": self.max_add_count,
            
            # 信号过滤
            "enable_divergence_filter": self.enable_divergence_filter,
            "enable_trend_filter": self.enable_trend_filter,
            "enable_overbought_oversold_filter": self.enable_overbought_oversold_filter,
            "min_signal_strength": self.min_signal_strength,
            
            # 立即开仓
            "immediate_open_enabled": self.immediate_open_enabled,
            "immediate_check_interval": self.immediate_check_interval,
            
            # 交易设置
            "exchange_name": self.exchange_name,
        }
    
    def from_dict(self, config_dict: Dict[str, Any]):
        """从字典加载配置"""
        for key, value in config_dict.items():
            if hasattr(self, key):
                setattr(self, key, value)
            else:
                logger.warning(f"未知的配置参数: {key}")
    
    def __str__(self) -> str:
        """配置信息的字符串表示"""
        return f"""威廉指标策略配置:
        威廉指标参数: 周期={self.period}, 超买={self.overbought_threshold}, 超卖={self.oversold_threshold}, 确认周期={self.signal_confirmation_periods}
        自定义交易: 交易对={self.custom_symbol}, 杠杆={self.custom_leverage}x, 保证金={self.initial_margin}
        风险控制: 止盈={self.take_profit_percent}%, 止损={self.stop_loss_percent}%
        加仓设置: 最大次数={self.max_add_count}, 最大亏损={self.max_total_loss_percent}%
        立即开仓: {'启用' if self.immediate_open_enabled else '禁用'}
        交易所: {self.exchange_name}"""

class WilliamsCalculator:
    """威廉指标计算器 - 核心计算逻辑"""

    def __init__(self, period: int = 14, overbought: float = -20, oversold: float = -80):
        self.period = period
        self.overbought_threshold = overbought
        self.oversold_threshold = oversold

    def calculate_williams(self, highs: List[float], lows: List[float], closes: List[float],
                          min_periods: int = None) -> Optional[WilliamsData]:
        """
        计算威廉指标（支持部分数据计算）

        Args:
            highs: 最高价列表
            lows: 最低价列表
            closes: 收盘价列表
            min_periods: 最小计算周期，默认为period

        Returns:
            Optional[WilliamsData]: 威廉指标数据（包含可靠性评估）
        """
        # 设置最小计算周期
        if min_periods is None:
            min_periods = max(self.period, 14)  # 至少需要period个数据点

        data_count = len(closes)
        required_count = self.period + 10  # 额外数据用于趋势分析

        # 数据完全不足
        if data_count < min_periods or len(highs) != data_count or len(lows) != data_count:
            logger.warning(f"❌ 威廉指标数据严重不足，需要至少{min_periods}个，当前{data_count}个")
            return None

        try:
            # 计算可靠性评分
            if data_count >= required_count:
                reliability = 1.0  # 完全可靠
                calculation_period = required_count
                logger.info(f"✅ 威廉指标数据充足: {data_count}/{required_count} (可靠性: 100%)")
            else:
                reliability = data_count / required_count  # 部分可靠
                calculation_period = data_count
                logger.warning(f"⚠️ 威廉指标数据不足: {data_count}/{required_count} (可靠性: {reliability*100:.1f}%)")

            # 使用实际可用的数据
            calc_highs = highs[-calculation_period:] if calculation_period < data_count else highs
            calc_lows = lows[-calculation_period:] if calculation_period < data_count else lows
            calc_closes = closes[-calculation_period:] if calculation_period < data_count else closes

            # 转换为pandas Series进行计算
            high_series = pd.Series(calc_highs)
            low_series = pd.Series(calc_lows)
            close_series = pd.Series(calc_closes)

            # 计算威廉指标
            williams_values = self._calculate_williams_values(high_series, low_series, close_series)

            if williams_values is None or len(williams_values) == 0:
                logger.warning("❌ 威廉指标计算失败")
                return None

            # 获取最新的威廉指标值
            current_williams = williams_values.iloc[-1]
            prev_williams = williams_values.iloc[-2] if len(williams_values) > 1 else current_williams
            williams_change = current_williams - prev_williams

            # 分析威廉指标趋势
            williams_trend = self._analyze_williams_trend(williams_values)

            # 分析价格趋势
            price_trend = self._analyze_price_trend(close_series)

            # 分析威廉指标信号
            signal = self._analyze_williams_signal(williams_values, close_series)

            # 计算信号强度
            signal_strength = self._calculate_signal_strength(current_williams, signal, williams_trend, price_trend)

            # 计算背离强度
            divergence_strength = self._calculate_divergence_strength(williams_values, close_series)

            # 计算趋势强度
            trend_strength = self._calculate_trend_strength(williams_values, close_series)

            # 记录详细信息
            logger.info(f"📊 威廉指标计算完成: 当前值={current_williams:.2f}, 变化={williams_change:.2f}")
            logger.info(f"📊 威廉指标趋势: {williams_trend}, 价格趋势: {price_trend}")
            logger.info(f"📊 信号: {signal.value}, 强度: {signal_strength:.3f}")
            logger.info(f"📊 背离强度: {divergence_strength:.3f}, 趋势强度: {trend_strength:.3f}")
            logger.info(f"📊 数据统计: 使用{len(calc_closes)}个数据点, 可靠性={reliability:.2f}")

            return WilliamsData(
                williams_value=round(current_williams, 2),
                williams_change=round(williams_change, 2),
                williams_trend=williams_trend,
                price_trend=price_trend,
                signal=signal,
                signal_strength=round(signal_strength, 3),
                divergence_strength=round(divergence_strength, 3),
                trend_strength=round(trend_strength, 3),
                overbought_level=self.overbought_threshold,
                oversold_level=self.oversold_threshold,
                reliability=reliability,
                data_count=len(calc_closes),
                required_count=required_count,
                calculation_period=len(calc_closes)
            )

        except Exception as e:
            logger.error(f"计算威廉指标异常: {e}")
            return None

    def _calculate_williams_values(self, highs: pd.Series, lows: pd.Series, closes: pd.Series) -> Optional[pd.Series]:
        """
        计算威廉指标值序列

        Args:
            highs: 最高价序列
            lows: 最低价序列
            closes: 收盘价序列

        Returns:
            Optional[pd.Series]: 威廉指标值序列
        """
        try:
            if len(highs) < self.period:
                return None

            # 计算威廉指标
            # Williams %R = (Highest High - Close) / (Highest High - Lowest Low) * -100
            highest_high = highs.rolling(window=self.period).max()
            lowest_low = lows.rolling(window=self.period).min()

            williams_r = ((highest_high - closes) / (highest_high - lowest_low)) * -100

            # 去除NaN值
            williams_r = williams_r.dropna()

            logger.debug(f"威廉指标计算: 周期={self.period}, 数据点={len(williams_r)}")
            return williams_r

        except Exception as e:
            logger.error(f"计算威廉指标值异常: {e}")
            return None

    def _analyze_williams_trend(self, williams_values: pd.Series) -> str:
        """
        分析威廉指标趋势

        Args:
            williams_values: 威廉指标值序列

        Returns:
            str: 趋势方向
        """
        try:
            if len(williams_values) < 3:
                return "sideways"

            # 取最近几个值分析趋势
            recent_values = williams_values.tail(5)

            # 计算趋势斜率
            x = np.arange(len(recent_values))
            slope = np.polyfit(x, recent_values.values, 1)[0]

            if slope > 2:
                return "up"      # 上升趋势
            elif slope < -2:
                return "down"    # 下降趋势
            else:
                return "sideways"  # 横盘

        except Exception as e:
            logger.error(f"分析威廉指标趋势异常: {e}")
            return "sideways"

    def _analyze_price_trend(self, closes: pd.Series) -> str:
        """
        分析价格趋势

        Args:
            closes: 收盘价序列

        Returns:
            str: 价格趋势方向
        """
        try:
            if len(closes) < 3:
                return "sideways"

            # 取最近几个值分析趋势
            recent_closes = closes.tail(5)

            # 计算趋势斜率和价格变化
            x = np.arange(len(recent_closes))
            slope = np.polyfit(x, recent_closes.values, 1)[0]
            price_change_percent = (recent_closes.iloc[-1] - recent_closes.iloc[0]) / recent_closes.iloc[0] * 100

            # 结合斜率和价格变化判断趋势
            if price_change_percent > 0.5 and slope > 0:
                return "up"      # 上升趋势
            elif price_change_percent < -0.5 and slope < 0:
                return "down"    # 下降趋势
            else:
                return "sideways"  # 横盘

        except Exception as e:
            logger.error(f"分析价格趋势异常: {e}")
            return "sideways"

    def _analyze_williams_signal(self, williams_values: pd.Series, closes: pd.Series) -> WilliamsSignal:
        """
        分析威廉指标信号

        Args:
            williams_values: 威廉指标值序列
            closes: 收盘价序列

        Returns:
            WilliamsSignal: 威廉指标信号类型
        """
        try:
            if len(williams_values) < 3 or len(closes) < 3:
                return WilliamsSignal.NO_SIGNAL

            current_williams = williams_values.iloc[-1]
            prev_williams = williams_values.iloc[-2]

            # 检查超卖反弹信号
            if (prev_williams <= self.oversold_threshold and
                current_williams > self.oversold_threshold and
                current_williams > prev_williams):
                logger.info(f"🟢 超卖反弹信号: 威廉指标从{prev_williams:.2f}反弹至{current_williams:.2f}")
                return WilliamsSignal.OVERSOLD_BOUNCE

            # 检查超买回落信号
            if (prev_williams >= self.overbought_threshold and
                current_williams < self.overbought_threshold and
                current_williams < prev_williams):
                logger.info(f"🔴 超买回落信号: 威廉指标从{prev_williams:.2f}回落至{current_williams:.2f}")
                return WilliamsSignal.OVERBOUGHT_DECLINE

            # 检查背离信号
            divergence_signal = self._check_divergence(williams_values, closes)
            if divergence_signal != WilliamsSignal.NO_SIGNAL:
                return divergence_signal

            # 检查趋势延续信号
            if self._check_trend_continuation(williams_values, closes):
                logger.debug(f"📊 趋势延续信号: 威廉指标={current_williams:.2f}")
                return WilliamsSignal.TREND_CONTINUATION

            # 检查反转信号
            if self._check_reversal_signal(williams_values, closes):
                logger.info(f"🔄 反转信号: 威廉指标={current_williams:.2f}")
                return WilliamsSignal.REVERSAL_SIGNAL

            # 其他情况
            if self.oversold_threshold < current_williams < self.overbought_threshold:
                return WilliamsSignal.NEUTRAL
            else:
                return WilliamsSignal.NO_SIGNAL

        except Exception as e:
            logger.error(f"分析威廉指标信号异常: {e}")
            return WilliamsSignal.NO_SIGNAL

    def _check_divergence(self, williams_values: pd.Series, closes: pd.Series) -> WilliamsSignal:
        """
        检查威廉指标背离

        Args:
            williams_values: 威廉指标值序列
            closes: 收盘价序列

        Returns:
            WilliamsSignal: 背离信号类型
        """
        try:
            if len(williams_values) < 10 or len(closes) < 10:
                return WilliamsSignal.NO_SIGNAL

            # 取最近的数据进行背离分析
            recent_williams = williams_values.tail(10)
            recent_closes = closes.tail(10)

            # 找到最近的高点和低点
            williams_high_idx = recent_williams.idxmax()
            williams_low_idx = recent_williams.idxmin()
            price_high_idx = recent_closes.idxmax()
            price_low_idx = recent_closes.idxmin()

            # 检查看涨背离：价格创新低但威廉指标不创新低
            if (price_low_idx > williams_low_idx and
                recent_closes.iloc[-1] < recent_closes.iloc[0] and
                recent_williams.iloc[-1] > recent_williams.iloc[0]):
                logger.info(f"🔄 看涨背离: 价格新低但威廉指标走高")
                return WilliamsSignal.BULLISH_DIVERGENCE

            # 检查看跌背离：价格创新高但威廉指标不创新高
            if (price_high_idx > williams_high_idx and
                recent_closes.iloc[-1] > recent_closes.iloc[0] and
                recent_williams.iloc[-1] < recent_williams.iloc[0]):
                logger.info(f"🔄 看跌背离: 价格新高但威廉指标走低")
                return WilliamsSignal.BEARISH_DIVERGENCE

            return WilliamsSignal.NO_SIGNAL

        except Exception as e:
            logger.error(f"检查威廉指标背离异常: {e}")
            return WilliamsSignal.NO_SIGNAL

    def _check_trend_continuation(self, williams_values: pd.Series, closes: pd.Series) -> bool:
        """
        检查趋势延续信号

        Args:
            williams_values: 威廉指标值序列
            closes: 收盘价序列

        Returns:
            bool: 是否有趋势延续信号
        """
        try:
            if len(williams_values) < 5 or len(closes) < 5:
                return False

            recent_williams = williams_values.tail(5)
            recent_closes = closes.tail(5)

            # 价格和威廉指标同向运动
            price_trend = recent_closes.iloc[-1] > recent_closes.iloc[0]
            williams_trend = recent_williams.iloc[-1] > recent_williams.iloc[0]

            return price_trend == williams_trend

        except Exception as e:
            logger.error(f"检查趋势延续异常: {e}")
            return False

    def _check_reversal_signal(self, williams_values: pd.Series, closes: pd.Series) -> bool:
        """
        检查反转信号

        Args:
            williams_values: 威廉指标值序列
            closes: 收盘价序列

        Returns:
            bool: 是否有反转信号
        """
        try:
            if len(williams_values) < 3:
                return False

            current_williams = williams_values.iloc[-1]

            # 威廉指标在极值区域且开始反转
            if (current_williams <= self.oversold_threshold or
                current_williams >= self.overbought_threshold):

                # 检查威廉指标是否开始反转
                recent_change = williams_values.iloc[-1] - williams_values.iloc[-3]

                # 检查价格是否配合反转
                price_change = closes.iloc[-1] - closes.iloc[-3]

                if (current_williams <= self.oversold_threshold and
                    recent_change > 5 and price_change > 0):
                    return True  # 超卖区域开始上升，价格配合
                elif (current_williams >= self.overbought_threshold and
                      recent_change < -5 and price_change < 0):
                    return True  # 超买区域开始下降，价格配合

            return False

        except Exception as e:
            logger.error(f"检查反转信号异常: {e}")
            return False

    def _calculate_signal_strength(self, current_williams: float, signal: WilliamsSignal,
                                  williams_trend: str, price_trend: str) -> float:
        """
        计算信号强度

        Args:
            current_williams: 当前威廉指标值
            signal: 威廉指标信号
            williams_trend: 威廉指标趋势
            price_trend: 价格趋势

        Returns:
            float: 信号强度 (0.0-1.0)
        """
        try:
            base_strength = 0.5

            # 根据信号类型调整基础强度
            if signal in [WilliamsSignal.OVERSOLD_BOUNCE, WilliamsSignal.OVERBOUGHT_DECLINE]:
                base_strength = 0.8
            elif signal in [WilliamsSignal.BULLISH_DIVERGENCE, WilliamsSignal.BEARISH_DIVERGENCE]:
                base_strength = 0.9
            elif signal == WilliamsSignal.REVERSAL_SIGNAL:
                base_strength = 0.7

            # 根据威廉指标位置调整强度
            if current_williams <= self.oversold_threshold:
                base_strength *= 1.2  # 超卖区域增强
            elif current_williams >= self.overbought_threshold:
                base_strength *= 1.2  # 超买区域增强

            # 根据趋势一致性调整强度
            if williams_trend == price_trend and williams_trend != "sideways":
                base_strength *= 1.1  # 趋势一致增强

            return min(1.0, base_strength)

        except Exception as e:
            logger.error(f"计算信号强度异常: {e}")
            return 0.5

    def _calculate_divergence_strength(self, williams_values: pd.Series, closes: pd.Series) -> float:
        """
        计算背离强度

        Args:
            williams_values: 威廉指标值序列
            closes: 收盘价序列

        Returns:
            float: 背离强度 (0.0-1.0)
        """
        try:
            if len(williams_values) < 10 or len(closes) < 10:
                return 0.0

            recent_williams = williams_values.tail(10)
            recent_closes = closes.tail(10)

            # 计算价格和威廉指标的相关性
            correlation = recent_williams.corr(recent_closes)

            # 背离强度 = 1 - |相关性|
            divergence_strength = 1.0 - abs(correlation) if not np.isnan(correlation) else 0.0

            return max(0.0, min(1.0, divergence_strength))

        except Exception as e:
            logger.error(f"计算背离强度异常: {e}")
            return 0.0

    def _calculate_trend_strength(self, williams_values: pd.Series, closes: pd.Series) -> float:
        """
        计算趋势强度

        Args:
            williams_values: 威廉指标值序列
            closes: 收盘价序列

        Returns:
            float: 趋势强度 (0.0-1.0)
        """
        try:
            if len(williams_values) < 5:
                return 0.0

            recent_williams = williams_values.tail(5)
            recent_closes = closes.tail(5)

            # 计算威廉指标的变化幅度
            williams_range = recent_williams.max() - recent_williams.min()

            # 计算价格变化幅度
            price_change = abs(recent_closes.iloc[-1] - recent_closes.iloc[0]) / recent_closes.iloc[0]

            # 计算趋势的一致性
            williams_changes = recent_williams.diff().dropna()
            price_changes = recent_closes.diff().dropna()

            # 威廉指标和价格变化方向的一致性
            consistent_changes = sum(1 for w, p in zip(williams_changes, price_changes)
                                   if (w > 0 and p > 0) or (w < 0 and p < 0))
            consistency = consistent_changes / len(williams_changes) if len(williams_changes) > 0 else 0

            # 趋势强度 = 威廉指标变化幅度 * 价格变化幅度 * 一致性
            trend_strength = (williams_range / 100.0) * price_change * consistency

            return max(0.0, min(1.0, trend_strength))

        except Exception as e:
            logger.error(f"计算趋势强度异常: {e}")
            return 0.0

    def calculate_williams_signal_direction(self, williams_data: WilliamsData) -> str:
        """
        基于威廉指标数据计算开仓方向

        Args:
            williams_data: 威廉指标数据

        Returns:
            str: 开仓方向 ("long", "short", "hold")
        """
        try:
            # 超卖反弹信号 - 做多
            if williams_data.signal == WilliamsSignal.OVERSOLD_BOUNCE:
                logger.info("🚀 威廉指标超卖反弹做多信号")
                return "long"

            # 超买回落信号 - 做空
            elif williams_data.signal == WilliamsSignal.OVERBOUGHT_DECLINE:
                logger.info("💥 威廉指标超买回落做空信号")
                return "short"

            # 看涨背离信号 - 做多
            elif williams_data.signal == WilliamsSignal.BULLISH_DIVERGENCE:
                if williams_data.divergence_strength > 0.6:  # 需要足够的背离强度
                    logger.info("📈 威廉指标看涨背离做多信号")
                    return "long"
                else:
                    logger.info("⚠️ 威廉指标看涨背离但强度不足")
                    return "hold"

            # 看跌背离信号 - 做空
            elif williams_data.signal == WilliamsSignal.BEARISH_DIVERGENCE:
                if williams_data.divergence_strength > 0.6:  # 需要足够的背离强度
                    logger.info("📉 威廉指标看跌背离做空信号")
                    return "short"
                else:
                    logger.info("⚠️ 威廉指标看跌背离但强度不足")
                    return "hold"

            # 反转信号
            elif williams_data.signal == WilliamsSignal.REVERSAL_SIGNAL:
                if williams_data.williams_value <= williams_data.oversold_level:
                    logger.info("🔄 威廉指标超卖反转做多信号")
                    return "long"
                elif williams_data.williams_value >= williams_data.overbought_level:
                    logger.info("🔄 威廉指标超买反转做空信号")
                    return "short"
                else:
                    logger.debug("🔄 威廉指标反转但位置不明确")
                    return "hold"

            # 趋势延续信号
            elif williams_data.signal == WilliamsSignal.TREND_CONTINUATION:
                if williams_data.williams_trend == "up" and williams_data.price_trend == "up":
                    logger.info("📊 威廉指标趋势延续做多信号")
                    return "long"
                elif williams_data.williams_trend == "down" and williams_data.price_trend == "down":
                    logger.info("📊 威廉指标趋势延续做空信号")
                    return "short"
                else:
                    logger.debug("📊 威廉指标趋势延续但方向不一致")
                    return "hold"

            # 其他情况保持观望
            else:
                logger.debug(f"⏸️ 保持观望: 威廉指标信号={williams_data.signal.value}")
                return "hold"

        except Exception as e:
            logger.error(f"计算威廉指标开仓方向异常: {e}")
            return "hold"

    def detect_williams_signal(self, highs: List[float], lows: List[float], closes: List[float],
                              min_periods: int = None) -> Tuple[bool, str, float]:
        """
        专门检测威廉指标信号

        Args:
            highs: 最高价列表
            lows: 最低价列表
            closes: 收盘价列表
            min_periods: 最小计算周期

        Returns:
            Tuple[bool, str, float]: (是否有信号, 信号类型, 信号强度)
        """
        try:
            if len(closes) < self.period or len(highs) < self.period or len(lows) < self.period:
                return False, "hold", 0.0

            # 计算威廉指标数据
            williams_data = self.calculate_williams(highs, lows, closes, min_periods)
            if not williams_data:
                return False, "hold", 0.0

            # 计算开仓方向
            direction = self.calculate_williams_signal_direction(williams_data)

            if direction != "hold":
                # 获取信号强度
                signal_strength = williams_data.signal_strength

                # 结合背离强度调整
                if williams_data.divergence_strength > 0.7:
                    signal_strength *= 1.2  # 强背离增强信号

                # 结合趋势强度调整
                if williams_data.trend_strength > 0.7:
                    signal_strength *= 1.1  # 强趋势增强信号

                # 结合可靠性调整强度
                signal_strength *= williams_data.reliability

                # 限制在0-1之间
                signal_strength = min(1.0, signal_strength)

                return True, direction, signal_strength

            return False, "hold", 0.0

        except Exception as e:
            logger.error(f"检测威廉指标信号异常: {e}")
            return False, "hold", 0.0

    def is_signal_confirmed(self, highs: List[float], lows: List[float], closes: List[float],
                           signal_type: str, confirmation_periods: int = 2) -> bool:
        """
        确认威廉指标信号的有效性

        Args:
            highs: 最高价列表
            lows: 最低价列表
            closes: 收盘价列表
            signal_type: 信号类型
            confirmation_periods: 确认周期数

        Returns:
            bool: 信号是否确认
        """
        try:
            if (len(closes) < confirmation_periods + 1 or
                len(highs) < confirmation_periods + 1 or
                len(lows) < confirmation_periods + 1):
                return False

            confirmed_count = 0

            # 检查最近几个周期的信号一致性
            for i in range(confirmation_periods):
                if i == 0:
                    check_highs = highs
                    check_lows = lows
                    check_closes = closes
                else:
                    check_highs = highs[:-(i)]
                    check_lows = lows[:-(i)]
                    check_closes = closes[:-(i)]

                has_signal, detected_type, strength = self.detect_williams_signal(
                    check_highs, check_lows, check_closes)

                if has_signal and detected_type == signal_type and strength >= 0.3:
                    confirmed_count += 1

            # 需要至少一半的周期确认信号
            required_confirmations = max(1, confirmation_periods // 2)
            is_confirmed = confirmed_count >= required_confirmations

            if is_confirmed:
                logger.info(f"✅ 威廉指标信号确认: {signal_type}, 确认次数: {confirmed_count}/{confirmation_periods}")
            else:
                logger.warning(f"⚠️ 威廉指标信号未确认: {signal_type}, 确认次数: {confirmed_count}/{confirmation_periods}")

            return is_confirmed

        except Exception as e:
            logger.error(f"确认威廉指标信号异常: {e}")
            return False

class WilliamsStrategy:
    """威廉指标策略主类"""

    def __init__(self, config: WilliamsConfig, exchange_manager=None):
        self.config = config
        self.exchange_manager = exchange_manager
        self.calculator = WilliamsCalculator(
            period=config.period,
            overbought=config.overbought_threshold,
            oversold=config.oversold_threshold
        )

        # 策略状态
        self.is_running = False
        self.last_signal_time = None
        self.last_order_time = None
        self.current_position = None
        self.add_position_records: List[AddPositionRecord] = []

        # 性能监控
        self.total_trades = 0
        self.successful_trades = 0
        self.total_pnl = 0.0

        # 异步任务管理
        self.monitoring_task = None
        self.immediate_open_task = None

        logger.info(f"📊 威廉指标策略初始化完成: {config}")

    async def start(self):
        """启动威廉指标策略"""
        try:
            if self.is_running:
                logger.warning("⚠️ 威廉指标策略已在运行中")
                return

            # 验证配置
            if not self.config.validate():
                logger.error("❌ 威廉指标策略配置验证失败")
                return

            # 验证交易所连接
            if not self.exchange_manager:
                logger.error("❌ 交易所管理器未设置")
                return

            self.is_running = True
            logger.info("🚀 威廉指标策略启动成功")

            # 启动立即开仓功能（如果启用）
            if self.config.immediate_open_enabled:
                await self._start_immediate_open_monitor()

            # 启动持仓监控
            self.monitoring_task = asyncio.create_task(self._start_position_monitor())

        except Exception as e:
            logger.error(f"❌ 威廉指标策略启动失败: {e}")
            self.is_running = False

    async def stop(self):
        """停止威廉指标策略"""
        try:
            self.is_running = False

            # 停止异步任务
            if self.monitoring_task:
                self.monitoring_task.cancel()
                try:
                    await self.monitoring_task
                except asyncio.CancelledError:
                    pass

            if self.immediate_open_task:
                self.immediate_open_task.cancel()
                try:
                    await self.immediate_open_task
                except asyncio.CancelledError:
                    pass

            logger.info("🛑 威廉指标策略已停止")

        except Exception as e:
            logger.error(f"❌ 威廉指标策略停止失败: {e}")

    @monitor_task(TaskType.OPENING_CHECK)
    async def _start_immediate_open_monitor(self):
        """启动立即开仓监控"""
        try:
            logger.info(f"🔍 启动威廉指标立即开仓监控，检测间隔: {self.config.immediate_check_interval}秒")

            async def check_immediate_open():
                while self.is_running and self.config.immediate_open_enabled:
                    try:
                        await self._check_immediate_open_opportunity()
                        await asyncio.sleep(self.config.immediate_check_interval)
                    except Exception as e:
                        logger.error(f"❌ 立即开仓检查异常: {e}")
                        await asyncio.sleep(5)  # 错误时短暂等待

            self.immediate_open_task = asyncio.create_task(check_immediate_open())

        except Exception as e:
            logger.error(f"❌ 启动立即开仓监控失败: {e}")

    @monitor_task(TaskType.OPENING_CHECK)
    async def _check_immediate_open_opportunity(self):
        """检查立即开仓机会"""
        try:
            # 检查是否有持仓
            if await self._has_position():
                logger.debug("已有持仓，跳过开仓检查")
                return

            # 检查冷却时间
            if not self._check_order_cooldown():
                logger.debug("冷却时间未到，跳过开仓检查")
                return

            # 获取当前价格数据
            price_data = await self._get_price_data()
            if (not price_data or
                len(price_data['closes']) < self.config.get_required_data_count() or
                len(price_data['highs']) < self.config.get_required_data_count() or
                len(price_data['lows']) < self.config.get_required_data_count()):
                logger.warning("⚠️ 价格数据不足，无法进行威廉指标分析")
                return

            # 检测威廉指标信号
            has_signal, signal_type, signal_strength = self.calculator.detect_williams_signal(
                price_data['highs'], price_data['lows'], price_data['closes'])

            if not has_signal:
                logger.debug("未检测到威廉指标信号")
                return

            # 信号确认（如果启用）
            if self.config.signal_confirmation_periods > 1:
                if not self.calculator.is_signal_confirmed(
                    price_data['highs'], price_data['lows'], price_data['closes'],
                    signal_type, self.config.signal_confirmation_periods):
                    logger.warning(f"⚠️ 威廉指标信号未确认: {signal_type}")
                    return

            # 信号过滤
            if not self._filter_signal(price_data, signal_type, signal_strength):
                logger.warning(f"⚠️ 威廉指标信号被过滤: {signal_type}")
                return

            logger.info(f"🚀 准备执行威廉指标开仓: 方向={signal_type}, 强度={signal_strength:.3f}")
            await self._execute_immediate_opening(signal_type, signal_strength)

        except Exception as e:
            logger.error(f"❌ 检查立即开仓机会异常: {e}")

    def _filter_signal(self, price_data: Dict, signal_type: str, signal_strength: float) -> bool:
        """
        过滤威廉指标信号

        Args:
            price_data: 价格数据字典
            signal_type: 信号类型
            signal_strength: 信号强度

        Returns:
            bool: 信号是否通过过滤
        """
        try:
            # 计算威廉指标数据用于过滤
            williams_data = self.calculator.calculate_williams(
                price_data['highs'], price_data['lows'], price_data['closes'])
            if not williams_data:
                return False

            # 信号强度过滤
            if signal_strength < self.config.min_signal_strength:
                logger.debug(f"信号强度不足: {signal_strength:.3f} < {self.config.min_signal_strength}")
                return False

            # 背离过滤
            if self.config.enable_divergence_filter:
                if williams_data.divergence_strength < 0.3:
                    logger.debug(f"背离强度不足: {williams_data.divergence_strength:.3f}")
                    return False

            # 趋势过滤
            if self.config.enable_trend_filter:
                if williams_data.trend_strength < 0.3:
                    logger.debug("趋势强度不足，跳过交易")
                    return False

            # 超买超卖过滤（根据信号类型）
            if self.config.enable_overbought_oversold_filter:
                if signal_type == "long":
                    # 做多信号需要在超卖区域
                    if williams_data.williams_value > self.config.oversold_threshold:
                        logger.debug(f"做多信号但威廉指标不在超卖区域: {williams_data.williams_value}")
                        return False
                elif signal_type == "short":
                    # 做空信号需要在超买区域
                    if williams_data.williams_value < self.config.overbought_threshold:
                        logger.debug(f"做空信号但威廉指标不在超买区域: {williams_data.williams_value}")
                        return False

            return True

        except Exception as e:
            logger.error(f"过滤威廉指标信号异常: {e}")
            return False

    async def _execute_immediate_opening(self, direction: str, signal_strength: float):
        """执行立即开仓"""
        try:
            logger.info(f"🚀 [威廉指标立即开仓] 执行开仓: 方向={direction}, 强度={signal_strength:.3f}")

            # 获取当前价格
            current_price = await self._get_current_price()
            if not current_price:
                logger.error("❌ 无法获取当前价格")
                return

            # 计算开仓数量
            quantity = self._calculate_position_size(current_price)
            if quantity <= 0:
                logger.error("❌ 计算的开仓数量无效")
                return

            # 计算止损价格
            stop_loss_price = self._calculate_stop_loss_price(current_price, direction)

            # 记录开仓信息
            logger.info(f"📊 [威廉指标开仓详情] 价格: {current_price:.4f}, 数量: {quantity}, 止损: {stop_loss_price:.4f}")
            logger.info(f"📊 [威廉指标信号详情] 方向: {direction}, 强度: {signal_strength:.3f}")
            logger.info(f"📊 [自定义设置] 交易对: {self.config.custom_symbol}, 杠杆: {self.config.custom_leverage}x")
            logger.info(f"📊 [威廉指标参数] 周期: {self.config.period}, 超买: {self.config.overbought_threshold}, 超卖: {self.config.oversold_threshold}")

            # 执行开仓
            order_result = await self._place_market_order(direction, quantity, current_price)
            if order_result:
                logger.info(f"✅ [威廉指标立即开仓] 开仓成功: {direction}仓位，价格: {current_price:.4f}，数量: {quantity}")

                # 设置止损订单
                await self._place_stop_loss_order(direction, quantity, stop_loss_price)

                # 更新策略状态
                self.last_order_time = datetime.now()
                self.last_signal_time = datetime.now()
                self.total_trades += 1

                # 记录信号强度用于后续分析
                if not hasattr(self, 'signal_history'):
                    self.signal_history = []
                self.signal_history.append({
                    'timestamp': datetime.now(),
                    'direction': direction,
                    'signal_strength': signal_strength,
                    'price': current_price,
                    'quantity': quantity,
                    'custom_symbol': self.config.custom_symbol,
                    'custom_leverage': self.config.custom_leverage,
                    'williams_period': self.config.period,
                    'overbought_threshold': self.config.overbought_threshold,
                    'oversold_threshold': self.config.oversold_threshold
                })

                # 禁用立即开仓功能（开仓后自动禁用）
                self.config.immediate_open_enabled = False
                logger.info("🔒 [威廉指标立即开仓] 开仓后自动禁用立即开仓功能")

            else:
                logger.error("❌ [威廉指标立即开仓] 开仓失败")

        except Exception as e:
            logger.error(f"❌ 执行立即开仓异常: {e}")

    def _calculate_stop_loss_price(self, entry_price: float, direction: str) -> float:
        """计算止损价格"""
        try:
            stop_loss_ratio = self.config.stop_loss_percent / 100.0

            if direction == "long":
                # 多仓止损价格 = 入场价格 * (1 - 止损比例)
                stop_loss_price = entry_price * (1 - stop_loss_ratio)
            else:
                # 空仓止损价格 = 入场价格 * (1 + 止损比例)
                stop_loss_price = entry_price * (1 + stop_loss_ratio)

            logger.info(f"💡 [止损计算] 入场价: {entry_price:.4f}, 方向: {direction}, 止损价: {stop_loss_price:.4f}")
            return round(stop_loss_price, self.config.price_precision)

        except Exception as e:
            logger.error(f"❌ 计算止损价格异常: {e}")
            return entry_price * 0.955 if direction == "long" else entry_price * 1.045  # 默认4.5%止损

    # 辅助方法（与其他策略保持一致）
    async def _place_stop_loss_order(self, direction: str, quantity: float, stop_loss_price: float):
        """设置止损订单"""
        try:
            if not self.exchange_manager:
                logger.error("❌ 交易所管理器未设置")
                return

            # 确定止损订单方向（与开仓方向相反）
            stop_side = "sell" if direction == "long" else "buy"

            logger.info(f"🛡️ [止损订单] 设置止损: 方向={stop_side}, 数量={quantity}, 止损价={stop_loss_price:.4f}")
            logger.info(f"🛡️ [止损订单] 交易对: {self.config.custom_symbol}")

            # 根据交易所类型选择止损订单方式
            exchange_name = self.config.exchange_name.lower()

            if exchange_name == "okx":
                await self._place_okx_algo_stop_loss_order(stop_side, quantity, stop_loss_price)
            elif exchange_name == "gate":
                await self._place_gate_stop_loss_order(stop_side, quantity, stop_loss_price)
            else:
                # 默认使用限价单作为止损
                await self._place_limit_stop_loss_order(stop_side, quantity, stop_loss_price)

        except Exception as e:
            logger.error(f"❌ 设置止损订单异常: {e}")

    async def _place_okx_algo_stop_loss_order(self, side: str, quantity: float, stop_loss_price: float):
        """使用OKX策略委托接口设置止损订单"""
        try:
            if hasattr(self.exchange_manager.exchange, 'place_algo_order'):
                # 使用OKX策略委托接口
                order_result = await self.exchange_manager.exchange.place_algo_order(
                    symbol=self.config.custom_symbol,
                    side=side,
                    order_type="conditional",
                    quantity=quantity,
                    trigger_price=stop_loss_price,
                    order_price=-1,  # 市价执行
                    reduce_only=True
                )

                if order_result:
                    logger.info(f"✅ [OKX策略委托] 止损订单设置成功: {order_result.order_id}")
                else:
                    logger.warning("⚠️ [OKX策略委托] 止损订单设置失败，尝试限价单")
                    await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)
            else:
                logger.warning("⚠️ OKX交易所不支持策略委托，使用限价单")
                await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)

        except Exception as e:
            logger.error(f"❌ OKX策略委托止损失败: {e}")
            await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)

    async def _place_gate_stop_loss_order(self, side: str, quantity: float, stop_loss_price: float):
        """使用Gate.io止损订单"""
        try:
            # Gate.io的止损订单实现
            logger.info(f"🔧 [Gate.io止损] 设置止损订单: {side}, {quantity}, {stop_loss_price}")

            # 暂时使用限价单作为备选
            await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)

        except Exception as e:
            logger.error(f"❌ Gate.io止损订单失败: {e}")
            await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)

    async def _place_limit_stop_loss_order(self, side: str, quantity: float, stop_loss_price: float):
        """使用限价单作为止损（备选方案）"""
        try:
            logger.info(f"📋 [限价止损] 设置限价止损单: {side}, {quantity}, {stop_loss_price}")

            # 这里可以实现限价单止损逻辑
            # 注意：限价单不是真正的止损，需要持续监控价格

        except Exception as e:
            logger.error(f"❌ 限价止损订单失败: {e}")

    async def _start_position_monitor(self):
        """启动持仓监控"""
        try:
            logger.info("👁️ 启动威廉指标持仓监控")

            while self.is_running:
                try:
                    await self._check_position_status()
                    await asyncio.sleep(self.config.position_check_interval)
                except Exception as e:
                    logger.error(f"❌ 持仓监控异常: {e}")
                    await asyncio.sleep(5)

        except Exception as e:
            logger.error(f"❌ 持仓监控启动失败: {e}")

    async def _check_position_status(self):
        """检查持仓状态"""
        try:
            # 这里实现持仓状态检查逻辑
            # 包括止盈止损检查、加仓条件检查等
            pass

        except Exception as e:
            logger.error(f"❌ 检查持仓状态异常: {e}")

    # 基础辅助方法
    async def _has_position(self) -> bool:
        """检查是否有持仓"""
        try:
            if not self.exchange_manager:
                return False

            # 这里实现持仓检查逻辑
            return False  # 临时返回

        except Exception as e:
            logger.error(f"❌ 检查持仓异常: {e}")
            return False

    def _check_order_cooldown(self) -> bool:
        """检查下单冷却时间"""
        try:
            if not self.last_order_time:
                return True

            elapsed = (datetime.now() - self.last_order_time).total_seconds()
            return elapsed >= self.config.order_cooldown

        except Exception as e:
            logger.error(f"❌ 检查冷却时间异常: {e}")
            return True

    async def _get_price_data(self) -> Dict:
        """获取价格数据"""
        try:
            if not self.exchange_manager:
                return {}

            # 这里实现价格数据获取逻辑
            return {
                'highs': [],
                'lows': [],
                'closes': []
            }  # 临时返回

        except Exception as e:
            logger.error(f"❌ 获取价格数据异常: {e}")
            return {}

    async def _get_current_price(self) -> Optional[float]:
        """获取当前价格"""
        try:
            if not self.exchange_manager:
                return None

            # 这里实现当前价格获取逻辑
            return None  # 临时返回

        except Exception as e:
            logger.error(f"❌ 获取当前价格异常: {e}")
            return None

    def _calculate_position_size(self, price: float) -> float:
        """计算开仓数量"""
        try:
            # 基于初始保证金和自定义杠杆计算开仓数量
            position_value = self.config.initial_margin * self.config.custom_leverage
            quantity = position_value / price

            # 应用精度
            quantity = round(quantity, self.config.quantity_precision)

            logger.info(f"💰 [仓位计算] 价格: {price:.4f}, 保证金: {self.config.initial_margin}, 杠杆: {self.config.custom_leverage}x, 数量: {quantity}")
            return quantity

        except Exception as e:
            logger.error(f"❌ 计算开仓数量异常: {e}")
            return 0.0

    async def _place_market_order(self, direction: str, quantity: float, price: float) -> bool:
        """下市价单"""
        try:
            if not self.exchange_manager:
                logger.error("❌ 交易所管理器未设置")
                return False

            logger.info(f"📈 [威廉指标市价下单] 方向: {direction}, 数量: {quantity}, 参考价格: {price:.4f}")
            logger.info(f"📊 [交易对] 使用自定义交易对: {self.config.custom_symbol}")
            logger.info(f"📊 [杠杆] 使用自定义杠杆: {self.config.custom_leverage}x")
            logger.info(f"📊 [威廉指标设置] 周期: {self.config.period}, 超买: {self.config.overbought_threshold}, 超卖: {self.config.oversold_threshold}")

            # 确定订单方向
            from exchanges.base_exchange import OrderSide
            if direction == "long":
                order_side = OrderSide.BUY
            elif direction == "short":
                order_side = OrderSide.SELL
            else:
                logger.error(f"❌ 未知的开仓方向: {direction}")
                return False

            # 调用交易所API下单
            order_result = await self.exchange_manager.place_market_order(
                symbol=self.config.custom_symbol,
                side=order_side,
                amount=quantity,
                reduce_only=False  # 开仓单，不是平仓单
            )

            if order_result and hasattr(order_result, 'order_id') and order_result.order_id:
                logger.info(f"✅ [威廉指标开仓] 开仓订单已提交: 订单ID {order_result.order_id}")

                # 等待一段时间让订单执行
                await asyncio.sleep(1)

                # 验证开仓是否成功
                positions_after = await self.exchange_manager.get_positions(self.config.custom_symbol)
                if positions_after and any(p.size > 0 for p in positions_after):
                    logger.info(f"✅ [威廉指标开仓] 开仓验证成功：已建立持仓")
                    return True
                else:
                    logger.warning(f"⚠️ [威廉指标开仓] 开仓可能未成功，未检测到持仓")
                    return False
            else:
                logger.error(f"❌ [威廉指标开仓] 开仓订单提交失败")
                return False

        except Exception as e:
            logger.error(f"❌ 威廉指标市价下单异常: {e}")
            return False

    def get_strategy_status(self) -> Dict[str, Any]:
        """获取策略状态"""
        return {
            "is_running": self.is_running,
            "config": self.config.to_dict(),
            "total_trades": self.total_trades,
            "successful_trades": self.successful_trades,
            "success_rate": self.successful_trades / self.total_trades if self.total_trades > 0 else 0,
            "total_pnl": self.total_pnl,
            "add_position_count": len(self.add_position_records),
            "last_signal_time": self.last_signal_time.isoformat() if self.last_signal_time else None,
            "last_order_time": self.last_order_time.isoformat() if self.last_order_time else None,
            "custom_symbol": self.config.custom_symbol,
            "custom_leverage": self.config.custom_leverage,
            "williams_period": self.config.period,
            "overbought_threshold": self.config.overbought_threshold,
            "oversold_threshold": self.config.oversold_threshold,
        }
