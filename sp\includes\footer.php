<?php
/**
 * BitV MACD智能加仓交易系统 - 页面尾部模板
 * 实盘交易版本
 */

if (!defined('BITV_ACCESS')) {
    die('Direct access not allowed');
}
?>

            </main>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-white border-top mt-5 py-4">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="text-muted">
                        <strong><?php echo SYSTEM_NAME; ?></strong> v<?php echo SYSTEM_VERSION; ?>
                        <br>
                        <small>专业级量化交易系统 | 实盘交易版本</small>
                    </div>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="text-muted">
                        <small>
                            © <?php echo date('Y'); ?> <?php echo SYSTEM_AUTHOR; ?>. All rights reserved.
                            <br>
                            <i class="fas fa-shield-alt text-success"></i> 安全连接 | 
                            <i class="fas fa-lock text-primary"></i> 数据加密
                        </small>
                    </div>
                </div>
            </div>
            
            <!-- 系统信息 -->
            <div class="row mt-3 pt-3 border-top">
                <div class="col-12">
                    <div class="d-flex flex-wrap justify-content-center gap-4 text-muted small">
                        <div>
                            <i class="fas fa-server text-primary"></i>
                            服务器时间: <span id="serverTime"><?php echo date('Y-m-d H:i:s'); ?></span>
                        </div>
                        <div>
                            <i class="fas fa-exchange-alt text-success"></i>
                            支持交易所: OKX, Gate.io
                        </div>
                        <div>
                            <i class="fas fa-chart-line text-info"></i>
                            策略: MACD智能加仓
                        </div>
                        <div>
                            <i class="fas fa-shield-alt text-warning"></i>
                            风险等级: 
                            <span class="badge bg-warning text-dark">高风险</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- 风险提示模态框 -->
    <div class="modal fade" id="riskWarningModal" tabindex="-1" aria-labelledby="riskWarningModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="riskWarningModalLabel">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        重要风险提示
                    </h5>
                </div>
                <div class="modal-body">
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-warning me-2"></i>请仔细阅读以下风险提示：</h6>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-danger">交易风险</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-exclamation-circle text-danger me-2"></i>数字货币交易存在极高风险</li>
                                <li><i class="fas fa-exclamation-circle text-danger me-2"></i>价格波动可能导致重大损失</li>
                                <li><i class="fas fa-exclamation-circle text-danger me-2"></i>杠杆交易会放大盈亏</li>
                                <li><i class="fas fa-exclamation-circle text-danger me-2"></i>可能面临强制平仓风险</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-warning">系统风险</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-exclamation-triangle text-warning me-2"></i>网络延迟可能影响交易</li>
                                <li><i class="fas fa-exclamation-triangle text-warning me-2"></i>系统故障可能导致损失</li>
                                <li><i class="fas fa-exclamation-triangle text-warning me-2"></i>策略失效的可能性</li>
                                <li><i class="fas fa-exclamation-triangle text-warning me-2"></i>API连接中断风险</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="alert alert-info mt-3">
                        <h6><i class="fas fa-info-circle me-2"></i>安全建议：</h6>
                        <ul class="mb-0">
                            <li>仅投入您能承受损失的资金</li>
                            <li>建议先在沙盒环境测试</li>
                            <li>设置合理的止损点</li>
                            <li>密切监控持仓状况</li>
                            <li>定期检查系统状态</li>
                        </ul>
                    </div>
                    
                    <div class="form-check mt-3">
                        <input class="form-check-input" type="checkbox" id="riskAcknowledge">
                        <label class="form-check-label fw-bold" for="riskAcknowledge">
                            我已充分了解上述风险，并愿意承担相应后果
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="window.location.href='logout.php'">
                        <i class="fas fa-times me-2"></i>
                        退出系统
                    </button>
                    <button type="button" class="btn btn-danger" id="acceptRiskBtn" disabled onclick="acceptRisk()">
                        <i class="fas fa-check me-2"></i>
                        接受风险并继续
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 紧急停止确认模态框 -->
    <div class="modal fade" id="emergencyStopModal" tabindex="-1" aria-labelledby="emergencyStopModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="emergencyStopModalLabel">
                        <i class="fas fa-stop-circle me-2"></i>
                        紧急停止确认
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>警告：</strong>此操作将立即停止所有交易并平仓所有持仓！
                    </div>
                    <p>确认执行紧急停止操作吗？此操作不可撤销。</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmEmergencyStop">
                        <i class="fas fa-stop-circle me-2"></i>
                        确认紧急停止
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 自定义JavaScript -->
    <script>
        // 全局变量
        let riskAccepted = localStorage.getItem('riskAccepted') === 'true';
        let systemHealthCheck = null;
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 显示风险提示（首次访问）
            if (!riskAccepted) {
                showRiskWarning();
            }
            
            // 启动系统健康检查
            startHealthCheck();
            
            // 更新服务器时间
            updateServerTime();
            setInterval(updateServerTime, 1000);
            
            // 绑定风险确认复选框事件
            document.getElementById('riskAcknowledge').addEventListener('change', function() {
                document.getElementById('acceptRiskBtn').disabled = !this.checked;
            });
            
            // 绑定紧急停止确认事件
            document.getElementById('confirmEmergencyStop').addEventListener('click', function() {
                executeEmergencyStop();
            });
        });
        
        // 显示风险提示
        function showRiskWarning() {
            const modal = new bootstrap.Modal(document.getElementById('riskWarningModal'));
            modal.show();
        }
        
        // 接受风险
        function acceptRisk() {
            localStorage.setItem('riskAccepted', 'true');
            riskAccepted = true;
            
            const modal = bootstrap.Modal.getInstance(document.getElementById('riskWarningModal'));
            modal.hide();
            
            toastr.success('风险提示已确认，系统已启用');
        }
        
        // 启动系统健康检查
        function startHealthCheck() {
            systemHealthCheck = setInterval(function() {
                checkSystemHealth();
            }, 30000); // 每30秒检查一次
        }
        
        // 检查系统健康状态
        function checkSystemHealth() {
            fetch(window.BITV.API_BASE + '/health')
                .then(response => response.json())
                .then(data => {
                    updateSystemStatus(data);
                })
                .catch(error => {
                    console.error('健康检查失败:', error);
                    updateSystemStatus({ status: 'unhealthy', error: error.message });
                });
        }
        
        // 更新系统状态显示
        function updateSystemStatus(healthData) {
            const statusElement = document.getElementById('systemStatus');
            
            if (healthData.status === 'healthy') {
                statusElement.innerHTML = '<span class="badge bg-success">系统正常</span>';
            } else {
                statusElement.innerHTML = '<span class="badge bg-danger">系统异常</span>';
                
                // 显示系统异常通知
                if (riskAccepted) {
                    toastr.error('系统健康检查异常，请检查连接状态');
                }
            }
        }
        
        // 更新服务器时间
        function updateServerTime() {
            const now = new Date();
            const timeString = now.getFullYear() + '-' + 
                             String(now.getMonth() + 1).padStart(2, '0') + '-' + 
                             String(now.getDate()).padStart(2, '0') + ' ' + 
                             String(now.getHours()).padStart(2, '0') + ':' + 
                             String(now.getMinutes()).padStart(2, '0') + ':' + 
                             String(now.getSeconds()).padStart(2, '0');
            
            const serverTimeElement = document.getElementById('serverTime');
            if (serverTimeElement) {
                serverTimeElement.textContent = timeString;
            }
        }
        
        // 执行紧急停止
        function executeEmergencyStop() {
            showLoading('正在执行紧急停止...');
            
            fetch(window.BITV.API_BASE + '/api/emergency/stop', {
                method: 'POST',
                headers: {
                    'Authorization': 'Bearer ' + window.BITV.API_TOKEN,
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                
                const modal = bootstrap.Modal.getInstance(document.getElementById('emergencyStopModal'));
                modal.hide();
                
                if (data.success) {
                    toastr.success('紧急停止执行成功！');
                    setTimeout(() => location.reload(), 2000);
                } else {
                    toastr.error('紧急停止失败: ' + (data.error || '未知错误'));
                }
            })
            .catch(error => {
                hideLoading();
                console.error('紧急停止失败:', error);
                toastr.error('紧急停止失败: ' + error.message);
            });
        }
        
        // 通用API请求函数
        function apiRequest(endpoint, options = {}) {
            const defaultOptions = {
                headers: {
                    'Authorization': 'Bearer ' + window.BITV.API_TOKEN,
                    'Content-Type': 'application/json',
                    'X-CSRF-Token': window.BITV.CSRF_TOKEN
                }
            };
            
            const mergedOptions = {
                ...defaultOptions,
                ...options,
                headers: {
                    ...defaultOptions.headers,
                    ...options.headers
                }
            };
            
            return fetch(window.BITV.API_BASE + endpoint, mergedOptions);
        }
        
        // 格式化数字显示
        function formatNumber(number, decimals = 2) {
            if (typeof number !== 'number' || isNaN(number)) {
                return '---';
            }
            return number.toLocaleString('zh-CN', {
                minimumFractionDigits: decimals,
                maximumFractionDigits: decimals
            });
        }
        
        // 格式化价格显示
        function formatPrice(price, symbol = '') {
            if (typeof price !== 'number' || isNaN(price)) {
                return '---';
            }
            
            let decimals = 2;
            if (symbol.includes('BTC')) {
                decimals = 1;
            } else if (symbol.includes('ETH')) {
                decimals = 2;
            }
            
            return '$' + formatNumber(price, decimals);
        }
        
        // 格式化百分比显示
        function formatPercentage(percentage, showSign = true) {
            if (typeof percentage !== 'number' || isNaN(percentage)) {
                return '---';
            }
            
            const sign = showSign && percentage > 0 ? '+' : '';
            return sign + formatNumber(percentage, 2) + '%';
        }
        
        // 获取风险等级颜色
        function getRiskLevelColor(level) {
            const colors = {
                'safe': 'success',
                'low': 'info',
                'warning': 'warning',
                'danger': 'danger',
                'critical': 'danger'
            };
            return colors[level] || 'secondary';
        }
        
        // 获取风险等级文本
        function getRiskLevelText(level) {
            const texts = {
                'safe': '安全',
                'low': '低风险',
                'warning': '警告',
                'danger': '危险',
                'critical': '极危险'
            };
            return texts[level] || '未知';
        }
        
        // 页面卸载时清理
        window.addEventListener('beforeunload', function() {
            if (systemHealthCheck) {
                clearInterval(systemHealthCheck);
            }
        });
        
        // 全局错误处理
        window.addEventListener('error', function(event) {
            console.error('全局错误:', event.error);
            
            // 记录错误到服务器（可选）
            if (window.BITV && window.BITV.API_BASE) {
                fetch(window.BITV.API_BASE + '/api/log-error', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + window.BITV.API_TOKEN
                    },
                    body: JSON.stringify({
                        message: event.error.message,
                        stack: event.error.stack,
                        url: window.location.href,
                        timestamp: new Date().toISOString()
                    })
                }).catch(() => {
                    // 忽略日志记录失败
                });
            }
        });
        
        // 响应式侧边栏控制
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('show');
        }
        
        // 点击外部区域关闭侧边栏（移动端）
        document.addEventListener('click', function(event) {
            const sidebar = document.getElementById('sidebar');
            const menuBtn = document.querySelector('.mobile-menu-btn');
            
            if (window.innerWidth <= 768 && 
                sidebar.classList.contains('show') && 
                !sidebar.contains(event.target) && 
                !menuBtn.contains(event.target)) {
                sidebar.classList.remove('show');
            }
        });
        
        // 窗口大小改变时处理侧边栏
        window.addEventListener('resize', function() {
            const sidebar = document.getElementById('sidebar');
            if (window.innerWidth > 768) {
                sidebar.classList.remove('show');
            }
        });
    </script>
    
    <!-- 页面特定的JavaScript -->
    <?php if (isset($pageScript)): ?>
        <script><?php echo $pageScript; ?></script>
    <?php endif; ?>
    
</body>
</html>
