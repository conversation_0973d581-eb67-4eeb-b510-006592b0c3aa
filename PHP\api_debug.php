<?php
/**
 * API调试页面 - 实时显示API调用状态
 */

require_once 'config/config.php';
require_once 'includes/ApiClient.php';

// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API调试 - BitV交易系统</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .debug-box { background: #f5f5f5; border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; }
        pre { background: #f0f0f0; padding: 10px; border: 1px solid #ccc; overflow-x: auto; }
        .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px; }
        .status-online { background-color: #4CAF50; }
        .status-offline { background-color: #f44336; }
        .status-warning { background-color: #ff9800; }
    </style>
</head>
<body>

<h1>🔧 API调试页面</h1>

<div class="debug-box">
    <h2>📋 配置信息</h2>
    <p><strong>API基础URL:</strong> <?php echo PYTHON_API_BASE; ?></p>
    <p><strong>WebSocket URL:</strong> <?php echo PYTHON_WS_BASE; ?></p>
    <p><strong>API令牌:</strong> <?php echo substr(PYTHON_API_TOKEN, 0, 10) . '...'; ?></p>
    <p><strong>调试模式:</strong> <?php echo DEBUG_MODE ? '✅ 开启' : '❌ 关闭'; ?></p>
</div>

<?php
// 创建API客户端实例
try {
    $api = ApiClient::getInstance();
    echo "<div class='debug-box success'>";
    echo "<h2>✅ API客户端创建成功</h2>";
    echo "</div>";
} catch (Exception $e) {
    echo "<div class='debug-box error'>";
    echo "<h2>❌ API客户端创建失败</h2>";
    echo "<p>错误: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
    exit;
}

// 测试1: 健康检查
echo "<div class='debug-box'>";
echo "<h2>🏥 健康检查测试</h2>";
try {
    $start_time = microtime(true);
    $healthResult = $api->healthCheck();
    $end_time = microtime(true);
    $response_time = round(($end_time - $start_time) * 1000, 2);
    
    echo "<p><span class='status-indicator " . ($healthResult ? 'status-online' : 'status-offline') . "'></span>";
    echo "<strong>结果:</strong> " . ($healthResult ? "✅ 健康" : "❌ 不健康") . "</p>";
    echo "<p><strong>响应时间:</strong> {$response_time}ms</p>";
    
    // 获取原始响应
    $rawResponse = $api->get('/health');
    echo "<p><strong>原始响应:</strong></p>";
    echo "<pre>" . htmlspecialchars(json_encode($rawResponse, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . "</pre>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ 异常: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>异常类型:</strong> " . get_class($e) . "</p>";
}
echo "</div>";

// 测试2: 系统状态
echo "<div class='debug-box'>";
echo "<h2>📊 系统状态测试</h2>";
try {
    $start_time = microtime(true);
    $statusResult = $api->getSystemStatus();
    $end_time = microtime(true);
    $response_time = round(($end_time - $start_time) * 1000, 2);
    
    echo "<p><span class='status-indicator " . ($statusResult['success'] ? 'status-online' : 'status-offline') . "'></span>";
    echo "<strong>结果:</strong> " . ($statusResult['success'] ? "✅ 成功" : "❌ 失败") . "</p>";
    echo "<p><strong>响应时间:</strong> {$response_time}ms</p>";
    echo "<pre>" . htmlspecialchars(json_encode($statusResult, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . "</pre>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ 异常: " . htmlspecialchars($e->getMessage()) . "</p>";
}
echo "</div>";

// 测试3: 交易状态
echo "<div class='debug-box'>";
echo "<h2>💹 交易状态测试</h2>";
try {
    $start_time = microtime(true);
    $tradingResult = $api->getTradingStatus();
    $end_time = microtime(true);
    $response_time = round(($end_time - $start_time) * 1000, 2);
    
    echo "<p><span class='status-indicator " . ($tradingResult['success'] ? 'status-online' : 'status-offline') . "'></span>";
    echo "<strong>结果:</strong> " . ($tradingResult['success'] ? "✅ 成功" : "❌ 失败") . "</p>";
    echo "<p><strong>响应时间:</strong> {$response_time}ms</p>";
    echo "<pre>" . htmlspecialchars(json_encode($tradingResult, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . "</pre>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ 异常: " . htmlspecialchars($e->getMessage()) . "</p>";
}
echo "</div>";

// 测试4: 直接cURL测试
echo "<div class='debug-box'>";
echo "<h2>🌐 直接cURL测试</h2>";
$ch = curl_init();
curl_setopt_array($ch, [
    CURLOPT_URL => PYTHON_API_BASE . '/health',
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_TIMEOUT => 5,
    CURLOPT_CONNECTTIMEOUT => 3,
    CURLOPT_HTTPHEADER => [
        'Content-Type: application/json',
        'Accept: application/json',
        'Authorization: Bearer ' . PYTHON_API_TOKEN
    ]
]);

$start_time = microtime(true);
$response = curl_exec($ch);
$end_time = microtime(true);
$response_time = round(($end_time - $start_time) * 1000, 2);

$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curl_error = curl_error($ch);
curl_close($ch);

echo "<p><strong>HTTP状态码:</strong> {$http_code}</p>";
echo "<p><strong>响应时间:</strong> {$response_time}ms</p>";
if ($curl_error) {
    echo "<p class='error'><strong>cURL错误:</strong> " . htmlspecialchars($curl_error) . "</p>";
} else {
    echo "<p class='success'><strong>cURL成功</strong></p>";
    echo "<p><strong>响应内容:</strong></p>";
    echo "<pre>" . htmlspecialchars($response) . "</pre>";
}
echo "</div>";
?>

<div class="debug-box">
    <h2>🔄 操作</h2>
    <button onclick="location.reload()" style="background: #2196f3; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">刷新测试</button>
    <a href="index.php" style="background: #4caf50; color: white; text-decoration: none; padding: 10px 20px; border-radius: 5px; margin-left: 10px;">返回首页</a>
</div>

</body>
</html>
