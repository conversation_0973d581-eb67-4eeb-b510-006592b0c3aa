import { defineStore } from 'pinia'
import { ref, reactive } from 'vue'
import api from '@/utils/api'

export const useSystemStore = defineStore('system', () => {
  // 状态
  const apiStatus = ref(false)
  const systemInfo = reactive({
    version: '1.0.0',
    status: 'unknown',
    uptime: 0,
    lastUpdate: null
  })
  
  const exchangeStatus = reactive({
    connected: false,
    name: null,
    balance: 0,
    lastUpdate: null
  })
  
  const loading = ref(false)
  const error = ref(null)
  
  // 动作
  const initializeSystem = async () => {
    loading.value = true
    error.value = null
    
    try {
      await checkApiStatus()
      await getSystemInfo()
      await getExchangeStatus()
    } catch (err) {
      error.value = err.message
      console.error('系统初始化失败:', err)
    } finally {
      loading.value = false
    }
  }
  
  const checkApiStatus = async () => {
    try {
      const response = await api.get('/health')
      apiStatus.value = response.data.status === 'healthy'
      return apiStatus.value
    } catch (err) {
      apiStatus.value = false
      throw new Error('API健康检查失败')
    }
  }
  
  const getSystemInfo = async () => {
    try {
      const response = await api.get('/api/status')
      if (response.data.success) {
        Object.assign(systemInfo, {
          status: response.data.data.system_status,
          lastUpdate: new Date().toISOString()
        })
      }
    } catch (err) {
      console.error('获取系统信息失败:', err)
    }
  }
  
  const getExchangeStatus = async () => {
    try {
      const response = await api.get('/api/exchange/status')
      if (response.data.success) {
        Object.assign(exchangeStatus, {
          connected: response.data.data.connected,
          name: response.data.data.name,
          balance: response.data.data.balance || 0,
          lastUpdate: new Date().toISOString()
        })
      }
    } catch (err) {
      console.error('获取交易所状态失败:', err)
    }
  }
  
  const updateSystemStatus = (data) => {
    if (data.system_status) {
      Object.assign(systemInfo, data.system_status)
    }
    if (data.exchange_status) {
      Object.assign(exchangeStatus, data.exchange_status)
    }
  }
  
  // 定期更新状态
  const startStatusUpdates = () => {
    setInterval(async () => {
      if (apiStatus.value) {
        await getSystemInfo()
        await getExchangeStatus()
      }
    }, 30000) // 每30秒更新一次
  }
  
  return {
    // 状态
    apiStatus,
    systemInfo,
    exchangeStatus,
    loading,
    error,
    
    // 动作
    initializeSystem,
    checkApiStatus,
    getSystemInfo,
    getExchangeStatus,
    updateSystemStatus,
    startStatusUpdates
  }
})
