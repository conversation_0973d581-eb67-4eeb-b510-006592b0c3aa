<?php
/**
 * 首页控制器
 * 
 * @description 处理首页和基础页面请求
 */

require_once 'BaseController.php';

class HomeController extends BaseController {

    /**
     * 首页
     */
    public function index() {
        try {
            // 检查API连接状态
            $apiStatus = false;
            $systemStatus = null;
            $tradingStatus = null;

            try {
                // 强制重新检查API连接，不使用缓存
                $apiStatus = $this->api->healthCheck();

                if ($apiStatus) {
                    // 获取系统状态
                    $systemResponse = $this->api->getSystemStatus();
                    if (isset($systemResponse['success']) && $systemResponse['success']) {
                        $systemStatus = $systemResponse['data'];
                    }

                    // 获取交易状态
                    $tradingResponse = $this->api->getTradingStatus();
                    if (isset($tradingResponse['success']) && $tradingResponse['success']) {
                        $tradingStatus = $tradingResponse['data'];
                    }
                }
            } catch (Exception $e) {
                // API调用失败，记录错误但不影响页面显示
                error_log('API连接失败: ' . $e->getMessage());
                $apiStatus = false;
            }
            
            // 获取用户配置
            $userConfig = $this->getUserConfig();
            
            // 获取最近的交易会话
            $recentSessions = $this->getRecentTradingSessions();
            
            // 获取系统统计
            $statistics = $this->getSystemStatistics();
            
            $this->render('home/index', [
                'title' => '首页 - ' . SYSTEM_NAME,
                'api_status' => $apiStatus,
                'system_status' => $systemStatus,
                'trading_status' => $tradingStatus,
                'user_config' => $userConfig,
                'recent_sessions' => $recentSessions,
                'statistics' => $statistics,
                'supported_exchanges' => $GLOBALS['SUPPORTED_EXCHANGES'],
                'supported_strategies' => $GLOBALS['SUPPORTED_STRATEGIES']
            ]);
            
        } catch (Exception $e) {
            if (DEBUG_MODE) {
                throw $e;
            } else {
                $this->render('error', [
                    'title' => '系统错误',
                    'message' => '系统暂时不可用，请稍后再试'
                ]);
            }
        }
    }

    /**
     * 获取最近的交易会话
     */
    private function getRecentTradingSessions($limit = 5) {
        try {
            $userId = Session::getUserId() ?? 'default';
            
            return $this->db->fetchAll(
                'SELECT session_id, exchange, symbol, strategy, initial_margin, leverage, 
                        status, start_time, end_time, total_pnl, total_add_times
                 FROM trading_sessions 
                 WHERE user_id = ? 
                 ORDER BY start_time DESC 
                 LIMIT ?',
                [$userId, $limit]
            );
        } catch (Exception $e) {
            error_log('获取最近交易会话失败: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 获取系统统计信息
     */
    private function getSystemStatistics() {
        try {
            $userId = Session::getUserId() ?? 'default';
            
            // 总交易会话数
            $totalSessions = $this->db->fetch(
                'SELECT COUNT(*) as count FROM trading_sessions WHERE user_id = ?',
                [$userId]
            )['count'] ?? 0;
            
            // 活跃会话数
            $activeSessions = $this->db->fetch(
                'SELECT COUNT(*) as count FROM trading_sessions WHERE user_id = ? AND status = "active"',
                [$userId]
            )['count'] ?? 0;
            
            // 总盈亏
            $totalPnl = $this->db->fetch(
                'SELECT SUM(total_pnl) as total FROM trading_sessions WHERE user_id = ? AND status != "active"',
                [$userId]
            )['total'] ?? 0;
            
            // 今日交易次数
            $todayTrades = $this->db->fetch(
                'SELECT COUNT(*) as count FROM trading_records 
                 WHERE session_id IN (SELECT session_id FROM trading_sessions WHERE user_id = ?)
                 AND DATE(timestamp) = CURDATE()',
                [$userId]
            )['count'] ?? 0;
            
            // 最常用的交易所
            $favoriteExchange = $this->db->fetch(
                'SELECT exchange, COUNT(*) as count FROM trading_sessions 
                 WHERE user_id = ? 
                 GROUP BY exchange 
                 ORDER BY count DESC 
                 LIMIT 1',
                [$userId]
            );
            
            // 最常用的策略
            $favoriteStrategy = $this->db->fetch(
                'SELECT strategy, COUNT(*) as count FROM trading_sessions 
                 WHERE user_id = ? 
                 GROUP BY strategy 
                 ORDER BY count DESC 
                 LIMIT 1',
                [$userId]
            );
            
            return [
                'total_sessions' => $totalSessions,
                'active_sessions' => $activeSessions,
                'total_pnl' => $totalPnl,
                'today_trades' => $todayTrades,
                'favorite_exchange' => $favoriteExchange['exchange'] ?? null,
                'favorite_strategy' => $favoriteStrategy['strategy'] ?? null
            ];
            
        } catch (Exception $e) {
            error_log('获取系统统计失败: ' . $e->getMessage());
            return [
                'total_sessions' => 0,
                'active_sessions' => 0,
                'total_pnl' => 0,
                'today_trades' => 0,
                'favorite_exchange' => null,
                'favorite_strategy' => null
            ];
        }
    }

    /**
     * 系统信息页面
     */
    public function about() {
        $this->render('home/about', [
            'title' => '关于系统 - ' . SYSTEM_NAME,
            'system_info' => [
                'name' => SYSTEM_NAME,
                'version' => SYSTEM_VERSION,
                'php_version' => PHP_VERSION,
                'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
                'python_api_base' => PYTHON_API_BASE,
                'supported_exchanges' => array_keys($GLOBALS['SUPPORTED_EXCHANGES']),
                'supported_strategies' => array_keys($GLOBALS['SUPPORTED_STRATEGIES'])
            ]
        ]);
    }

    /**
     * 帮助页面
     */
    public function help() {
        $this->render('home/help', [
            'title' => '帮助文档 - ' . SYSTEM_NAME,
            'help_sections' => [
                'getting_started' => '快速开始',
                'configuration' => '配置说明',
                'trading' => '交易操作',
                'monitoring' => '监控功能',
                'troubleshooting' => '故障排除'
            ]
        ]);
    }

    /**
     * API状态检查
     */
    public function apiStatus() {
        try {
            $status = [
                'api_available' => false,
                'system_status' => null,
                'trading_status' => null,
                'last_check' => date('Y-m-d H:i:s')
            ];
            
            // 检查API连接
            if ($this->api->healthCheck()) {
                $status['api_available'] = true;
                
                // 获取系统状态
                try {
                    $systemResponse = $this->api->getSystemStatus();
                    if ($systemResponse['success']) {
                        $status['system_status'] = $systemResponse['data'];
                    }
                } catch (Exception $e) {
                    $status['system_error'] = $e->getMessage();
                }
                
                // 获取交易状态
                try {
                    $tradingResponse = $this->api->getTradingStatus();
                    if ($tradingResponse['success']) {
                        $status['trading_status'] = $tradingResponse['data'];
                    }
                } catch (Exception $e) {
                    $status['trading_error'] = $e->getMessage();
                }
            }
            
            $this->success('状态检查完成', $status);
            
        } catch (Exception $e) {
            $this->error('状态检查失败: ' . $e->getMessage());
        }
    }

    /**
     * 系统健康检查
     */
    public function health() {
        $health = [
            'status' => 'ok',
            'timestamp' => date('c'),
            'checks' => []
        ];
        
        // 检查数据库连接
        try {
            $this->db->fetch('SELECT 1');
            $health['checks']['database'] = 'ok';
        } catch (Exception $e) {
            $health['checks']['database'] = 'error';
            $health['status'] = 'error';
        }
        
        // 检查Python API连接
        try {
            if ($this->api->healthCheck()) {
                $health['checks']['python_api'] = 'ok';
            } else {
                $health['checks']['python_api'] = 'unavailable';
                $health['status'] = 'degraded';
            }
        } catch (Exception $e) {
            $health['checks']['python_api'] = 'error';
            $health['status'] = 'error';
        }
        
        // 检查会话系统
        try {
            Session::getSessionInfo();
            $health['checks']['session'] = 'ok';
        } catch (Exception $e) {
            $health['checks']['session'] = 'error';
            $health['status'] = 'error';
        }
        
        // 检查日志目录
        $logDir = ROOT_PATH . '/logs';
        if (is_dir($logDir) && is_writable($logDir)) {
            $health['checks']['logging'] = 'ok';
        } else {
            $health['checks']['logging'] = 'warning';
            if ($health['status'] === 'ok') {
                $health['status'] = 'warning';
            }
        }
        
        $httpCode = $health['status'] === 'ok' ? 200 : 
                   ($health['status'] === 'warning' ? 200 : 503);
        
        $this->json($health, $httpCode);
    }
}
?>
