<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo htmlspecialchars($GLOBALS['csrf_token']); ?>">
    <title><?php echo htmlspecialchars($GLOBALS['page_title']); ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- 自定义样式 -->
    <link href="<?php echo Router::url('assets/css/style.css'); ?>" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #059669;
            --danger-color: #dc2626;
            --warning-color: #d97706;
            --info-color: #0891b2;
            --dark-color: #1e293b;
            --light-color: #f8fafc;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--light-color);
            color: var(--dark-color);
        }

        .navbar-brand {
            font-weight: 700;
            color: var(--primary-color) !important;
        }

        .sidebar {
            min-height: calc(100vh - 56px);
            background: linear-gradient(135deg, var(--primary-color), #3b82f6);
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }

        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            border-radius: 8px;
            margin: 4px 8px;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.2);
            transform: translateX(5px);
        }

        .main-content {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            margin: 20px 0;
            padding: 30px;
        }

        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.12);
        }

        .btn {
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), #3b82f6);
            border: none;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #1d4ed8, var(--primary-color));
            transform: translateY(-1px);
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-online { background-color: var(--success-color); }
        .status-offline { background-color: var(--danger-color); }
        .status-warning { background-color: var(--warning-color); }

        .trading-card {
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            border-left: 4px solid var(--primary-color);
        }

        .metric-card {
            text-align: center;
            padding: 20px;
        }

        .metric-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .metric-label {
            color: var(--secondary-color);
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .alert {
            border: none;
            border-radius: 10px;
            padding: 15px 20px;
        }

        .table {
            border-radius: 8px;
            overflow: hidden;
        }

        .table thead th {
            background-color: var(--light-color);
            border: none;
            font-weight: 600;
            color: var(--dark-color);
        }

        .footer {
            background-color: var(--dark-color);
            color: white;
            padding: 20px 0;
            margin-top: 50px;
        }

        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                top: 56px;
                left: -100%;
                width: 280px;
                height: calc(100vh - 56px);
                z-index: 1000;
                transition: left 0.3s ease;
            }

            .sidebar.show {
                left: 0;
            }

            .main-content {
                margin: 10px;
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <button class="navbar-toggler d-lg-none" type="button" id="sidebarToggle">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <a class="navbar-brand" href="<?php echo Router::url(); ?>">
                <i class="fas fa-chart-line me-2"></i>
                <?php echo SYSTEM_NAME; ?>
            </a>

            <div class="navbar-nav ms-auto">
                <!-- API状态指示器 -->
                <div class="nav-item d-flex align-items-center me-3">
                    <span class="status-indicator" id="apiStatus"></span>
                    <small class="text-light">API状态</small>
                </div>

                <!-- 用户菜单 -->
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        用户
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="<?php echo Router::url('config'); ?>">
                            <i class="fas fa-cog me-2"></i>系统配置
                        </a></li>
                        <li><a class="dropdown-item" href="<?php echo Router::url('help'); ?>">
                            <i class="fas fa-question-circle me-2"></i>帮助文档
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="<?php echo Router::url('about'); ?>">
                            <i class="fas fa-info-circle me-2"></i>关于系统
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-lg-2 sidebar" id="sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo Router::url(); ?>">
                                <i class="fas fa-home me-2"></i>首页
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo Router::url('dashboard'); ?>">
                                <i class="fas fa-tachometer-alt me-2"></i>仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo Router::url('config'); ?>">
                                <i class="fas fa-cog me-2"></i>交易配置
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo Router::url('trading'); ?>">
                                <i class="fas fa-play-circle me-2"></i>交易控制
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo Router::url('monitoring'); ?>">
                                <i class="fas fa-chart-area me-2"></i>实时监控
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo Router::url('history'); ?>">
                                <i class="fas fa-history me-2"></i>交易历史
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区域 -->
            <main class="col-lg-10 ms-sm-auto px-md-4">
                <div class="main-content">
                    <?php echo $GLOBALS['content']; ?>
                </div>
            </main>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2024 <?php echo SYSTEM_NAME; ?> v<?php echo SYSTEM_VERSION; ?></p>
                </div>
                <div class="col-md-6 text-end">
                    <small class="text-muted">
                        基于Python异步架构的智能交易系统
                    </small>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 自定义JavaScript -->
    <script>
        // CSRF令牌设置
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        
        // 设置AJAX默认头部
        if (typeof $ !== 'undefined') {
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': csrfToken
                }
            });
        }

        // 侧边栏切换
        document.getElementById('sidebarToggle')?.addEventListener('click', function() {
            document.getElementById('sidebar').classList.toggle('show');
        });

        // API状态检查
        function checkApiStatus() {
            fetch('<?php echo Router::url("api/status"); ?>')
                .then(response => response.json())
                .then(data => {
                    const indicator = document.getElementById('apiStatus');
                    if (data.success && data.data.api_available) {
                        indicator.className = 'status-indicator status-online';
                    } else {
                        indicator.className = 'status-indicator status-offline';
                    }
                })
                .catch(() => {
                    document.getElementById('apiStatus').className = 'status-indicator status-offline';
                });
        }

        // 页面加载完成后检查API状态
        document.addEventListener('DOMContentLoaded', function() {
            checkApiStatus();
            // 每30秒检查一次API状态
            setInterval(checkApiStatus, 30000);
        });

        // 通用AJAX错误处理
        function handleAjaxError(xhr, status, error) {
            let message = '操作失败';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                message = xhr.responseJSON.message;
            } else if (xhr.status === 0) {
                message = '网络连接失败';
            } else if (xhr.status === 404) {
                message = '请求的资源不存在';
            } else if (xhr.status === 500) {
                message = '服务器内部错误';
            }
            
            showAlert('danger', message);
        }

        // 显示警告消息
        function showAlert(type, message, container = 'body') {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            if (container === 'body') {
                document.body.insertAdjacentHTML('afterbegin', alertHtml);
            } else {
                document.querySelector(container).insertAdjacentHTML('afterbegin', alertHtml);
            }
            
            // 5秒后自动关闭
            setTimeout(() => {
                const alert = document.querySelector('.alert');
                if (alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }
            }, 5000);
        }

        // 格式化数字
        function formatNumber(num, decimals = 4) {
            return parseFloat(num).toFixed(decimals);
        }

        // 格式化百分比
        function formatPercentage(num, decimals = 2) {
            return (parseFloat(num) * 100).toFixed(decimals) + '%';
        }

        // 格式化时间
        function formatTime(timestamp) {
            return new Date(timestamp * 1000).toLocaleString('zh-CN');
        }
    </script>

    <!-- 页面特定的JavaScript -->
    <?php if (isset($scripts)): ?>
        <?php foreach ($scripts as $script): ?>
            <script src="<?php echo Router::url($script); ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>
</body>
</html>
