#!/usr/bin/env python3
"""
BitV MACD智能加仓交易系统 - 实盘交易版本
完全移除模拟功能，集成真实交易所API
专业级量化交易系统
"""

import asyncio
import uvicorn
from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from contextlib import asynccontextmanager
import logging
import os
import json
from datetime import datetime, timedelta
import time
from typing import Dict, Any, Optional, List
import random
import uuid
import sqlite3
from dataclasses import dataclass, asdict
import ccxt.async_support as ccxt
import hashlib
import hmac
import base64
from cryptography.fernet import Fernet

# 配置日志系统 - 修复Windows编码问题
import sys
import io

# 设置标准输出编码为UTF-8
if sys.platform == 'win32':
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)s | %(name)s | %(message)s',
    handlers=[
        logging.FileHandler('logs/real_trading.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# 创建必要的目录
os.makedirs('logs', exist_ok=True)
os.makedirs('data', exist_ok=True)
os.makedirs('config', exist_ok=True)

# 安全管理器
class SecurityManager:
    def __init__(self):
        self.encryption_key = self._get_or_create_key()
        self.cipher_suite = Fernet(self.encryption_key)
        
    def _get_or_create_key(self):
        """获取或创建加密密钥"""
        key_file = 'config/encryption.key'
        if os.path.exists(key_file):
            with open(key_file, 'rb') as f:
                return f.read()
        else:
            key = Fernet.generate_key()
            with open(key_file, 'wb') as f:
                f.write(key)
            return key
    
    def encrypt_api_key(self, api_key: str) -> str:
        """加密API密钥"""
        return self.cipher_suite.encrypt(api_key.encode()).decode()
    
    def decrypt_api_key(self, encrypted_key: str) -> str:
        """解密API密钥"""
        return self.cipher_suite.decrypt(encrypted_key.encode()).decode()
    
    def hash_password(self, password: str) -> str:
        """哈希密码"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def verify_password(self, password: str, hashed: str) -> bool:
        """验证密码"""
        return self.hash_password(password) == hashed

# 操作日志记录器
class AuditLogger:
    def __init__(self, db_path: str = "data/audit.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """初始化审计日志数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS audit_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                user_id TEXT,
                action TEXT NOT NULL,
                details TEXT,
                ip_address TEXT,
                success BOOLEAN NOT NULL,
                error_message TEXT,
                session_id TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def log_action(self, action: str, details: Dict = None, user_id: str = None,
                  success: bool = True, error_message: str = None, 
                  ip_address: str = None, session_id: str = None):
        """记录操作日志"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO audit_logs 
            (timestamp, user_id, action, details, ip_address, success, error_message, session_id)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            datetime.now().isoformat(),
            user_id,
            action,
            json.dumps(details) if details else None,
            ip_address,
            success,
            error_message,
            session_id
        ))
        
        conn.commit()
        conn.close()
        
        # 同时记录到日志文件
        log_level = logging.INFO if success else logging.ERROR
        logger.log(log_level, f"AUDIT: {action} | User: {user_id} | Success: {success}")

# 真实交易所连接器基类
class RealExchangeConnector:
    """真实交易所连接器基类"""
    
    def __init__(self, exchange_name: str, api_key: str, api_secret: str, 
                 passphrase: str = None, sandbox: bool = True):
        self.exchange_name = exchange_name
        self.api_key = api_key
        self.api_secret = api_secret
        self.passphrase = passphrase
        self.sandbox = sandbox
        self.exchange = None
        self.connected = False
        
    async def connect(self) -> bool:
        """连接到交易所"""
        try:
            if self.exchange_name.lower() == 'okx':
                self.exchange = ccxt.okx({
                    'apiKey': self.api_key,
                    'secret': self.api_secret,
                    'password': self.passphrase,
                    'sandbox': self.sandbox,
                    'enableRateLimit': True,
                    'options': {'defaultType': 'swap'}
                })
            elif self.exchange_name.lower() == 'gateio':
                self.exchange = ccxt.gateio({
                    'apiKey': self.api_key,
                    'secret': self.api_secret,
                    'sandbox': self.sandbox,
                    'enableRateLimit': True,
                    'options': {'defaultType': 'swap'}
                })
            else:
                raise ValueError(f"不支持的交易所: {self.exchange_name}")
            
            # 测试连接
            await self.exchange.load_markets()
            balance = await self.exchange.fetch_balance()
            
            self.connected = True
            logger.info(f"✅ {self.exchange_name}交易所连接成功 (沙盒: {self.sandbox})")
            return True
            
        except Exception as e:
            logger.error(f"❌ {self.exchange_name}交易所连接失败: {e}")
            self.connected = False
            return False
    
    async def disconnect(self):
        """断开连接"""
        if self.exchange:
            await self.exchange.close()
            self.connected = False
            logger.info(f"🔌 {self.exchange_name}交易所连接已断开")
    
    async def get_real_balance(self) -> Dict:
        """获取真实账户余额"""
        if not self.connected:
            raise Exception("交易所未连接")
        
        balance = await self.exchange.fetch_balance()
        return balance
    
    async def get_real_positions(self) -> List[Dict]:
        """获取真实持仓"""
        if not self.connected:
            raise Exception("交易所未连接")
        
        positions = await self.exchange.fetch_positions()
        # 过滤掉零持仓
        return [pos for pos in positions if pos.get('contracts', 0) != 0]
    
    async def get_real_ticker(self, symbol: str) -> Dict:
        """获取真实行情数据"""
        if not self.connected:
            raise Exception("交易所未连接")
        
        return await self.exchange.fetch_ticker(symbol)
    
    async def get_real_klines(self, symbol: str, timeframe: str, limit: int = 100) -> List:
        """获取真实K线数据"""
        if not self.connected:
            raise Exception("交易所未连接")
        
        return await self.exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
    
    async def create_real_order(self, symbol: str, order_type: str, side: str, 
                              amount: float, price: float = None, params: Dict = None) -> Dict:
        """创建真实订单"""
        if not self.connected:
            raise Exception("交易所未连接")
        
        order = await self.exchange.create_order(
            symbol=symbol,
            type=order_type,
            side=side,
            amount=amount,
            price=price,
            params=params or {}
        )
        
        logger.info(f"📈 真实订单创建: {symbol} {side} {amount} @ {price or 'market'}")
        return order
    
    async def cancel_real_order(self, order_id: str, symbol: str) -> Dict:
        """撤销真实订单"""
        if not self.connected:
            raise Exception("交易所未连接")
        
        result = await self.exchange.cancel_order(order_id, symbol)
        logger.info(f"🚫 真实订单撤销: {order_id}")
        return result
    
    def format_symbol(self, symbol: str) -> str:
        """格式化交易对符号"""
        if self.exchange_name.lower() == 'okx':
            # OKX格式: BTC-USDT-SWAP -> BTC/USDT:USDT
            if '-SWAP' in symbol:
                base_quote = symbol.replace('-SWAP', '')
                if '-' in base_quote:
                    base, quote = base_quote.split('-')
                    return f"{base}/{quote}:{quote}"
        elif self.exchange_name.lower() == 'gateio':
            # Gate.io格式: BTC-USDT-SWAP -> BTC_USDT
            if '-SWAP' in symbol:
                return symbol.replace('-SWAP', '').replace('-', '_')
        
        return symbol

# 真实市场数据管理器
class RealMarketDataManager:
    """真实市场数据管理器 - 替代价格模拟器"""
    
    def __init__(self, exchange_connector: RealExchangeConnector):
        self.exchange = exchange_connector
        self.price_data = {}
        self.kline_data = {}
        self.running = False
        self.websocket_connections = []
        self.update_interval = 5  # 5秒更新一次
        
    async def start(self):
        """启动真实市场数据获取"""
        if not self.exchange.connected:
            raise Exception("交易所未连接，无法启动市场数据获取")
        
        self.running = True
        logger.info("🎯 真实市场数据获取器启动")
        
        # 支持的交易对
        symbols = [
            "BTC-USDT-SWAP",
            "ETH-USDT-SWAP", 
            "LTC-USDT-SWAP",
            "XRP-USDT-SWAP",
            "ADA-USDT-SWAP"
        ]
        
        while self.running:
            try:
                for symbol in symbols:
                    formatted_symbol = self.exchange.format_symbol(symbol)
                    
                    # 获取真实行情数据
                    ticker = await self.exchange.get_real_ticker(formatted_symbol)
                    
                    # 获取真实K线数据
                    klines = await self.exchange.get_real_klines(formatted_symbol, '1m', 100)
                    
                    # 格式化价格数据
                    price_data = {
                        "symbol": symbol,
                        "price": ticker.get('last', 0),
                        "bid": ticker.get('bid', 0),
                        "ask": ticker.get('ask', 0),
                        "change_24h": ticker.get('change', 0),
                        "percentage_24h": ticker.get('percentage', 0),
                        "volume_24h": ticker.get('baseVolume', 0),
                        "high_24h": ticker.get('high', 0),
                        "low_24h": ticker.get('low', 0),
                        "timestamp": datetime.now().isoformat(),
                        "exchange": self.exchange.exchange_name
                    }
                    
                    self.price_data[symbol] = price_data
                    self.kline_data[symbol] = klines
                    
                    # 广播到WebSocket连接
                    await self.broadcast_real_data(price_data)
                
                await asyncio.sleep(self.update_interval)
                
            except Exception as e:
                logger.error(f"❌ 获取真实市场数据失败: {e}")
                await asyncio.sleep(10)  # 错误时等待更长时间
    
    async def broadcast_real_data(self, price_data: Dict):
        """广播真实数据到WebSocket连接"""
        if self.websocket_connections:
            message = json.dumps({
                "type": "real_price_update",
                "data": price_data
            })
            
            # 移除断开的连接
            active_connections = []
            for websocket in self.websocket_connections:
                try:
                    await websocket.send_text(message)
                    active_connections.append(websocket)
                except:
                    pass
            
            self.websocket_connections = active_connections
    
    def get_current_price(self, symbol: str) -> float:
        """获取当前真实价格"""
        price_data = self.price_data.get(symbol, {})
        return price_data.get('price', 0)
    
    def get_price_history(self, symbol: str, limit: int = 100) -> List[float]:
        """获取价格历史（从K线数据提取）"""
        klines = self.kline_data.get(symbol, [])
        # 提取收盘价
        return [kline[4] for kline in klines[-limit:]] if klines else []
    
    def stop(self):
        """停止真实市场数据获取"""
        self.running = False
        logger.info("🛑 真实市场数据获取器已停止")

# MACD计算器（保留原有逻辑）
class MACDCalculator:
    def __init__(self, fast=12, slow=26, signal=9):
        self.fast = fast
        self.slow = slow
        self.signal = signal
        self.price_history = {}

    def calculate_ema(self, prices: List[float], period: int) -> float:
        """计算指数移动平均线"""
        if len(prices) < period:
            return sum(prices) / len(prices)

        multiplier = 2 / (period + 1)
        ema = prices[0]

        for price in prices[1:]:
            ema = (price * multiplier) + (ema * (1 - multiplier))

        return ema

    def calculate(self, symbol: str, prices: List[float]):
        """计算MACD指标"""
        if len(prices) < self.slow:
            return None

        # 计算快线和慢线EMA
        fast_ema = self.calculate_ema(prices[-self.fast:], self.fast)
        slow_ema = self.calculate_ema(prices[-self.slow:], self.slow)

        # MACD线 = 快线EMA - 慢线EMA
        macd_line = fast_ema - slow_ema

        # 更新历史MACD值
        if symbol not in self.price_history:
            self.price_history[symbol] = []

        self.price_history[symbol].append(macd_line)

        # 保持历史数据在合理范围内
        if len(self.price_history[symbol]) > self.signal * 2:
            self.price_history[symbol] = self.price_history[symbol][-self.signal * 2:]

        # 计算信号线（MACD的EMA）
        signal_line = self.calculate_ema(self.price_history[symbol], self.signal)

        # 柱状图 = MACD线 - 信号线
        histogram = macd_line - signal_line

        return {
            "symbol": symbol,
            "macd": round(macd_line, 6),
            "signal": round(signal_line, 6),
            "histogram": round(histogram, 6),
            "fast_ema": round(fast_ema, 2),
            "slow_ema": round(slow_ema, 2),
            "timestamp": datetime.now().isoformat()
        }

# 风险管理器（保留并增强）
class RealRiskManager:
    def __init__(self, exchange_connector: RealExchangeConnector):
        self.exchange = exchange_connector
        self.max_leverage = 100
        self.min_margin_ratio = 0.1  # 10%
        self.liquidation_threshold = 0.05  # 5%
        self.max_position_size_ratio = 0.8  # 最大仓位占总资金80%

    async def calculate_real_liquidation_price(self, symbol: str, side: str,
                                             entry_price: float, leverage: int) -> float:
        """计算真实强平价格"""
        try:
            # 获取真实交易手续费
            fees = await self.get_real_trading_fees(symbol)
            maker_fee = fees.get('maker', 0.001)

            margin_ratio = 1 / leverage

            if side.lower() == 'long':
                liquidation_price = entry_price * (1 - margin_ratio + maker_fee)
            else:
                liquidation_price = entry_price * (1 + margin_ratio + maker_fee)

            return round(liquidation_price, 6)

        except Exception as e:
            logger.error(f"计算真实强平价格失败: {e}")
            return 0.0

    async def get_real_trading_fees(self, symbol: str) -> Dict:
        """获取真实交易手续费"""
        try:
            if not self.exchange.connected:
                raise Exception("交易所未连接")

            formatted_symbol = self.exchange.format_symbol(symbol)
            fees = await self.exchange.exchange.fetch_trading_fees()

            symbol_fees = fees.get(formatted_symbol, {})

            return {
                'maker': symbol_fees.get('maker', 0.0002),
                'taker': symbol_fees.get('taker', 0.0005),
                'symbol': symbol
            }

        except Exception as e:
            logger.error(f"获取真实交易手续费失败: {e}")
            # 返回保守的默认费率
            return {'maker': 0.001, 'taker': 0.002, 'symbol': symbol}

    async def check_real_margin_requirements(self, symbol: str, side: str,
                                           amount: float, leverage: int) -> Dict:
        """检查真实保证金要求"""
        try:
            # 获取真实账户余额
            balance = await self.exchange.get_real_balance()
            usdt_balance = balance.get('USDT', {}).get('free', 0)

            # 获取当前真实价格
            ticker = await self.exchange.get_real_ticker(self.exchange.format_symbol(symbol))
            current_price = ticker.get('last', 0)

            # 计算所需保证金
            notional_value = amount * current_price
            required_margin = notional_value / leverage

            # 检查是否超过最大仓位限制
            max_allowed_margin = usdt_balance * self.max_position_size_ratio

            return {
                'required_margin': required_margin,
                'available_balance': usdt_balance,
                'sufficient': usdt_balance >= required_margin,
                'within_limit': required_margin <= max_allowed_margin,
                'notional_value': notional_value,
                'current_price': current_price,
                'max_allowed_margin': max_allowed_margin
            }

        except Exception as e:
            logger.error(f"检查真实保证金要求失败: {e}")
            return {'sufficient': False, 'error': str(e)}

    def calculate_unrealized_pnl(self, entry_price: float, current_price: float,
                                size: float, side: str) -> float:
        """计算未实现盈亏"""
        if side.lower() == 'long':
            pnl = (current_price - entry_price) * size
        else:
            pnl = (entry_price - current_price) * size

        return round(pnl, 6)

    def get_risk_level(self, current_price: float, liquidation_price: float, side: str) -> str:
        """获取风险等级"""
        if liquidation_price <= 0:
            return "unknown"

        if side.lower() == 'long':
            distance_ratio = (current_price - liquidation_price) / current_price
        else:
            distance_ratio = (liquidation_price - current_price) / current_price

        if distance_ratio > 0.3:
            return "safe"
        elif distance_ratio > 0.2:
            return "low"
        elif distance_ratio > 0.1:
            return "warning"
        elif distance_ratio > 0.05:
            return "danger"
        else:
            return "critical"

    def should_add_position(self, current_pnl: float, initial_margin: float,
                          add_times: int, max_add_times: int) -> bool:
        """判断是否应该加仓"""
        if add_times >= max_add_times:
            return False

        loss_ratio = abs(current_pnl) / initial_margin

        # 更保守的加仓阈值
        thresholds = [0.15, 0.25, 0.35, 0.45, 0.55]  # 15%, 25%, 35%, 45%, 55%

        if add_times < len(thresholds):
            return current_pnl < 0 and loss_ratio >= thresholds[add_times]

        return False

    async def emergency_risk_check(self, symbol: str) -> Dict:
        """紧急风险检查"""
        try:
            # 获取真实持仓
            positions = await self.exchange.get_real_positions()

            risk_alerts = []
            total_unrealized_pnl = 0

            for position in positions:
                if position.get('symbol') == symbol:
                    unrealized_pnl = position.get('unrealizedPnl', 0)
                    liquidation_price = position.get('liquidationPrice', 0)
                    mark_price = position.get('markPrice', 0)
                    side = position.get('side', '')

                    total_unrealized_pnl += unrealized_pnl

                    # 检查风险等级
                    risk_level = self.get_risk_level(mark_price, liquidation_price, side)

                    if risk_level in ['danger', 'critical']:
                        risk_alerts.append({
                            'symbol': symbol,
                            'risk_level': risk_level,
                            'unrealized_pnl': unrealized_pnl,
                            'liquidation_price': liquidation_price,
                            'mark_price': mark_price,
                            'side': side
                        })

            return {
                'total_unrealized_pnl': total_unrealized_pnl,
                'risk_alerts': risk_alerts,
                'high_risk': len(risk_alerts) > 0,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"紧急风险检查失败: {e}")
            return {'high_risk': True, 'error': str(e)}

# 实盘交易引擎
class RealTradingEngine:
    """实盘交易引擎 - 完全真实交易功能"""

    def __init__(self, exchange_connector: RealExchangeConnector,
                 market_data_manager: RealMarketDataManager,
                 risk_manager: RealRiskManager,
                 audit_logger: AuditLogger):
        self.exchange = exchange_connector
        self.market_data = market_data_manager
        self.risk_manager = risk_manager
        self.audit_logger = audit_logger
        self.macd_calculator = MACDCalculator()

        # 交易状态
        self.active_sessions = {}
        self.active_positions = {}
        self.monitoring_tasks = {}
        self.emergency_stop = False

    async def start_real_trading_session(self, config: Dict[str, Any], user_id: str = None) -> Dict:
        """启动真实交易会话"""
        try:
            # 验证交易所连接
            if not self.exchange.connected:
                raise Exception("交易所未连接，无法启动交易")

            session_id = f"real_session_{int(time.time())}_{uuid.uuid4().hex[:8]}"

            # 验证配置参数
            symbol = config.get("symbol", "BTC-USDT-SWAP")
            leverage = config.get("leverage", 10)
            initial_margin = config.get("initial_margin", 100.0)
            max_add_times = config.get("max_add_times", 3)

            # 检查保证金要求
            margin_check = await self.risk_manager.check_real_margin_requirements(
                symbol, "long", initial_margin * leverage / 50000, leverage  # 假设BTC价格50000
            )

            if not margin_check.get('sufficient', False):
                raise Exception(f"保证金不足: 需要 {margin_check.get('required_margin', 0):.2f} USDT")

            if not margin_check.get('within_limit', False):
                raise Exception("超过最大仓位限制")

            # 设置杠杆
            try:
                formatted_symbol = self.exchange.format_symbol(symbol)
                await self.exchange.exchange.set_leverage(leverage, formatted_symbol)
                logger.info(f"⚙️ 杠杆设置成功: {symbol} {leverage}x")
            except Exception as e:
                logger.warning(f"杠杆设置失败，使用默认杠杆: {e}")

            # 创建交易会话
            session_data = {
                "session_id": session_id,
                "exchange": self.exchange.exchange_name,
                "symbol": symbol,
                "strategy": config.get("strategy", "macd"),
                "leverage": leverage,
                "initial_margin": initial_margin,
                "max_add_times": max_add_times,
                "status": "active",
                "start_time": datetime.now().isoformat(),
                "total_pnl": 0.0,
                "add_times": 0,
                "positions": [],
                "user_id": user_id,
                "real_trading": True
            }

            # 保存会话
            self.active_sessions[session_id] = session_data

            # 启动监控任务
            self.monitoring_tasks[session_id] = asyncio.create_task(
                self.monitor_real_session(session_id)
            )

            # 记录审计日志
            self.audit_logger.log_action(
                action="START_REAL_TRADING_SESSION",
                details={
                    "session_id": session_id,
                    "symbol": symbol,
                    "leverage": leverage,
                    "initial_margin": initial_margin
                },
                user_id=user_id,
                success=True,
                session_id=session_id
            )

            logger.info(f"🚀 真实交易会话启动: {session_id}")
            logger.info(f"   交易所: {self.exchange.exchange_name}")
            logger.info(f"   交易对: {symbol}")
            logger.info(f"   杠杆: {leverage}x")
            logger.info(f"   初始保证金: {initial_margin} USDT")

            return session_data

        except Exception as e:
            # 记录失败日志
            self.audit_logger.log_action(
                action="START_REAL_TRADING_SESSION",
                details=config,
                user_id=user_id,
                success=False,
                error_message=str(e)
            )
            logger.error(f"❌ 启动真实交易会话失败: {e}")
            raise

    async def stop_real_trading_session(self, session_id: str, user_id: str = None,
                                       emergency: bool = False) -> Dict:
        """停止真实交易会话"""
        try:
            if session_id not in self.active_sessions:
                raise ValueError(f"会话 {session_id} 不存在或已停止")

            session = self.active_sessions[session_id]

            # 停止监控任务
            if session_id in self.monitoring_tasks:
                self.monitoring_tasks[session_id].cancel()
                del self.monitoring_tasks[session_id]

            # 平仓所有持仓
            close_results = await self.close_all_real_positions(session_id, emergency)

            # 更新会话状态
            session["status"] = "stopped"
            session["end_time"] = datetime.now().isoformat()
            session["stop_reason"] = "emergency" if emergency else "manual"

            # 记录审计日志
            self.audit_logger.log_action(
                action="STOP_REAL_TRADING_SESSION",
                details={
                    "session_id": session_id,
                    "emergency": emergency,
                    "close_results": close_results
                },
                user_id=user_id,
                success=True,
                session_id=session_id
            )

            # 从活跃会话中移除
            del self.active_sessions[session_id]

            logger.info(f"🛑 真实交易会话停止: {session_id} (紧急: {emergency})")

            return {
                "session_id": session_id,
                "status": "stopped",
                "end_time": session["end_time"],
                "total_pnl": session["total_pnl"],
                "close_results": close_results
            }

        except Exception as e:
            # 记录失败日志
            self.audit_logger.log_action(
                action="STOP_REAL_TRADING_SESSION",
                details={"session_id": session_id, "emergency": emergency},
                user_id=user_id,
                success=False,
                error_message=str(e),
                session_id=session_id
            )
            logger.error(f"❌ 停止真实交易会话失败: {e}")
            raise

    async def monitor_real_session(self, session_id: str):
        """监控真实交易会话"""
        logger.info(f"📊 开始监控真实交易会话: {session_id}")

        try:
            while (session_id in self.active_sessions and
                   self.active_sessions[session_id]["status"] == "active" and
                   not self.emergency_stop):

                session = self.active_sessions[session_id]
                symbol = session["symbol"]

                # 获取真实价格数据
                current_price = self.market_data.get_current_price(symbol)
                if current_price <= 0:
                    await asyncio.sleep(5)
                    continue

                # 获取真实价格历史用于MACD计算
                price_history = self.market_data.get_price_history(symbol, 50)

                # 计算MACD指标
                macd_data = self.macd_calculator.calculate(symbol, price_history)

                if macd_data:
                    # 检查交易信号
                    await self.check_real_trading_signals(session_id, current_price, macd_data)

                # 更新真实持仓信息
                await self.update_real_positions(session_id, current_price)

                # 执行风险管理检查
                await self.check_real_risk_management(session_id, current_price)

                await asyncio.sleep(10)  # 每10秒检查一次

        except asyncio.CancelledError:
            logger.info(f"📊 真实交易会话监控已停止: {session_id}")
        except Exception as e:
            logger.error(f"❌ 监控真实交易会话错误 {session_id}: {e}")

            # 发生错误时紧急停止会话
            try:
                await self.stop_real_trading_session(session_id, emergency=True)
            except:
                pass

    async def check_real_trading_signals(self, session_id: str, current_price: float, macd_data: Dict):
        """检查真实交易信号"""
        session = self.active_sessions[session_id]
        symbol = session["symbol"]

        # MACD金叉信号（买入）
        if (macd_data["macd"] > macd_data["signal"] and
            macd_data["histogram"] > 0 and
            session_id not in self.active_positions):

            await self.open_real_position(session_id, "long", current_price)

        # MACD死叉信号（卖出）
        elif (macd_data["macd"] < macd_data["signal"] and
              macd_data["histogram"] < 0 and
              session_id in self.active_positions):

            await self.close_real_position(session_id, current_price)

    async def open_real_position(self, session_id: str, side: str, entry_price: float):
        """开启真实持仓"""
        try:
            session = self.active_sessions[session_id]
            symbol = session["symbol"]
            leverage = session["leverage"]
            initial_margin = session["initial_margin"]

            # 计算仓位大小
            position_size = (initial_margin * leverage) / entry_price

            # 最终的保证金检查
            margin_check = await self.risk_manager.check_real_margin_requirements(
                symbol, side, position_size, leverage
            )

            if not margin_check.get('sufficient', False):
                logger.warning(f"⚠️ 保证金不足，跳过开仓: {session_id}")
                return

            # 创建真实订单
            formatted_symbol = self.exchange.format_symbol(symbol)
            order = await self.exchange.create_real_order(
                symbol=formatted_symbol,
                order_type="market",
                side="buy" if side == "long" else "sell",
                amount=position_size,
                params={"leverage": leverage}
            )

            # 计算强平价格
            liquidation_price = await self.risk_manager.calculate_real_liquidation_price(
                symbol, side, entry_price, leverage
            )

            # 创建持仓记录
            position = {
                "position_id": f"real_pos_{session_id}_{int(time.time())}",
                "session_id": session_id,
                "symbol": symbol,
                "side": side,
                "size": position_size,
                "entry_price": entry_price,
                "current_price": entry_price,
                "unrealized_pnl": 0.0,
                "margin": initial_margin,
                "leverage": leverage,
                "liquidation_price": liquidation_price,
                "order_id": order.get('id'),
                "timestamp": datetime.now().isoformat(),
                "real_trading": True
            }

            self.active_positions[session_id] = position
            session["positions"].append(position)

            # 记录审计日志
            self.audit_logger.log_action(
                action="OPEN_REAL_POSITION",
                details={
                    "session_id": session_id,
                    "symbol": symbol,
                    "side": side,
                    "size": position_size,
                    "entry_price": entry_price,
                    "order_id": order.get('id')
                },
                user_id=session.get('user_id'),
                success=True,
                session_id=session_id
            )

            logger.info(f"📈 真实开仓成功: {session_id} | {side.upper()} | {position_size:.6f} | ${entry_price}")

        except Exception as e:
            # 记录失败日志
            self.audit_logger.log_action(
                action="OPEN_REAL_POSITION",
                details={
                    "session_id": session_id,
                    "side": side,
                    "entry_price": entry_price
                },
                user_id=session.get('user_id'),
                success=False,
                error_message=str(e),
                session_id=session_id
            )
            logger.error(f"❌ 真实开仓失败 {session_id}: {e}")

    async def close_real_position(self, session_id: str, exit_price: float):
        """平仓真实持仓"""
        try:
            if session_id not in self.active_positions:
                return

            position = self.active_positions[session_id]
            session = self.active_sessions[session_id]
            symbol = position["symbol"]

            # 创建平仓订单
            formatted_symbol = self.exchange.format_symbol(symbol)
            close_side = "sell" if position["side"] == "long" else "buy"

            order = await self.exchange.create_real_order(
                symbol=formatted_symbol,
                order_type="market",
                side=close_side,
                amount=position["size"],
                params={"reduceOnly": True}
            )

            # 计算盈亏
            pnl = self.risk_manager.calculate_unrealized_pnl(
                position["entry_price"], exit_price, position["size"], position["side"]
            )

            # 获取真实手续费
            fees = await self.risk_manager.get_real_trading_fees(symbol)
            fee = position["size"] * exit_price * fees.get('taker', 0.001)
            net_pnl = pnl - fee

            # 更新会话盈亏
            session["total_pnl"] += net_pnl

            # 记录审计日志
            self.audit_logger.log_action(
                action="CLOSE_REAL_POSITION",
                details={
                    "session_id": session_id,
                    "symbol": symbol,
                    "exit_price": exit_price,
                    "pnl": pnl,
                    "fee": fee,
                    "net_pnl": net_pnl,
                    "order_id": order.get('id')
                },
                user_id=session.get('user_id'),
                success=True,
                session_id=session_id
            )

            logger.info(f"📉 真实平仓成功: {session_id} | PnL: ${pnl:.2f} | Fee: ${fee:.2f} | Net: ${net_pnl:.2f}")

            # 移除持仓
            del self.active_positions[session_id]

        except Exception as e:
            # 记录失败日志
            self.audit_logger.log_action(
                action="CLOSE_REAL_POSITION",
                details={
                    "session_id": session_id,
                    "exit_price": exit_price
                },
                user_id=session.get('user_id'),
                success=False,
                error_message=str(e),
                session_id=session_id
            )
            logger.error(f"❌ 真实平仓失败 {session_id}: {e}")

    async def update_real_positions(self, session_id: str, current_price: float):
        """更新真实持仓信息"""
        if session_id not in self.active_positions:
            return

        try:
            position = self.active_positions[session_id]
            position["current_price"] = current_price

            # 计算未实现盈亏
            position["unrealized_pnl"] = self.risk_manager.calculate_unrealized_pnl(
                position["entry_price"], current_price, position["size"], position["side"]
            )

            # 获取真实持仓数据进行校验
            real_positions = await self.exchange.get_real_positions()
            for real_pos in real_positions:
                if real_pos.get('symbol') == self.exchange.format_symbol(position["symbol"]):
                    # 更新真实数据
                    position["real_size"] = real_pos.get('contracts', 0)
                    position["real_unrealized_pnl"] = real_pos.get('unrealizedPnl', 0)
                    position["real_liquidation_price"] = real_pos.get('liquidationPrice', 0)
                    break

        except Exception as e:
            logger.error(f"❌ 更新真实持仓信息失败 {session_id}: {e}")

    async def check_real_risk_management(self, session_id: str, current_price: float):
        """检查真实风险管理"""
        if session_id not in self.active_positions:
            return

        try:
            position = self.active_positions[session_id]
            session = self.active_sessions[session_id]
            symbol = position["symbol"]

            # 执行紧急风险检查
            risk_check = await self.risk_manager.emergency_risk_check(symbol)

            if risk_check.get('high_risk', False):
                logger.warning(f"⚠️ 高风险警告: {session_id}")

                # 检查是否需要紧急平仓
                for alert in risk_check.get('risk_alerts', []):
                    if alert.get('risk_level') == 'critical':
                        logger.critical(f"🚨 紧急风险！执行强制平仓: {session_id}")
                        await self.close_real_position(session_id, current_price)
                        return

            # 检查是否需要加仓
            if self.risk_manager.should_add_position(
                position["unrealized_pnl"], session["initial_margin"],
                session["add_times"], session["max_add_times"]
            ):
                await self.add_real_position(session_id, current_price)

        except Exception as e:
            logger.error(f"❌ 真实风险管理检查失败 {session_id}: {e}")

    async def close_all_real_positions(self, session_id: str, emergency: bool = False) -> List[Dict]:
        """平仓所有真实持仓"""
        close_results = []

        try:
            if session_id in self.active_positions:
                current_price = self.market_data.get_current_price(
                    self.active_positions[session_id]["symbol"]
                )

                if current_price > 0:
                    await self.close_real_position(session_id, current_price)
                    close_results.append({
                        "session_id": session_id,
                        "status": "closed",
                        "price": current_price,
                        "emergency": emergency
                    })

            # 同时检查交易所的真实持仓
            real_positions = await self.exchange.get_real_positions()
            for real_pos in real_positions:
                symbol = real_pos.get('symbol', '')
                size = real_pos.get('contracts', 0)

                if size != 0:
                    try:
                        # 紧急平仓
                        side = "sell" if real_pos.get('side') == 'long' else "buy"
                        order = await self.exchange.create_real_order(
                            symbol=symbol,
                            order_type="market",
                            side=side,
                            amount=abs(size),
                            params={"reduceOnly": True}
                        )

                        close_results.append({
                            "symbol": symbol,
                            "status": "emergency_closed",
                            "order_id": order.get('id'),
                            "size": size
                        })

                        logger.info(f"🚨 紧急平仓真实持仓: {symbol} {size}")

                    except Exception as e:
                        logger.error(f"❌ 紧急平仓失败 {symbol}: {e}")
                        close_results.append({
                            "symbol": symbol,
                            "status": "close_failed",
                            "error": str(e)
                        })

        except Exception as e:
            logger.error(f"❌ 平仓所有真实持仓失败: {e}")
            close_results.append({
                "status": "error",
                "error": str(e)
            })

        return close_results

    async def emergency_stop_all_trading(self, user_id: str = None) -> Dict:
        """紧急停止所有交易"""
        try:
            self.emergency_stop = True

            # 停止所有会话
            stop_results = []
            for session_id in list(self.active_sessions.keys()):
                try:
                    result = await self.stop_real_trading_session(session_id, user_id, emergency=True)
                    stop_results.append(result)
                except Exception as e:
                    logger.error(f"❌ 紧急停止会话失败 {session_id}: {e}")

            # 紧急平仓所有交易所持仓
            emergency_close_results = await self.exchange.emergency_close_all_positions()

            # 记录审计日志
            self.audit_logger.log_action(
                action="EMERGENCY_STOP_ALL_TRADING",
                details={
                    "stopped_sessions": len(stop_results),
                    "emergency_closes": len(emergency_close_results)
                },
                user_id=user_id,
                success=True
            )

            logger.critical("🚨 紧急停止所有交易完成")

            return {
                "status": "emergency_stopped",
                "stopped_sessions": stop_results,
                "emergency_closes": emergency_close_results,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.critical(f"🚨 紧急停止所有交易失败: {e}")
            return {"status": "emergency_stop_failed", "error": str(e)}

# 全局状态管理
class RealTradingSystemState:
    def __init__(self):
        self.is_running = False
        self.trading_sessions = {}
        self.active_positions = {}
        self.system_stats = {
            "total_trades": 0,
            "total_pnl": 0.0,
            "win_rate": 0.0,
            "start_time": datetime.now().isoformat(),
            "real_trading": True
        }
        self.price_data = {}
        self.macd_data = {}
        self.websocket_connections = []
        self.emergency_stop = False

# 全局实例初始化
security_manager = SecurityManager()
audit_logger = AuditLogger()
trading_state = RealTradingSystemState()

# 交易所连接器（需要配置真实API密钥）
exchange_connector = None
market_data_manager = None
risk_manager = None
trading_engine = None

# API密钥配置函数
async def configure_exchange(exchange_name: str, api_key: str, api_secret: str,
                           passphrase: str = None, sandbox: bool = True) -> bool:
    """配置交易所连接"""
    global exchange_connector, market_data_manager, risk_manager, trading_engine

    try:
        # 加密存储API密钥
        encrypted_key = security_manager.encrypt_api_key(api_key)
        encrypted_secret = security_manager.encrypt_api_key(api_secret)
        encrypted_passphrase = security_manager.encrypt_api_key(passphrase) if passphrase else None

        # 创建交易所连接器
        exchange_connector = RealExchangeConnector(
            exchange_name=exchange_name,
            api_key=api_key,
            api_secret=api_secret,
            passphrase=passphrase,
            sandbox=sandbox
        )

        # 连接到交易所
        connected = await exchange_connector.connect()
        if not connected:
            return False

        # 初始化其他组件
        market_data_manager = RealMarketDataManager(exchange_connector)
        risk_manager = RealRiskManager(exchange_connector)
        trading_engine = RealTradingEngine(
            exchange_connector, market_data_manager, risk_manager, audit_logger
        )

        # 记录审计日志
        audit_logger.log_action(
            action="CONFIGURE_EXCHANGE",
            details={
                "exchange": exchange_name,
                "sandbox": sandbox
            },
            success=True
        )

        logger.info(f"✅ 交易所配置成功: {exchange_name} (沙盒: {sandbox})")
        return True

    except Exception as e:
        logger.error(f"❌ 交易所配置失败: {e}")
        audit_logger.log_action(
            action="CONFIGURE_EXCHANGE",
            details={"exchange": exchange_name},
            success=False,
            error_message=str(e)
        )
        return False

# 应用生命周期管理
@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动事件
    logger.info("[启动] BitV MACD智能加仓交易系统 - 实盘版本启动中...")
    logger.info("[系统] 系统组件初始化:")
    logger.info("   [OK] 安全管理器")
    logger.info("   [OK] 审计日志记录器")
    logger.info("   [OK] 实盘交易状态管理")
    logger.info("   [等待] 等待交易所配置...")

    logger.info("[完成] 实盘交易系统启动完成！")
    logger.warning("[提示] 请通过API配置交易所连接后开始交易")

    yield

    # 关闭事件
    logger.info("[关闭] 正在关闭实盘交易系统...")

    # 紧急停止所有交易
    if trading_engine:
        try:
            await trading_engine.emergency_stop_all_trading()
        except:
            pass

    # 断开交易所连接
    if exchange_connector:
        try:
            await exchange_connector.disconnect()
        except:
            pass

    # 停止市场数据获取
    if market_data_manager:
        try:
            market_data_manager.stop()
        except:
            pass

    logger.info("[完成] 实盘交易系统已关闭")

# 创建FastAPI应用
app = FastAPI(
    title="BitV MACD智能加仓交易系统 - 实盘版本",
    description="专业级实盘量化交易系统，支持OKX/Gate.io真实交易",
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 安全认证
security = HTTPBearer()

def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """验证访问令牌"""
    # 这里可以实现JWT验证或其他认证机制
    # 暂时简单验证
    if credentials.credentials != "bitv_real_trading_token":
        raise HTTPException(status_code=401, detail="无效的访问令牌")
    return credentials.credentials

# API路由
@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "BitV MACD智能加仓交易系统 - 实盘版本",
        "version": "2.0.0",
        "status": "running",
        "features": [
            "🔥 真实交易所API集成",
            "📈 完整MACD策略分析",
            "⚡ 智能加仓管理",
            "🛡️ 实时风险监控",
            "🔐 安全认证和审计",
            "🚨 紧急停止功能",
            "📊 实时数据推送"
        ],
        "supported_exchanges": ["OKX", "Gate.io"],
        "real_trading": True,
        "timestamp": datetime.now().isoformat(),
        "docs": "/docs"
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    exchange_status = "connected" if (exchange_connector and exchange_connector.connected) else "disconnected"
    market_data_status = "active" if (market_data_manager and market_data_manager.running) else "inactive"

    return {
        "status": "healthy",
        "components": {
            "api": "active",
            "security_manager": "active",
            "audit_logger": "active",
            "exchange_connector": exchange_status,
            "market_data_manager": market_data_status,
            "risk_manager": "active" if risk_manager else "inactive",
            "trading_engine": "active" if trading_engine else "inactive"
        },
        "real_trading": True,
        "emergency_stop": trading_state.emergency_stop,
        "uptime": str(datetime.now() - datetime.fromisoformat(trading_state.system_stats["start_time"])),
        "timestamp": datetime.now().isoformat()
    }

@app.post("/api/configure-exchange")
async def configure_exchange_api(config: Dict[str, Any], token: str = Depends(verify_token)):
    """配置交易所连接"""
    try:
        exchange_name = config.get("exchange")
        api_key = config.get("api_key")
        api_secret = config.get("api_secret")
        passphrase = config.get("passphrase")
        sandbox = config.get("sandbox", True)

        if not all([exchange_name, api_key, api_secret]):
            raise HTTPException(status_code=400, detail="缺少必要的配置参数")

        success = await configure_exchange(exchange_name, api_key, api_secret, passphrase, sandbox)

        if success:
            # 启动市场数据获取
            if market_data_manager:
                asyncio.create_task(market_data_manager.start())

            return {
                "success": True,
                "message": f"交易所 {exchange_name} 配置成功",
                "exchange": exchange_name,
                "sandbox": sandbox,
                "timestamp": datetime.now().isoformat()
            }
        else:
            raise HTTPException(status_code=500, detail="交易所配置失败")

    except Exception as e:
        logger.error(f"❌ 配置交易所API失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/status")
async def get_system_status(token: str = Depends(verify_token)):
    """获取系统状态"""
    if not trading_engine:
        raise HTTPException(status_code=503, detail="交易引擎未初始化")

    active_sessions = len([s for s in trading_engine.active_sessions.values() if s["status"] == "active"])

    return {
        "success": True,
        "data": {
            "system_status": "running",
            "trading_engine_status": "active",
            "exchange_connected": exchange_connector.connected if exchange_connector else False,
            "exchange_name": exchange_connector.exchange_name if exchange_connector else None,
            "is_trading": len(trading_engine.active_sessions) > 0,
            "active_sessions": active_sessions,
            "total_sessions": len(trading_engine.active_sessions),
            "active_positions": len(trading_engine.active_positions),
            "emergency_stop": trading_engine.emergency_stop,
            "system_stats": trading_state.system_stats,
            "real_trading": True,
            "timestamp": datetime.now().isoformat()
        }
    }

@app.get("/api/account/balance")
async def get_account_balance(token: str = Depends(verify_token)):
    """获取真实账户余额"""
    if not exchange_connector or not exchange_connector.connected:
        raise HTTPException(status_code=503, detail="交易所未连接")

    try:
        balance = await exchange_connector.get_real_balance()

        return {
            "success": True,
            "data": {
                "balance": balance,
                "exchange": exchange_connector.exchange_name,
                "timestamp": datetime.now().isoformat()
            }
        }
    except Exception as e:
        logger.error(f"❌ 获取账户余额失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/account/positions")
async def get_account_positions(token: str = Depends(verify_token)):
    """获取真实持仓"""
    if not exchange_connector or not exchange_connector.connected:
        raise HTTPException(status_code=503, detail="交易所未连接")

    try:
        positions = await exchange_connector.get_real_positions()

        return {
            "success": True,
            "data": {
                "positions": positions,
                "exchange": exchange_connector.exchange_name,
                "timestamp": datetime.now().isoformat()
            }
        }
    except Exception as e:
        logger.error(f"❌ 获取持仓信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/trading/start")
async def start_real_trading(config: Dict[str, Any], token: str = Depends(verify_token)):
    """启动真实交易"""
    if not trading_engine:
        raise HTTPException(status_code=503, detail="交易引擎未初始化")

    try:
        # 验证配置
        required_fields = ["symbol", "leverage", "initial_margin"]
        for field in required_fields:
            if field not in config:
                raise HTTPException(status_code=400, detail=f"缺少必要参数: {field}")

        # 安全检查
        leverage = config.get("leverage", 10)
        if leverage > 50:  # 限制最大杠杆
            raise HTTPException(status_code=400, detail="杠杆倍数过高，最大允许50倍")

        initial_margin = config.get("initial_margin", 100)
        if initial_margin > 10000:  # 限制最大保证金
            raise HTTPException(status_code=400, detail="初始保证金过高，最大允许10000 USDT")

        session_data = await trading_engine.start_real_trading_session(config, user_id="api_user")

        return {
            "success": True,
            "data": session_data
        }
    except Exception as e:
        logger.error(f"❌ 启动真实交易失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/trading/stop")
async def stop_real_trading(session_id: Optional[str] = None, emergency: bool = False,
                           token: str = Depends(verify_token)):
    """停止真实交易"""
    if not trading_engine:
        raise HTTPException(status_code=503, detail="交易引擎未初始化")

    try:
        if session_id:
            result = await trading_engine.stop_real_trading_session(
                session_id, user_id="api_user", emergency=emergency
            )
        else:
            result = await trading_engine.emergency_stop_all_trading(user_id="api_user")

        return {
            "success": True,
            "data": result
        }
    except Exception as e:
        logger.error(f"❌ 停止真实交易失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/market/prices")
async def get_market_prices(token: str = Depends(verify_token)):
    """获取真实市场价格"""
    if not market_data_manager:
        raise HTTPException(status_code=503, detail="市场数据管理器未初始化")

    return {
        "success": True,
        "data": {
            "prices": market_data_manager.price_data,
            "exchange": exchange_connector.exchange_name if exchange_connector else None,
            "real_data": True,
            "timestamp": datetime.now().isoformat()
        }
    }

@app.get("/api/market/prices/{symbol}")
async def get_symbol_price(symbol: str, token: str = Depends(verify_token)):
    """获取特定交易对价格"""
    if not market_data_manager:
        raise HTTPException(status_code=503, detail="市场数据管理器未初始化")

    price_data = market_data_manager.price_data.get(symbol)
    if not price_data:
        raise HTTPException(status_code=404, detail=f"未找到交易对 {symbol} 的价格数据")

    return {
        "success": True,
        "data": price_data
    }

@app.get("/api/trading/sessions")
async def get_trading_sessions(token: str = Depends(verify_token)):
    """获取交易会话列表"""
    if not trading_engine:
        raise HTTPException(status_code=503, detail="交易引擎未初始化")

    return {
        "success": True,
        "data": {
            "sessions": list(trading_engine.active_sessions.values()),
            "total": len(trading_engine.active_sessions),
            "timestamp": datetime.now().isoformat()
        }
    }

@app.get("/api/trading/positions")
async def get_trading_positions(token: str = Depends(verify_token)):
    """获取交易持仓"""
    if not trading_engine:
        raise HTTPException(status_code=503, detail="交易引擎未初始化")

    return {
        "success": True,
        "data": {
            "positions": list(trading_engine.active_positions.values()),
            "total": len(trading_engine.active_positions),
            "timestamp": datetime.now().isoformat()
        }
    }

@app.post("/api/emergency/stop")
async def emergency_stop(token: str = Depends(verify_token)):
    """紧急停止所有交易"""
    if not trading_engine:
        raise HTTPException(status_code=503, detail="交易引擎未初始化")

    try:
        result = await trading_engine.emergency_stop_all_trading(user_id="api_user")

        return {
            "success": True,
            "data": result,
            "message": "紧急停止执行完成"
        }
    except Exception as e:
        logger.critical(f"🚨 紧急停止失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/audit/logs")
async def get_audit_logs(limit: int = 100, token: str = Depends(verify_token)):
    """获取审计日志"""
    try:
        conn = sqlite3.connect(audit_logger.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT * FROM audit_logs
            ORDER BY timestamp DESC
            LIMIT ?
        ''', (limit,))

        columns = [description[0] for description in cursor.description]
        logs = [dict(zip(columns, row)) for row in cursor.fetchall()]

        conn.close()

        return {
            "success": True,
            "data": {
                "logs": logs,
                "total": len(logs),
                "timestamp": datetime.now().isoformat()
            }
        }
    except Exception as e:
        logger.error(f"❌ 获取审计日志失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket端点 - 实时数据推送"""
    await websocket.accept()

    if market_data_manager:
        market_data_manager.websocket_connections.append(websocket)

    try:
        logger.info("🔌 实盘交易WebSocket连接已建立")

        # 发送初始数据
        initial_data = {
            "type": "connection_established",
            "data": {
                "message": "实盘交易WebSocket连接成功",
                "real_trading": True,
                "exchange": exchange_connector.exchange_name if exchange_connector else None,
                "timestamp": datetime.now().isoformat()
            }
        }
        await websocket.send_text(json.dumps(initial_data))

        # 保持连接
        while True:
            try:
                data = await websocket.receive_text()
                message = json.loads(data)

                if message.get("action") == "get_current_data":
                    # 发送当前数据快照
                    snapshot = {
                        "type": "real_data_snapshot",
                        "data": {
                            "prices": market_data_manager.price_data if market_data_manager else {},
                            "positions": list(trading_engine.active_positions.values()) if trading_engine else [],
                            "sessions": list(trading_engine.active_sessions.values()) if trading_engine else [],
                            "exchange_connected": exchange_connector.connected if exchange_connector else False,
                            "real_trading": True,
                            "timestamp": datetime.now().isoformat()
                        }
                    }
                    await websocket.send_text(json.dumps(snapshot))

            except WebSocketDisconnect:
                break
            except Exception as e:
                logger.error(f"WebSocket消息处理错误: {e}")
                break

    except WebSocketDisconnect:
        logger.info("🔌 实盘交易WebSocket连接已断开")
    except Exception as e:
        logger.error(f"WebSocket错误: {e}")
    finally:
        # 清理连接
        if market_data_manager and websocket in market_data_manager.websocket_connections:
            market_data_manager.websocket_connections.remove(websocket)

def main():
    """主函数"""
    print("🚀 启动BitV MACD智能加仓交易系统 - 实盘版本")
    print("=" * 80)
    print("🔥 实盘交易特性:")
    print("   💰 真实交易所API集成 (OKX/Gate.io)")
    print("   📈 完整MACD策略分析")
    print("   ⚡ 智能加仓管理")
    print("   🛡️ 实时风险监控")
    print("   🔐 安全认证和审计")
    print("   🚨 紧急停止功能")
    print("   📊 实时数据推送")
    print("=" * 80)
    print("⚠️  重要安全提示:")
    print("   🔑 请妥善保管API密钥")
    print("   💸 建议先在沙盒环境测试")
    print("   📊 密切监控风险指标")
    print("   🚨 设置合理的止损点")
    print("=" * 80)
    print("🌐 服务地址:")
    print("   - API服务: http://localhost:8000")
    print("   - API文档: http://localhost:8000/docs")
    print("   - 健康检查: http://localhost:8000/health")
    print("   - WebSocket: ws://localhost:8000/ws")
    print("=" * 80)
    print("🔧 使用步骤:")
    print("   1. 访问 /docs 查看API文档")
    print("   2. 使用 /api/configure-exchange 配置交易所")
    print("   3. 使用 /api/trading/start 启动交易")
    print("   4. 监控 /api/trading/positions 持仓状态")
    print("   5. 必要时使用 /api/emergency/stop 紧急停止")
    print("=" * 80)
    print("🔐 访问令牌: bitv_real_trading_token")
    print("=" * 80)

    try:
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            log_level="info",
            access_log=True,
            reload=False  # 生产环境必须设为False
        )
    except KeyboardInterrupt:
        print("\n👋 实盘交易系统已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
