# BitV MACD智能加仓交易系统 - Vue前端

## 🎯 项目简介

这是BitV MACD智能加仓交易系统的Vue 3前端界面，完美展示Python GUI的所有标签页和功能，提供现代化的Web交易界面。

## ✨ 主要特性

### 🖥️ 完整的GUI功能映射
- **仪表板** - 系统状态概览、实时监控
- **配置设置** - 交易所配置、基础参数设置
- **策略配置** - 12种技术指标策略配置
  - 布林带策略 (Bollinger Bands)
  - MACD策略 (Moving Average Convergence Divergence)
  - RSI策略 (Relative Strength Index)
  - KDJ策略 (Stochastic Oscillator)
  - 移动平均线策略 (Moving Average)
  - 资金流量指标策略 (Money Flow Index)
  - 平均方向指数策略 (Average Directional Index)
  - 成交量平衡指标策略 (On-Balance Volume)
  - 斐波那契回撤策略 (Fibonacci Retracement)
  - 威廉指标策略 (Williams %R)
  - 一目均衡表策略 (Ichimoku Cloud)
  - 插针策略 (Pin Bar)
- **资金设置** - 风险管理和资金配置
- **交易控制** - 启动/停止交易、紧急停止
- **实时监控** - 持仓监控、风险指标
- **系统日志** - 实时日志查看

### 🚀 技术特性
- **Vue 3 + Composition API** - 现代化前端框架
- **Element Plus** - 企业级UI组件库
- **Pinia** - 状态管理
- **WebSocket实时通信** - 与Python后端实时数据同步
- **ECharts图表** - 专业的数据可视化
- **响应式设计** - 支持桌面和移动端
- **TypeScript支持** - 类型安全
- **Vite构建** - 快速开发和构建

### 🔗 与Python后端完美集成
- **API代理** - 自动代理到Python后端
- **WebSocket连接** - 实时数据推送
- **状态同步** - 前后端状态实时同步
- **错误处理** - 完善的错误处理机制

## 📁 项目结构

```
vue/
├── src/
│   ├── components/          # 公共组件
│   │   ├── Charts/         # 图表组件
│   │   └── Common/         # 通用组件
│   ├── layout/             # 布局组件
│   ├── router/             # 路由配置
│   ├── stores/             # 状态管理
│   │   ├── system.js       # 系统状态
│   │   ├── trading.js      # 交易状态
│   │   └── websocket.js    # WebSocket管理
│   ├── styles/             # 样式文件
│   ├── utils/              # 工具函数
│   │   └── api.js          # API封装
│   └── views/              # 页面组件
│       ├── Dashboard/      # 仪表板
│       ├── Config/         # 配置设置
│       ├── Strategies/     # 策略配置
│       │   ├── MACD.vue
│       │   ├── Bollinger.vue
│       │   ├── RSI.vue
│       │   └── ...
│       ├── Risk/           # 资金设置
│       ├── Trading/        # 交易控制
│       ├── Monitor/        # 实时监控
│       └── Logs/           # 系统日志
├── package.json
├── vite.config.js
└── README.md
```

## 🛠️ 安装和运行

### 前置要求
- Node.js 16+ 
- npm 或 yarn
- Python后端服务运行在 http://localhost:8000

### 安装依赖
```bash
cd vue
npm install
```

### 开发模式运行
```bash
npm run dev
```
访问: http://localhost:3000

### 生产构建
```bash
npm run build
```

### 预览生产版本
```bash
npm run preview
```

## 🔧 配置说明

### API代理配置
在 `vite.config.js` 中配置了API代理：
```javascript
server: {
  proxy: {
    '/api': {
      target: 'http://localhost:8000',
      changeOrigin: true
    },
    '/ws': {
      target: 'ws://localhost:8000',
      ws: true
    }
  }
}
```

### 环境变量
创建 `.env.local` 文件：
```
VITE_API_BASE_URL=http://localhost:8000
VITE_WS_URL=ws://localhost:8000/ws
```

## 📊 功能对应关系

| Python GUI标签页 | Vue前端页面 | 功能描述 |
|------------------|-------------|----------|
| 仪表板 | /dashboard | 系统状态概览、实时数据 |
| 配置设置 | /config | 交易所配置、基础参数 |
| 策略配置 | /strategy | 基础策略配置 |
| 布林带策略 | /bollinger | 布林带技术指标策略 |
| MACD策略 | /macd | MACD技术指标策略 |
| RSI策略 | /rsi | RSI技术指标策略 |
| KDJ策略 | /kdj | KDJ技术指标策略 |
| 移动平均线 | /ma | 移动平均线策略 |
| 资金流量指标 | /mfi | MFI技术指标策略 |
| 平均方向指数 | /adx | ADX技术指标策略 |
| 成交量平衡指标 | /obv | OBV技术指标策略 |
| 斐波那契回撤 | /fibonacci | 斐波那契回撤策略 |
| 威廉指标 | /williams | Williams %R策略 |
| 一目均衡表 | /ichimoku | 一目均衡表策略 |
| 插针策略 | /pinbar | Pin Bar策略 |
| 资金设置 | /risk | 风险管理和资金配置 |
| 交易控制 | /trading | 交易启停控制 |
| 实时监控 | /monitor | 持仓和风险监控 |
| 系统日志 | /logs | 日志查看和分析 |

## 🌐 WebSocket实时通信

前端通过WebSocket与Python后端保持实时连接：

### 订阅的数据频道
- `price_update` - 价格更新
- `position_update` - 持仓更新  
- `risk_update` - 风险指标更新
- `trading_update` - 交易状态更新
- `log_update` - 日志更新

### 使用示例
```javascript
import { useWebSocketStore } from '@/stores/websocket'

const wsStore = useWebSocketStore()

// 监听价格更新
wsStore.on('price_update', (data) => {
  console.log('价格更新:', data)
})

// 发送消息
wsStore.emit('subscribe', { channel: 'price_update' })
```

## 🎨 UI/UX特性

### 现代化设计
- **渐变色彩** - 专业的视觉效果
- **卡片布局** - 清晰的信息层次
- **响应式设计** - 适配各种屏幕尺寸
- **动画效果** - 流畅的交互体验

### 状态指示器
- **连接状态** - 实时显示后端连接状态
- **交易状态** - 清晰的交易状态指示
- **风险等级** - 直观的风险等级显示

### 数据可视化
- **实时图表** - ECharts专业图表
- **统计卡片** - 关键指标展示
- **进度条** - 风险和收益可视化

## 🔒 安全特性

- **API认证** - Bearer Token认证
- **请求拦截** - 统一的请求处理
- **错误处理** - 完善的错误处理机制
- **输入验证** - 表单数据验证

## 📱 移动端支持

- **响应式布局** - 自适应移动设备
- **触摸优化** - 移动端交互优化
- **性能优化** - 移动端性能优化

## 🚀 部署建议

### 开发环境
```bash
npm run dev
```

### 生产环境
```bash
npm run build
# 将dist目录部署到Web服务器
```

### Docker部署
```dockerfile
FROM nginx:alpine
COPY dist/ /usr/share/nginx/html/
COPY nginx.conf /etc/nginx/nginx.conf
```

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

MIT License

## 📞 支持

如有问题，请联系开发团队或提交Issue。
