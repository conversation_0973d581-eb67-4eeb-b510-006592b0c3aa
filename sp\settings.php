<?php
/**
 * BitV MACD智能加仓交易系统 - 系统设置页面
 * 实盘交易版本 - 交易所配置和系统参数设置
 */

session_start();

// 加载配置
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/security.php';

// 检查认证和权限
requireAuth();
requirePermission('manage');

$pageTitle = '系统设置';
$currentPage = 'settings';

$success = '';
$error = '';

// 处理交易所配置
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        // 验证CSRF令牌
        if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
            throw new Exception('安全验证失败，请刷新页面重试');
        }
        
        if ($_POST['action'] === 'configure_exchange') {
            $config = [
                'exchange' => sanitizeInput($_POST['exchange'] ?? ''),
                'api_key' => sanitizeInput($_POST['api_key'] ?? ''),
                'api_secret' => sanitizeInput($_POST['api_secret'] ?? ''),
                'passphrase' => sanitizeInput($_POST['passphrase'] ?? ''),
                'sandbox' => isset($_POST['sandbox'])
            ];
            
            // 验证必填字段
            if (empty($config['exchange']) || empty($config['api_key']) || empty($config['api_secret'])) {
                throw new Exception('请填写所有必填字段');
            }
            
            // 配置交易所
            $result = configureExchange($config);
            
            if ($result['success']) {
                $success = '交易所配置成功！';
                
                // 记录操作日志
                logUserAction('CONFIGURE_EXCHANGE', [
                    'exchange' => $config['exchange'],
                    'sandbox' => $config['sandbox']
                ]);
            } else {
                throw new Exception($result['error'] ?? '配置失败');
            }
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
        
        // 记录错误日志
        logUserAction('CONFIGURE_EXCHANGE_FAILED', [
            'error' => $error
        ]);
    }
}

// 获取系统状态
$systemStatus = getSystemStatus();

include 'includes/header.php';
?>

<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">
            <i class="fas fa-cog text-primary me-2"></i>
            系统设置
        </h1>
        <div>
            <button type="button" class="btn btn-outline-primary" onclick="checkSystemHealth()">
                <i class="fas fa-heartbeat me-2"></i>
                系统健康检查
            </button>
        </div>
    </div>

    <!-- 成功/错误消息 -->
    <?php if ($success): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>
        <?php echo htmlspecialchars($success); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <?php echo htmlspecialchars($error); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <div class="row">
        <!-- 左侧：交易所配置 -->
        <div class="col-xl-8 col-lg-7">
            <!-- 交易所配置卡片 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-exchange-alt me-2"></i>
                        交易所配置
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="" id="exchangeConfigForm">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="action" value="configure_exchange">
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="exchange" class="form-label">
                                    <i class="fas fa-building me-2"></i>
                                    选择交易所 <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="exchange" name="exchange" required>
                                    <option value="">请选择交易所</option>
                                    <?php foreach (SUPPORTED_EXCHANGES as $key => $exchange): ?>
                                    <option value="<?php echo $key; ?>">
                                        <?php echo htmlspecialchars($exchange['display_name']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="form-text">
                                    支持的交易所：OKX、Gate.io
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="sandbox" class="form-label">
                                    <i class="fas fa-flask me-2"></i>
                                    运行模式
                                </label>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="sandbox" name="sandbox" checked>
                                    <label class="form-check-label" for="sandbox">
                                        沙盒模式（推荐）
                                    </label>
                                </div>
                                <div class="form-text">
                                    <span class="text-warning">
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        关闭沙盒模式将使用真实资金进行交易！
                                    </span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="api_key" class="form-label">
                                    <i class="fas fa-key me-2"></i>
                                    API Key <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="api_key" name="api_key" 
                                       placeholder="输入您的API Key" required>
                                <div class="form-text">
                                    从交易所获取的API密钥
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="api_secret" class="form-label">
                                    <i class="fas fa-lock me-2"></i>
                                    API Secret <span class="text-danger">*</span>
                                </label>
                                <input type="password" class="form-control" id="api_secret" name="api_secret" 
                                       placeholder="输入您的API Secret" required>
                                <div class="form-text">
                                    API密钥对应的密码
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="passphrase" class="form-label">
                                    <i class="fas fa-shield-alt me-2"></i>
                                    Passphrase
                                </label>
                                <input type="password" class="form-control" id="passphrase" name="passphrase" 
                                       placeholder="输入Passphrase（如需要）">
                                <div class="form-text">
                                    某些交易所需要的额外密码（如OKX）
                                </div>
                            </div>
                        </div>
                        
                        <!-- 安全提示 -->
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>安全提示：</h6>
                            <ul class="mb-0">
                                <li>API密钥将被加密存储，但请确保密钥安全</li>
                                <li>建议为API密钥设置IP白名单限制</li>
                                <li>仅授予必要的权限（交易、查询余额、查询持仓）</li>
                                <li>定期更换API密钥以提高安全性</li>
                                <li>首次使用建议在沙盒环境测试</li>
                            </ul>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="button" class="btn btn-outline-secondary me-md-2" onclick="testConnection()">
                                <i class="fas fa-plug me-2"></i>
                                测试连接
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                保存配置
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 系统参数配置 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-sliders-h me-2"></i>
                        系统参数
                    </h6>
                </div>
                <div class="card-body">
                    <form id="systemParamsForm">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="default_leverage" class="form-label">默认杠杆倍数</label>
                                <select class="form-select" id="default_leverage" name="default_leverage">
                                    <option value="5">5x</option>
                                    <option value="10" selected>10x</option>
                                    <option value="20">20x</option>
                                    <option value="50">50x</option>
                                </select>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="max_add_times" class="form-label">默认最大加仓次数</label>
                                <select class="form-select" id="max_add_times" name="max_add_times">
                                    <option value="1">1次</option>
                                    <option value="2">2次</option>
                                    <option value="3" selected>3次</option>
                                    <option value="5">5次</option>
                                </select>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="risk_level" class="form-label">风险控制等级</label>
                                <select class="form-select" id="risk_level" name="risk_level">
                                    <option value="conservative">保守</option>
                                    <option value="moderate" selected>适中</option>
                                    <option value="aggressive">激进</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="macd_fast" class="form-label">MACD快线周期</label>
                                <input type="number" class="form-control" id="macd_fast" name="macd_fast" 
                                       value="<?php echo MACD_FAST_PERIOD; ?>" min="5" max="50">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="macd_slow" class="form-label">MACD慢线周期</label>
                                <input type="number" class="form-control" id="macd_slow" name="macd_slow" 
                                       value="<?php echo MACD_SLOW_PERIOD; ?>" min="10" max="100">
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="button" class="btn btn-outline-secondary me-md-2" onclick="resetDefaults()">
                                <i class="fas fa-undo me-2"></i>
                                恢复默认
                            </button>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-2"></i>
                                保存参数
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 右侧：系统状态和信息 -->
        <div class="col-xl-4 col-lg-5">
            <!-- 系统状态 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-heartbeat me-2"></i>
                        系统状态
                    </h6>
                </div>
                <div class="card-body">
                    <div id="systemStatusContainer">
                        <div class="text-center py-3">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">检查中...</span>
                            </div>
                            <div class="mt-2">正在检查系统状态...</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 支持的交易对 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-coins me-2"></i>
                        支持的交易对
                    </h6>
                </div>
                <div class="card-body">
                    <?php foreach (SUPPORTED_SYMBOLS as $symbol => $info): ?>
                    <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                        <div>
                            <strong><?php echo htmlspecialchars($info['display_name']); ?></strong>
                            <br>
                            <small class="text-muted">
                                最小: <?php echo $info['min_size']; ?> | 
                                精度: <?php echo $info['tick_size']; ?>
                            </small>
                        </div>
                        <span class="badge bg-primary"><?php echo htmlspecialchars($info['base_currency']); ?></span>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- 系统信息 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>
                        系统信息
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row g-0">
                        <div class="col-6">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                系统版本
                            </div>
                            <div class="h6 mb-3 font-weight-bold text-gray-800">
                                v<?php echo SYSTEM_VERSION; ?>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                PHP版本
                            </div>
                            <div class="h6 mb-3 font-weight-bold text-gray-800">
                                <?php echo PHP_VERSION; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row g-0">
                        <div class="col-6">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                运行模式
                            </div>
                            <div class="h6 mb-3 font-weight-bold text-gray-800">
                                实盘交易
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                时区
                            </div>
                            <div class="h6 mb-3 font-weight-bold text-gray-800">
                                <?php echo date_default_timezone_get(); ?>
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="text-center">
                        <small class="text-muted">
                            服务器时间: <?php echo date('Y-m-d H:i:s'); ?>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>

<script>
// 页面加载完成后执行
$(document).ready(function() {
    // 加载系统状态
    loadSystemStatus();
    
    // 表单提交处理
    $('#exchangeConfigForm').on('submit', function(e) {
        e.preventDefault();
        
        // 验证表单
        if (!validateExchangeForm()) {
            return;
        }
        
        // 显示确认对话框
        if (confirm('确认保存交易所配置？这将重新连接到交易所。')) {
            this.submit();
        }
    });
    
    // 系统参数表单处理
    $('#systemParamsForm').on('submit', function(e) {
        e.preventDefault();
        saveSystemParams();
    });
    
    // 交易所选择变化时的处理
    $('#exchange').on('change', function() {
        const exchange = $(this).val();
        updateExchangeInfo(exchange);
    });
});

// 加载系统状态
function loadSystemStatus() {
    fetch(window.BITV.API_BASE + '/api/status', {
        headers: {
            'Authorization': 'Bearer ' + window.BITV.API_TOKEN
        }
    })
    .then(response => response.json())
    .then(data => {
        displaySystemStatus(data);
    })
    .catch(error => {
        console.error('获取系统状态失败:', error);
        displaySystemStatus({ success: false, error: error.message });
    });
}

// 显示系统状态
function displaySystemStatus(data) {
    const container = document.getElementById('systemStatusContainer');
    
    if (data.success) {
        const statusData = data.data;
        container.innerHTML = `
            <div class="row g-0">
                <div class="col-6">
                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                        系统状态
                    </div>
                    <div class="h6 mb-3 font-weight-bold">
                        <span class="badge bg-${statusData.system_status === 'running' ? 'success' : 'danger'}">
                            ${statusData.system_status === 'running' ? '运行中' : '离线'}
                        </span>
                    </div>
                </div>
                <div class="col-6">
                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                        交易所连接
                    </div>
                    <div class="h6 mb-3 font-weight-bold">
                        <span class="badge bg-${statusData.exchange_connected ? 'success' : 'danger'}">
                            ${statusData.exchange_connected ? '已连接' : '未连接'}
                        </span>
                    </div>
                </div>
            </div>
            
            <div class="row g-0">
                <div class="col-6">
                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                        活跃会话
                    </div>
                    <div class="h6 mb-3 font-weight-bold text-gray-800">
                        ${statusData.active_sessions || 0}
                    </div>
                </div>
                <div class="col-6">
                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                        活跃持仓
                    </div>
                    <div class="h6 mb-3 font-weight-bold text-gray-800">
                        ${statusData.active_positions || 0}
                    </div>
                </div>
            </div>
            
            ${statusData.exchange_name ? `
            <hr>
            <div class="text-center">
                <small class="text-muted">
                    当前交易所: <strong>${statusData.exchange_name}</strong>
                </small>
            </div>
            ` : ''}
        `;
    } else {
        container.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                无法获取系统状态: ${data.error || '未知错误'}
            </div>
        `;
    }
}

// 验证交易所配置表单
function validateExchangeForm() {
    const exchange = document.getElementById('exchange').value;
    const apiKey = document.getElementById('api_key').value.trim();
    const apiSecret = document.getElementById('api_secret').value.trim();
    
    if (!exchange) {
        toastr.error('请选择交易所');
        return false;
    }
    
    if (!apiKey) {
        toastr.error('请输入API Key');
        return false;
    }
    
    if (!apiSecret) {
        toastr.error('请输入API Secret');
        return false;
    }
    
    if (apiKey.length < 10) {
        toastr.error('API Key长度不足');
        return false;
    }
    
    if (apiSecret.length < 10) {
        toastr.error('API Secret长度不足');
        return false;
    }
    
    return true;
}

// 测试连接
function testConnection() {
    if (!validateExchangeForm()) {
        return;
    }
    
    const formData = new FormData(document.getElementById('exchangeConfigForm'));
    const config = Object.fromEntries(formData);
    
    showLoading('正在测试连接...');
    
    fetch(window.BITV.API_BASE + '/api/configure-exchange', {
        method: 'POST',
        headers: {
            'Authorization': 'Bearer ' + window.BITV.API_TOKEN,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            ...config,
            sandbox: document.getElementById('sandbox').checked,
            test_only: true
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        
        if (data.success) {
            toastr.success('连接测试成功！');
        } else {
            toastr.error('连接测试失败: ' + (data.error || '未知错误'));
        }
    })
    .catch(error => {
        hideLoading();
        console.error('测试连接失败:', error);
        toastr.error('测试连接失败: ' + error.message);
    });
}

// 保存系统参数
function saveSystemParams() {
    const formData = new FormData(document.getElementById('systemParamsForm'));
    const params = Object.fromEntries(formData);
    
    showLoading('正在保存参数...');
    
    // 这里可以添加保存系统参数的API调用
    setTimeout(() => {
        hideLoading();
        toastr.success('系统参数保存成功！');
    }, 1000);
}

// 恢复默认参数
function resetDefaults() {
    if (confirm('确认恢复默认参数？')) {
        document.getElementById('default_leverage').value = '10';
        document.getElementById('max_add_times').value = '3';
        document.getElementById('risk_level').value = 'moderate';
        document.getElementById('macd_fast').value = '<?php echo MACD_FAST_PERIOD; ?>';
        document.getElementById('macd_slow').value = '<?php echo MACD_SLOW_PERIOD; ?>';
        
        toastr.info('参数已恢复为默认值');
    }
}

// 更新交易所信息
function updateExchangeInfo(exchange) {
    const passphraseField = document.getElementById('passphrase').parentNode;
    
    if (exchange === 'okx') {
        passphraseField.style.display = 'block';
        document.getElementById('passphrase').required = true;
    } else {
        passphraseField.style.display = 'none';
        document.getElementById('passphrase').required = false;
    }
}

// 系统健康检查
function checkSystemHealth() {
    showLoading('正在进行系统健康检查...');
    
    fetch(window.BITV.API_BASE + '/health')
        .then(response => response.json())
        .then(data => {
            hideLoading();
            
            let message = '系统健康检查完成:\n\n';
            message += `整体状态: ${data.status}\n`;
            
            if (data.components) {
                message += '\n组件状态:\n';
                for (const [component, status] of Object.entries(data.components)) {
                    message += `- ${component}: ${status}\n`;
                }
            }
            
            alert(message);
        })
        .catch(error => {
            hideLoading();
            console.error('健康检查失败:', error);
            toastr.error('健康检查失败: ' + error.message);
        });
}
</script>

</body>
</html>
