# 🚀 BitV Vue前端快速启动指南

## 📋 前置条件

### 1. 确保Python后端正常运行
```bash
# 在项目根目录启动Python后端
cd C:\Users\<USER>\Desktop\bitV
python main_full.py
```
后端应该运行在: http://localhost:8000

### 2. 安装Node.js
- 下载并安装Node.js 16+: https://nodejs.org/
- 验证安装: `node --version`

## 🛠️ 安装和启动

### 方法一：使用启动脚本（推荐）

#### Windows用户
```bash
cd C:\Users\<USER>\Desktop\bitV\vue
start.bat
```

#### Linux/Mac用户
```bash
cd C:\Users\<USER>\Desktop\bitV\vue
chmod +x start.sh
./start.sh
```

### 方法二：手动启动

```bash
# 1. 进入Vue项目目录
cd C:\Users\<USER>\Desktop\bitV\vue

# 2. 安装依赖（首次运行）
npm install

# 3. 启动开发服务器
npm run dev
```

## 🌐 访问地址

启动成功后，在浏览器中访问：
- **本地访问**: http://localhost:3000
- **网络访问**: http://0.0.0.0:3000

## 📱 功能导航

### 🏠 主要页面
- **仪表板** (`/dashboard`) - 系统状态概览和实时监控
- **配置设置** (`/config`) - 交易所配置和基础参数设置

### ⚙️ 策略配置
- **基础策略** (`/strategy`) - 通用策略配置
- **布林带策略** (`/bollinger`) - 布林带技术指标策略
- **MACD策略** (`/macd`) - MACD技术指标策略 ✅ 已完成
- **RSI策略** (`/rsi`) - RSI技术指标策略
- **KDJ策略** (`/kdj`) - KDJ技术指标策略
- **移动平均线** (`/ma`) - 移动平均线策略
- **资金流量指标** (`/mfi`) - MFI技术指标策略
- **平均方向指数** (`/adx`) - ADX技术指标策略
- **成交量平衡指标** (`/obv`) - OBV技术指标策略
- **斐波那契回撤** (`/fibonacci`) - 斐波那契回撤策略
- **威廉指标** (`/williams`) - Williams %R策略
- **一目均衡表** (`/ichimoku`) - 一目均衡表策略
- **插针策略** (`/pinbar`) - Pin Bar策略

### 🛡️ 风险管理
- **资金设置** (`/risk`) - 风险管理和资金配置 ✅ 已完成
- **交易控制** (`/trading`) - 交易启停控制
- **实时监控** (`/monitor`) - 持仓和风险监控
- **系统日志** (`/logs`) - 日志查看和分析

## 🔧 开发状态

### ✅ 已完成的功能
- [x] 项目基础架构
- [x] 路由和导航系统
- [x] 状态管理 (Pinia)
- [x] API封装和WebSocket连接
- [x] 仪表板页面
- [x] 配置设置页面
- [x] MACD策略配置页面（完整功能）
- [x] 布林带策略配置页面（基础版）
- [x] 风险管理页面（完整功能）
- [x] 响应式布局和UI组件

### 🚧 开发中的功能
- [ ] 其他策略配置页面的详细实现
- [ ] 交易控制页面
- [ ] 实时监控页面
- [ ] 系统日志页面
- [ ] 图表组件 (ECharts)
- [ ] 数据可视化

### 🎯 核心特性
- **完整的GUI映射** - 与Python GUI的所有标签页一一对应
- **实时数据同步** - WebSocket实时通信
- **现代化UI** - Element Plus企业级组件
- **响应式设计** - 支持桌面和移动端
- **类型安全** - TypeScript支持

## 🔗 与Python后端的连接

### API代理配置
前端自动将API请求代理到Python后端：
```javascript
// vite.config.js
server: {
  proxy: {
    '/api': 'http://localhost:8000',
    '/ws': 'ws://localhost:8000'
  }
}
```

### WebSocket连接
实时数据通过WebSocket同步：
- 价格更新
- 持仓变化
- 风险指标
- 交易状态
- 系统日志

## 🎨 UI特性

### 设计风格
- **现代化渐变** - 专业的视觉效果
- **卡片布局** - 清晰的信息层次
- **状态指示器** - 直观的系统状态显示
- **动画效果** - 流畅的交互体验

### 响应式支持
- 桌面端优化 (1200px+)
- 平板端适配 (768px-1200px)
- 移动端支持 (< 768px)

## 🛠️ 开发工具

### 推荐的开发环境
- **VS Code** + Vue 3 插件
- **Chrome DevTools** 用于调试
- **Vue DevTools** 浏览器扩展

### 常用命令
```bash
# 开发模式
npm run dev

# 生产构建
npm run build

# 预览构建结果
npm run preview

# 代码检查
npm run lint
```

## 🐛 常见问题

### 1. 前端无法连接后端
- 确保Python后端运行在 http://localhost:8000
- 检查防火墙设置
- 查看浏览器控制台错误信息

### 2. 依赖安装失败
```bash
# 清除缓存重新安装
npm cache clean --force
rm -rf node_modules package-lock.json
npm install
```

### 3. 端口被占用
```bash
# 修改端口
npm run dev -- --port 3001
```

### 4. WebSocket连接失败
- 确保后端支持WebSocket
- 检查网络连接
- 查看浏览器网络面板

## 📞 技术支持

如遇到问题：
1. 查看浏览器控制台错误信息
2. 检查Python后端日志
3. 确认网络连接状态
4. 联系开发团队

## 🎉 开始使用

1. 启动Python后端服务
2. 运行Vue前端项目
3. 在浏览器中访问 http://localhost:3000
4. 开始配置和使用交易系统

祝您使用愉快！🚀
