"""
GUI交互性测试模块
测试GUI界面与后端代码的交互性
"""

import unittest
import asyncio
import tkinter as tk
from unittest.mock import Mock, AsyncMock, patch, MagicMock
import threading
import time

from tests import TEST_CONFIG, get_event_loop

class TestGUIInteraction(unittest.TestCase):
    """GUI交互性测试"""
    
    def setUp(self):
        """测试前准备"""
        self.loop = get_event_loop()
        
        # 创建测试用的Tkinter根窗口
        self.root = tk.Tk()
        self.root.withdraw()  # 隐藏窗口
        
        # 模拟异步Tkinter
        self.async_tk = Mock()
        self.async_tk.call_soon_threadsafe = Mock()
        self.async_tk.run_forever = AsyncMock()
    
    def tearDown(self):
        """测试后清理"""
        if hasattr(self, 'root'):
            self.root.destroy()
    
    @patch('gui.main_window.AsyncTradingController')
    @patch('gui.main_window.ExchangeManager')
    def test_main_window_initialization(self, mock_exchange_manager, mock_trading_controller):
        """测试主窗口初始化"""
        try:
            from gui.main_window import MainWindow
            
            # 模拟依赖
            mock_trading_controller.return_value = AsyncMock()
            mock_exchange_manager.return_value = Mock()
            
            # 创建主窗口
            main_window = MainWindow(self.root, self.async_tk)
            
            # 验证初始化
            self.assertIsNotNone(main_window)
            self.assertEqual(main_window.root, self.root)
            self.assertEqual(main_window.async_tk, self.async_tk)
            
        except ImportError as e:
            self.skipTest(f"GUI模块导入失败: {e}")
    
    def test_config_manager_integration(self):
        """测试配置管理器集成"""
        try:
            from gui.config_manager import ConfigManager
            
            config_manager = ConfigManager()
            
            # 测试配置验证
            test_config = {
                'api_key': 'test_key',
                'api_secret': 'test_secret',
                'exchange': 'okx',
                'symbol': 'BTC-USDT',
                'timeframe': '1m'
            }
            
            is_valid, message = config_manager.validate_config(test_config)
            self.assertIsInstance(is_valid, bool)
            self.assertIsInstance(message, str)
            
        except ImportError as e:
            self.skipTest(f"配置管理器导入失败: {e}")
    
    def test_async_gui_integration(self):
        """测试异步GUI集成"""
        try:
            from utils.async_gui import AsyncTkinter
            
            async_tk = AsyncTkinter()
            
            # 测试异步调用
            test_result = []
            
            def test_callback():
                test_result.append("callback_executed")
            
            # 模拟异步调用
            async_tk.call_soon_threadsafe(test_callback)
            
            # 验证回调机制存在
            self.assertIsNotNone(async_tk.call_soon_threadsafe)
            
        except ImportError as e:
            self.skipTest(f"异步GUI模块导入失败: {e}")
    
    @patch('gui.main_window.AsyncTradingController')
    def test_trading_controller_gui_binding(self, mock_trading_controller):
        """测试交易控制器与GUI的绑定"""
        try:
            from gui.main_window import MainWindow
            
            # 模拟交易控制器
            mock_controller = AsyncMock()
            mock_controller.trading_state = Mock()
            mock_controller.current_session = None
            mock_trading_controller.return_value = mock_controller
            
            # 创建主窗口
            main_window = MainWindow(self.root, self.async_tk)
            
            # 测试控制器绑定
            if hasattr(main_window, 'trading_controller'):
                self.assertIsNotNone(main_window.trading_controller)
            
        except ImportError as e:
            self.skipTest(f"GUI模块导入失败: {e}")
    
    def test_gui_event_handling(self):
        """测试GUI事件处理"""
        # 创建测试按钮
        button = tk.Button(self.root, text="Test Button")
        
        # 测试事件绑定
        click_count = [0]
        
        def on_click():
            click_count[0] += 1
        
        button.config(command=on_click)
        
        # 模拟点击
        button.invoke()
        
        self.assertEqual(click_count[0], 1)
    
    def test_gui_variable_binding(self):
        """测试GUI变量绑定"""
        # 创建Tkinter变量
        string_var = tk.StringVar(value="test_value")
        int_var = tk.IntVar(value=42)
        bool_var = tk.BooleanVar(value=True)
        
        # 测试变量值
        self.assertEqual(string_var.get(), "test_value")
        self.assertEqual(int_var.get(), 42)
        self.assertTrue(bool_var.get())
        
        # 测试变量更新
        string_var.set("new_value")
        int_var.set(100)
        bool_var.set(False)
        
        self.assertEqual(string_var.get(), "new_value")
        self.assertEqual(int_var.get(), 100)
        self.assertFalse(bool_var.get())
    
    def test_gui_widget_creation(self):
        """测试GUI组件创建"""
        # 创建各种组件
        frame = tk.Frame(self.root)
        label = tk.Label(frame, text="Test Label")
        entry = tk.Entry(frame)
        button = tk.Button(frame, text="Test Button")
        
        # 验证组件创建
        self.assertIsInstance(frame, tk.Frame)
        self.assertIsInstance(label, tk.Label)
        self.assertIsInstance(entry, tk.Entry)
        self.assertIsInstance(button, tk.Button)
        
        # 测试组件配置
        label.config(text="Updated Label")
        self.assertEqual(label.cget("text"), "Updated Label")
    
    @patch('gui.main_window.asyncio.create_task')
    def test_async_task_creation(self, mock_create_task):
        """测试异步任务创建"""
        try:
            from gui.main_window import MainWindow
            
            # 模拟异步任务
            mock_task = AsyncMock()
            mock_create_task.return_value = mock_task
            
            main_window = MainWindow(self.root, self.async_tk)
            
            # 验证异步任务创建机制存在
            self.assertIsNotNone(mock_create_task)
            
        except ImportError as e:
            self.skipTest(f"GUI模块导入失败: {e}")

class TestGUIDataFlow(unittest.TestCase):
    """GUI数据流测试"""
    
    def setUp(self):
        """测试前准备"""
        self.root = tk.Tk()
        self.root.withdraw()
    
    def tearDown(self):
        """测试后清理"""
        self.root.destroy()
    
    def test_config_data_flow(self):
        """测试配置数据流"""
        try:
            from gui.config_manager import ConfigManager
            
            config_manager = ConfigManager()
            
            # 测试默认配置
            default_config = config_manager.get_default_config()
            self.assertIsInstance(default_config, dict)
            self.assertIn('api_key', default_config)
            self.assertIn('api_secret', default_config)
            
            # 测试配置保存和加载
            test_config = {
                'api_key': 'test_key_123',
                'api_secret': 'test_secret_456',
                'exchange': 'okx'
            }
            
            # 模拟保存配置
            config_manager.current_config = test_config
            
            # 验证配置更新
            self.assertEqual(config_manager.current_config['api_key'], 'test_key_123')
            
        except ImportError as e:
            self.skipTest(f"配置管理器导入失败: {e}")
    
    def test_trading_state_display(self):
        """测试交易状态显示"""
        # 创建状态显示组件
        status_var = tk.StringVar(value="IDLE")
        status_label = tk.Label(self.root, textvariable=status_var)
        
        # 测试状态更新
        status_var.set("ANALYZING")
        self.assertEqual(status_label.cget("text"), "ANALYZING")
        
        status_var.set("TRADING")
        self.assertEqual(status_label.cget("text"), "TRADING")
    
    def test_real_time_data_update(self):
        """测试实时数据更新"""
        # 模拟实时数据显示
        price_var = tk.StringVar(value="0.00")
        pnl_var = tk.StringVar(value="0.00")
        
        price_label = tk.Label(self.root, textvariable=price_var)
        pnl_label = tk.Label(self.root, textvariable=pnl_var)
        
        # 模拟数据更新
        price_var.set("50000.50")
        pnl_var.set("+125.75")
        
        self.assertEqual(price_label.cget("text"), "50000.50")
        self.assertEqual(pnl_label.cget("text"), "+125.75")

class TestGUIErrorHandling(unittest.TestCase):
    """GUI错误处理测试"""
    
    def setUp(self):
        """测试前准备"""
        self.root = tk.Tk()
        self.root.withdraw()
    
    def tearDown(self):
        """测试后清理"""
        self.root.destroy()
    
    def test_invalid_input_handling(self):
        """测试无效输入处理"""
        # 创建输入验证函数
        def validate_number(value):
            try:
                float(value)
                return True
            except ValueError:
                return False
        
        # 测试有效输入
        self.assertTrue(validate_number("123.45"))
        self.assertTrue(validate_number("0"))
        
        # 测试无效输入
        self.assertFalse(validate_number("abc"))
        self.assertFalse(validate_number(""))
    
    def test_connection_error_display(self):
        """测试连接错误显示"""
        error_var = tk.StringVar(value="")
        error_label = tk.Label(self.root, textvariable=error_var, fg="red")
        
        # 模拟错误显示
        error_var.set("连接失败：网络超时")
        self.assertEqual(error_label.cget("text"), "连接失败：网络超时")
        
        # 清除错误
        error_var.set("")
        self.assertEqual(error_label.cget("text"), "")

if __name__ == '__main__':
    unittest.main()
