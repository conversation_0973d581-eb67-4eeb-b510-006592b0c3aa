"""
布林带核心计算逻辑
保留布林带指标计算的核心方法，作为参考实现
"""
import logging
import pandas as pd
import numpy as np
from typing import List, Optional
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class BollingerBandSignal(Enum):
    """布林带信号"""
    THREE_LINES_DOWN = "three_lines_down"    # 三线向下
    THREE_LINES_UP = "three_lines_up"        # 三线向上
    PRICE_TOUCH_UPPER = "price_touch_upper"  # 价格触及上轨
    PRICE_TOUCH_LOWER = "price_touch_lower"  # 价格触及下轨
    SQUEEZE = "squeeze"                      # 布林带收缩
    EXPANSION = "expansion"                  # 布林带扩张

@dataclass
class BollingerBandData:
    """布林带数据"""
    upper_band: float      # 上轨
    middle_band: float     # 中轨(移动平均线)
    lower_band: float      # 下轨
    percent_b: float       # %B指标
    band_width: float      # 带宽
    reliability: float     # 可靠性评分 (0.0-1.0)
    data_count: int        # 实际使用的数据点数
    required_count: int    # 需要的数据点数

class BollingerBandCalculator:
    """布林带计算器 - 核心计算逻辑"""

    def __init__(self, period: int = 20, std_dev: float = 2.0):
        self.period = period
        self.std_dev = std_dev
    
    def calculate_bollinger_bands(self, prices: List[float], min_periods: int = None) -> Optional[BollingerBandData]:
        """
        计算布林带指标（支持部分数据计算）
        
        Args:
            prices: 价格列表
            min_periods: 最小计算周期，默认为period的一半
            
        Returns:
            Optional[BollingerBandData]: 布林带数据（包含可靠性评估）
        """
        # 设置最小计算周期
        if min_periods is None:
            min_periods = max(10, self.period // 2)  # 至少10个，或者period的一半
        
        data_count = len(prices)
        required_count = self.period
        
        # 数据完全不足
        if data_count < min_periods:
            logger.warning(f"❌ 价格数据严重不足，需要至少{min_periods}个，当前{data_count}个")
            return None
        
        try:
            # 计算可靠性评分和实际使用的周期
            if data_count >= required_count:
                reliability = 1.0  # 完全可靠
                calculation_period = required_count
                logger.info(f"✅ 布林带数据充足: {data_count}/{required_count} (可靠性: 100%)")
            else:
                reliability = data_count / required_count  # 部分可靠
                calculation_period = data_count
                logger.warning(f"⚠️ 布林带数据不足: {data_count}/{required_count} (可靠性: {reliability*100:.1f}%)")
            
            # 转换为pandas Series，使用实际可用的数据
            price_series = pd.Series(prices[-calculation_period:])
            
            # 计算移动平均线(中轨)
            middle_band = price_series.mean()  # 使用简单平均，更适合少量数据
            
            # 计算标准差
            std_dev = price_series.std()
            
            # 根据可靠性调整标准差倍数（数据不足时收窄布林带）
            reliability_factor = 0.7 + 0.3 * reliability  # 0.7-1.0之间
            adjusted_std_multiplier = self.std_dev * reliability_factor
            
            # 计算上轨和下轨
            upper_band = middle_band + (adjusted_std_multiplier * std_dev)
            lower_band = middle_band - (adjusted_std_multiplier * std_dev)
            
            # 计算%B指标（当前价格在布林带中的位置）
            current_price = prices[-1]
            if upper_band != lower_band:
                percent_b = ((current_price - lower_band) / (upper_band - lower_band)) * 100
            else:
                percent_b = 50.0  # 如果上下轨相等，设为中性
            
            # 计算带宽（布林带宽度相对于中轨的百分比）
            if middle_band != 0:
                band_width = ((upper_band - lower_band) / middle_band) * 100
            else:
                band_width = 0.0
            
            # 记录详细信息
            logger.info(f"📊 布林带计算完成: 上轨={upper_band:.4f}, 中轨={middle_band:.4f}, 下轨={lower_band:.4f}")
            logger.info(f"📊 %B指标={percent_b:.1f}%, 带宽={band_width:.2f}%, 可靠性={reliability:.2f}")
            logger.info(f"📊 数据统计: 使用{calculation_period}个数据点 (需要{required_count}个)")
            logger.info(f"📊 计算参数: 标准差倍数={adjusted_std_multiplier:.3f} (原始={self.std_dev}, 调整因子={reliability_factor:.3f})")
            logger.info(f"📊 价格位置: 当前价格={current_price:.4f}, 距上轨={((current_price-upper_band)/current_price*100):+.2f}%, 距下轨={((current_price-lower_band)/current_price*100):+.2f}%")
            
            return BollingerBandData(
                upper_band=round(upper_band, 4),
                middle_band=round(middle_band, 4),
                lower_band=round(lower_band, 4),
                percent_b=round(percent_b, 1),
                band_width=round(band_width, 2),
                reliability=reliability,
                data_count=calculation_period,
                required_count=required_count
            )
            
        except Exception as e:
            logger.error(f"计算布林带异常: {e}")
            return None
    
    def analyze_bollinger_signal(self, bb_data: BollingerBandData, current_price: float) -> BollingerBandSignal:
        """
        分析布林带信号
        
        Args:
            bb_data: 布林带数据
            current_price: 当前价格
            
        Returns:
            BollingerBandSignal: 布林带信号
        """
        try:
            # 计算价格相对位置
            upper_distance = abs(current_price - bb_data.upper_band) / current_price
            lower_distance = abs(current_price - bb_data.lower_band) / current_price
            
            # 定义触及阈值（价格距离轨道的百分比）
            touch_threshold = 0.001  # 0.1%
            
            # 判断价格是否触及轨道
            if upper_distance <= touch_threshold:
                logger.info(f"🔴 价格触及上轨: {current_price:.4f} ≈ {bb_data.upper_band:.4f}")
                return BollingerBandSignal.PRICE_TOUCH_UPPER
            elif lower_distance <= touch_threshold:
                logger.info(f"🔵 价格触及下轨: {current_price:.4f} ≈ {bb_data.lower_band:.4f}")
                return BollingerBandSignal.PRICE_TOUCH_LOWER
            
            # 判断布林带收缩/扩张
            if bb_data.band_width < 1.0:  # 带宽小于1%
                logger.info(f"🔸 布林带收缩: 带宽={bb_data.band_width:.2f}%")
                return BollingerBandSignal.SQUEEZE
            elif bb_data.band_width > 3.0:  # 带宽大于3%
                logger.info(f"🔹 布林带扩张: 带宽={bb_data.band_width:.2f}%")
                return BollingerBandSignal.EXPANSION
            
            # 默认返回触及下轨(适合逆势做多)
            return BollingerBandSignal.PRICE_TOUCH_LOWER
            
        except Exception as e:
            logger.error(f"分析布林带信号异常: {e}")
            return BollingerBandSignal.PRICE_TOUCH_LOWER
    
    def calculate_percent_b_signal(self, percent_b: float) -> str:
        """
        基于%B指标计算开仓信号
        
        Args:
            percent_b: %B指标值
            
        Returns:
            str: 开仓方向 ("long", "short", "hold")
        """
        if percent_b < 30:
            return "long"   # 做多信号
        elif percent_b > 70:
            return "short"  # 做空信号
        else:
            return "hold"   # 中性，不开仓
