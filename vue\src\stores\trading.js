import { defineStore } from 'pinia'
import { ref, reactive } from 'vue'
import api from '@/utils/api'

export const useTradingStore = defineStore('trading', () => {
  // 交易状态
  const isRunning = ref(false)
  const tradingState = ref('idle')
  const currentSession = ref(null)
  const activeSessions = ref(0)
  const totalSessions = ref(0)
  const emergencyStop = ref(false)
  
  // 持仓信息
  const positions = ref([])
  const totalPnl = ref(0)
  const totalMargin = ref(0)
  
  // 交易记录
  const tradeHistory = ref([])
  const addPositionHistory = ref([])
  
  // 风险指标
  const riskMetrics = reactive({
    liquidationDistance: 0,
    riskLevel: 'safe',
    marginRatio: 0,
    maxDrawdown: 0
  })
  
  // 加载状态
  const loading = ref(false)
  const error = ref(null)
  
  // 获取交易状态
  const getTradingStatus = async () => {
    try {
      const response = await api.get('/api/trading/status')
      if (response.data.success) {
        const data = response.data.data
        isRunning.value = data.is_running
        tradingState.value = data.trading_state
        currentSession.value = data.current_session
        activeSessions.value = data.active_sessions
        totalSessions.value = data.total_sessions
        emergencyStop.value = data.emergency_stop
      }
    } catch (err) {
      error.value = err.message
      console.error('获取交易状态失败:', err)
    }
  }
  
  // 启动交易
  const startTrading = async (config) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await api.post('/api/trading/start', config)
      if (response.data.success) {
        await getTradingStatus()
        ElMessage.success('交易启动成功')
        return true
      } else {
        throw new Error(response.data.error || '启动交易失败')
      }
    } catch (err) {
      error.value = err.message
      ElMessage.error(`启动交易失败: ${err.message}`)
      return false
    } finally {
      loading.value = false
    }
  }
  
  // 停止交易
  const stopTrading = async () => {
    loading.value = true
    error.value = null
    
    try {
      const response = await api.post('/api/trading/stop')
      if (response.data.success) {
        await getTradingStatus()
        ElMessage.success('交易停止成功')
        return true
      } else {
        throw new Error(response.data.error || '停止交易失败')
      }
    } catch (err) {
      error.value = err.message
      ElMessage.error(`停止交易失败: ${err.message}`)
      return false
    } finally {
      loading.value = false
    }
  }
  
  // 紧急停止
  const emergencyStopTrading = async () => {
    loading.value = true
    error.value = null
    
    try {
      const response = await api.post('/api/trading/emergency-stop')
      if (response.data.success) {
        await getTradingStatus()
        ElMessage.warning('紧急停止执行成功')
        return true
      } else {
        throw new Error(response.data.error || '紧急停止失败')
      }
    } catch (err) {
      error.value = err.message
      ElMessage.error(`紧急停止失败: ${err.message}`)
      return false
    } finally {
      loading.value = false
    }
  }
  
  // 获取持仓信息
  const getPositions = async () => {
    try {
      const response = await api.get('/api/trading/positions')
      if (response.data.success) {
        positions.value = response.data.data.positions || []
        totalPnl.value = response.data.data.total_pnl || 0
        totalMargin.value = response.data.data.total_margin || 0
      }
    } catch (err) {
      console.error('获取持仓信息失败:', err)
    }
  }
  
  // 获取交易历史
  const getTradeHistory = async (limit = 100) => {
    try {
      const response = await api.get('/api/trading/history', {
        params: { limit }
      })
      if (response.data.success) {
        tradeHistory.value = response.data.data || []
      }
    } catch (err) {
      console.error('获取交易历史失败:', err)
    }
  }
  
  // 获取加仓历史
  const getAddPositionHistory = async (limit = 100) => {
    try {
      const response = await api.get('/api/trading/add-position-history', {
        params: { limit }
      })
      if (response.data.success) {
        addPositionHistory.value = response.data.data || []
      }
    } catch (err) {
      console.error('获取加仓历史失败:', err)
    }
  }
  
  // 获取风险指标
  const getRiskMetrics = async () => {
    try {
      const response = await api.get('/api/trading/risk-metrics')
      if (response.data.success) {
        Object.assign(riskMetrics, response.data.data)
      }
    } catch (err) {
      console.error('获取风险指标失败:', err)
    }
  }
  
  // 手动平仓
  const closePosition = async (symbol, size = null) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await api.post('/api/trading/close-position', {
        symbol,
        size
      })
      if (response.data.success) {
        await getPositions()
        ElMessage.success('平仓操作成功')
        return true
      } else {
        throw new Error(response.data.error || '平仓操作失败')
      }
    } catch (err) {
      error.value = err.message
      ElMessage.error(`平仓操作失败: ${err.message}`)
      return false
    } finally {
      loading.value = false
    }
  }
  
  // 更新交易状态（WebSocket）
  const updateTradingStatus = (data) => {
    if (data.is_running !== undefined) isRunning.value = data.is_running
    if (data.trading_state) tradingState.value = data.trading_state
    if (data.current_session) currentSession.value = data.current_session
    if (data.active_sessions !== undefined) activeSessions.value = data.active_sessions
    if (data.emergency_stop !== undefined) emergencyStop.value = data.emergency_stop
  }
  
  // 更新持仓信息（WebSocket）
  const updatePositions = (data) => {
    if (data.positions) positions.value = data.positions
    if (data.total_pnl !== undefined) totalPnl.value = data.total_pnl
    if (data.total_margin !== undefined) totalMargin.value = data.total_margin
  }
  
  // 更新风险指标（WebSocket）
  const updateRiskMetrics = (data) => {
    Object.assign(riskMetrics, data)
  }
  
  return {
    // 状态
    isRunning,
    tradingState,
    currentSession,
    activeSessions,
    totalSessions,
    emergencyStop,
    positions,
    totalPnl,
    totalMargin,
    tradeHistory,
    addPositionHistory,
    riskMetrics,
    loading,
    error,
    
    // 方法
    getTradingStatus,
    startTrading,
    stopTrading,
    emergencyStopTrading,
    getPositions,
    getTradeHistory,
    getAddPositionHistory,
    getRiskMetrics,
    closePosition,
    updateTradingStatus,
    updatePositions,
    updateRiskMetrics
  }
})
