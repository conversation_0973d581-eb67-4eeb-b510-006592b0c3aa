#!/usr/bin/env python3
"""
BitV MACD智能加仓交易系统 - 快速配置脚本
帮助用户快速设置API密钥和交易参数
"""

import os
import sys

def print_banner():
    """打印欢迎横幅"""
    print("=" * 60)
    print("🚀 BitV MACD智能加仓交易系统 - 配置向导")
    print("=" * 60)
    print()

def get_user_input(prompt, default="", required=True):
    """获取用户输入"""
    if default:
        full_prompt = f"{prompt} (默认: {default}): "
    else:
        full_prompt = f"{prompt}: "
    
    while True:
        value = input(full_prompt).strip()
        if not value and default:
            return default
        elif not value and required:
            print("❌ 此项为必填项，请输入有效值")
            continue
        else:
            return value

def choose_exchange():
    """选择交易所"""
    print("📊 选择交易所:")
    print("1. OKX (推荐)")
    print("2. Gate.io")
    
    while True:
        choice = input("请选择 (1 或 2): ").strip()
        if choice == "1":
            return "okx"
        elif choice == "2":
            return "gateio"
        else:
            print("❌ 请输入 1 或 2")

def get_api_credentials(exchange):
    """获取API凭证"""
    print(f"\n🔑 配置 {exchange.upper()} API密钥:")
    print("⚠️  请确保你已经在交易所创建了API密钥")
    
    if exchange == "okx":
        print("📝 OKX API获取方式: https://www.okx.com/account/my-api")
        api_key = get_user_input("API Key")
        api_secret = get_user_input("API Secret")
        passphrase = get_user_input("Passphrase")
        return api_key, api_secret, passphrase
    else:
        print("📝 Gate.io API获取方式: https://www.gate.io/myaccount/apiv4keys")
        api_key = get_user_input("API Key")
        api_secret = get_user_input("API Secret")
        return api_key, api_secret, ""

def get_trading_config():
    """获取交易配置"""
    print("\n⚙️  交易参数配置:")
    
    leverage = get_user_input("杠杆倍数 (1-100)", "10")
    initial_margin = get_user_input("初始保证金 (USDT)", "100.0")
    max_add_times = get_user_input("最大加仓次数", "3")
    
    print("\n🏖️  运行模式:")
    print("1. 沙盒模式 (测试环境，推荐)")
    print("2. 实盘模式 (真实交易)")
    
    while True:
        mode_choice = input("请选择模式 (1 或 2): ").strip()
        if mode_choice == "1":
            sandbox = "true"
            break
        elif mode_choice == "2":
            print("⚠️  警告：实盘模式将使用真实资金进行交易！")
            confirm = input("确认使用实盘模式？(yes/no): ").strip().lower()
            if confirm in ["yes", "y"]:
                sandbox = "false"
                break
            else:
                continue
        else:
            print("❌ 请输入 1 或 2")
    
    return leverage, initial_margin, max_add_times, sandbox

def create_env_file(exchange, api_key, api_secret, passphrase, leverage, initial_margin, max_add_times, sandbox):
    """创建.env文件"""
    
    # 根据交易所设置交易对格式
    if exchange == "okx":
        symbol = "BTC-USDT-SWAP"
    else:
        symbol = "BTC_USDT"
    
    env_content = f"""# BitV MACD智能加仓交易系统 - 环境变量配置
# 由配置向导自动生成于 {os.popen('date').read().strip()}

# 交易所配置
EXCHANGE={exchange}
API_KEY={api_key}
API_SECRET={api_secret}"""

    if passphrase:
        env_content += f"\nPASSPHRASE={passphrase}"
    
    env_content += f"""
SANDBOX={sandbox}

# 交易参数
SYMBOL={symbol}
TIMEFRAME=30m
LEVERAGE={leverage}
INITIAL_MARGIN={initial_margin}
ALERT_POINTS=0.5
ADD_POSITION_TYPE=half
MAX_ADD_TIMES={max_add_times}

# MACD策略参数
MACD_FAST=12
MACD_SLOW=26
MACD_SIGNAL=9

# 系统配置
LOG_LEVEL=INFO
LOG_FILE=trading.log
PRICE_CHECK_INTERVAL=5
POSITION_CHECK_INTERVAL=10

# 安全提醒：
# - 不要将此文件上传到公共代码仓库
# - 定期更换API密钥
# - 只授予必要的交易权限
"""
    
    # 写入.env文件
    with open('.env', 'w', encoding='utf-8') as f:
        f.write(env_content)
    
    print("✅ .env文件创建成功！")

def test_config():
    """测试配置"""
    print("\n🧪 测试配置...")
    
    try:
        from config import TradingConfig
        TradingConfig.validate_config()
        print("✅ 配置验证通过！")
        
        print("\n📊 当前配置摘要:")
        print(f"   交易所: {TradingConfig.EXCHANGE}")
        print(f"   交易对: {TradingConfig.SYMBOL}")
        print(f"   杠杆: {TradingConfig.LEVERAGE}x")
        print(f"   初始保证金: {TradingConfig.INITIAL_MARGIN} USDT")
        print(f"   沙盒模式: {TradingConfig.SANDBOX}")
        print(f"   API Key: {TradingConfig.API_KEY[:8]}...")
        
        return True
    except Exception as e:
        print(f"❌ 配置验证失败: {e}")
        return False

def main():
    """主函数"""
    print_banner()
    
    # 检查是否已有配置
    if os.path.exists('.env'):
        print("⚠️  发现现有的.env配置文件")
        overwrite = input("是否要重新配置？(yes/no): ").strip().lower()
        if overwrite not in ["yes", "y"]:
            print("👋 配置已取消")
            return
    
    try:
        # 选择交易所
        exchange = choose_exchange()
        
        # 获取API凭证
        if exchange == "okx":
            api_key, api_secret, passphrase = get_api_credentials(exchange)
        else:
            api_key, api_secret, passphrase = get_api_credentials(exchange)
        
        # 获取交易配置
        leverage, initial_margin, max_add_times, sandbox = get_trading_config()
        
        # 创建配置文件
        print("\n📝 正在创建配置文件...")
        create_env_file(exchange, api_key, api_secret, passphrase, leverage, initial_margin, max_add_times, sandbox)
        
        # 测试配置
        if test_config():
            print("\n🎉 配置完成！现在可以运行交易系统了:")
            print("   python main.py")
            print("\n📚 其他有用命令:")
            print("   python setup_config.py  # 重新配置")
            print("   python -c \"from config import TradingConfig; TradingConfig.validate_config()\"  # 验证配置")
        else:
            print("\n❌ 配置有误，请检查后重试")
            
    except KeyboardInterrupt:
        print("\n\n👋 配置已取消")
    except Exception as e:
        print(f"\n❌ 配置过程中出现错误: {e}")

if __name__ == "__main__":
    main()
