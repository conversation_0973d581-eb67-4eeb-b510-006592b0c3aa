<?php
/**
 * 交易控制器
 * 
 * @description 处理交易启动、停止、监控等核心交易功能
 */

require_once 'BaseController.php';

class TradingController extends BaseController {

    /**
     * 交易控制主页
     */
    public function index() {
        try {
            // 检查API连接
            $this->checkApiConnection();
            
            // 获取当前交易状态
            $tradingStatus = $this->getTradingStatus();
            
            // 获取用户配置
            $userConfig = $this->getUserConfig();
            
            // 获取支持的配置选项
            $supportedExchanges = $GLOBALS['SUPPORTED_EXCHANGES'];
            $supportedStrategies = $GLOBALS['SUPPORTED_STRATEGIES'];
            
            $this->render('trading/index', [
                'title' => '交易控制中心 - ' . SYSTEM_NAME,
                'trading_status' => $tradingStatus,
                'user_config' => $userConfig,
                'supported_exchanges' => $supportedExchanges,
                'supported_strategies' => $supportedStrategies
            ]);
            
        } catch (Exception $e) {
            $this->render('trading/error', [
                'title' => '交易控制错误',
                'error_message' => $e->getMessage()
            ]);
        }
    }

    /**
     * 启动交易
     */
    public function start() {
        try {
            $this->validateCSRF();
            $this->checkApiConnection();
            
            // 获取交易配置
            $config = $this->prepareTradingConfig();
            
            // 验证配置
            $this->validateTradingConfig($config);
            
            // 调用Python API启动交易
            $response = $this->api->startTrading($config);
            
            if ($response['success']) {
                // 记录交易会话
                $sessionId = $this->createTradingSession($config, $response['data']);
                
                // 记录操作日志
                $this->logAction('start_trading', [
                    'session_id' => $sessionId,
                    'exchange' => $config['exchange'],
                    'symbol' => $config['symbol'],
                    'strategy' => $config['strategy']
                ]);
                
                $this->success('交易启动成功', [
                    'session_id' => $sessionId,
                    'trading_data' => $response['data']
                ]);
            } else {
                $this->error('交易启动失败: ' . ($response['data']['error'] ?? '未知错误'));
            }
            
        } catch (Exception $e) {
            $this->error('交易启动失败: ' . $e->getMessage());
        }
    }

    /**
     * 停止交易
     */
    public function stop() {
        try {
            $this->validateCSRF();
            $this->checkApiConnection();
            
            // 调用Python API停止交易
            $response = $this->api->stopTrading();
            
            if ($response['success']) {
                // 更新交易会话状态
                $this->updateActiveSessions('stopped');
                
                // 记录操作日志
                $this->logAction('stop_trading');
                
                $this->success('交易已停止', $response['data']);
            } else {
                $this->error('停止交易失败: ' . ($response['data']['error'] ?? '未知错误'));
            }
            
        } catch (Exception $e) {
            $this->error('停止交易失败: ' . $e->getMessage());
        }
    }

    /**
     * 紧急停止
     */
    public function emergencyStop() {
        try {
            $this->validateCSRF();
            $this->checkApiConnection();
            
            // 调用Python API紧急停止
            $response = $this->api->emergencyStop();
            
            if ($response['success']) {
                // 更新所有活跃会话为错误状态
                $this->updateActiveSessions('error');
                
                // 记录紧急停止日志
                $this->logAction('emergency_stop', ['reason' => 'user_triggered']);
                
                $this->success('紧急停止执行成功', $response['data']);
            } else {
                $this->error('紧急停止失败: ' . ($response['data']['error'] ?? '未知错误'));
            }
            
        } catch (Exception $e) {
            $this->error('紧急停止失败: ' . $e->getMessage());
        }
    }

    /**
     * 手动平仓
     */
    public function closePosition() {
        try {
            $this->validateCSRF();
            $this->validateRequired(['symbol']);
            $this->checkApiConnection();
            
            $symbol = $this->request['symbol'];
            
            // 调用Python API平仓
            $response = $this->api->closePosition($symbol);
            
            if ($response['success']) {
                // 记录平仓操作
                $this->logAction('close_position', ['symbol' => $symbol]);
                
                $this->success('平仓操作成功', $response['data']);
            } else {
                $this->error('平仓操作失败: ' . ($response['data']['error'] ?? '未知错误'));
            }
            
        } catch (Exception $e) {
            $this->error('平仓操作失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取交易状态
     */
    public function getStatus() {
        try {
            $this->checkApiConnection();
            
            $response = $this->api->getTradingStatus();
            
            if ($response['success']) {
                $this->success('获取交易状态成功', $response['data']);
            } else {
                $this->error('获取交易状态失败');
            }
            
        } catch (Exception $e) {
            $this->error('获取交易状态失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新交易配置
     */
    public function updateConfig() {
        try {
            $this->validateCSRF();
            $this->checkApiConnection();
            
            // 准备配置数据
            $config = $this->prepareTradingConfig();
            
            // 验证配置
            $this->validateTradingConfig($config);
            
            // 调用Python API更新配置
            $response = $this->api->updateTradingConfig($config);
            
            if ($response['success']) {
                // 保存到数据库
                $this->saveUserConfig('trading_config', $config);
                
                // 记录操作日志
                $this->logAction('update_trading_config', $config);
                
                $this->success('交易配置更新成功', $response['data']);
            } else {
                $this->error('配置更新失败: ' . ($response['data']['error'] ?? '未知错误'));
            }
            
        } catch (Exception $e) {
            $this->error('配置更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 准备交易配置
     */
    private function prepareTradingConfig() {
        // 从请求中获取配置，如果没有则使用用户保存的配置
        $userConfig = $this->getUserConfig('trading_config') ?? [];
        
        return [
            'exchange' => $this->request['exchange'] ?? $userConfig['exchange'] ?? 'okx',
            'symbol' => $this->request['symbol'] ?? $userConfig['symbol'] ?? 'BTC-USDT-SWAP',
            'leverage' => intval($this->request['leverage'] ?? $userConfig['leverage'] ?? 10),
            'initial_margin' => floatval($this->request['initial_margin'] ?? $userConfig['initial_margin'] ?? 100),
            'timeframe' => $this->request['timeframe'] ?? $userConfig['timeframe'] ?? '30m',
            'strategy' => $this->request['strategy'] ?? 'macd',
            
            // API配置
            'api_key' => $this->request['api_key'] ?? $userConfig['api_key'] ?? '',
            'api_secret' => $this->request['api_secret'] ?? $userConfig['api_secret'] ?? '',
            'passphrase' => $this->request['passphrase'] ?? $userConfig['passphrase'] ?? '',
            'sandbox' => isset($this->request['sandbox']) ? $this->request['sandbox'] === 'true' : ($userConfig['sandbox'] ?? false),
            
            // MACD策略参数
            'macd_enabled' => isset($this->request['macd_enabled']) ? true : ($userConfig['macd_enabled'] ?? true),
            'macd_fast' => intval($this->request['macd_fast'] ?? $userConfig['macd_fast'] ?? 12),
            'macd_slow' => intval($this->request['macd_slow'] ?? $userConfig['macd_slow'] ?? 26),
            'macd_signal' => intval($this->request['macd_signal'] ?? $userConfig['macd_signal'] ?? 9),
            'min_signal_strength' => floatval($this->request['min_signal_strength'] ?? $userConfig['min_signal_strength'] ?? 0.3),
            
            // 加仓策略参数
            'max_add_times' => intval($this->request['max_add_times'] ?? $userConfig['max_add_times'] ?? 3),
            'add_position_types' => $this->request['add_position_types'] ?? $userConfig['add_position_types'] ?? ['half'],
            'alert_points' => floatval($this->request['alert_points'] ?? $userConfig['alert_points'] ?? 1.5),
            
            // 风险管理参数
            'risk_control_enabled' => isset($this->request['risk_control_enabled']) ? true : ($userConfig['risk_control_enabled'] ?? true),
            'emergency_stop' => isset($this->request['emergency_stop']) ? true : ($userConfig['emergency_stop'] ?? false)
        ];
    }

    /**
     * 验证交易配置
     */
    private function validateTradingConfig($config) {
        // 验证交易所配置
        $this->validateExchangeConfig($config);
        
        // 验证交易参数
        $this->validateTradingParams($config);
        
        // 验证API密钥
        if (empty($config['api_key']) || empty($config['api_secret'])) {
            throw new Exception('API密钥和密钥不能为空');
        }
        
        // 验证交易对
        global $SUPPORTED_EXCHANGES;
        $exchange = $SUPPORTED_EXCHANGES[$config['exchange']];
        if (!isset($exchange['supported_symbols'][$config['symbol']])) {
            throw new Exception('不支持的交易对: ' . $config['symbol']);
        }
    }

    /**
     * 创建交易会话记录
     */
    private function createTradingSession($config, $tradingData) {
        $sessionId = 'session_' . uniqid() . '_' . time();
        $userId = Session::getUserId() ?? 'default';
        
        $this->db->insert('trading_sessions', [
            'session_id' => $sessionId,
            'user_id' => $userId,
            'exchange' => $config['exchange'],
            'symbol' => $config['symbol'],
            'strategy' => $config['strategy'],
            'initial_margin' => $config['initial_margin'],
            'leverage' => $config['leverage'],
            'status' => 'active',
            'metadata' => json_encode([
                'config' => $config,
                'trading_data' => $tradingData
            ])
        ]);
        
        return $sessionId;
    }

    /**
     * 更新活跃会话状态
     */
    private function updateActiveSessions($status) {
        $userId = Session::getUserId() ?? 'default';
        
        $this->db->update(
            'trading_sessions',
            [
                'status' => $status,
                'end_time' => date('Y-m-d H:i:s')
            ],
            'user_id = ? AND status = "active"',
            [$userId]
        );
    }

    /**
     * 获取交易状态
     */
    private function getTradingStatus() {
        try {
            $response = $this->api->getTradingStatus();
            return $response['success'] ? $response['data'] : null;
        } catch (Exception $e) {
            error_log('获取交易状态失败: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 获取活跃交易会话
     */
    public function getActiveSessions() {
        try {
            $userId = Session::getUserId() ?? 'default';
            
            $sessions = $this->db->fetchAll(
                'SELECT * FROM trading_sessions 
                 WHERE user_id = ? AND status = "active" 
                 ORDER BY start_time DESC',
                [$userId]
            );
            
            $this->success('获取活跃会话成功', $sessions);
            
        } catch (Exception $e) {
            $this->error('获取活跃会话失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取交易会话详情
     */
    public function getSessionDetails() {
        try {
            $this->validateRequired(['session_id']);
            
            $sessionId = $this->request['session_id'];
            $userId = Session::getUserId() ?? 'default';
            
            $session = $this->db->fetch(
                'SELECT * FROM trading_sessions WHERE session_id = ? AND user_id = ?',
                [$sessionId, $userId]
            );
            
            if (!$session) {
                $this->error('交易会话不存在', 404);
            }
            
            // 获取相关的交易记录
            $trades = $this->db->fetchAll(
                'SELECT * FROM trading_records WHERE session_id = ? ORDER BY timestamp DESC',
                [$sessionId]
            );
            
            $this->success('获取会话详情成功', [
                'session' => $session,
                'trades' => $trades
            ]);
            
        } catch (Exception $e) {
            $this->error('获取会话详情失败: ' . $e->getMessage());
        }
    }
}
?>
