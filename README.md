# MACD智能加仓交易系统

基于OKX和Gate.io交易所的异步MACD智能加仓交易系统，支持自动开仓、持仓监控、智能加仓等功能。

## 🚀 核心功能

### 1. MACD趋势分析
- 基于30分钟K线数据计算MACD指标
- 智能判断做多/做空信号
- 支持自定义MACD参数（快线12、慢线26、信号线9）

### 2. 自动开仓策略
- 根据MACD趋势自动开仓
- 支持多仓和空仓
- 可配置信号强度阈值

### 3. 智能加仓系统
- 当价格接近强平价时自动加仓
- 支持等量加仓和半量加仓
- 可设置最大加仓次数和间隔时间

### 4. 实时监控系统
- 持仓风险实时监控
- 强平价距离计算
- 价格预警和趋势变化检测

### 5. 交易所完全分离
- 支持OKX和Gate.io交易所
- 统一的异步接口设计
- 可无缝切换交易所

## 📋 系统要求

- Python 3.8+
- 支持的交易所：OKX、Gate.io
- 网络连接（用于API调用和WebSocket连接）

## 🛠️ 安装配置

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 配置API密钥
复制 `.env.example` 为 `.env` 并填入你的API信息：

```bash
cp .env.example .env
```

编辑 `.env` 文件：
```env
# 交易所选择 (okx 或 gateio)
EXCHANGE=okx

# API配置
API_KEY=your_api_key_here
API_SECRET=your_api_secret_here
PASSPHRASE=your_passphrase_here  # 仅OKX需要

# 是否使用沙盒环境
SANDBOX=True

# 交易参数
SYMBOL=BTC/USDT
LEVERAGE=10
INITIAL_MARGIN=100.0
ALERT_POINTS=0.5
ADD_POSITION_TYPE=half
```

### 3. 参数说明

| 参数 | 说明 | 默认值 |
|------|------|--------|
| EXCHANGE | 交易所 (okx/gateio) | okx |
| SYMBOL | 交易对 | BTC/USDT |
| LEVERAGE | 杠杆倍数 | 10 |
| INITIAL_MARGIN | 初始保证金(USDT) | 100.0 |
| ALERT_POINTS | 预警点数 | 0.5 |
| ADD_POSITION_TYPE | 加仓类型 (等量/半量/自定义) | half |

## 🚀 运行系统

### 方式一：图形界面版本（推荐）
```bash
python gui_main.py
```

### 方式二：命令行版本
```bash
python main.py
```

## 🖥️ 图形界面使用说明

### 界面功能
1. **配置设置**：在"配置设置"标签页中设置API密钥和基础交易参数
2. **策略配置**：在"策略配置"标签页中配置MACD策略参数和其他技术指标
3. **资金设置**：在"资金设置"标签页中配置风险管理参数和紧急控制
4. **交易控制**：在"交易控制"标签页中启动/停止交易和监控状态
5. **实时监控**：在"实时监控"标签页中查看持仓和加仓记录
6. **系统日志**：在"系统日志"标签页中查看详细的运行日志

### 首次使用步骤
1. 启动GUI：`python gui_main.py`
2. 在"配置设置"页面填入API信息和基础交易参数
3. 在"策略配置"页面设置MACD参数和加仓策略
4. 点击"测试连接"验证配置
5. 点击"保存配置"保存设置
6. 切换到"交易控制"页面
7. 点击"开始交易"启动自动交易

### 界面改进特性
- **多选加仓类型**：支持同时选择多种加仓策略（等量、半量、自定义）
- **策略配置页面**：独立的策略参数配置，支持MACD参数精细调节
- **多时间周期确认**：支持多个时间周期的MACD趋势一致性验证
- **资金风险管理**：专业的风险控制参数和紧急停止功能
- **用户友好界面**：面向小白用户的详细说明和操作指引
- **卡片式设计**：为未来添加RSI、布林带等策略预留扩展空间
- **向后兼容**：自动迁移旧版本配置文件
- **实时验证**：参数修改时立即验证有效性

### 系统运行流程

1. **初始化阶段**
   - 连接交易所
   - 验证API密钥
   - 初始化各个模块

2. **分析阶段**
   - 获取30分钟K线数据
   - 计算MACD指标
   - 判断开仓信号

3. **开仓阶段**
   - 根据MACD信号开多/空仓
   - 设置杠杆
   - 记录开仓信息

4. **监控阶段**
   - 实时监控持仓状态
   - 计算强平价距离
   - 监控价格变化

5. **加仓阶段**
   - 当价格接近预警价格时触发
   - 根据配置执行等量或半量加仓
   - 更新持仓信息

## 📊 风险管理

### 1. 强平价监控
- 实时计算距离强平价的距离
- 风险等级分类：安全、警告、危险、极危险
- 自动预警机制

### 2. 加仓风险控制
- 最大加仓次数限制
- 加仓间隔时间控制
- 账户余额检查

### 3. 止损机制
- 可手动平仓
- 支持紧急止损

## 🔧 高级配置

### 时间周期配置
系统支持多种时间周期用于MACD计算：

| 时间周期 | 说明 | OKX支持 | Gate.io支持 |
|---------|------|---------|-------------|
| 1m | 1分钟 | ✓ | ✓ |
| 5m | 5分钟 | ✓ | ✓ |
| 10m | 10分钟 | ✓ | ✓ |
| 15m | 15分钟 | ✓ | ✓ |
| 30m | 30分钟 | ✓ | ✓ |
| 1h | 1小时 | ✓ | ✓ |
| 4h | 4小时 | ✓ | ✓ |
| 1d | 1日 | ✓ | ✓ |
| 15d | 15日 | ✓ | ✗ (用1日替代) |
| 1M | 1月 | ✓ | ✗ (用1日替代) |

**注意**：Gate.io不支持15日和1月周期，系统会自动使用1日周期替代。

### 加仓类型配置
系统支持三种加仓类型，用户可根据风险偏好选择：

| 加仓类型 | 中文名称 | 说明 | 风险等级 | 适用场景 |
|---------|----------|------|----------|----------|
| equal | 等量加仓 | 使用与初始开仓相同的保证金金额 | 高 | 强趋势市场 |
| half | 半量加仓 | 使用初始保证金的一半金额 | 中 | 一般市场（推荐） |
| custom | 自定义比例 | 可设置自定义的加仓比例 | 可调 | 特定策略需求 |

**使用建议**：
- **新手用户**：建议选择"半量加仓"，风险相对可控
- **有经验用户**：可根据市场情况选择"等量加仓"
- **策略用户**：可使用"自定义比例"实现特定的风险管理策略

### 多时间周期趋势确认
系统支持多时间周期MACD趋势一致性验证，提高交易信号的可靠性：

| 确认周期 | 说明 | 适用主周期 | 效果 |
|---------|------|------------|------|
| 15分钟确认 | 15分钟MACD趋势确认 | 1分钟、5分钟、10分钟 | 过滤短期噪音 |
| 30分钟确认 | 30分钟MACD趋势确认 | 5分钟、10分钟、15分钟 | 中短期趋势确认 |
| 1小时确认 | 1小时MACD趋势确认 | 15分钟、30分钟 | 中期趋势确认 |
| 2小时确认 | 2小时MACD趋势确认 | 30分钟、1小时 | 强化中期确认 |
| 4小时确认 | 4小时MACD趋势确认 | 1小时、2小时 | 长期趋势确认 |
| 1日确认 | 1日MACD趋势确认 | 2小时、4小时 | 主要趋势确认 |

**工作原理**：
- 系统首先分析主时间周期的MACD信号
- 然后检查所有选中的确认时间周期的MACD趋势方向
- 只有当所有时间周期的趋势方向一致时才执行开仓
- 确认时间周期必须大于主时间周期

**配置建议**：
- **短期交易**（5分钟主周期）：选择15分钟、30分钟确认
- **中期交易**（30分钟主周期）：选择1小时、4小时确认
- **长期交易**（1小时主周期）：选择4小时、1日确认
- **保守策略**：选择更多确认周期，信号更可靠但机会更少
- **激进策略**：选择较少确认周期，机会更多但风险更高

### 资金风险管理
系统提供全面的资金风险管理功能，帮助用户控制交易风险：

| 风险参数 | 说明 | 默认值 | 建议范围 | 作用 |
|---------|------|--------|----------|------|
| 最大红开 | 最大开仓数量限制 | 10个 | 5-20个 | 防止过度开仓 |
| 保证金率 | 保证金使用比例 | 0.8 | 0.5-0.9 | 控制资金利用率 |
| 最大合仓比例 | 单品种最大仓位比例 | 0.1 | 0.05-0.2 | 分散投资风险 |
| 日盈亏限制 | 每日盈亏限制比例 | 0.2 | 0.1-0.3 | 控制单日风险 |
| 最大开仓数量 | 单次开仓数量限制 | 4个 | 2-10个 | 限制单次风险 |

**紧急控制功能**：
- **紧急停止**：在市场异常波动时立即停止所有交易操作
- **恢复交易**：确认市场恢复正常后重新启用交易功能
- **状态监控**：实时显示紧急控制状态和风险参数应用情况

**风险控制策略**：
- **新手用户**：建议保持默认风险参数，启用所有风险控制
- **保守用户**：降低保证金率(0.5-0.6)和最大合仓比例(0.05-0.08)
- **激进用户**：可适当提高参数，但最大合仓比例不建议超过0.2
- **专业用户**：根据市场情况和个人风险承受能力灵活调整

**风险管理原则**：
1. **分散风险**：通过最大合仓比例限制单一品种风险
2. **控制杠杆**：通过保证金率控制整体资金杠杆
3. **限制损失**：通过日盈亏限制控制单日最大损失
4. **防止过度**：通过开仓数量限制防止冲动交易
5. **紧急应对**：通过紧急停止功能应对极端市场情况

### 用户友好界面设计
系统专门为完全没有量化交易经验的小白用户设计了友好的界面：

#### 🎯 **面向新手的设计理念**
- **通俗易懂的语言**：避免专业术语，多用生活化的比喻
- **详细的操作指引**：每个功能都有分步骤的使用说明
- **智能提示系统**：鼠标悬停即可查看详细说明
- **风险提示明确**：重要操作有清晰的警告和建议

#### 💡 **工具提示(Tooltip)功能**
每个输入框、按钮、选项都配备了详细的工具提示：

| 功能区域 | 提示内容 | 示例 |
|---------|----------|------|
| API配置 | 获取方法、安全提醒、权限设置 | "API密钥就像银行卡密码，用来连接交易所" |
| MACD参数 | 参数作用、建议值、调整影响 | "快线周期控制信号敏感度，数值越小反应越快" |
| 风险管理 | 参数含义、设置建议、风险说明 | "保证金率就像买房首付比例，影响资金利用率" |
| 时间周期 | 周期含义、组合建议、确认作用 | "就像看天气预报，可以看今天的、这周的" |

#### 📚 **术语解释词典**
内置专业术语的通俗解释：

| 专业术语 | 生活化解释 |
|---------|-----------|
| API密钥 | 就像银行卡密码，用来连接交易所账户 |
| 杠杆 | 用少量资金控制更大金额，像贷款买房 |
| MACD | 技术分析指标，像股市的"红绿灯" |
| 保证金 | 交易时冻结的资金，像买房的首付款 |
| 加仓 | 在已有持仓基础上继续买入，像定投基金 |
| 时间周期 | 分析价格的时间长度，像看不同时段的天气 |

#### 🔧 **分步操作指南**
复杂功能提供详细的操作步骤：

**首次使用指南：**
1. **配置设置**：填写API信息，建议先开启沙盒模式
2. **策略配置**：保持默认MACD参数，可选择确认周期
3. **资金设置**：使用保守的风险参数设置
4. **测试连接**：确认配置正确
5. **沙盒交易**：在虚拟环境下观察效果
6. **真实交易**：确认无误后切换到真实模式

#### ⚠️ **智能风险提示**
使用颜色编码的风险提示系统：

- 🟢 **绿色（安全）**：推荐操作，风险较低
- 🟡 **橙色（注意）**：需要谨慎，建议了解后操作
- 🔴 **红色（警告）**：高风险操作，新手不建议

#### 💬 **帮助说明窗口**
每个标签页都有详细的帮助说明：

- **MACD策略详解**：用交通信号灯比喻解释MACD原理
- **时间周期说明**：用天气预报比喻解释多周期确认
- **风险管理指南**：用买房、开车比喻解释风险控制
- **参数设置建议**：针对不同用户类型的推荐设置

#### 🎨 **界面设计特点**
- **卡片式布局**：功能分区清晰，降低认知负担
- **图标提示**：使用表情符号和图标增强可读性
- **颜色区分**：不同类型的信息使用不同颜色
- **字体层次**：标题、正文、提示使用不同字体大小

### 期货合约交易对支持 🆕
系统已全面升级为期货合约交易，支持主流交易所的永续合约和期货合约：

#### 📊 **交易对格式标准化**

| 交易所 | 格式示例 | 说明 | 合约类型 |
|--------|----------|------|----------|
| **OKX** | `BTC-USDT-SWAP` | 永续合约格式 | 永续合约 |
| **Gate.io** | `BTC_USDT` | 期货合约格式 | 期货合约 |

**智能格式转换**：
```python
# 系统自动根据选择的交易所转换交易对格式
用户输入: "BTC/USDT"
OKX格式: "BTC-USDT-SWAP"
Gate.io格式: "BTC_USDT"
```

#### 🔄 **支持的主流期货合约**

| 基础交易对 | 币种名称 | OKX格式 | Gate.io格式 | 热门度 |
|------------|----------|---------|-------------|--------|
| BTC/USDT | Bitcoin | BTC-USDT-SWAP | BTC_USDT | ⭐⭐⭐ |
| ETH/USDT | Ethereum | ETH-USDT-SWAP | ETH_USDT | ⭐⭐⭐ |
| SOL/USDT | Solana | SOL-USDT-SWAP | SOL_USDT | ⭐⭐⭐ |
| ADA/USDT | Cardano | ADA-USDT-SWAP | ADA_USDT | ⭐⭐ |
| DOT/USDT | Polkadot | DOT-USDT-SWAP | DOT_USDT | ⭐⭐ |
| AVAX/USDT | Avalanche | AVAX-USDT-SWAP | AVAX_USDT | ⭐⭐ |
| MATIC/USDT | Polygon | MATIC-USDT-SWAP | MATIC_USDT | ⭐⭐ |

#### 🎯 **智能交易对管理**

**自动格式转换**：
- 用户选择交易所时，系统自动转换交易对为对应格式
- 支持多种输入格式的智能识别和转换
- 实时验证交易对格式的正确性

**格式验证机制**：
```python
# OKX格式验证
def validate_okx_symbol(symbol):
    return symbol.endswith('-SWAP') and len(symbol.split('-')) == 3

# Gate.io格式验证
def validate_gateio_symbol(symbol):
    return '_' in symbol and len(symbol.split('_')) == 2
```

### 交易所API深度集成
系统深度研究并集成了主流数字货币交易所的期货合约API：

#### 🏢 **支持的交易所**

| 交易所 | API版本 | 合约类型 | 结算币种 | 集成状态 |
|--------|---------|----------|----------|----------|
| OKX | V5 | 永续/交割/期权 | USDT/BTC/ETH | ✅ 完整支持 |
| Gate.io | V4 | 永续/交割/期权 | USDT/BTC | ✅ 完整支持 |

#### 🔧 **核心API功能对比**

**期货合约下单**：
- **OKX**: `POST /api/v5/trade/order` - 支持多种交易模式和高级订单类型
- **Gate.io**: `POST /api/v4/futures/{settle}/orders` - 支持双仓模式和自动减仓

**持仓管理**：
- **OKX**: `GET /api/v5/account/positions` - 详细的风险指标和保证金信息
- **Gate.io**: `GET /api/v4/futures/{settle}/positions` - ADL排名和维持保证金率

**实时数据**：
- **OKX**: WebSocket V5 - 订单/持仓/账户实时更新
- **Gate.io**: WebSocket V4 - 期货专用频道和用户数据推送

#### ⚡ **性能和限频对比**

| 功能 | OKX限频 | Gate.io限频 | 优化策略 |
|------|---------|-------------|----------|
| 下单 | 60次/2秒(单产品) | 100次/10秒 | 批量操作 |
| 撤单 | 60次/2秒(单产品) | 100次/10秒 | 智能撤单 |
| 查询 | 20次/2秒 | 200次/10秒 | 数据缓存 |
| 特点 | 产品级限频 | 用户级限频 | 令牌桶算法 |

#### 🔐 **认证机制差异**

**OKX认证**：
```
签名 = HMAC-SHA256(timestamp + method + path + body)
Headers: OK-ACCESS-KEY, OK-ACCESS-SIGN, OK-ACCESS-TIMESTAMP, OK-ACCESS-PASSPHRASE
```

**Gate.io认证**：
```
签名 = HMAC-SHA512(method + url + query + payload + timestamp)
Headers: KEY, SIGN, Timestamp
```

#### 🛡️ **统一风险控制**

**参数映射层**：
- 交易对符号转换：`BTC/USDT` → `BTC-USDT-SWAP`(OKX) / `BTC_USDT`(Gate.io)
- 订单方向映射：统一`OrderSide`枚举 → 各交易所原生格式
- 订单类型转换：`OrderType` → `ordType`(OKX) / `tif+price`(Gate.io)

**错误处理统一**：
```python
ERROR_MAPPING = {
    'INSUFFICIENT_BALANCE': {
        'okx': ['51008', '51009'],
        'gateio': ['BALANCE_NOT_ENOUGH']
    },
    'RATE_LIMIT': {
        'okx': ['50011'],
        'gateio': ['TOO_FAST']
    }
}
```

**风险控制机制**：
- 统一的资金管理模块
- 实时订单风险检查
- 持仓比例动态监控
- 紧急停止和恢复机制

#### 📊 **WebSocket实时数据**

**数据订阅统一接口**：
```python
# 订阅订单更新
await exchange.subscribe_orders(symbol)

# 订阅持仓更新
await exchange.subscribe_positions(symbol)

# 订阅账户更新
await exchange.subscribe_account()
```

**实时数据处理**：
- 断线自动重连机制
- 数据完整性校验
- 延迟监控和优化
- 异常数据过滤

#### 🔄 **多交易所套利支持**

**统一接口设计**：
```python
class UnifiedTradingAPI:
    def place_order(self, symbol, side, quantity, price=None):
        # 自动路由到最优交易所

    def get_best_price(self, symbol, side):
        # 跨交易所价格比较

    def arbitrage_opportunity(self, symbol):
        # 套利机会检测
```

**智能路由策略**：
- 基于流动性的交易所选择
- 手续费成本优化
- 延迟和成功率考量
- 风险分散配置

### 布林带逆势加仓策略 🆕
系统新增了专业的布林带(Bollinger Bands)逆势加仓策略，为用户提供更多样化的交易选择：

#### 🎯 **策略核心原理**

**布林带指标**：
- **上轨**: 移动平均线 + (标准差 × 倍数) - 价格阻力位
- **中轨**: 20日移动平均线 - 价格平衡点
- **下轨**: 移动平均线 - (标准差 × 倍数) - 价格支撑位

**逆势加仓机制**：
```
初始开仓 → 价格下跌 → 触发加仓 → 成本摊薄 → 等待反弹获利
```

#### 📊 **策略配置参数**

| 参数类别 | 配置项 | 默认值 | 说明 |
|----------|--------|--------|------|
| **布林带指标** | 计算周期 | 20 | 布林带计算的K线周期数 |
| | 标准差倍数 | 2.0 | 上下轨距离中轨的标准差倍数 |
| | 信号确认 | 启用 | 是否需要布林带信号确认 |
| **加仓触发** | 触发方式 | 点数触发 | 按固定点数或百分比触发 |
| | 触发距离 | 50点/1% | 距离止损价的触发距离 |
| | 最大次数 | 3次 | 最多允许加仓的次数 |
| **加仓类型** | 等量加仓 | ✅ | 每次加仓金额与初始相同 |
| | 半量加仓 | ❌ | 每次加仓金额为初始的一半 |
| | 四分之一量 | ❌ | 每次加仓金额为初始的1/4 |
| **风险控制** | 最大总亏损 | 15% | 总体亏损超过此比例强制平仓 |
| | 最大投入比例 | 20% | 最多投入账户资金的比例 |
| | 止盈百分比 | 2% | 达到此盈利自动止盈 |
| | 止损百分比 | 5% | 单次开仓的止损比例 |

#### 🔄 **加仓执行流程**

**第一阶段：信号识别**
```python
# 布林带信号检测
if 价格触及下轨 and 三线向下:
    signal = "超跌反弹机会"

# 加仓条件判断
if 距离止损价 <= 触发距离:
    trigger_add_position = True
```

**第二阶段：成本摊薄**
```python
# 等量加仓示例 (ETH/USDT)
初始开仓: 3685.95 USDT, 保证金: 1.91 USDT
第1次加仓: 3479.95 USDT, 保证金: 1.91 USDT
平均成本: (1.91+1.91) ÷ (0.000518+0.000549) = 3580.04 USDT
成本摊薄: 105.91 USDT (2.87%)
```

**第三阶段：风险监控**
```python
# 实时风险评估
total_loss_percent = (total_investment - current_value) / total_investment * 100
if total_loss_percent > max_total_loss_percent:
    emergency_close_position()
```

#### 📈 **策略效果对比**

| 加仓策略 | 资金使用 | 成本摊薄效果 | 风险等级 | 适用场景 |
|----------|----------|--------------|----------|----------|
| **等量加仓** | 高 | 最佳 | 高 | 资金充足，对反弹有信心 |
| **递减加仓** | 中 | 良好 | 中 | 平衡风险和收益 |
| **保守加仓** | 低 | 一般 | 低 | 资金有限，风险厌恶 |

#### 🛡️ **多层风险控制**

**参数验证层**：
- 布林带周期：5-100
- 标准差倍数：0.5-5.0
- 最大加仓次数：1-10
- 触发距离：合理范围检查

**执行控制层**：
- 总投入资金比例限制
- 最大总亏损百分比控制
- 紧急停止机制
- 实时风险监控

**用户保护层**：
- 策略启用前风险警告
- 详细的策略说明和教程
- 参数测试和验证功能
- 完整的操作日志记录

#### ⚠️ **风险警告与适用性**

**高风险提示**：
```
🚨 布林带逆势加仓策略属于高风险策略！

主要风险：
• 单边下跌趋势中可能导致重大损失
• 连续加仓会快速消耗可用资金
• 需要较强的心理承受能力
• 不适合交易新手使用

适用场景：
✅ 震荡市场环境
✅ 有明确技术支撑位
✅ 超跌反弹机会
✅ 基本面良好的品种

不适用场景：
❌ 单边下跌趋势
❌ 基本面恶化
❌ 流动性危机
❌ 市场恐慌时期
```

**使用建议**：
- 🔰 **新手用户**：建议先学习基础交易知识，使用模拟账户测试
- 💰 **资金管理**：只用可承受全部损失的资金，建议不超过总资金的10-20%
- 📊 **参数设置**：从保守参数开始，逐步根据经验调整
- 🎯 **市场选择**：优先在震荡市场中使用，避免强趋势时期
- 🛡️ **风险控制**：严格遵守预设的风险控制参数，绝不违反纪律

#### 🎮 **功能演示和测试**

系统提供了完整的测试和演示功能：

**策略测试脚本**：
```bash
python test_bollinger_strategy.py    # 单元测试
python demo_bollinger_strategy.py    # 功能演示
```

**测试覆盖范围**：
- ✅ 布林带指标计算验证
- ✅ 加仓触发条件测试
- ✅ 成本摊薄效果验证
- ✅ 风险控制机制测试
- ✅ 多种市场场景模拟
- ✅ 配置参数验证测试

**演示功能包括**：
- 📊 布林带指标计算和信号识别
- 🔄 逆势加仓策略执行过程
- 🚨 风险场景分析和控制
- 📈 盈利场景和止盈策略
- ⚖️ 不同策略效果对比

### 自定义MACD参数
在 `config.py` 中修改：
```python
MACD_FAST = 12      # 快线周期
MACD_SLOW = 26      # 慢线周期
MACD_SIGNAL = 9     # 信号线周期
```

### 自定义风险阈值
```python
ALERT_POINTS = 0.5          # 预警点数
MAX_ADD_TIMES = 3           # 最大加仓次数
PRICE_CHECK_INTERVAL = 5    # 价格检查间隔(秒)
```

## 📝 日志系统

系统会生成详细的交易日志：
- 控制台输出：实时状态信息
- 文件日志：`trading.log` 完整交易记录

## ⚠️ 重要提醒

1. **风险警告**：期货交易存在高风险，可能导致全部资金损失
2. **测试建议**：建议先在沙盒环境测试
3. **资金管理**：请合理控制仓位大小
4. **监控重要**：交易期间请密切关注系统状态

## 🔍 故障排除

### 常见问题

1. **连接失败**
   - 检查API密钥是否正确
   - 确认网络连接正常
   - 验证交易所API权限

2. **开仓失败**
   - 检查账户余额
   - 确认交易对是否正确
   - 验证杠杆设置

3. **监控异常**
   - 检查WebSocket连接
   - 确认持仓状态
   - 查看日志错误信息

## 📞 技术支持

如遇到问题，请查看：
1. 系统日志 `trading.log`
2. 控制台错误信息
3. 交易所API文档

## 📄 免责声明

本系统仅供学习和研究使用，不构成投资建议。使用者需自行承担交易风险，开发者不对任何损失负责。
