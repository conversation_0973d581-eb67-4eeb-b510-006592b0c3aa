import axios from 'axios'
import { ElMessage, ElLoading } from 'element-plus'

// 创建axios实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 添加认证token
    const token = localStorage.getItem('bitv_token') || 'bitv_real_trading_token'
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    // 添加时间戳防止缓存
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }
    
    return config
  },
  (error) => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    // 检查业务状态码
    if (response.data && response.data.success === false) {
      const errorMsg = response.data.error || response.data.message || '请求失败'
      ElMessage.error(errorMsg)
      return Promise.reject(new Error(errorMsg))
    }
    
    return response
  },
  (error) => {
    console.error('API请求错误:', error)
    
    let errorMessage = '网络请求失败'
    
    if (error.response) {
      // 服务器响应错误
      const { status, data } = error.response
      
      switch (status) {
        case 400:
          errorMessage = data?.error || '请求参数错误'
          break
        case 401:
          errorMessage = '认证失败，请检查API密钥'
          break
        case 403:
          errorMessage = '权限不足'
          break
        case 404:
          errorMessage = '请求的资源不存在'
          break
        case 500:
          errorMessage = '服务器内部错误'
          break
        case 502:
          errorMessage = '网关错误'
          break
        case 503:
          errorMessage = '服务不可用'
          break
        default:
          errorMessage = data?.error || `请求失败 (${status})`
      }
    } else if (error.request) {
      // 网络错误
      if (error.code === 'ECONNABORTED') {
        errorMessage = '请求超时，请检查网络连接'
      } else {
        errorMessage = '网络连接失败，请检查Python后端是否运行'
      }
    }
    
    // 显示错误消息（避免重复显示）
    if (!error.config?.skipErrorMessage) {
      ElMessage.error(errorMessage)
    }
    
    return Promise.reject(new Error(errorMessage))
  }
)

// API方法封装
const apiMethods = {
  // 基础请求方法
  get: (url, config = {}) => api.get(url, config),
  post: (url, data = {}, config = {}) => api.post(url, data, config),
  put: (url, data = {}, config = {}) => api.put(url, data, config),
  delete: (url, config = {}) => api.delete(url, config),
  
  // 系统相关
  health: () => api.get('/health'),
  systemStatus: () => api.get('/api/status'),
  
  // 交易所相关
  exchangeStatus: () => api.get('/api/exchange/status'),
  configureExchange: (config) => api.post('/api/configure-exchange', config),
  testConnection: () => api.post('/api/exchange/test-connection'),
  
  // 交易相关
  tradingStatus: () => api.get('/api/trading/status'),
  startTrading: (config) => api.post('/api/trading/start', config),
  stopTrading: () => api.post('/api/trading/stop'),
  emergencyStop: () => api.post('/api/trading/emergency-stop'),
  
  // 持仓相关
  getPositions: () => api.get('/api/trading/positions'),
  closePosition: (data) => api.post('/api/trading/close-position', data),
  
  // 历史数据
  getTradeHistory: (params) => api.get('/api/trading/history', { params }),
  getAddPositionHistory: (params) => api.get('/api/trading/add-position-history', { params }),
  
  // 风险管理
  getRiskMetrics: () => api.get('/api/trading/risk-metrics'),
  
  // 市场数据
  getKlineData: (params) => api.get('/api/market/kline', { params }),
  getMACDData: (params) => api.get('/api/indicators/macd', { params }),
  
  // 策略相关
  getStrategyConfig: (strategy) => api.get(`/api/strategy/${strategy}/config`),
  updateStrategyConfig: (strategy, config) => api.post(`/api/strategy/${strategy}/config`, config),
  testStrategy: (strategy, config) => api.post(`/api/strategy/${strategy}/test`, config),
  
  // 日志相关
  getLogs: (params) => api.get('/api/logs', { params }),
  
  // 配置相关
  getConfig: () => api.get('/api/config'),
  updateConfig: (config) => api.post('/api/config', config),
  exportConfig: () => api.get('/api/config/export'),
  importConfig: (config) => api.post('/api/config/import', config)
}

// 带加载状态的请求方法
const createLoadingRequest = (method) => {
  return async (...args) => {
    const loading = ElLoading.service({
      lock: true,
      text: '请求中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })
    
    try {
      const result = await method(...args)
      return result
    } finally {
      loading.close()
    }
  }
}

// 导出带加载状态的方法
export const loadingApi = {
  post: createLoadingRequest(apiMethods.post),
  put: createLoadingRequest(apiMethods.put),
  delete: createLoadingRequest(apiMethods.delete)
}

export default apiMethods
