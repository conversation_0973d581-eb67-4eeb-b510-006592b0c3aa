"""
KDJ策略实现
基于KDJ指标的超买超卖和金叉死叉信号进行交易
"""
import asyncio
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import pandas as pd
import numpy as np
from datetime import datetime

# 导入并发监控工具
try:
    from utils.concurrency_monitor import monitor_task, TaskType
except ImportError:
    # 如果导入失败，使用fallback实现
    from utils.fallback_imports import monitor_task, TaskType

logger = logging.getLogger(__name__)

class AddPositionType(Enum):
    """加仓类型"""
    EQUAL = "equal"      # 等量加仓
    HALF = "half"        # 半量加仓
    QUARTER = "quarter"  # 四分之一量加仓

class KDJSignal(Enum):
    """KDJ信号"""
    OVERSOLD = "oversold"              # 超卖信号 (K < 20, D < 20)
    OVERBOUGHT = "overbought"          # 超买信号 (K > 80, D > 80)
    GOLDEN_CROSS = "golden_cross"      # 金叉信号 (K线上穿D线)
    DEATH_CROSS = "death_cross"        # 死叉信号 (K线下穿D线)
    NEUTRAL = "neutral"                # 中性区域
    EXTREME_OVERSOLD = "extreme_oversold"        # 极度超卖 (K < 10, D < 10)
    EXTREME_OVERBOUGHT = "extreme_overbought"    # 极度超买 (K > 90, D > 90)
    BULLISH_DIVERGENCE = "bullish_divergence"    # 牛市背离
    BEARISH_DIVERGENCE = "bearish_divergence"    # 熊市背离

@dataclass
class AddPositionRecord:
    """加仓记录"""
    timestamp: datetime
    price: float
    quantity: float
    margin: float
    add_type: AddPositionType
    trigger_reason: str
    new_avg_cost: float
    total_margin: float
    add_count: int

@dataclass
class KDJData:
    """KDJ数据（包含可靠性评估）"""
    k_value: float            # K值
    d_value: float            # D值
    j_value: float            # J值
    signal: KDJSignal         # KDJ信号
    cross_strength: float     # 交叉强度 (0.0-1.0)
    trend_direction: str      # 趋势方向 ("up", "down", "sideways")
    reliability: float = 1.0  # 可靠性评分 (0.0-1.0)
    data_count: int = 0       # 实际数据点数
    required_count: int = 14  # 需要的数据点数
    calculation_period: int = 14  # 实际计算周期

class KDJConfig:
    """KDJ策略配置"""
    
    def __init__(self):
        # KDJ指标参数
        self.k_period = 9                # K值周期
        self.d_period = 3                # D值周期
        self.j_period = 3                # J值周期
        self.oversold_threshold = 20     # 超卖阈值
        self.overbought_threshold = 80   # 超买阈值
        self.extreme_oversold = 10       # 极度超卖阈值
        self.extreme_overbought = 90     # 极度超买阈值
        
        # 自定义交易参数（不使用全局配置）
        self.custom_symbol = "BTC/USDT"  # 自定义交易对
        self.custom_leverage = 20        # 自定义杠杆倍数
        self.initial_margin = 1000.0     # 初始保证金 (USDT)
        self.take_profit_percent = 2.0   # 止盈百分比
        self.stop_loss_percent = 4.5     # 止损百分比
        
        # 加仓触发参数
        self.trigger_distance_points = 100   # 触发加仓的距离(点数)
        self.trigger_distance_percent = 1.5  # 触发加仓的距离(百分比)
        self.use_points_trigger = True       # 是否使用点数触发
        
        # 加仓类型和次数
        self.add_position_types = [AddPositionType.EQUAL]  # 加仓类型序列
        self.max_add_count = 4              # 最大加仓次数
        
        # 加仓间距设置(递增)
        self.add_intervals = [3.0, 5.0, 8.0, 12.0, 20.0]  # 加仓间距百分比
        
        # 风险控制
        self.max_total_loss_percent = 15.0   # 最大总亏损百分比
        self.max_investment_ratio = 0.20     # 最大投入资金比例 (20%)
        self.enable_emergency_stop = True    # 启用紧急停止
        
        # KDJ信号过滤
        self.enable_cross_confirmation = True   # 启用交叉确认
        self.min_cross_strength = 0.2          # 最小交叉强度
        self.signal_confirmation_periods = 2    # 信号确认周期数
        self.enable_extreme_levels = True      # 启用极值水平
        self.enable_j_filter = True            # 启用J值过滤
        
        # 立即开仓功能
        self.immediate_open_enabled = False   # 立即开仓功能开关
        self.immediate_check_interval = 30    # 立即开仓检测间隔(秒)
        
        # 紧急加仓保护
        self.enable_emergency_add = True      # 启用紧急加仓
        self.emergency_distance_threshold = 75.0  # 紧急距离阈值(%)
        
        # 冷却时间设置
        self.order_cooldown = 30             # 下单冷却时间(秒)
        self.position_check_interval = 10    # 持仓检查间隔(秒)
        
        # 交易所设置（使用自定义设置）
        self.exchange_name = "okx"          # 默认交易所
        
        # 数据获取设置
        self.kline_limit = 100              # K线数据获取数量
        self.price_precision = 4            # 价格精度
        self.quantity_precision = 8         # 数量精度
        
        # 日志和监控
        self.enable_detailed_logging = True  # 启用详细日志
        self.enable_performance_monitoring = True  # 启用性能监控
        
        # 模拟交易设置
        self.is_simulation = False          # 是否为模拟交易
        self.simulation_balance = 10000.0   # 模拟账户余额
        
    def validate(self) -> bool:
        """验证配置参数的有效性"""
        try:
            # 验证KDJ参数
            if self.k_period <= 0 or self.k_period > 50:
                logger.error("K周期必须在1-50之间")
                return False
            
            if self.d_period <= 0 or self.d_period > 20:
                logger.error("D周期必须在1-20之间")
                return False
            
            if self.j_period <= 0 or self.j_period > 20:
                logger.error("J周期必须在1-20之间")
                return False
            
            if not (0 <= self.oversold_threshold <= 30):
                logger.error("超卖阈值必须在0-30之间")
                return False
            
            if not (70 <= self.overbought_threshold <= 100):
                logger.error("超买阈值必须在70-100之间")
                return False
            
            if self.oversold_threshold >= self.overbought_threshold:
                logger.error("超卖阈值必须小于超买阈值")
                return False
            
            # 验证自定义交易参数
            if not self.custom_symbol or "/" not in self.custom_symbol:
                logger.error("自定义交易对格式无效")
                return False
            
            if self.custom_leverage <= 0 or self.custom_leverage > 100:
                logger.error("自定义杠杆倍数必须在1-100之间")
                return False
            
            if self.initial_margin <= 0:
                logger.error("初始保证金必须大于0")
                return False
            
            # 验证风险控制参数
            if self.take_profit_percent <= 0:
                logger.error("止盈百分比必须大于0")
                return False
            
            if self.stop_loss_percent <= 0:
                logger.error("止损百分比必须大于0")
                return False
            
            if self.max_total_loss_percent <= 0:
                logger.error("最大总亏损百分比必须大于0")
                return False
            
            if self.max_investment_ratio <= 0 or self.max_investment_ratio > 1:
                logger.error("最大投入资金比例必须在0-1之间")
                return False
            
            # 验证加仓参数
            if self.max_add_count < 0:
                logger.error("最大加仓次数不能为负数")
                return False
            
            if len(self.add_intervals) < self.max_add_count:
                logger.error("加仓间距设置数量不足")
                return False
            
            logger.info("✅ KDJ策略配置验证通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ KDJ策略配置验证失败: {e}")
            return False
    
    def get_required_data_count(self) -> int:
        """获取所需的最小数据点数"""
        # KDJ需要的最小数据点数
        return max(self.k_period, 14) + 10  # 额外10个点用于交叉检测
    
    def to_dict(self) -> Dict[str, Any]:
        """将配置转换为字典"""
        return {
            # KDJ参数
            "k_period": self.k_period,
            "d_period": self.d_period,
            "j_period": self.j_period,
            "oversold_threshold": self.oversold_threshold,
            "overbought_threshold": self.overbought_threshold,
            "extreme_oversold": self.extreme_oversold,
            "extreme_overbought": self.extreme_overbought,
            
            # 自定义交易参数
            "custom_symbol": self.custom_symbol,
            "custom_leverage": self.custom_leverage,
            "initial_margin": self.initial_margin,
            "take_profit_percent": self.take_profit_percent,
            "stop_loss_percent": self.stop_loss_percent,
            
            # 风险控制
            "max_total_loss_percent": self.max_total_loss_percent,
            "max_investment_ratio": self.max_investment_ratio,
            "max_add_count": self.max_add_count,
            
            # 信号过滤
            "enable_cross_confirmation": self.enable_cross_confirmation,
            "min_cross_strength": self.min_cross_strength,
            "signal_confirmation_periods": self.signal_confirmation_periods,
            "enable_extreme_levels": self.enable_extreme_levels,
            "enable_j_filter": self.enable_j_filter,
            
            # 立即开仓
            "immediate_open_enabled": self.immediate_open_enabled,
            "immediate_check_interval": self.immediate_check_interval,
            
            # 交易设置
            "exchange_name": self.exchange_name,
        }
    
    def from_dict(self, config_dict: Dict[str, Any]):
        """从字典加载配置"""
        for key, value in config_dict.items():
            if hasattr(self, key):
                setattr(self, key, value)
            else:
                logger.warning(f"未知的配置参数: {key}")
    
    def __str__(self) -> str:
        """配置信息的字符串表示"""
        return f"""KDJ策略配置:
        KDJ参数: K周期={self.k_period}, D周期={self.d_period}, J周期={self.j_period}
        阈值设置: 超卖={self.oversold_threshold}, 超买={self.overbought_threshold}
        自定义交易: 交易对={self.custom_symbol}, 杠杆={self.custom_leverage}x, 保证金={self.initial_margin}
        风险控制: 止盈={self.take_profit_percent}%, 止损={self.stop_loss_percent}%
        加仓设置: 最大次数={self.max_add_count}, 最大亏损={self.max_total_loss_percent}%
        立即开仓: {'启用' if self.immediate_open_enabled else '禁用'}
        交易所: {self.exchange_name}"""

class KDJCalculator:
    """KDJ计算器 - 核心计算逻辑"""

    def __init__(self, k_period: int = 9, d_period: int = 3, j_period: int = 3,
                 oversold: float = 20, overbought: float = 80):
        self.k_period = k_period
        self.d_period = d_period
        self.j_period = j_period
        self.oversold = oversold
        self.overbought = overbought

    def calculate_kdj(self, highs: List[float], lows: List[float], closes: List[float],
                      min_periods: int = None) -> Optional[KDJData]:
        """
        计算KDJ指标（支持部分数据计算）

        Args:
            highs: 最高价列表
            lows: 最低价列表
            closes: 收盘价列表
            min_periods: 最小计算周期，默认为k_period

        Returns:
            Optional[KDJData]: KDJ数据（包含可靠性评估）
        """
        # 设置最小计算周期
        if min_periods is None:
            min_periods = max(self.k_period, 10)  # 至少需要k_period个数据点

        data_count = len(closes)
        required_count = self.k_period + 10  # 额外数据用于交叉检测

        # 数据完全不足
        if data_count < min_periods or len(highs) != data_count or len(lows) != data_count:
            logger.warning(f"❌ 价格数据不足或不匹配，需要至少{min_periods}个，当前{data_count}个")
            return None

        try:
            # 计算可靠性评分
            if data_count >= required_count:
                reliability = 1.0  # 完全可靠
                calculation_period = required_count
                logger.info(f"✅ KDJ数据充足: {data_count}/{required_count} (可靠性: 100%)")
            else:
                reliability = data_count / required_count  # 部分可靠
                calculation_period = data_count
                logger.warning(f"⚠️ KDJ数据不足: {data_count}/{required_count} (可靠性: {reliability*100:.1f}%)")

            # 使用实际可用的数据
            calc_highs = highs[-calculation_period:] if calculation_period < data_count else highs
            calc_lows = lows[-calculation_period:] if calculation_period < data_count else lows
            calc_closes = closes[-calculation_period:] if calculation_period < data_count else closes

            # 转换为pandas Series进行计算
            high_series = pd.Series(calc_highs)
            low_series = pd.Series(calc_lows)
            close_series = pd.Series(calc_closes)

            # 计算RSV (Raw Stochastic Value)
            lowest_low = low_series.rolling(window=self.k_period, min_periods=1).min()
            highest_high = high_series.rolling(window=self.k_period, min_periods=1).max()

            # 避免除零错误
            rsv = ((close_series - lowest_low) / (highest_high - lowest_low + 1e-10)) * 100

            # 计算K值 (使用指数移动平均)
            k_values = []
            k_prev = 50.0  # K值初始值
            for rsv_val in rsv:
                if pd.isna(rsv_val):
                    k_val = k_prev
                else:
                    k_val = (2/3) * k_prev + (1/3) * rsv_val
                k_values.append(k_val)
                k_prev = k_val

            # 计算D值 (K值的移动平均)
            d_values = []
            d_prev = 50.0  # D值初始值
            for k_val in k_values:
                d_val = (2/3) * d_prev + (1/3) * k_val
                d_values.append(d_val)
                d_prev = d_val

            # 计算J值 (3*K - 2*D)
            j_values = [3 * k - 2 * d for k, d in zip(k_values, d_values)]

            # 获取最新KDJ值
            k_value = k_values[-1]
            d_value = d_values[-1]
            j_value = j_values[-1]

            # 分析KDJ信号
            signal = self._analyze_kdj_signal(k_value, d_value, j_value, k_values, d_values)

            # 计算交叉强度
            cross_strength = self._calculate_cross_strength(k_values, d_values)

            # 分析趋势方向
            trend_direction = self._analyze_trend_direction(calc_closes, k_values, d_values)

            # 记录详细信息
            logger.info(f"📊 KDJ计算完成: K={k_value:.2f}, D={d_value:.2f}, J={j_value:.2f}")
            logger.info(f"📊 信号: {signal.value}, 交叉强度: {cross_strength:.3f}, 趋势: {trend_direction}")
            logger.info(f"📊 数据统计: 使用{len(calc_closes)}个数据点, 可靠性={reliability:.2f}")

            return KDJData(
                k_value=round(k_value, 2),
                d_value=round(d_value, 2),
                j_value=round(j_value, 2),
                signal=signal,
                cross_strength=cross_strength,
                trend_direction=trend_direction,
                reliability=reliability,
                data_count=len(calc_closes),
                required_count=required_count,
                calculation_period=len(calc_closes)
            )

        except Exception as e:
            logger.error(f"计算KDJ异常: {e}")
            return None

    def _analyze_kdj_signal(self, k_value: float, d_value: float, j_value: float,
                           k_history: List[float], d_history: List[float]) -> KDJSignal:
        """
        分析KDJ信号

        Args:
            k_value: 当前K值
            d_value: 当前D值
            j_value: 当前J值
            k_history: K值历史
            d_history: D值历史

        Returns:
            KDJSignal: KDJ信号类型
        """
        try:
            # 检测金叉死叉（需要历史数据）
            if len(k_history) >= 2 and len(d_history) >= 2:
                prev_k = k_history[-2]
                prev_d = d_history[-2]

                # 金叉：K线上穿D线
                if prev_k <= prev_d and k_value > d_value:
                    cross_strength = abs(k_value - d_value)
                    if cross_strength > 1.0:  # 最小交叉幅度
                        logger.info(f"🟢 KDJ金叉信号: K({k_value:.2f}) > D({d_value:.2f}), 强度: {cross_strength:.2f}")
                        return KDJSignal.GOLDEN_CROSS

                # 死叉：K线下穿D线
                elif prev_k >= prev_d and k_value < d_value:
                    cross_strength = abs(k_value - d_value)
                    if cross_strength > 1.0:  # 最小交叉幅度
                        logger.info(f"🔴 KDJ死叉信号: K({k_value:.2f}) < D({d_value:.2f}), 强度: {cross_strength:.2f}")
                        return KDJSignal.DEATH_CROSS

            # 极值水平检测
            if k_value <= 10 and d_value <= 10:
                logger.info(f"🔵 极度超卖信号: K={k_value:.2f}, D={d_value:.2f}")
                return KDJSignal.EXTREME_OVERSOLD
            elif k_value >= 90 and d_value >= 90:
                logger.info(f"🔴 极度超买信号: K={k_value:.2f}, D={d_value:.2f}")
                return KDJSignal.EXTREME_OVERBOUGHT

            # 标准超买超卖检测
            elif k_value <= self.oversold and d_value <= self.oversold:
                logger.info(f"🟢 超卖信号: K={k_value:.2f}, D={d_value:.2f}")
                return KDJSignal.OVERSOLD
            elif k_value >= self.overbought and d_value >= self.overbought:
                logger.info(f"🔴 超买信号: K={k_value:.2f}, D={d_value:.2f}")
                return KDJSignal.OVERBOUGHT

            # 中性区域
            else:
                logger.debug(f"⚪ 中性区域: K={k_value:.2f}, D={d_value:.2f}")
                return KDJSignal.NEUTRAL

        except Exception as e:
            logger.error(f"分析KDJ信号异常: {e}")
            return KDJSignal.NEUTRAL

    def _calculate_cross_strength(self, k_history: List[float], d_history: List[float]) -> float:
        """
        计算KDJ交叉强度

        Args:
            k_history: K值历史
            d_history: D值历史

        Returns:
            float: 交叉强度 (0.0-1.0)
        """
        try:
            if len(k_history) < 3 or len(d_history) < 3:
                return 0.0

            # 计算最近的K、D差值变化
            current_diff = abs(k_history[-1] - d_history[-1])
            prev_diff = abs(k_history[-2] - d_history[-2])

            # 交叉强度基于差值变化和绝对差值
            strength = min(1.0, (current_diff + abs(current_diff - prev_diff)) / 20.0)

            return strength

        except Exception as e:
            logger.error(f"计算交叉强度异常: {e}")
            return 0.0

    def _analyze_trend_direction(self, prices: List[float], k_history: List[float],
                               d_history: List[float]) -> str:
        """
        分析趋势方向

        Args:
            prices: 价格列表
            k_history: K值历史
            d_history: D值历史

        Returns:
            str: 趋势方向 ("up", "down", "sideways")
        """
        try:
            if len(prices) < 10 or len(k_history) < 5:
                return "sideways"

            # 价格趋势分析
            price_ma_short = sum(prices[-5:]) / 5
            price_ma_long = sum(prices[-10:]) / 10
            price_trend_strength = abs(price_ma_short - price_ma_long) / price_ma_long

            # KDJ趋势分析
            k_trend = (k_history[-1] - k_history[-5]) / 5 if len(k_history) >= 5 else 0
            d_trend = (d_history[-1] - d_history[-5]) / 5 if len(d_history) >= 5 else 0

            # 综合判断
            if price_trend_strength < 0.01:  # 1%以内认为是横盘
                return "sideways"
            elif price_ma_short > price_ma_long and k_trend > 0 and d_trend > 0:
                return "up"
            elif price_ma_short < price_ma_long and k_trend < 0 and d_trend < 0:
                return "down"
            else:
                return "sideways"

        except Exception as e:
            logger.error(f"分析趋势方向异常: {e}")
            return "sideways"

    def calculate_kdj_signal_direction(self, kdj_data: KDJData) -> str:
        """
        基于KDJ数据计算开仓方向

        Args:
            kdj_data: KDJ数据

        Returns:
            str: 开仓方向 ("long", "short", "hold")
        """
        try:
            # 极度超卖 - 强烈做多信号
            if kdj_data.signal == KDJSignal.EXTREME_OVERSOLD:
                logger.info("🚀 极度超卖做多信号: K < 10, D < 10")
                return "long"

            # 极度超买 - 强烈做空信号
            elif kdj_data.signal == KDJSignal.EXTREME_OVERBOUGHT:
                logger.info("💥 极度超买做空信号: K > 90, D > 90")
                return "short"

            # 金叉信号 - 做多信号
            elif kdj_data.signal == KDJSignal.GOLDEN_CROSS:
                # 结合位置判断信号强度
                if kdj_data.k_value < 50:  # 低位金叉更可靠
                    logger.info("📈 低位金叉做多信号")
                    return "long"
                else:
                    logger.info("⚠️ 高位金叉，谨慎做多")
                    return "hold"

            # 死叉信号 - 做空信号
            elif kdj_data.signal == KDJSignal.DEATH_CROSS:
                # 结合位置判断信号强度
                if kdj_data.k_value > 50:  # 高位死叉更可靠
                    logger.info("📉 高位死叉做空信号")
                    return "short"
                else:
                    logger.info("⚠️ 低位死叉，谨慎做空")
                    return "hold"

            # 标准超卖 - 做多信号
            elif kdj_data.signal == KDJSignal.OVERSOLD:
                if kdj_data.trend_direction != "up":  # 避免下跌趋势中的假信号
                    logger.info("📈 超卖做多信号: K < 20, D < 20")
                    return "long"
                else:
                    logger.info("⚠️ 超卖但趋势向上，观望")
                    return "hold"

            # 标准超买 - 做空信号
            elif kdj_data.signal == KDJSignal.OVERBOUGHT:
                if kdj_data.trend_direction != "down":  # 避免上涨趋势中的假信号
                    logger.info("📉 超买做空信号: K > 80, D > 80")
                    return "short"
                else:
                    logger.info("⚠️ 超买但趋势向下，观望")
                    return "hold"

            # 其他情况保持观望
            else:
                logger.debug(f"⏸️ 保持观望: K={kdj_data.k_value:.2f}, D={kdj_data.d_value:.2f}, 信号={kdj_data.signal.value}")
                return "hold"

        except Exception as e:
            logger.error(f"计算KDJ开仓方向异常: {e}")
            return "hold"

    def detect_kdj_signal(self, highs: List[float], lows: List[float], closes: List[float],
                         min_periods: int = None) -> Tuple[bool, str, float]:
        """
        专门检测KDJ信号

        Args:
            highs: 最高价列表
            lows: 最低价列表
            closes: 收盘价列表
            min_periods: 最小计算周期

        Returns:
            Tuple[bool, str, float]: (是否有信号, 信号类型, 信号强度)
        """
        try:
            if len(closes) < self.k_period:
                return False, "hold", 0.0

            # 计算KDJ数据
            kdj_data = self.calculate_kdj(highs, lows, closes, min_periods)
            if not kdj_data:
                return False, "hold", 0.0

            # 计算开仓方向
            direction = self.calculate_kdj_signal_direction(kdj_data)

            if direction != "hold":
                # 计算信号强度
                if kdj_data.signal in [KDJSignal.EXTREME_OVERSOLD, KDJSignal.EXTREME_OVERBOUGHT]:
                    signal_strength = 1.0  # 极值信号强度最高
                elif kdj_data.signal in [KDJSignal.GOLDEN_CROSS, KDJSignal.DEATH_CROSS]:
                    signal_strength = 0.8 + kdj_data.cross_strength * 0.2  # 交叉信号中高强度
                elif kdj_data.signal in [KDJSignal.OVERSOLD, KDJSignal.OVERBOUGHT]:
                    signal_strength = 0.6  # 标准信号中等强度
                else:
                    signal_strength = 0.4  # 其他信号较低强度

                # 结合可靠性调整强度
                signal_strength *= kdj_data.reliability

                return True, direction, signal_strength

            return False, "hold", 0.0

        except Exception as e:
            logger.error(f"检测KDJ信号异常: {e}")
            return False, "hold", 0.0

    def is_signal_confirmed(self, highs: List[float], lows: List[float], closes: List[float],
                           signal_type: str, confirmation_periods: int = 2) -> bool:
        """
        确认KDJ信号的有效性

        Args:
            highs: 最高价列表
            lows: 最低价列表
            closes: 收盘价列表
            signal_type: 信号类型
            confirmation_periods: 确认周期数

        Returns:
            bool: 信号是否确认
        """
        try:
            if len(closes) < confirmation_periods + 1:
                return False

            confirmed_count = 0

            # 检查最近几个周期的信号一致性
            for i in range(confirmation_periods):
                if i == 0:
                    check_highs = highs
                    check_lows = lows
                    check_closes = closes
                else:
                    check_highs = highs[:-(i)]
                    check_lows = lows[:-(i)]
                    check_closes = closes[:-(i)]

                has_signal, detected_type, strength = self.detect_kdj_signal(
                    check_highs, check_lows, check_closes)

                if has_signal and detected_type == signal_type:
                    confirmed_count += 1

            # 需要至少一半的周期确认信号
            required_confirmations = max(1, confirmation_periods // 2)
            is_confirmed = confirmed_count >= required_confirmations

            if is_confirmed:
                logger.info(f"✅ KDJ信号确认: {signal_type}, 确认次数: {confirmed_count}/{confirmation_periods}")
            else:
                logger.warning(f"⚠️ KDJ信号未确认: {signal_type}, 确认次数: {confirmed_count}/{confirmation_periods}")

            return is_confirmed

        except Exception as e:
            logger.error(f"确认KDJ信号异常: {e}")
            return False

    def calculate_kdj_from_prices(self, prices: List[float], min_periods: int = None) -> Optional[KDJData]:
        """
        从价格列表计算KDJ（假设价格为收盘价，高低价相同）

        Args:
            prices: 价格列表
            min_periods: 最小计算周期

        Returns:
            Optional[KDJData]: KDJ数据
        """
        try:
            if not prices:
                return None

            # 简化处理：假设高低价等于收盘价
            highs = prices.copy()
            lows = prices.copy()
            closes = prices.copy()

            return self.calculate_kdj(highs, lows, closes, min_periods)

        except Exception as e:
            logger.error(f"从价格计算KDJ异常: {e}")
            return None

class KDJStrategy:
    """KDJ策略主类"""

    def __init__(self, config: KDJConfig, exchange_manager=None):
        self.config = config
        self.exchange_manager = exchange_manager
        self.calculator = KDJCalculator(
            k_period=config.k_period,
            d_period=config.d_period,
            j_period=config.j_period,
            oversold=config.oversold_threshold,
            overbought=config.overbought_threshold
        )

        # 策略状态
        self.is_running = False
        self.last_signal_time = None
        self.last_order_time = None
        self.current_position = None
        self.add_position_records: List[AddPositionRecord] = []

        # 性能监控
        self.total_trades = 0
        self.successful_trades = 0
        self.total_pnl = 0.0

        # 异步任务管理
        self.monitoring_task = None
        self.immediate_open_task = None

        logger.info(f"🎯 KDJ策略初始化完成: {config}")

    async def start(self):
        """启动KDJ策略"""
        try:
            if self.is_running:
                logger.warning("⚠️ KDJ策略已在运行中")
                return

            # 验证配置
            if not self.config.validate():
                logger.error("❌ KDJ策略配置验证失败")
                return

            # 验证交易所连接
            if not self.exchange_manager:
                logger.error("❌ 交易所管理器未设置")
                return

            self.is_running = True
            logger.info("🚀 KDJ策略启动成功")

            # 启动立即开仓功能（如果启用）
            if self.config.immediate_open_enabled:
                await self._start_immediate_open_monitor()

            # 启动持仓监控
            self.monitoring_task = asyncio.create_task(self._start_position_monitor())

        except Exception as e:
            logger.error(f"❌ KDJ策略启动失败: {e}")
            self.is_running = False

    async def stop(self):
        """停止KDJ策略"""
        try:
            self.is_running = False

            # 停止异步任务
            if self.monitoring_task:
                self.monitoring_task.cancel()
                try:
                    await self.monitoring_task
                except asyncio.CancelledError:
                    pass

            if self.immediate_open_task:
                self.immediate_open_task.cancel()
                try:
                    await self.immediate_open_task
                except asyncio.CancelledError:
                    pass

            logger.info("🛑 KDJ策略已停止")

        except Exception as e:
            logger.error(f"❌ KDJ策略停止失败: {e}")

    @monitor_task(TaskType.OPENING_CHECK)
    async def _start_immediate_open_monitor(self):
        """启动立即开仓监控"""
        try:
            logger.info(f"🔍 启动KDJ立即开仓监控，检测间隔: {self.config.immediate_check_interval}秒")

            async def check_immediate_open():
                while self.is_running and self.config.immediate_open_enabled:
                    try:
                        await self._check_immediate_open_opportunity()
                        await asyncio.sleep(self.config.immediate_check_interval)
                    except Exception as e:
                        logger.error(f"❌ 立即开仓检查异常: {e}")
                        await asyncio.sleep(5)  # 错误时短暂等待

            self.immediate_open_task = asyncio.create_task(check_immediate_open())

        except Exception as e:
            logger.error(f"❌ 启动立即开仓监控失败: {e}")

    @monitor_task(TaskType.OPENING_CHECK)
    async def _check_immediate_open_opportunity(self):
        """检查立即开仓机会"""
        try:
            # 检查是否有持仓
            if await self._has_position():
                logger.debug("已有持仓，跳过开仓检查")
                return

            # 检查冷却时间
            if not self._check_order_cooldown():
                logger.debug("冷却时间未到，跳过开仓检查")
                return

            # 获取当前价格数据（需要高低收数据）
            highs, lows, closes = await self._get_ohlc_data()
            if not closes or len(closes) < self.config.get_required_data_count():
                logger.warning("⚠️ OHLC数据不足，无法进行KDJ分析")
                return

            # 检测KDJ信号
            has_signal, signal_type, signal_strength = self.calculator.detect_kdj_signal(highs, lows, closes)

            if not has_signal:
                logger.debug("未检测到KDJ信号")
                return

            # 信号确认（如果启用）
            if self.config.signal_confirmation_periods > 1:
                if not self.calculator.is_signal_confirmed(highs, lows, closes, signal_type,
                                                         self.config.signal_confirmation_periods):
                    logger.warning(f"⚠️ KDJ信号未确认: {signal_type}")
                    return

            # 交叉强度过滤（如果启用）
            if self.config.enable_cross_confirmation:
                kdj_data = self.calculator.calculate_kdj(highs, lows, closes)
                if kdj_data and kdj_data.cross_strength < self.config.min_cross_strength:
                    # 如果不是极值信号且交叉强度不足，跳过
                    if signal_strength < 0.9:  # 非极值信号
                        logger.warning(f"交叉强度不足，跳过开仓: {kdj_data.cross_strength:.3f} < {self.config.min_cross_strength}")
                        return

            logger.info(f"🚀 准备执行KDJ开仓: 方向={signal_type}, 强度={signal_strength:.3f}")
            await self._execute_immediate_opening(signal_type, signal_strength)

        except Exception as e:
            logger.error(f"❌ 检查立即开仓机会异常: {e}")

    async def _execute_immediate_opening(self, direction: str, signal_strength: float):
        """执行立即开仓"""
        try:
            logger.info(f"🚀 [KDJ立即开仓] 执行开仓: 方向={direction}, 强度={signal_strength:.3f}")

            # 获取当前价格
            current_price = await self._get_current_price()
            if not current_price:
                logger.error("❌ 无法获取当前价格")
                return

            # 计算开仓数量
            quantity = self._calculate_position_size(current_price)
            if quantity <= 0:
                logger.error("❌ 计算的开仓数量无效")
                return

            # 计算止损价格
            stop_loss_price = self._calculate_stop_loss_price(current_price, direction)

            # 记录开仓信息
            logger.info(f"📊 [KDJ开仓详情] 价格: {current_price:.4f}, 数量: {quantity}, 止损: {stop_loss_price:.4f}")
            logger.info(f"📊 [KDJ信号详情] 方向: {direction}, 强度: {signal_strength:.3f}")
            logger.info(f"📊 [自定义设置] 交易对: {self.config.custom_symbol}, 杠杆: {self.config.custom_leverage}x")

            # 执行开仓
            order_result = await self._place_market_order(direction, quantity, current_price)
            if order_result:
                logger.info(f"✅ [KDJ立即开仓] 开仓成功: {direction}仓位，价格: {current_price:.4f}，数量: {quantity}")

                # 设置止损订单
                await self._place_stop_loss_order(direction, quantity, stop_loss_price)

                # 更新策略状态
                self.last_order_time = datetime.now()
                self.last_signal_time = datetime.now()
                self.total_trades += 1

                # 记录信号强度用于后续分析
                if not hasattr(self, 'signal_history'):
                    self.signal_history = []
                self.signal_history.append({
                    'timestamp': datetime.now(),
                    'direction': direction,
                    'signal_strength': signal_strength,
                    'price': current_price,
                    'quantity': quantity,
                    'custom_symbol': self.config.custom_symbol,
                    'custom_leverage': self.config.custom_leverage
                })

                # 禁用立即开仓功能（开仓后自动禁用）
                self.config.immediate_open_enabled = False
                logger.info("🔒 [KDJ立即开仓] 开仓后自动禁用立即开仓功能")

            else:
                logger.error("❌ [KDJ立即开仓] 开仓失败")

        except Exception as e:
            logger.error(f"❌ 执行立即开仓异常: {e}")

    def _calculate_stop_loss_price(self, entry_price: float, direction: str) -> float:
        """计算止损价格"""
        try:
            stop_loss_ratio = self.config.stop_loss_percent / 100.0

            if direction == "long":
                # 多仓止损价格 = 入场价格 * (1 - 止损比例)
                stop_loss_price = entry_price * (1 - stop_loss_ratio)
            else:
                # 空仓止损价格 = 入场价格 * (1 + 止损比例)
                stop_loss_price = entry_price * (1 + stop_loss_ratio)

            logger.info(f"💡 [止损计算] 入场价: {entry_price:.4f}, 方向: {direction}, 止损价: {stop_loss_price:.4f}")
            return round(stop_loss_price, self.config.price_precision)

        except Exception as e:
            logger.error(f"❌ 计算止损价格异常: {e}")
            return entry_price * 0.95 if direction == "long" else entry_price * 1.05  # 默认5%止损

    async def _place_stop_loss_order(self, direction: str, quantity: float, stop_loss_price: float):
        """设置止损订单"""
        try:
            if not self.exchange_manager:
                logger.error("❌ 交易所管理器未设置")
                return

            # 确定止损订单方向（与开仓方向相反）
            stop_side = "sell" if direction == "long" else "buy"

            logger.info(f"🛡️ [止损订单] 设置止损: 方向={stop_side}, 数量={quantity}, 止损价={stop_loss_price:.4f}")
            logger.info(f"🛡️ [止损订单] 交易对: {self.config.custom_symbol}")

            # 根据交易所类型选择止损订单方式
            exchange_name = self.config.exchange_name.lower()

            if exchange_name == "okx":
                await self._place_okx_algo_stop_loss_order(stop_side, quantity, stop_loss_price)
            elif exchange_name == "gate":
                await self._place_gate_stop_loss_order(stop_side, quantity, stop_loss_price)
            else:
                # 默认使用限价单作为止损
                await self._place_limit_stop_loss_order(stop_side, quantity, stop_loss_price)

        except Exception as e:
            logger.error(f"❌ 设置止损订单异常: {e}")

    async def _place_okx_algo_stop_loss_order(self, side: str, quantity: float, stop_loss_price: float):
        """使用OKX策略委托接口设置止损订单"""
        try:
            if hasattr(self.exchange_manager.exchange, 'place_algo_order'):
                # 使用OKX策略委托接口
                order_result = await self.exchange_manager.exchange.place_algo_order(
                    symbol=self.config.custom_symbol,
                    side=side,
                    order_type="conditional",
                    quantity=quantity,
                    trigger_price=stop_loss_price,
                    order_price=-1,  # 市价执行
                    reduce_only=True
                )

                if order_result:
                    logger.info(f"✅ [OKX策略委托] 止损订单设置成功: {order_result.order_id}")
                else:
                    logger.warning("⚠️ [OKX策略委托] 止损订单设置失败，尝试限价单")
                    await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)
            else:
                logger.warning("⚠️ OKX交易所不支持策略委托，使用限价单")
                await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)

        except Exception as e:
            logger.error(f"❌ OKX策略委托止损失败: {e}")
            await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)

    async def _place_gate_stop_loss_order(self, side: str, quantity: float, stop_loss_price: float):
        """使用Gate.io止损订单"""
        try:
            # Gate.io的止损订单实现
            logger.info(f"🔧 [Gate.io止损] 设置止损订单: {side}, {quantity}, {stop_loss_price}")

            # 暂时使用限价单作为备选
            await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)

        except Exception as e:
            logger.error(f"❌ Gate.io止损订单失败: {e}")
            await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)

    async def _place_limit_stop_loss_order(self, side: str, quantity: float, stop_loss_price: float):
        """使用限价单作为止损（备选方案）"""
        try:
            logger.info(f"📋 [限价止损] 设置限价止损单: {side}, {quantity}, {stop_loss_price}")

            # 这里可以实现限价单止损逻辑
            # 注意：限价单不是真正的止损，需要持续监控价格

        except Exception as e:
            logger.error(f"❌ 限价止损订单失败: {e}")

    async def _start_position_monitor(self):
        """启动持仓监控"""
        try:
            logger.info("👁️ 启动KDJ持仓监控")

            while self.is_running:
                try:
                    await self._check_position_status()
                    await asyncio.sleep(self.config.position_check_interval)
                except Exception as e:
                    logger.error(f"❌ 持仓监控异常: {e}")
                    await asyncio.sleep(5)

        except Exception as e:
            logger.error(f"❌ 持仓监控启动失败: {e}")

    async def _check_position_status(self):
        """检查持仓状态"""
        try:
            # 这里实现持仓状态检查逻辑
            # 包括止盈止损检查、加仓条件检查等
            pass

        except Exception as e:
            logger.error(f"❌ 检查持仓状态异常: {e}")

    # 辅助方法
    async def _has_position(self) -> bool:
        """检查是否有持仓"""
        try:
            if not self.exchange_manager:
                return False

            # 这里实现持仓检查逻辑
            return False  # 临时返回

        except Exception as e:
            logger.error(f"❌ 检查持仓异常: {e}")
            return False

    def _check_order_cooldown(self) -> bool:
        """检查下单冷却时间"""
        try:
            if not self.last_order_time:
                return True

            elapsed = (datetime.now() - self.last_order_time).total_seconds()
            return elapsed >= self.config.order_cooldown

        except Exception as e:
            logger.error(f"❌ 检查冷却时间异常: {e}")
            return True

    async def _get_ohlc_data(self) -> Tuple[List[float], List[float], List[float]]:
        """获取OHLC数据"""
        try:
            if not self.exchange_manager:
                return [], [], []

            # 这里实现OHLC数据获取逻辑
            return [], [], []  # 临时返回

        except Exception as e:
            logger.error(f"❌ 获取OHLC数据异常: {e}")
            return [], [], []

    async def _get_current_price(self) -> Optional[float]:
        """获取当前价格"""
        try:
            if not self.exchange_manager:
                return None

            # 这里实现当前价格获取逻辑
            return None  # 临时返回

        except Exception as e:
            logger.error(f"❌ 获取当前价格异常: {e}")
            return None

    def _calculate_position_size(self, price: float) -> float:
        """计算开仓数量"""
        try:
            # 基于初始保证金和自定义杠杆计算开仓数量
            position_value = self.config.initial_margin * self.config.custom_leverage
            quantity = position_value / price

            # 应用精度
            quantity = round(quantity, self.config.quantity_precision)

            logger.info(f"💰 [仓位计算] 价格: {price:.4f}, 保证金: {self.config.initial_margin}, 杠杆: {self.config.custom_leverage}x, 数量: {quantity}")
            return quantity

        except Exception as e:
            logger.error(f"❌ 计算开仓数量异常: {e}")
            return 0.0

    async def _place_market_order(self, direction: str, quantity: float, price: float) -> bool:
        """下市价单"""
        try:
            if not self.exchange_manager:
                logger.error("❌ 交易所管理器未设置")
                return False

            logger.info(f"📈 [KDJ市价下单] 方向: {direction}, 数量: {quantity}, 参考价格: {price:.4f}")
            logger.info(f"📊 [交易对] 使用自定义交易对: {self.config.custom_symbol}")
            logger.info(f"📊 [杠杆] 使用自定义杠杆: {self.config.custom_leverage}x")

            # 确定订单方向
            from exchanges.base_exchange import OrderSide
            if direction == "long":
                order_side = OrderSide.BUY
            elif direction == "short":
                order_side = OrderSide.SELL
            else:
                logger.error(f"❌ 未知的开仓方向: {direction}")
                return False

            # 调用交易所API下单
            order_result = await self.exchange_manager.place_market_order(
                symbol=self.config.custom_symbol,
                side=order_side,
                amount=quantity,
                reduce_only=False  # 开仓单，不是平仓单
            )

            if order_result and hasattr(order_result, 'order_id') and order_result.order_id:
                logger.info(f"✅ [KDJ开仓] 开仓订单已提交: 订单ID {order_result.order_id}")

                # 等待一段时间让订单执行
                await asyncio.sleep(1)

                # 验证开仓是否成功
                positions_after = await self.exchange_manager.get_positions(self.config.custom_symbol)
                if positions_after and any(p.size > 0 for p in positions_after):
                    logger.info(f"✅ [KDJ开仓] 开仓验证成功：已建立持仓")
                    return True
                else:
                    logger.warning(f"⚠️ [KDJ开仓] 开仓可能未成功，未检测到持仓")
                    return False
            else:
                logger.error(f"❌ [KDJ开仓] 开仓订单提交失败")
                return False

        except Exception as e:
            logger.error(f"❌ KDJ市价下单异常: {e}")
            return False

    def get_strategy_status(self) -> Dict[str, Any]:
        """获取策略状态"""
        return {
            "is_running": self.is_running,
            "config": self.config.to_dict(),
            "total_trades": self.total_trades,
            "successful_trades": self.successful_trades,
            "success_rate": self.successful_trades / self.total_trades if self.total_trades > 0 else 0,
            "total_pnl": self.total_pnl,
            "add_position_count": len(self.add_position_records),
            "last_signal_time": self.last_signal_time.isoformat() if self.last_signal_time else None,
            "last_order_time": self.last_order_time.isoformat() if self.last_order_time else None,
            "custom_symbol": self.config.custom_symbol,
            "custom_leverage": self.config.custom_leverage,
        }
