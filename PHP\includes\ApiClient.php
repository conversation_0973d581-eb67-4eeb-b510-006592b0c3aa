<?php
/**
 * Python后端API客户端
 * 
 * @description 与Python异步交易系统的API通信客户端
 */

class ApiClient {
    private $baseUrl;
    private $wsUrl;
    private $timeout;
    private $headers;
    private static $instance = null;

    private function __construct() {
        $this->baseUrl = PYTHON_API_BASE;
        $this->wsUrl = PYTHON_WS_BASE;
        $this->timeout = 30;
        $this->headers = [
            'Content-Type: application/json',
            'Accept: application/json',
            'User-Agent: BitV-PHP-Client/1.0'
        ];
    }

    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * 发送GET请求
     */
    public function get($endpoint, $params = []) {
        $url = $this->baseUrl . $endpoint;
        if (!empty($params)) {
            $url .= '?' . http_build_query($params);
        }

        return $this->makeRequest('GET', $url);
    }

    /**
     * 发送POST请求
     */
    public function post($endpoint, $data = []) {
        $url = $this->baseUrl . $endpoint;
        return $this->makeRequest('POST', $url, $data);
    }

    /**
     * 发送PUT请求
     */
    public function put($endpoint, $data = []) {
        $url = $this->baseUrl . $endpoint;
        return $this->makeRequest('PUT', $url, $data);
    }

    /**
     * 发送DELETE请求
     */
    public function delete($endpoint) {
        $url = $this->baseUrl . $endpoint;
        return $this->makeRequest('DELETE', $url);
    }

    /**
     * 执行HTTP请求
     */
    private function makeRequest($method, $url, $data = null) {
        $ch = curl_init();

        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => $this->timeout,
            CURLOPT_HTTPHEADER => $this->headers,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_MAXREDIRS => 3
        ]);

        switch ($method) {
            case 'POST':
                curl_setopt($ch, CURLOPT_POST, true);
                if ($data) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                }
                break;
            case 'PUT':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
                if ($data) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                }
                break;
            case 'DELETE':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
                break;
        }

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            throw new Exception("API请求失败: " . $error);
        }

        $decodedResponse = json_decode($response, true);
        
        if ($httpCode >= 400) {
            $errorMessage = isset($decodedResponse['error']) ? $decodedResponse['error'] : "HTTP错误: {$httpCode}";
            throw new Exception($errorMessage, $httpCode);
        }

        return [
            'success' => $httpCode >= 200 && $httpCode < 300,
            'data' => $decodedResponse,
            'http_code' => $httpCode
        ];
    }

    // ==================== 交易系统API方法 ====================

    /**
     * 获取系统状态
     */
    public function getSystemStatus() {
        return $this->get('/api/status');
    }

    /**
     * 获取交易状态
     */
    public function getTradingStatus() {
        return $this->get('/api/trading/status');
    }

    /**
     * 启动交易
     */
    public function startTrading($config) {
        return $this->post('/api/trading/start', $config);
    }

    /**
     * 停止交易
     */
    public function stopTrading() {
        return $this->post('/api/trading/stop');
    }

    /**
     * 获取持仓信息
     */
    public function getPositions($symbol = null) {
        $params = $symbol ? ['symbol' => $symbol] : [];
        return $this->get('/api/positions', $params);
    }

    /**
     * 获取当前价格
     */
    public function getCurrentPrice($symbol) {
        return $this->get('/api/price/' . urlencode($symbol));
    }

    /**
     * 获取K线数据
     */
    public function getKlineData($symbol, $timeframe, $limit = 100) {
        return $this->get('/api/klines', [
            'symbol' => $symbol,
            'timeframe' => $timeframe,
            'limit' => $limit
        ]);
    }

    /**
     * 测试交易所连接
     */
    public function testExchangeConnection($config) {
        return $this->post('/api/exchange/test', $config);
    }

    /**
     * 获取账户余额
     */
    public function getAccountBalance() {
        return $this->get('/api/account/balance');
    }

    /**
     * 获取交易历史
     */
    public function getTradingHistory($limit = 50, $offset = 0) {
        return $this->get('/api/trading/history', [
            'limit' => $limit,
            'offset' => $offset
        ]);
    }

    /**
     * 获取MACD指标数据
     */
    public function getMACDData($symbol, $timeframe) {
        return $this->get('/api/indicators/macd', [
            'symbol' => $symbol,
            'timeframe' => $timeframe
        ]);
    }

    /**
     * 获取布林带数据
     */
    public function getBollingerData($symbol, $timeframe, $period = 20, $std_dev = 2.0) {
        return $this->get('/api/indicators/bollinger', [
            'symbol' => $symbol,
            'timeframe' => $timeframe,
            'period' => $period,
            'std_dev' => $std_dev
        ]);
    }

    /**
     * 手动平仓
     */
    public function closePosition($symbol) {
        return $this->post('/api/trading/close', ['symbol' => $symbol]);
    }

    /**
     * 紧急停止所有交易
     */
    public function emergencyStop() {
        return $this->post('/api/trading/emergency-stop');
    }

    /**
     * 获取风险指标
     */
    public function getRiskMetrics($symbol = null) {
        $params = $symbol ? ['symbol' => $symbol] : [];
        return $this->get('/api/risk/metrics', $params);
    }

    /**
     * 更新交易配置
     */
    public function updateTradingConfig($config) {
        return $this->put('/api/config', $config);
    }

    /**
     * 获取支持的交易对
     */
    public function getSupportedSymbols($exchange) {
        return $this->get('/api/exchange/' . urlencode($exchange) . '/symbols');
    }

    /**
     * 获取系统日志
     */
    public function getSystemLogs($level = null, $limit = 100) {
        $params = ['limit' => $limit];
        if ($level) {
            $params['level'] = $level;
        }
        return $this->get('/api/logs', $params);
    }

    /**
     * 健康检查
     */
    public function healthCheck() {
        try {
            $response = $this->get('/health');
            return isset($response['status']) && $response['status'] === 'healthy';
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * 获取WebSocket连接URL
     */
    public function getWebSocketUrl() {
        return $this->wsUrl;
    }

    // 防止克隆
    private function __clone() {}
    
    // 防止反序列化
    private function __wakeup() {}
}
?>
