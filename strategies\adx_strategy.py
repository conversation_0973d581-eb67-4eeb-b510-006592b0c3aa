"""
平均方向指数(ADX)策略实现
基于ADX指标的趋势强度和方向信号进行交易
"""
import asyncio
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import pandas as pd
import numpy as np
from datetime import datetime

# 导入并发监控工具
try:
    from utils.concurrency_monitor import monitor_task, TaskType
except ImportError:
    # 如果导入失败，使用fallback实现
    from utils.fallback_imports import monitor_task, TaskType

logger = logging.getLogger(__name__)

class AddPositionType(Enum):
    """加仓类型"""
    EQUAL = "equal"      # 等量加仓
    HALF = "half"        # 半量加仓
    QUARTER = "quarter"  # 四分之一量加仓

class ADXSignal(Enum):
    """ADX信号"""
    STRONG_BULLISH = "strong_bullish"      # 强势多头 (+DI > -DI, ADX > 25, ADX上升)
    STRONG_BEARISH = "strong_bearish"      # 强势空头 (-DI > +DI, ADX > 25, ADX上升)
    WEAK_BULLISH = "weak_bullish"          # 弱势多头 (+DI > -DI, ADX < 25)
    WEAK_BEARISH = "weak_bearish"          # 弱势空头 (-DI > +DI, ADX < 25)
    TREND_STRENGTHENING = "trend_strengthening"  # 趋势加强 (ADX上升)
    TREND_WEAKENING = "trend_weakening"    # 趋势减弱 (ADX下降)
    NO_TREND = "no_trend"                  # 无趋势 (ADX < 20)
    NEUTRAL = "neutral"                    # 中性区域
    DI_CROSSOVER_BULLISH = "di_crossover_bullish"  # +DI上穿-DI
    DI_CROSSOVER_BEARISH = "di_crossover_bearish"  # -DI上穿+DI

@dataclass
class AddPositionRecord:
    """加仓记录"""
    timestamp: datetime
    price: float
    quantity: float
    margin: float
    add_type: AddPositionType
    trigger_reason: str
    new_avg_cost: float
    total_margin: float
    add_count: int

@dataclass
class ADXData:
    """ADX数据（包含可靠性评估）"""
    adx_value: float          # ADX值
    plus_di: float            # +DI值
    minus_di: float           # -DI值
    dx_value: float           # DX值
    signal: ADXSignal         # ADX信号
    trend_strength: str       # 趋势强度 ("强", "中", "弱", "无")
    trend_direction: str      # 趋势方向 ("多头", "空头", "震荡")
    di_spread: float          # DI差值 (+DI - -DI)
    adx_slope: float          # ADX斜率 (上升/下降趋势)
    reliability: float = 1.0  # 可靠性评分 (0.0-1.0)
    data_count: int = 0       # 实际数据点数
    required_count: int = 14  # 需要的数据点数
    calculation_period: int = 14  # 实际计算周期

class ADXConfig:
    """平均方向指数策略配置"""
    
    def __init__(self):
        # ADX指标参数
        self.period = 14                 # ADX计算周期
        self.strong_trend_threshold = 25 # 强趋势阈值
        self.weak_trend_threshold = 20   # 弱趋势阈值
        self.very_strong_threshold = 40  # 极强趋势阈值
        self.signal_confirmation_periods = 2  # 信号确认周期
        
        # 自定义交易参数（不使用全局配置）
        self.custom_symbol = "ADA/USDT"  # 自定义交易对
        self.custom_leverage = 20        # 自定义杠杆倍数
        self.initial_margin = 500.0      # 初始保证金 (USDT)
        self.take_profit_percent = 2.2   # 止盈百分比
        self.stop_loss_percent = 4.0     # 止损百分比
        
        # 加仓触发参数
        self.trigger_distance_points = 50    # 触发加仓的距离(点数)
        self.trigger_distance_percent = 0.8  # 触发加仓的距离(百分比)
        self.use_points_trigger = True       # 是否使用点数触发
        
        # 加仓类型和次数
        self.add_position_types = [AddPositionType.EQUAL]  # 加仓类型序列
        self.max_add_count = 3              # 最大加仓次数
        
        # 加仓间距设置(递增)
        self.add_intervals = [1.8, 3.2, 5.0, 7.5]  # 加仓间距百分比
        
        # 风险控制
        self.max_total_loss_percent = 8.0    # 最大总亏损百分比
        self.max_investment_ratio = 0.15     # 最大投入资金比例 (15%)
        self.enable_emergency_stop = True    # 启用紧急停止
        
        # ADX信号过滤
        self.enable_trend_strength_filter = True  # 启用趋势强度过滤
        self.min_di_spread = 2.0                 # 最小DI差值
        self.enable_adx_slope_filter = True      # 启用ADX斜率过滤
        self.min_adx_slope = 0.5                # 最小ADX斜率
        self.enable_di_crossover = True          # 启用DI交叉信号
        
        # 立即开仓功能
        self.immediate_open_enabled = False   # 立即开仓功能开关
        self.immediate_check_interval = 30    # 立即开仓检测间隔(秒)
        
        # 紧急加仓保护
        self.enable_emergency_add = True      # 启用紧急加仓
        self.emergency_distance_threshold = 55.0  # 紧急距离阈值(%)
        
        # 冷却时间设置
        self.order_cooldown = 30             # 下单冷却时间(秒)
        self.position_check_interval = 10    # 持仓检查间隔(秒)
        
        # 交易所设置（使用自定义设置）
        self.exchange_name = "okx"          # 默认交易所
        
        # 数据获取设置
        self.kline_limit = 100              # K线数据获取数量
        self.price_precision = 4            # 价格精度
        self.quantity_precision = 8         # 数量精度
        
        # 日志和监控
        self.enable_detailed_logging = True  # 启用详细日志
        self.enable_performance_monitoring = True  # 启用性能监控
        
        # 模拟交易设置
        self.is_simulation = False          # 是否为模拟交易
        self.simulation_balance = 10000.0   # 模拟账户余额
        
    def validate(self) -> bool:
        """验证配置参数的有效性"""
        try:
            # 验证ADX参数
            if self.period <= 0 or self.period > 50:
                logger.error("ADX周期必须在1-50之间")
                return False
            
            if not (10 <= self.weak_trend_threshold <= 30):
                logger.error("弱趋势阈值必须在10-30之间")
                return False
            
            if not (20 <= self.strong_trend_threshold <= 50):
                logger.error("强趋势阈值必须在20-50之间")
                return False
            
            if self.weak_trend_threshold >= self.strong_trend_threshold:
                logger.error("弱趋势阈值必须小于强趋势阈值")
                return False
            
            if not (30 <= self.very_strong_threshold <= 70):
                logger.error("极强趋势阈值必须在30-70之间")
                return False
            
            if self.signal_confirmation_periods < 1 or self.signal_confirmation_periods > 10:
                logger.error("信号确认周期必须在1-10之间")
                return False
            
            # 验证自定义交易参数
            if not self.custom_symbol or "/" not in self.custom_symbol:
                logger.error("自定义交易对格式无效")
                return False
            
            if self.custom_leverage <= 0 or self.custom_leverage > 100:
                logger.error("自定义杠杆倍数必须在1-100之间")
                return False
            
            if self.initial_margin <= 0:
                logger.error("初始保证金必须大于0")
                return False
            
            # 验证风险控制参数
            if self.take_profit_percent <= 0:
                logger.error("止盈百分比必须大于0")
                return False
            
            if self.stop_loss_percent <= 0:
                logger.error("止损百分比必须大于0")
                return False
            
            if self.max_total_loss_percent <= 0:
                logger.error("最大总亏损百分比必须大于0")
                return False
            
            if self.max_investment_ratio <= 0 or self.max_investment_ratio > 1:
                logger.error("最大投入资金比例必须在0-1之间")
                return False
            
            # 验证加仓参数
            if self.max_add_count < 0:
                logger.error("最大加仓次数不能为负数")
                return False
            
            if len(self.add_intervals) < self.max_add_count:
                logger.error("加仓间距设置数量不足")
                return False
            
            # 验证信号过滤参数
            if self.min_di_spread < 0 or self.min_di_spread > 20:
                logger.error("最小DI差值必须在0-20之间")
                return False
            
            if self.min_adx_slope < 0 or self.min_adx_slope > 10:
                logger.error("最小ADX斜率必须在0-10之间")
                return False
            
            logger.info("✅ ADX策略配置验证通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ ADX策略配置验证失败: {e}")
            return False
    
    def get_required_data_count(self) -> int:
        """获取所需的最小数据点数"""
        # ADX需要的最小数据点数
        return max(self.period, 14) + 15  # 额外15个点用于DI和DX计算
    
    def to_dict(self) -> Dict[str, Any]:
        """将配置转换为字典"""
        return {
            # ADX参数
            "period": self.period,
            "strong_trend_threshold": self.strong_trend_threshold,
            "weak_trend_threshold": self.weak_trend_threshold,
            "very_strong_threshold": self.very_strong_threshold,
            "signal_confirmation_periods": self.signal_confirmation_periods,
            
            # 自定义交易参数
            "custom_symbol": self.custom_symbol,
            "custom_leverage": self.custom_leverage,
            "initial_margin": self.initial_margin,
            "take_profit_percent": self.take_profit_percent,
            "stop_loss_percent": self.stop_loss_percent,
            
            # 风险控制
            "max_total_loss_percent": self.max_total_loss_percent,
            "max_investment_ratio": self.max_investment_ratio,
            "max_add_count": self.max_add_count,
            
            # 信号过滤
            "enable_trend_strength_filter": self.enable_trend_strength_filter,
            "min_di_spread": self.min_di_spread,
            "enable_adx_slope_filter": self.enable_adx_slope_filter,
            "min_adx_slope": self.min_adx_slope,
            "enable_di_crossover": self.enable_di_crossover,
            
            # 立即开仓
            "immediate_open_enabled": self.immediate_open_enabled,
            "immediate_check_interval": self.immediate_check_interval,
            
            # 交易设置
            "exchange_name": self.exchange_name,
        }
    
    def from_dict(self, config_dict: Dict[str, Any]):
        """从字典加载配置"""
        for key, value in config_dict.items():
            if hasattr(self, key):
                setattr(self, key, value)
            else:
                logger.warning(f"未知的配置参数: {key}")
    
    def __str__(self) -> str:
        """配置信息的字符串表示"""
        return f"""ADX策略配置:
        ADX参数: 周期={self.period}, 强趋势={self.strong_trend_threshold}, 弱趋势={self.weak_trend_threshold}
        自定义交易: 交易对={self.custom_symbol}, 杠杆={self.custom_leverage}x, 保证金={self.initial_margin}
        风险控制: 止盈={self.take_profit_percent}%, 止损={self.stop_loss_percent}%
        加仓设置: 最大次数={self.max_add_count}, 最大亏损={self.max_total_loss_percent}%
        立即开仓: {'启用' if self.immediate_open_enabled else '禁用'}
        交易所: {self.exchange_name}"""

class ADXCalculator:
    """平均方向指数计算器 - 核心计算逻辑"""

    def __init__(self, period: int = 14):
        self.period = period

    def calculate_adx(self, highs: List[float], lows: List[float], closes: List[float],
                     min_periods: int = None) -> Optional[ADXData]:
        """
        计算ADX指标（支持部分数据计算）

        Args:
            highs: 最高价列表
            lows: 最低价列表
            closes: 收盘价列表
            min_periods: 最小计算周期，默认为period

        Returns:
            Optional[ADXData]: ADX数据（包含可靠性评估）
        """
        # 设置最小计算周期
        if min_periods is None:
            min_periods = max(self.period, 14)  # 至少需要period个数据点

        data_count = len(closes)
        required_count = self.period + 15  # 额外数据用于DI和DX计算

        # 数据完全不足
        if data_count < min_periods or len(highs) != data_count or len(lows) != data_count:
            logger.warning(f"❌ ADX数据严重不足，需要至少{min_periods}个，当前{data_count}个")
            return None

        try:
            # 计算可靠性评分
            if data_count >= required_count:
                reliability = 1.0  # 完全可靠
                calculation_period = required_count
                logger.info(f"✅ ADX数据充足: {data_count}/{required_count} (可靠性: 100%)")
            else:
                reliability = data_count / required_count  # 部分可靠
                calculation_period = data_count
                logger.warning(f"⚠️ ADX数据不足: {data_count}/{required_count} (可靠性: {reliability*100:.1f}%)")

            # 使用实际可用的数据
            calc_highs = highs[-calculation_period:] if calculation_period < data_count else highs
            calc_lows = lows[-calculation_period:] if calculation_period < data_count else lows
            calc_closes = closes[-calculation_period:] if calculation_period < data_count else closes

            # 转换为pandas Series进行计算
            high_series = pd.Series(calc_highs)
            low_series = pd.Series(calc_lows)
            close_series = pd.Series(calc_closes)

            # 计算True Range (TR)
            tr = self._calculate_true_range(high_series, low_series, close_series)

            # 计算方向移动 (+DM, -DM)
            plus_dm, minus_dm = self._calculate_directional_movement(high_series, low_series)

            # 计算平滑的TR, +DM, -DM
            atr = self._smooth_series(tr, self.period)
            plus_dm_smooth = self._smooth_series(plus_dm, self.period)
            minus_dm_smooth = self._smooth_series(minus_dm, self.period)

            # 计算方向指标 (+DI, -DI)
            plus_di = self._calculate_di(plus_dm_smooth, atr)
            minus_di = self._calculate_di(minus_dm_smooth, atr)

            # 计算DX
            dx = self._calculate_dx(plus_di, minus_di)

            # 计算ADX
            adx = self._smooth_series(dx, self.period)

            # 获取最新值
            adx_value = adx.iloc[-1]
            plus_di_value = plus_di.iloc[-1]
            minus_di_value = minus_di.iloc[-1]
            dx_value = dx.iloc[-1]

            # 分析ADX信号
            signal = self._analyze_adx_signal(adx.tolist(), plus_di.tolist(), minus_di.tolist())

            # 计算趋势强度
            trend_strength = self._calculate_trend_strength(adx_value)

            # 计算趋势方向
            trend_direction = self._calculate_trend_direction(plus_di_value, minus_di_value)

            # 计算DI差值
            di_spread = plus_di_value - minus_di_value

            # 计算ADX斜率
            adx_slope = self._calculate_adx_slope(adx.tolist())

            # 记录详细信息
            logger.info(f"📊 ADX计算完成: ADX={adx_value:.2f}, +DI={plus_di_value:.2f}, -DI={minus_di_value:.2f}")
            logger.info(f"📊 趋势: 强度={trend_strength}, 方向={trend_direction}, DI差值={di_spread:.2f}")
            logger.info(f"📊 信号: {signal.value}, ADX斜率: {adx_slope:.3f}")
            logger.info(f"📊 数据统计: 使用{len(calc_closes)}个数据点, 可靠性={reliability:.2f}")

            return ADXData(
                adx_value=round(adx_value, 2),
                plus_di=round(plus_di_value, 2),
                minus_di=round(minus_di_value, 2),
                dx_value=round(dx_value, 2),
                signal=signal,
                trend_strength=trend_strength,
                trend_direction=trend_direction,
                di_spread=round(di_spread, 2),
                adx_slope=round(adx_slope, 3),
                reliability=reliability,
                data_count=len(calc_closes),
                required_count=required_count,
                calculation_period=len(calc_closes)
            )

        except Exception as e:
            logger.error(f"计算ADX异常: {e}")
            return None

    def _calculate_true_range(self, highs: pd.Series, lows: pd.Series, closes: pd.Series) -> pd.Series:
        """
        计算真实波幅 (True Range)

        Args:
            highs: 最高价序列
            lows: 最低价序列
            closes: 收盘价序列

        Returns:
            pd.Series: 真实波幅序列
        """
        try:
            # TR = max(H-L, |H-C_prev|, |L-C_prev|)
            prev_close = closes.shift(1)

            tr1 = highs - lows
            tr2 = abs(highs - prev_close)
            tr3 = abs(lows - prev_close)

            tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)

            # 第一个值使用 H-L
            tr.iloc[0] = highs.iloc[0] - lows.iloc[0]

            return tr

        except Exception as e:
            logger.error(f"计算真实波幅异常: {e}")
            return pd.Series([0.0] * len(highs))

    def _calculate_directional_movement(self, highs: pd.Series, lows: pd.Series) -> Tuple[pd.Series, pd.Series]:
        """
        计算方向移动 (+DM, -DM)

        Args:
            highs: 最高价序列
            lows: 最低价序列

        Returns:
            Tuple[pd.Series, pd.Series]: (+DM, -DM)
        """
        try:
            # 计算价格变化
            high_diff = highs.diff()
            low_diff = -lows.diff()  # 注意负号

            # +DM: 当前高点比前一高点高，且高点变化大于低点变化
            plus_dm = pd.Series([0.0] * len(highs), index=highs.index)
            minus_dm = pd.Series([0.0] * len(highs), index=highs.index)

            for i in range(1, len(highs)):
                if high_diff.iloc[i] > low_diff.iloc[i] and high_diff.iloc[i] > 0:
                    plus_dm.iloc[i] = high_diff.iloc[i]
                elif low_diff.iloc[i] > high_diff.iloc[i] and low_diff.iloc[i] > 0:
                    minus_dm.iloc[i] = low_diff.iloc[i]

            return plus_dm, minus_dm

        except Exception as e:
            logger.error(f"计算方向移动异常: {e}")
            return pd.Series([0.0] * len(highs)), pd.Series([0.0] * len(highs))

    def _smooth_series(self, series: pd.Series, period: int) -> pd.Series:
        """
        使用Wilder平滑方法平滑序列

        Args:
            series: 待平滑的序列
            period: 平滑周期

        Returns:
            pd.Series: 平滑后的序列
        """
        try:
            # Wilder平滑：类似于EMA，但使用1/period作为alpha
            alpha = 1.0 / period
            smoothed = series.ewm(alpha=alpha, adjust=False).mean()
            return smoothed

        except Exception as e:
            logger.error(f"平滑序列异常: {e}")
            return series

    def _calculate_di(self, dm_smooth: pd.Series, atr: pd.Series) -> pd.Series:
        """
        计算方向指标 (DI)

        Args:
            dm_smooth: 平滑的方向移动
            atr: 平均真实波幅

        Returns:
            pd.Series: DI序列
        """
        try:
            # DI = (DM_smooth / ATR) * 100
            di = (dm_smooth / atr) * 100

            # 处理除零情况
            di = di.fillna(0)
            di = di.replace([np.inf, -np.inf], 0)

            return di

        except Exception as e:
            logger.error(f"计算DI异常: {e}")
            return pd.Series([0.0] * len(dm_smooth))

    def _calculate_dx(self, plus_di: pd.Series, minus_di: pd.Series) -> pd.Series:
        """
        计算DX

        Args:
            plus_di: +DI序列
            minus_di: -DI序列

        Returns:
            pd.Series: DX序列
        """
        try:
            # DX = |+DI - -DI| / (+DI + -DI) * 100
            di_sum = plus_di + minus_di
            di_diff = abs(plus_di - minus_di)

            # 避免除零
            dx = pd.Series([0.0] * len(plus_di), index=plus_di.index)
            mask = di_sum != 0
            dx[mask] = (di_diff[mask] / di_sum[mask]) * 100

            return dx

        except Exception as e:
            logger.error(f"计算DX异常: {e}")
            return pd.Series([0.0] * len(plus_di))

    def _analyze_adx_signal(self, adx_list: List[float], plus_di_list: List[float],
                           minus_di_list: List[float]) -> ADXSignal:
        """
        分析ADX信号

        Args:
            adx_list: ADX历史值
            plus_di_list: +DI历史值
            minus_di_list: -DI历史值

        Returns:
            ADXSignal: ADX信号类型
        """
        try:
            if len(adx_list) < 2 or len(plus_di_list) < 2 or len(minus_di_list) < 2:
                return ADXSignal.NEUTRAL

            current_adx = adx_list[-1]
            prev_adx = adx_list[-2]
            current_plus_di = plus_di_list[-1]
            current_minus_di = minus_di_list[-1]
            prev_plus_di = plus_di_list[-2]
            prev_minus_di = minus_di_list[-2]

            # 检测DI交叉
            if prev_plus_di <= prev_minus_di and current_plus_di > current_minus_di:
                logger.info(f"🟢 +DI上穿-DI: +DI({current_plus_di:.2f}) > -DI({current_minus_di:.2f})")
                return ADXSignal.DI_CROSSOVER_BULLISH
            elif prev_plus_di >= prev_minus_di and current_plus_di < current_minus_di:
                logger.info(f"🔴 -DI上穿+DI: -DI({current_minus_di:.2f}) > +DI({current_plus_di:.2f})")
                return ADXSignal.DI_CROSSOVER_BEARISH

            # 检测强趋势信号
            if current_adx >= 25:
                if current_plus_di > current_minus_di:
                    if current_adx > prev_adx:  # ADX上升
                        logger.info(f"🚀 强势多头信号: ADX={current_adx:.2f}↑, +DI > -DI")
                        return ADXSignal.STRONG_BULLISH
                    else:
                        logger.debug(f"📈 弱势多头: ADX={current_adx:.2f}↓, +DI > -DI")
                        return ADXSignal.WEAK_BULLISH
                else:
                    if current_adx > prev_adx:  # ADX上升
                        logger.info(f"💥 强势空头信号: ADX={current_adx:.2f}↑, -DI > +DI")
                        return ADXSignal.STRONG_BEARISH
                    else:
                        logger.debug(f"📉 弱势空头: ADX={current_adx:.2f}↓, -DI > +DI")
                        return ADXSignal.WEAK_BEARISH

            # 检测趋势变化
            elif current_adx >= 20:
                if current_adx > prev_adx:
                    logger.debug(f"📊 趋势加强: ADX={current_adx:.2f}↑")
                    return ADXSignal.TREND_STRENGTHENING
                else:
                    logger.debug(f"📊 趋势减弱: ADX={current_adx:.2f}↓")
                    return ADXSignal.TREND_WEAKENING

            # 无趋势
            else:
                logger.debug(f"⚪ 无趋势: ADX={current_adx:.2f} < 20")
                return ADXSignal.NO_TREND

        except Exception as e:
            logger.error(f"分析ADX信号异常: {e}")
            return ADXSignal.NEUTRAL

    def _calculate_trend_strength(self, adx_value: float) -> str:
        """
        计算趋势强度

        Args:
            adx_value: ADX值

        Returns:
            str: 趋势强度描述
        """
        try:
            if adx_value >= 40:
                return "极强"
            elif adx_value >= 25:
                return "强"
            elif adx_value >= 20:
                return "中"
            else:
                return "弱"

        except Exception as e:
            logger.error(f"计算趋势强度异常: {e}")
            return "未知"

    def _calculate_trend_direction(self, plus_di: float, minus_di: float) -> str:
        """
        计算趋势方向

        Args:
            plus_di: +DI值
            minus_di: -DI值

        Returns:
            str: 趋势方向
        """
        try:
            if plus_di > minus_di:
                return "多头"
            elif minus_di > plus_di:
                return "空头"
            else:
                return "震荡"

        except Exception as e:
            logger.error(f"计算趋势方向异常: {e}")
            return "未知"

    def _calculate_adx_slope(self, adx_list: List[float]) -> float:
        """
        计算ADX斜率

        Args:
            adx_list: ADX历史值

        Returns:
            float: ADX斜率
        """
        try:
            if len(adx_list) < 3:
                return 0.0

            # 计算最近3个点的斜率
            recent_slope = (adx_list[-1] - adx_list[-3]) / 2
            return recent_slope

        except Exception as e:
            logger.error(f"计算ADX斜率异常: {e}")
            return 0.0

    def calculate_adx_signal_direction(self, adx_data: ADXData) -> str:
        """
        基于ADX数据计算开仓方向

        Args:
            adx_data: ADX数据

        Returns:
            str: 开仓方向 ("long", "short", "hold")
        """
        try:
            # 强势多头信号 - 做多
            if adx_data.signal == ADXSignal.STRONG_BULLISH:
                logger.info("🚀 ADX强势多头做多信号")
                return "long"

            # 强势空头信号 - 做空
            elif adx_data.signal == ADXSignal.STRONG_BEARISH:
                logger.info("💥 ADX强势空头做空信号")
                return "short"

            # +DI上穿-DI信号 - 做多
            elif adx_data.signal == ADXSignal.DI_CROSSOVER_BULLISH:
                if adx_data.adx_value >= 20:  # 需要一定的趋势强度
                    logger.info("📈 +DI上穿-DI做多信号")
                    return "long"
                else:
                    logger.info("⚠️ +DI上穿-DI但ADX过低")
                    return "hold"

            # -DI上穿+DI信号 - 做空
            elif adx_data.signal == ADXSignal.DI_CROSSOVER_BEARISH:
                if adx_data.adx_value >= 20:  # 需要一定的趋势强度
                    logger.info("📉 -DI上穿+DI做空信号")
                    return "short"
                else:
                    logger.info("⚠️ -DI上穿+DI但ADX过低")
                    return "hold"

            # 趋势加强信号
            elif adx_data.signal == ADXSignal.TREND_STRENGTHENING:
                if adx_data.plus_di > adx_data.minus_di and adx_data.di_spread > 3:
                    logger.info("📊 趋势加强做多信号")
                    return "long"
                elif adx_data.minus_di > adx_data.plus_di and adx_data.di_spread < -3:
                    logger.info("📊 趋势加强做空信号")
                    return "short"
                else:
                    logger.debug("📊 趋势加强但方向不明确")
                    return "hold"

            # 其他情况保持观望
            else:
                logger.debug(f"⏸️ 保持观望: ADX={adx_data.adx_value:.2f}, 信号={adx_data.signal.value}")
                return "hold"

        except Exception as e:
            logger.error(f"计算ADX开仓方向异常: {e}")
            return "hold"

    def detect_adx_signal(self, highs: List[float], lows: List[float],
                         closes: List[float], min_periods: int = None) -> Tuple[bool, str, float]:
        """
        专门检测ADX信号

        Args:
            highs: 最高价列表
            lows: 最低价列表
            closes: 收盘价列表
            min_periods: 最小计算周期

        Returns:
            Tuple[bool, str, float]: (是否有信号, 信号类型, 信号强度)
        """
        try:
            if len(closes) < self.period:
                return False, "hold", 0.0

            # 计算ADX数据
            adx_data = self.calculate_adx(highs, lows, closes, min_periods)
            if not adx_data:
                return False, "hold", 0.0

            # 计算开仓方向
            direction = self.calculate_adx_signal_direction(adx_data)

            if direction != "hold":
                # 计算信号强度
                if adx_data.signal in [ADXSignal.STRONG_BULLISH, ADXSignal.STRONG_BEARISH]:
                    signal_strength = 0.8 + min(0.2, adx_data.adx_value / 100)  # 强势信号高强度
                elif adx_data.signal in [ADXSignal.DI_CROSSOVER_BULLISH, ADXSignal.DI_CROSSOVER_BEARISH]:
                    signal_strength = 0.7 + min(0.2, abs(adx_data.di_spread) / 20)  # 交叉信号基于DI差值
                elif adx_data.signal == ADXSignal.TREND_STRENGTHENING:
                    signal_strength = 0.6 + min(0.3, adx_data.adx_slope / 5)  # 趋势加强基于斜率
                else:
                    signal_strength = 0.4  # 其他信号较低强度

                # 结合可靠性调整强度
                signal_strength *= adx_data.reliability

                return True, direction, signal_strength

            return False, "hold", 0.0

        except Exception as e:
            logger.error(f"检测ADX信号异常: {e}")
            return False, "hold", 0.0

    def is_signal_confirmed(self, highs: List[float], lows: List[float],
                           closes: List[float], signal_type: str,
                           confirmation_periods: int = 2) -> bool:
        """
        确认ADX信号的有效性

        Args:
            highs: 最高价列表
            lows: 最低价列表
            closes: 收盘价列表
            signal_type: 信号类型
            confirmation_periods: 确认周期数

        Returns:
            bool: 信号是否确认
        """
        try:
            if len(closes) < confirmation_periods + 1:
                return False

            confirmed_count = 0

            # 检查最近几个周期的信号一致性
            for i in range(confirmation_periods):
                if i == 0:
                    check_highs = highs
                    check_lows = lows
                    check_closes = closes
                else:
                    check_highs = highs[:-(i)]
                    check_lows = lows[:-(i)]
                    check_closes = closes[:-(i)]

                has_signal, detected_type, strength = self.detect_adx_signal(
                    check_highs, check_lows, check_closes)

                if has_signal and detected_type == signal_type:
                    confirmed_count += 1

            # 需要至少一半的周期确认信号
            required_confirmations = max(1, confirmation_periods // 2)
            is_confirmed = confirmed_count >= required_confirmations

            if is_confirmed:
                logger.info(f"✅ ADX信号确认: {signal_type}, 确认次数: {confirmed_count}/{confirmation_periods}")
            else:
                logger.warning(f"⚠️ ADX信号未确认: {signal_type}, 确认次数: {confirmed_count}/{confirmation_periods}")

            return is_confirmed

        except Exception as e:
            logger.error(f"确认ADX信号异常: {e}")
            return False

class ADXStrategy:
    """平均方向指数策略主类"""

    def __init__(self, config: ADXConfig, exchange_manager=None):
        self.config = config
        self.exchange_manager = exchange_manager
        self.calculator = ADXCalculator(period=config.period)

        # 策略状态
        self.is_running = False
        self.last_signal_time = None
        self.last_order_time = None
        self.current_position = None
        self.add_position_records: List[AddPositionRecord] = []

        # 性能监控
        self.total_trades = 0
        self.successful_trades = 0
        self.total_pnl = 0.0

        # 异步任务管理
        self.monitoring_task = None
        self.immediate_open_task = None

        logger.info(f"🎯 ADX策略初始化完成: {config}")

    async def start(self):
        """启动ADX策略"""
        try:
            if self.is_running:
                logger.warning("⚠️ ADX策略已在运行中")
                return

            # 验证配置
            if not self.config.validate():
                logger.error("❌ ADX策略配置验证失败")
                return

            # 验证交易所连接
            if not self.exchange_manager:
                logger.error("❌ 交易所管理器未设置")
                return

            self.is_running = True
            logger.info("🚀 ADX策略启动成功")

            # 启动立即开仓功能（如果启用）
            if self.config.immediate_open_enabled:
                await self._start_immediate_open_monitor()

            # 启动持仓监控
            self.monitoring_task = asyncio.create_task(self._start_position_monitor())

        except Exception as e:
            logger.error(f"❌ ADX策略启动失败: {e}")
            self.is_running = False

    async def stop(self):
        """停止ADX策略"""
        try:
            self.is_running = False

            # 停止异步任务
            if self.monitoring_task:
                self.monitoring_task.cancel()
                try:
                    await self.monitoring_task
                except asyncio.CancelledError:
                    pass

            if self.immediate_open_task:
                self.immediate_open_task.cancel()
                try:
                    await self.immediate_open_task
                except asyncio.CancelledError:
                    pass

            logger.info("🛑 ADX策略已停止")

        except Exception as e:
            logger.error(f"❌ ADX策略停止失败: {e}")

    @monitor_task(TaskType.OPENING_CHECK)
    async def _start_immediate_open_monitor(self):
        """启动立即开仓监控"""
        try:
            logger.info(f"🔍 启动ADX立即开仓监控，检测间隔: {self.config.immediate_check_interval}秒")

            async def check_immediate_open():
                while self.is_running and self.config.immediate_open_enabled:
                    try:
                        await self._check_immediate_open_opportunity()
                        await asyncio.sleep(self.config.immediate_check_interval)
                    except Exception as e:
                        logger.error(f"❌ 立即开仓检查异常: {e}")
                        await asyncio.sleep(5)  # 错误时短暂等待

            self.immediate_open_task = asyncio.create_task(check_immediate_open())

        except Exception as e:
            logger.error(f"❌ 启动立即开仓监控失败: {e}")

    @monitor_task(TaskType.OPENING_CHECK)
    async def _check_immediate_open_opportunity(self):
        """检查立即开仓机会"""
        try:
            # 检查是否有持仓
            if await self._has_position():
                logger.debug("已有持仓，跳过开仓检查")
                return

            # 检查冷却时间
            if not self._check_order_cooldown():
                logger.debug("冷却时间未到，跳过开仓检查")
                return

            # 获取当前价格数据
            price_data = await self._get_price_data()
            if not price_data or len(price_data['closes']) < self.config.get_required_data_count():
                logger.warning("⚠️ 价格数据不足，无法进行ADX分析")
                return

            # 检测ADX信号
            has_signal, signal_type, signal_strength = self.calculator.detect_adx_signal(
                price_data['highs'], price_data['lows'], price_data['closes'])

            if not has_signal:
                logger.debug("未检测到ADX信号")
                return

            # 信号确认（如果启用）
            if self.config.signal_confirmation_periods > 1:
                if not self.calculator.is_signal_confirmed(
                    price_data['highs'], price_data['lows'], price_data['closes'],
                    signal_type, self.config.signal_confirmation_periods):
                    logger.warning(f"⚠️ ADX信号未确认: {signal_type}")
                    return

            # 信号过滤
            if not self._filter_signal(price_data, signal_type, signal_strength):
                logger.warning(f"⚠️ ADX信号被过滤: {signal_type}")
                return

            logger.info(f"🚀 准备执行ADX开仓: 方向={signal_type}, 强度={signal_strength:.3f}")
            await self._execute_immediate_opening(signal_type, signal_strength)

        except Exception as e:
            logger.error(f"❌ 检查立即开仓机会异常: {e}")

    def _filter_signal(self, price_data: Dict, signal_type: str, signal_strength: float) -> bool:
        """
        过滤ADX信号

        Args:
            price_data: 价格数据字典
            signal_type: 信号类型
            signal_strength: 信号强度

        Returns:
            bool: 信号是否通过过滤
        """
        try:
            # 计算ADX数据用于过滤
            adx_data = self.calculator.calculate_adx(
                price_data['highs'], price_data['lows'], price_data['closes'])
            if not adx_data:
                return False

            # 趋势强度过滤
            if self.config.enable_trend_strength_filter:
                if adx_data.adx_value < self.config.weak_trend_threshold:
                    logger.debug(f"趋势强度不足: ADX={adx_data.adx_value:.2f} < {self.config.weak_trend_threshold}")
                    return False

            # DI差值过滤
            if abs(adx_data.di_spread) < self.config.min_di_spread:
                logger.debug(f"DI差值不足: {abs(adx_data.di_spread):.2f} < {self.config.min_di_spread}")
                return False

            # ADX斜率过滤
            if self.config.enable_adx_slope_filter:
                if abs(adx_data.adx_slope) < self.config.min_adx_slope:
                    logger.debug(f"ADX斜率不足: {abs(adx_data.adx_slope):.3f} < {self.config.min_adx_slope}")
                    return False

            return True

        except Exception as e:
            logger.error(f"过滤ADX信号异常: {e}")
            return False

    async def _execute_immediate_opening(self, direction: str, signal_strength: float):
        """执行立即开仓"""
        try:
            logger.info(f"🚀 [ADX立即开仓] 执行开仓: 方向={direction}, 强度={signal_strength:.3f}")

            # 获取当前价格
            current_price = await self._get_current_price()
            if not current_price:
                logger.error("❌ 无法获取当前价格")
                return

            # 计算开仓数量
            quantity = self._calculate_position_size(current_price)
            if quantity <= 0:
                logger.error("❌ 计算的开仓数量无效")
                return

            # 计算止损价格
            stop_loss_price = self._calculate_stop_loss_price(current_price, direction)

            # 记录开仓信息
            logger.info(f"📊 [ADX开仓详情] 价格: {current_price:.4f}, 数量: {quantity}, 止损: {stop_loss_price:.4f}")
            logger.info(f"📊 [ADX信号详情] 方向: {direction}, 强度: {signal_strength:.3f}")
            logger.info(f"📊 [自定义设置] 交易对: {self.config.custom_symbol}, 杠杆: {self.config.custom_leverage}x")
            logger.info(f"📊 [ADX参数] 周期: {self.config.period}, 强趋势: {self.config.strong_trend_threshold}, 弱趋势: {self.config.weak_trend_threshold}")

            # 执行开仓
            order_result = await self._place_market_order(direction, quantity, current_price)
            if order_result:
                logger.info(f"✅ [ADX立即开仓] 开仓成功: {direction}仓位，价格: {current_price:.4f}，数量: {quantity}")

                # 设置止损订单
                await self._place_stop_loss_order(direction, quantity, stop_loss_price)

                # 更新策略状态
                self.last_order_time = datetime.now()
                self.last_signal_time = datetime.now()
                self.total_trades += 1

                # 记录信号强度用于后续分析
                if not hasattr(self, 'signal_history'):
                    self.signal_history = []
                self.signal_history.append({
                    'timestamp': datetime.now(),
                    'direction': direction,
                    'signal_strength': signal_strength,
                    'price': current_price,
                    'quantity': quantity,
                    'custom_symbol': self.config.custom_symbol,
                    'custom_leverage': self.config.custom_leverage,
                    'adx_period': self.config.period,
                    'strong_trend_threshold': self.config.strong_trend_threshold,
                    'weak_trend_threshold': self.config.weak_trend_threshold
                })

                # 禁用立即开仓功能（开仓后自动禁用）
                self.config.immediate_open_enabled = False
                logger.info("🔒 [ADX立即开仓] 开仓后自动禁用立即开仓功能")

            else:
                logger.error("❌ [ADX立即开仓] 开仓失败")

        except Exception as e:
            logger.error(f"❌ 执行立即开仓异常: {e}")

    def _calculate_stop_loss_price(self, entry_price: float, direction: str) -> float:
        """计算止损价格"""
        try:
            stop_loss_ratio = self.config.stop_loss_percent / 100.0

            if direction == "long":
                # 多仓止损价格 = 入场价格 * (1 - 止损比例)
                stop_loss_price = entry_price * (1 - stop_loss_ratio)
            else:
                # 空仓止损价格 = 入场价格 * (1 + 止损比例)
                stop_loss_price = entry_price * (1 + stop_loss_ratio)

            logger.info(f"💡 [止损计算] 入场价: {entry_price:.4f}, 方向: {direction}, 止损价: {stop_loss_price:.4f}")
            return round(stop_loss_price, self.config.price_precision)

        except Exception as e:
            logger.error(f"❌ 计算止损价格异常: {e}")
            return entry_price * 0.96 if direction == "long" else entry_price * 1.04  # 默认4%止损

    async def _place_stop_loss_order(self, direction: str, quantity: float, stop_loss_price: float):
        """设置止损订单"""
        try:
            if not self.exchange_manager:
                logger.error("❌ 交易所管理器未设置")
                return

            # 确定止损订单方向（与开仓方向相反）
            stop_side = "sell" if direction == "long" else "buy"

            logger.info(f"🛡️ [止损订单] 设置止损: 方向={stop_side}, 数量={quantity}, 止损价={stop_loss_price:.4f}")
            logger.info(f"🛡️ [止损订单] 交易对: {self.config.custom_symbol}")

            # 根据交易所类型选择止损订单方式
            exchange_name = self.config.exchange_name.lower()

            if exchange_name == "okx":
                await self._place_okx_algo_stop_loss_order(stop_side, quantity, stop_loss_price)
            elif exchange_name == "gate":
                await self._place_gate_stop_loss_order(stop_side, quantity, stop_loss_price)
            else:
                # 默认使用限价单作为止损
                await self._place_limit_stop_loss_order(stop_side, quantity, stop_loss_price)

        except Exception as e:
            logger.error(f"❌ 设置止损订单异常: {e}")

    async def _place_okx_algo_stop_loss_order(self, side: str, quantity: float, stop_loss_price: float):
        """使用OKX策略委托接口设置止损订单"""
        try:
            if hasattr(self.exchange_manager.exchange, 'place_algo_order'):
                # 使用OKX策略委托接口
                order_result = await self.exchange_manager.exchange.place_algo_order(
                    symbol=self.config.custom_symbol,
                    side=side,
                    order_type="conditional",
                    quantity=quantity,
                    trigger_price=stop_loss_price,
                    order_price=-1,  # 市价执行
                    reduce_only=True
                )

                if order_result:
                    logger.info(f"✅ [OKX策略委托] 止损订单设置成功: {order_result.order_id}")
                else:
                    logger.warning("⚠️ [OKX策略委托] 止损订单设置失败，尝试限价单")
                    await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)
            else:
                logger.warning("⚠️ OKX交易所不支持策略委托，使用限价单")
                await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)

        except Exception as e:
            logger.error(f"❌ OKX策略委托止损失败: {e}")
            await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)

    async def _place_gate_stop_loss_order(self, side: str, quantity: float, stop_loss_price: float):
        """使用Gate.io止损订单"""
        try:
            # Gate.io的止损订单实现
            logger.info(f"🔧 [Gate.io止损] 设置止损订单: {side}, {quantity}, {stop_loss_price}")

            # 暂时使用限价单作为备选
            await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)

        except Exception as e:
            logger.error(f"❌ Gate.io止损订单失败: {e}")
            await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)

    async def _place_limit_stop_loss_order(self, side: str, quantity: float, stop_loss_price: float):
        """使用限价单作为止损（备选方案）"""
        try:
            logger.info(f"📋 [限价止损] 设置限价止损单: {side}, {quantity}, {stop_loss_price}")

            # 这里可以实现限价单止损逻辑
            # 注意：限价单不是真正的止损，需要持续监控价格

        except Exception as e:
            logger.error(f"❌ 限价止损订单失败: {e}")

    async def _start_position_monitor(self):
        """启动持仓监控"""
        try:
            logger.info("👁️ 启动ADX持仓监控")

            while self.is_running:
                try:
                    await self._check_position_status()
                    await asyncio.sleep(self.config.position_check_interval)
                except Exception as e:
                    logger.error(f"❌ 持仓监控异常: {e}")
                    await asyncio.sleep(5)

        except Exception as e:
            logger.error(f"❌ 持仓监控启动失败: {e}")

    async def _check_position_status(self):
        """检查持仓状态"""
        try:
            # 这里实现持仓状态检查逻辑
            # 包括止盈止损检查、加仓条件检查等
            pass

        except Exception as e:
            logger.error(f"❌ 检查持仓状态异常: {e}")

    # 辅助方法
    async def _has_position(self) -> bool:
        """检查是否有持仓"""
        try:
            if not self.exchange_manager:
                return False

            # 这里实现持仓检查逻辑
            return False  # 临时返回

        except Exception as e:
            logger.error(f"❌ 检查持仓异常: {e}")
            return False

    def _check_order_cooldown(self) -> bool:
        """检查下单冷却时间"""
        try:
            if not self.last_order_time:
                return True

            elapsed = (datetime.now() - self.last_order_time).total_seconds()
            return elapsed >= self.config.order_cooldown

        except Exception as e:
            logger.error(f"❌ 检查冷却时间异常: {e}")
            return True

    async def _get_price_data(self) -> Dict:
        """获取价格数据"""
        try:
            if not self.exchange_manager:
                return {}

            # 这里实现价格数据获取逻辑
            return {
                'highs': [],
                'lows': [],
                'closes': []
            }  # 临时返回

        except Exception as e:
            logger.error(f"❌ 获取价格数据异常: {e}")
            return {}

    async def _get_current_price(self) -> Optional[float]:
        """获取当前价格"""
        try:
            if not self.exchange_manager:
                return None

            # 这里实现当前价格获取逻辑
            return None  # 临时返回

        except Exception as e:
            logger.error(f"❌ 获取当前价格异常: {e}")
            return None

    def _calculate_position_size(self, price: float) -> float:
        """计算开仓数量"""
        try:
            # 基于初始保证金和自定义杠杆计算开仓数量
            position_value = self.config.initial_margin * self.config.custom_leverage
            quantity = position_value / price

            # 应用精度
            quantity = round(quantity, self.config.quantity_precision)

            logger.info(f"💰 [仓位计算] 价格: {price:.4f}, 保证金: {self.config.initial_margin}, 杠杆: {self.config.custom_leverage}x, 数量: {quantity}")
            return quantity

        except Exception as e:
            logger.error(f"❌ 计算开仓数量异常: {e}")
            return 0.0

    async def _place_market_order(self, direction: str, quantity: float, price: float) -> bool:
        """下市价单"""
        try:
            if not self.exchange_manager:
                logger.error("❌ 交易所管理器未设置")
                return False

            logger.info(f"📈 [ADX市价下单] 方向: {direction}, 数量: {quantity}, 参考价格: {price:.4f}")
            logger.info(f"📊 [交易对] 使用自定义交易对: {self.config.custom_symbol}")
            logger.info(f"📊 [杠杆] 使用自定义杠杆: {self.config.custom_leverage}x")
            logger.info(f"📊 [ADX设置] 周期: {self.config.period}, 强趋势: {self.config.strong_trend_threshold}, 弱趋势: {self.config.weak_trend_threshold}")

            # 确定订单方向
            from exchanges.base_exchange import OrderSide
            if direction == "long":
                order_side = OrderSide.BUY
            elif direction == "short":
                order_side = OrderSide.SELL
            else:
                logger.error(f"❌ 未知的开仓方向: {direction}")
                return False

            # 调用交易所API下单
            order_result = await self.exchange_manager.place_market_order(
                symbol=self.config.custom_symbol,
                side=order_side,
                amount=quantity,
                reduce_only=False  # 开仓单，不是平仓单
            )

            if order_result and hasattr(order_result, 'order_id') and order_result.order_id:
                logger.info(f"✅ [ADX开仓] 开仓订单已提交: 订单ID {order_result.order_id}")

                # 等待一段时间让订单执行
                await asyncio.sleep(1)

                # 验证开仓是否成功
                positions_after = await self.exchange_manager.get_positions(self.config.custom_symbol)
                if positions_after and any(p.size > 0 for p in positions_after):
                    logger.info(f"✅ [ADX开仓] 开仓验证成功：已建立持仓")
                    return True
                else:
                    logger.warning(f"⚠️ [ADX开仓] 开仓可能未成功，未检测到持仓")
                    return False
            else:
                logger.error(f"❌ [ADX开仓] 开仓订单提交失败")
                return False

        except Exception as e:
            logger.error(f"❌ ADX市价下单异常: {e}")
            return False

    def get_strategy_status(self) -> Dict[str, Any]:
        """获取策略状态"""
        return {
            "is_running": self.is_running,
            "config": self.config.to_dict(),
            "total_trades": self.total_trades,
            "successful_trades": self.successful_trades,
            "success_rate": self.successful_trades / self.total_trades if self.total_trades > 0 else 0,
            "total_pnl": self.total_pnl,
            "add_position_count": len(self.add_position_records),
            "last_signal_time": self.last_signal_time.isoformat() if self.last_signal_time else None,
            "last_order_time": self.last_order_time.isoformat() if self.last_order_time else None,
            "custom_symbol": self.config.custom_symbol,
            "custom_leverage": self.config.custom_leverage,
            "adx_period": self.config.period,
            "strong_trend_threshold": self.config.strong_trend_threshold,
            "weak_trend_threshold": self.config.weak_trend_threshold,
        }
