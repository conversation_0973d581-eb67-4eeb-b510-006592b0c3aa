"""
GUI调试启动程序
"""
import sys
import os
import logging
import traceback

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def main():
    """主函数"""
    try:
        print("🚀 开始启动BitV GUI系统...")
        
        # 1. 测试基础导入
        print("📦 1. 测试基础模块导入...")
        import tkinter as tk
        import asyncio
        print("✅ 基础模块导入成功")
        
        # 2. 测试交易所模块
        print("📦 2. 测试交易所模块导入...")
        from exchanges import OKXExchange, GateIOExchange, ExchangeFactory
        print("✅ 交易所模块导入成功")
        
        # 3. 测试核心模块
        print("📦 3. 测试核心模块导入...")
        from core import AsyncTradingController, TradingState
        print("✅ 核心模块导入成功")
        
        # 4. 测试GUI模块
        print("📦 4. 测试GUI模块导入...")
        from gui import MainWindow
        from utils.async_gui import run_async_gui, set_async_tk
        print("✅ GUI模块导入成功")
        
        # 5. 创建GUI应用
        print("🖥️ 5. 创建GUI应用...")
        
        def create_app():
            """创建应用"""
            try:
                print("创建Tkinter根窗口...")
                root = tk.Tk()
                root.title("BitV MACD智能加仓交易系统")
                root.geometry("1200x800")

                print("设置异步Tkinter...")
                async_tk = set_async_tk(root)

                print("创建主窗口...")
                app = MainWindow(root, async_tk)

                print("✅ GUI应用创建成功")
                return root, app

            except Exception as e:
                print(f"❌ 创建GUI应用失败: {e}")
                traceback.print_exc()
                return None, None
        
        # 6. 启动应用
        print("🎯 6. 启动GUI应用...")
        
        try:
            root, app = create_app()
            if root and app:
                print("🎉 GUI系统启动成功！")
                print("📝 主窗口已创建，开始事件循环...")
                root.mainloop()
            else:
                print("❌ GUI应用创建失败")
                
        except Exception as e:
            print(f"❌ GUI启动失败: {e}")
            traceback.print_exc()
            
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        traceback.print_exc()
        
    except Exception as e:
        print(f"❌ 系统启动失败: {e}")
        traceback.print_exc()
    
    finally:
        print("🔚 GUI系统退出")

if __name__ == "__main__":
    main()
