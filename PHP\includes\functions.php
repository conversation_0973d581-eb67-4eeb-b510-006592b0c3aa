<?php
/**
 * 全局辅助函数
 * 
 * @description 系统通用的辅助函数
 */

/**
 * 格式化数字
 * 
 * @param float $number 要格式化的数字
 * @param int $decimals 小数位数
 * @return string 格式化后的数字字符串
 */
if (!function_exists('formatNumber')) {
    function formatNumber($number, $decimals = 4) {
        if ($number === null || $number === '') {
            return '0.' . str_repeat('0', $decimals);
        }
        return number_format((float)$number, $decimals, '.', '');
    }
}

/**
 * 格式化百分比
 * 
 * @param float $number 要格式化的数字（0-1之间）
 * @param int $decimals 小数位数
 * @return string 格式化后的百分比字符串
 */
if (!function_exists('formatPercentage')) {
    function formatPercentage($number, $decimals = 2) {
        if ($number === null || $number === '') {
            return '0.' . str_repeat('0', $decimals) . '%';
        }
        return number_format((float)$number * 100, $decimals, '.', '') . '%';
    }
}

/**
 * 格式化货币
 * 
 * @param float $amount 金额
 * @param string $currency 货币符号
 * @param int $decimals 小数位数
 * @return string 格式化后的货币字符串
 */
if (!function_exists('formatCurrency')) {
    function formatCurrency($amount, $currency = 'USDT', $decimals = 2) {
        if ($amount === null || $amount === '') {
            $amount = 0;
        }
        return formatNumber($amount, $decimals) . ' ' . $currency;
    }
}

/**
 * 格式化时间
 * 
 * @param string|int $timestamp 时间戳或时间字符串
 * @param string $format 时间格式
 * @return string 格式化后的时间字符串
 */
if (!function_exists('formatTime')) {
    function formatTime($timestamp, $format = 'Y-m-d H:i:s') {
        if (empty($timestamp)) {
            return '';
        }
        
        if (is_string($timestamp)) {
            $timestamp = strtotime($timestamp);
        }
        
        return date($format, $timestamp);
    }
}

/**
 * 安全的HTML输出
 * 
 * @param string $string 要输出的字符串
 * @return string 转义后的字符串
 */
if (!function_exists('e')) {
    function e($string) {
        return htmlspecialchars($string ?? '', ENT_QUOTES, 'UTF-8');
    }
}

/**
 * 获取数组值（带默认值）
 * 
 * @param array $array 数组
 * @param string $key 键名
 * @param mixed $default 默认值
 * @return mixed 数组值或默认值
 */
if (!function_exists('array_get')) {
    function array_get($array, $key, $default = null) {
        if (!is_array($array)) {
            return $default;
        }
        
        if (array_key_exists($key, $array)) {
            return $array[$key];
        }
        
        return $default;
    }
}

/**
 * 生成URL
 * 
 * @param string $path 路径
 * @return string 完整URL
 */
if (!function_exists('url')) {
    function url($path = '') {
        return Router::url($path);
    }
}

/**
 * 获取配置值
 * 
 * @param string $key 配置键名
 * @param mixed $default 默认值
 * @return mixed 配置值
 */
if (!function_exists('config')) {
    function config($key, $default = null) {
        $keys = explode('.', $key);
        $value = $GLOBALS;
        
        foreach ($keys as $k) {
            if (is_array($value) && array_key_exists($k, $value)) {
                $value = $value[$k];
            } else {
                return $default;
            }
        }
        
        return $value;
    }
}

/**
 * 检查是否为AJAX请求
 * 
 * @return bool
 */
if (!function_exists('isAjax')) {
    function isAjax() {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
}

/**
 * 获取客户端IP地址
 * 
 * @return string IP地址
 */
if (!function_exists('getClientIP')) {
    function getClientIP() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
}

/**
 * 生成随机字符串
 * 
 * @param int $length 长度
 * @return string 随机字符串
 */
if (!function_exists('generateRandomString')) {
    function generateRandomString($length = 32) {
        return bin2hex(random_bytes($length / 2));
    }
}

/**
 * 验证邮箱格式
 * 
 * @param string $email 邮箱地址
 * @return bool 是否有效
 */
if (!function_exists('isValidEmail')) {
    function isValidEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
}

/**
 * 计算文件大小（人类可读格式）
 * 
 * @param int $bytes 字节数
 * @param int $precision 精度
 * @return string 格式化后的文件大小
 */
if (!function_exists('formatFileSize')) {
    function formatFileSize($bytes, $precision = 2) {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}

/**
 * 调试输出
 * 
 * @param mixed $data 要输出的数据
 * @param bool $exit 是否退出
 */
if (!function_exists('dd')) {
    function dd($data, $exit = true) {
        echo '<pre>';
        var_dump($data);
        echo '</pre>';
        
        if ($exit) {
            exit;
        }
    }
}

/**
 * 记录日志
 * 
 * @param string $message 日志消息
 * @param string $level 日志级别
 * @param array $context 上下文数据
 */
if (!function_exists('writeLog')) {
    function writeLog($message, $level = 'INFO', $context = []) {
        $logFile = ROOT_PATH . '/logs/system.log';
        $logDir = dirname($logFile);
        
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        $timestamp = date('Y-m-d H:i:s');
        $contextStr = !empty($context) ? ' ' . json_encode($context) : '';
        $logEntry = "[{$timestamp}] {$level}: {$message}{$contextStr}" . PHP_EOL;
        
        file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }
}
?>
