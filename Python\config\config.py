"""
BitV MACD智能加仓交易系统 - 配置文件
"""

import os
from typing import Dict, Any

# 基础配置
class Config:
    # 服务器配置
    HOST = "0.0.0.0"
    PORT = 8000
    DEBUG = True
    
    # 数据库配置
    DATABASE_URL = "sqlite:///./trading.db"
    
    # 日志配置
    LOG_LEVEL = "INFO"
    LOG_FILE = "logs/trading.log"
    
    # 交易配置
    DEFAULT_TIMEFRAME = "30m"
    MAX_CONCURRENT_TRADES = 5
    
    # 风险管理
    MAX_LEVERAGE = 100
    MIN_MARGIN = 10
    MAX_MARGIN = 10000
    MAX_ADD_TIMES = 10
    
    # API配置
    REQUEST_TIMEOUT = 30
    RETRY_ATTEMPTS = 3
    
    # WebSocket配置
    WS_PORT = 8001
    WS_HEARTBEAT_INTERVAL = 30

# 支持的交易所配置
SUPPORTED_EXCHANGES = {
    "okx": {
        "name": "OKX",
        "display_name": "OKX交易所",
        "sandbox_url": "https://www.okx.com",
        "production_url": "https://www.okx.com",
        "requires_passphrase": True,
        "supported_symbols": {
            "BTC-USDT-SWAP": "BTC/USDT永续合约",
            "ETH-USDT-SWAP": "ETH/USDT永续合约",
            "LTC-USDT-SWAP": "LTC/USDT永续合约",
            "XRP-USDT-SWAP": "XRP/USDT永续合约",
            "ADA-USDT-SWAP": "ADA/USDT永续合约",
            "DOT-USDT-SWAP": "DOT/USDT永续合约",
            "LINK-USDT-SWAP": "LINK/USDT永续合约",
            "UNI-USDT-SWAP": "UNI/USDT永续合约"
        }
    },
    "gate": {
        "name": "Gate.io",
        "display_name": "Gate.io交易所",
        "sandbox_url": "https://fx-api-testnet.gateio.ws",
        "production_url": "https://api.gateio.ws",
        "requires_passphrase": False,
        "supported_symbols": {
            "BTC_USDT": "BTC/USDT永续合约",
            "ETH_USDT": "ETH/USDT永续合约",
            "LTC_USDT": "LTC/USDT永续合约",
            "XRP_USDT": "XRP/USDT永续合约",
            "ADA_USDT": "ADA/USDT永续合约",
            "DOT_USDT": "DOT/USDT永续合约",
            "LINK_USDT": "LINK/USDT永续合约",
            "UNI_USDT": "UNI/USDT永续合约"
        }
    }
}

# 支持的策略配置
SUPPORTED_STRATEGIES = {
    "macd": {
        "name": "MACD策略",
        "description": "基于MACD指标的智能加仓策略",
        "parameters": {
            "fast_period": {"default": 12, "min": 5, "max": 50},
            "slow_period": {"default": 26, "min": 10, "max": 100},
            "signal_period": {"default": 9, "min": 3, "max": 30},
            "min_signal_strength": {"default": 0.3, "min": 0.1, "max": 1.0}
        }
    },
    "bollinger": {
        "name": "布林带策略",
        "description": "基于布林带的趋势跟踪策略",
        "parameters": {
            "period": {"default": 20, "min": 10, "max": 50},
            "std_dev": {"default": 2.0, "min": 1.0, "max": 3.0}
        }
    },
    "rsi": {
        "name": "RSI策略",
        "description": "基于RSI指标的超买超卖策略",
        "parameters": {
            "period": {"default": 14, "min": 5, "max": 30},
            "overbought": {"default": 70, "min": 60, "max": 90},
            "oversold": {"default": 30, "min": 10, "max": 40}
        }
    }
}

# 时间周期配置
TIMEFRAMES = {
    "1m": "1分钟",
    "5m": "5分钟", 
    "15m": "15分钟",
    "30m": "30分钟",
    "1h": "1小时",
    "4h": "4小时",
    "1d": "1天"
}

# 加仓类型配置
ADD_POSITION_TYPES = {
    "fixed": {
        "name": "固定金额加仓",
        "description": "每次加仓使用固定金额"
    },
    "ratio": {
        "name": "比例加仓",
        "description": "按照初始保证金的比例加仓"
    },
    "martingale": {
        "name": "马丁格尔加仓",
        "description": "每次加仓金额翻倍"
    },
    "fibonacci": {
        "name": "斐波那契加仓",
        "description": "按照斐波那契数列加仓"
    }
}

# 风险管理配置
RISK_MANAGEMENT = {
    "max_leverage": 100,
    "min_margin": 10,
    "max_margin": 10000,
    "max_add_times": 10,
    "max_daily_loss_percent": 50,
    "max_position_ratio": 0.5,
    "liquidation_warning_distance": 0.1,
    "emergency_stop_loss": 0.8
}

# 交易状态配置
TRADING_STATES = {
    "idle": "空闲",
    "analyzing": "分析中",
    "opening": "开仓中",
    "monitoring": "监控中",
    "adding": "加仓中",
    "closing": "平仓中",
    "error": "错误",
    "stopped": "已停止"
}

# 环境变量配置
def get_env_config() -> Dict[str, Any]:
    """从环境变量获取配置"""
    return {
        "DATABASE_URL": os.getenv("DATABASE_URL", Config.DATABASE_URL),
        "DEBUG": os.getenv("DEBUG", "True").lower() == "true",
        "LOG_LEVEL": os.getenv("LOG_LEVEL", Config.LOG_LEVEL),
        "HOST": os.getenv("HOST", Config.HOST),
        "PORT": int(os.getenv("PORT", Config.PORT)),
        "WS_PORT": int(os.getenv("WS_PORT", Config.WS_PORT))
    }

# 获取完整配置
def get_config() -> Dict[str, Any]:
    """获取完整的系统配置"""
    base_config = {
        "server": {
            "host": Config.HOST,
            "port": Config.PORT,
            "debug": Config.DEBUG
        },
        "database": {
            "url": Config.DATABASE_URL
        },
        "trading": {
            "default_timeframe": Config.DEFAULT_TIMEFRAME,
            "max_concurrent_trades": Config.MAX_CONCURRENT_TRADES
        },
        "risk_management": RISK_MANAGEMENT,
        "supported_exchanges": SUPPORTED_EXCHANGES,
        "supported_strategies": SUPPORTED_STRATEGIES,
        "timeframes": TIMEFRAMES,
        "add_position_types": ADD_POSITION_TYPES,
        "trading_states": TRADING_STATES
    }
    
    # 合并环境变量配置
    env_config = get_env_config()
    base_config["server"].update({
        "host": env_config["HOST"],
        "port": env_config["PORT"],
        "debug": env_config["DEBUG"]
    })
    base_config["database"]["url"] = env_config["DATABASE_URL"]
    
    return base_config
