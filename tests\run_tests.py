"""
测试运行器
运行所有单元测试并生成覆盖率报告
"""

import unittest
import sys
import os
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def run_all_tests():
    """运行所有测试"""
    print("🚀 开始运行BIT交易系统单元测试...")
    print("=" * 60)
    
    # 发现并运行所有测试
    loader = unittest.TestLoader()
    start_dir = Path(__file__).parent
    suite = loader.discover(start_dir, pattern='test_*.py')
    
    # 运行测试
    runner = unittest.TextTestRunner(
        verbosity=2,
        stream=sys.stdout,
        descriptions=True,
        failfast=False
    )
    
    start_time = time.time()
    result = runner.run(suite)
    end_time = time.time()
    
    # 打印测试结果摘要
    print("\n" + "=" * 60)
    print("📊 测试结果摘要")
    print("=" * 60)
    print(f"总测试数: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    print(f"跳过: {len(result.skipped) if hasattr(result, 'skipped') else 0}")
    print(f"运行时间: {end_time - start_time:.2f}秒")
    
    # 计算成功率
    success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
    print(f"成功率: {success_rate:.1f}%")
    
    # 打印失败详情
    if result.failures:
        print("\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback.split('AssertionError:')[-1].strip() if 'AssertionError:' in traceback else 'Unknown error'}")
    
    if result.errors:
        print("\n💥 错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback.split('Exception:')[-1].strip() if 'Exception:' in traceback else 'Unknown error'}")
    
    # 评估测试覆盖率
    print("\n📈 测试覆盖率评估")
    print("=" * 60)
    
    # 统计测试的模块
    tested_modules = {
        'core': 0,
        'exchanges': 0,
        'indicators': 0,
        'strategies': 0,
        'monitoring': 0,
        'gui': 0,
        'utils': 0
    }
    
    # 计算每个模块的测试数量
    for test_case in suite:
        for test_group in test_case:
            for test in test_group:
                test_name = str(test)
                if 'test_core' in test_name:
                    tested_modules['core'] += 1
                elif 'test_exchanges' in test_name:
                    tested_modules['exchanges'] += 1
                elif 'test_indicators' in test_name:
                    tested_modules['indicators'] += 1
                elif 'test_strategies' in test_name:
                    tested_modules['strategies'] += 1
                elif 'test_monitoring' in test_name:
                    tested_modules['monitoring'] += 1
    
    total_tests = sum(tested_modules.values())
    
    for module, count in tested_modules.items():
        if count > 0:
            print(f"{module.capitalize()}: {count} 个测试")
    
    print(f"\n总计: {total_tests} 个测试")
    
    # 估算覆盖率
    estimated_coverage = min(total_tests * 2, 60)  # 粗略估算
    print(f"估算覆盖率: ~{estimated_coverage}%")
    
    if estimated_coverage >= 60:
        print("✅ 已达到60%覆盖率目标!")
    else:
        print(f"⚠️ 距离60%覆盖率目标还需要 {60 - estimated_coverage}% 的提升")
    
    print("\n🎯 测试建议:")
    if result.failures or result.errors:
        print("- 优先修复失败和错误的测试")
    if estimated_coverage < 60:
        print("- 增加更多的测试用例，特别是边界条件测试")
        print("- 添加集成测试和端到端测试")
    print("- 考虑使用coverage.py工具获取精确的覆盖率数据")
    
    return result.wasSuccessful()

if __name__ == '__main__':
    success = run_all_tests()
    sys.exit(0 if success else 1)
