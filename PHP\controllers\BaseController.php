<?php
/**
 * 基础控制器
 * 
 * @description 所有控制器的基类，提供通用功能
 */

abstract class BaseController {
    protected $db;
    protected $api;
    protected $request;
    protected $response;

    public function __construct() {
        $this->db = Database::getInstance();
        $this->api = ApiClient::getInstance();
        $this->request = $this->getRequestData();
        $this->response = [];
    }

    /**
     * 获取请求数据
     */
    protected function getRequestData() {
        return Router::getRequestData();
    }

    /**
     * 渲染模板
     */
    protected function render($template, $data = []) {
        $templateFile = TEMPLATES_PATH . '/' . $template . '.php';
        
        if (!file_exists($templateFile)) {
            throw new Exception("模板文件不存在: {$template}");
        }

        // 提取数据到变量
        extract($data);
        
        // 添加全局变量
        $GLOBALS['page_title'] = $data['title'] ?? SYSTEM_NAME;
        $GLOBALS['current_user'] = Session::getUserId();
        $GLOBALS['csrf_token'] = Session::getCSRFToken();
        $GLOBALS['system_name'] = SYSTEM_NAME;
        $GLOBALS['system_version'] = SYSTEM_VERSION;

        // 开始输出缓冲
        ob_start();
        include $templateFile;
        $content = ob_get_clean();

        // 如果是AJAX请求，直接返回内容
        if (Router::isAjax()) {
            echo $content;
        } else {
            // 使用布局模板
            $this->renderWithLayout($content, $data);
        }
    }

    /**
     * 使用布局渲染
     */
    protected function renderWithLayout($content, $data = []) {
        $layoutFile = TEMPLATES_PATH . '/layout.php';
        
        if (file_exists($layoutFile)) {
            extract($data);
            $GLOBALS['content'] = $content;
            include $layoutFile;
        } else {
            echo $content;
        }
    }

    /**
     * 返回JSON响应
     */
    protected function json($data, $code = 200) {
        Router::json($data, $code);
    }

    /**
     * 返回成功响应
     */
    protected function success($message = '操作成功', $data = null) {
        $response = [
            'success' => true,
            'message' => $message
        ];
        
        if ($data !== null) {
            $response['data'] = $data;
        }
        
        $this->json($response);
    }

    /**
     * 返回错误响应
     */
    protected function error($message = '操作失败', $code = 400, $data = null) {
        $response = [
            'success' => false,
            'message' => $message
        ];
        
        if ($data !== null) {
            $response['data'] = $data;
        }
        
        $this->json($response, $code);
    }

    /**
     * 重定向
     */
    protected function redirect($url, $code = 302) {
        Router::redirect($url, $code);
    }

    /**
     * 验证CSRF令牌
     */
    protected function validateCSRF() {
        $token = $this->request['_token'] ?? $_SERVER['HTTP_X_CSRF_TOKEN'] ?? '';
        
        if (!Session::validateCSRFToken($token)) {
            $this->error('CSRF令牌验证失败', 403);
        }
    }

    /**
     * 验证必需参数
     */
    protected function validateRequired($fields) {
        $missing = [];
        
        foreach ($fields as $field) {
            if (!isset($this->request[$field]) || $this->request[$field] === '') {
                $missing[] = $field;
            }
        }
        
        if (!empty($missing)) {
            $this->error('缺少必需参数: ' . implode(', ', $missing), 400);
        }
    }

    /**
     * 获取分页参数
     */
    protected function getPaginationParams() {
        $page = max(1, intval($this->request['page'] ?? 1));
        $limit = max(1, min(100, intval($this->request['limit'] ?? 20)));
        $offset = ($page - 1) * $limit;
        
        return [
            'page' => $page,
            'limit' => $limit,
            'offset' => $offset
        ];
    }

    /**
     * 记录操作日志
     */
    protected function logAction($action, $details = []) {
        try {
            $this->db->insert('system_logs', [
                'level' => 'INFO',
                'message' => "用户操作: {$action}",
                'context' => json_encode(array_merge([
                    'action' => $action,
                    'controller' => get_class($this),
                    'request_data' => $this->request
                ], $details)),
                'user_id' => Session::getUserId() ?? 'anonymous',
                'ip_address' => $this->getClientIP(),
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]);
        } catch (Exception $e) {
            error_log('记录操作日志失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取客户端IP
     */
    protected function getClientIP() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }

    /**
     * 检查API连接状态
     */
    protected function checkApiConnection() {
        if (!$this->api->healthCheck()) {
            $this->error('Python后端服务不可用', 503);
        }
    }

    /**
     * 处理API响应
     */
    protected function handleApiResponse($response, $successMessage = '操作成功') {
        if ($response['success']) {
            $this->success($successMessage, $response['data']);
        } else {
            $errorMessage = isset($response['data']['error']) ? $response['data']['error'] : '操作失败';
            $this->error($errorMessage, $response['http_code']);
        }
    }

    /**
     * 验证数值范围
     */
    protected function validateRange($value, $min, $max, $fieldName) {
        if ($value < $min || $value > $max) {
            $this->error("{$fieldName} 必须在 {$min} 到 {$max} 之间", 400);
        }
    }

    /**
     * 验证交易所配置
     */
    protected function validateExchangeConfig($config) {
        global $SUPPORTED_EXCHANGES;
        
        $required = ['exchange', 'api_key', 'api_secret'];
        $this->validateRequired($required);
        
        $exchange = $config['exchange'];
        if (!isset($SUPPORTED_EXCHANGES[$exchange])) {
            $this->error('不支持的交易所', 400);
        }
        
        $exchangeConfig = $SUPPORTED_EXCHANGES[$exchange];
        if ($exchangeConfig['requires_passphrase'] && empty($config['passphrase'])) {
            $this->error('该交易所需要设置Passphrase', 400);
        }
    }

    /**
     * 验证交易参数
     */
    protected function validateTradingParams($params) {
        global $RISK_MANAGEMENT;
        
        if (isset($params['leverage'])) {
            $this->validateRange(
                $params['leverage'], 
                1, 
                $RISK_MANAGEMENT['max_leverage'], 
                '杠杆倍数'
            );
        }
        
        if (isset($params['initial_margin'])) {
            $this->validateRange(
                $params['initial_margin'], 
                $RISK_MANAGEMENT['min_margin'], 
                $RISK_MANAGEMENT['max_margin'], 
                '初始保证金'
            );
        }
    }

    /**
     * 格式化数字
     */
    protected function formatNumber($number, $decimals = 4) {
        return number_format($number, $decimals, '.', '');
    }

    /**
     * 格式化百分比
     */
    protected function formatPercentage($number, $decimals = 2) {
        return number_format($number * 100, $decimals, '.', '') . '%';
    }

    /**
     * 获取用户配置
     */
    protected function getUserConfig($key = null) {
        $userId = Session::getUserId() ?? 'default';
        
        if ($key) {
            $config = $this->db->fetch(
                'SELECT config_value FROM user_configs WHERE user_id = ? AND config_key = ?',
                [$userId, $key]
            );
            return $config ? json_decode($config['config_value'], true) : null;
        } else {
            $configs = $this->db->fetchAll(
                'SELECT config_key, config_value FROM user_configs WHERE user_id = ?',
                [$userId]
            );
            
            $result = [];
            foreach ($configs as $config) {
                $result[$config['config_key']] = json_decode($config['config_value'], true);
            }
            return $result;
        }
    }

    /**
     * 保存用户配置
     */
    protected function saveUserConfig($key, $value) {
        $userId = Session::getUserId() ?? 'default';
        
        $existing = $this->db->fetch(
            'SELECT id FROM user_configs WHERE user_id = ? AND config_key = ?',
            [$userId, $key]
        );
        
        if ($existing) {
            $this->db->update(
                'user_configs',
                ['config_value' => json_encode($value)],
                'user_id = ? AND config_key = ?',
                [$userId, $key]
            );
        } else {
            $this->db->insert('user_configs', [
                'user_id' => $userId,
                'config_key' => $key,
                'config_value' => json_encode($value)
            ]);
        }
    }
}
?>
