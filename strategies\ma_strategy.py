"""
移动平均线策略实现
基于移动平均线金叉死叉信号进行交易
"""
import asyncio
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import pandas as pd
import numpy as np
from datetime import datetime

# 导入并发监控工具
try:
    from utils.concurrency_monitor import monitor_task, TaskType
except ImportError:
    # 如果导入失败，使用fallback实现
    from utils.fallback_imports import monitor_task, TaskType

logger = logging.getLogger(__name__)

class AddPositionType(Enum):
    """加仓类型"""
    EQUAL = "equal"      # 等量加仓
    HALF = "half"        # 半量加仓
    QUARTER = "quarter"  # 四分之一量加仓

class MAType(Enum):
    """移动平均线类型"""
    SMA = "sma"          # 简单移动平均
    EMA = "ema"          # 指数移动平均
    WMA = "wma"          # 加权移动平均

class MASignal(Enum):
    """移动平均线信号"""
    GOLDEN_CROSS = "golden_cross"      # 金叉信号 (快线上穿慢线)
    DEATH_CROSS = "death_cross"        # 死叉信号 (快线下穿慢线)
    BULLISH_TREND = "bullish_trend"    # 多头趋势 (快线在慢线上方)
    BEARISH_TREND = "bearish_trend"    # 空头趋势 (快线在慢线下方)
    NEUTRAL = "neutral"                # 中性区域
    STRONG_BULLISH = "strong_bullish"  # 强势多头 (价格远离均线)
    STRONG_BEARISH = "strong_bearish"  # 强势空头 (价格远离均线)

@dataclass
class AddPositionRecord:
    """加仓记录"""
    timestamp: datetime
    price: float
    quantity: float
    margin: float
    add_type: AddPositionType
    trigger_reason: str
    new_avg_cost: float
    total_margin: float
    add_count: int

@dataclass
class MAData:
    """移动平均线数据（包含可靠性评估）"""
    fast_ma: float            # 快线MA值
    slow_ma: float            # 慢线MA值
    current_price: float      # 当前价格
    signal: MASignal          # MA信号
    cross_strength: float     # 交叉强度 (0.0-1.0)
    trend_strength: float     # 趋势强度 (0.0-1.0)
    distance_ratio: float     # 价格与均线距离比例
    reliability: float = 1.0  # 可靠性评分 (0.0-1.0)
    data_count: int = 0       # 实际数据点数
    required_count: int = 20  # 需要的数据点数
    calculation_period: int = 20  # 实际计算周期

class MAConfig:
    """移动平均线策略配置"""
    
    def __init__(self):
        # 移动平均线指标参数
        self.fast_period = 5             # 快线周期
        self.slow_period = 20            # 慢线周期
        self.ma_type = MAType.SMA        # 移动平均线类型
        self.signal_confirmation_periods = 2  # 信号确认周期
        
        # 自定义交易参数（不使用全局配置）
        self.custom_symbol = "ETH/USDT"  # 自定义交易对
        self.custom_leverage = 15        # 自定义杠杆倍数
        self.initial_margin = 800.0      # 初始保证金 (USDT)
        self.take_profit_percent = 1.8   # 止盈百分比
        self.stop_loss_percent = 3.5     # 止损百分比
        
        # 加仓触发参数
        self.trigger_distance_points = 80    # 触发加仓的距离(点数)
        self.trigger_distance_percent = 1.2  # 触发加仓的距离(百分比)
        self.use_points_trigger = True       # 是否使用点数触发
        
        # 加仓类型和次数
        self.add_position_types = [AddPositionType.EQUAL]  # 加仓类型序列
        self.max_add_count = 3              # 最大加仓次数
        
        # 加仓间距设置(递增)
        self.add_intervals = [2.5, 4.0, 6.5, 10.0]  # 加仓间距百分比
        
        # 风险控制
        self.max_total_loss_percent = 10.0   # 最大总亏损百分比
        self.max_investment_ratio = 0.18     # 最大投入资金比例 (18%)
        self.enable_emergency_stop = True    # 启用紧急停止
        
        # MA信号过滤
        self.enable_trend_filter = True      # 启用趋势过滤
        self.min_cross_strength = 0.15      # 最小交叉强度
        self.min_trend_strength = 0.2       # 最小趋势强度
        self.enable_distance_filter = True  # 启用距离过滤
        self.max_distance_ratio = 0.05      # 最大距离比例 (5%)
        
        # 立即开仓功能
        self.immediate_open_enabled = False   # 立即开仓功能开关
        self.immediate_check_interval = 30    # 立即开仓检测间隔(秒)
        
        # 紧急加仓保护
        self.enable_emergency_add = True      # 启用紧急加仓
        self.emergency_distance_threshold = 70.0  # 紧急距离阈值(%)
        
        # 冷却时间设置
        self.order_cooldown = 30             # 下单冷却时间(秒)
        self.position_check_interval = 10    # 持仓检查间隔(秒)
        
        # 交易所设置（使用自定义设置）
        self.exchange_name = "okx"          # 默认交易所
        
        # 数据获取设置
        self.kline_limit = 100              # K线数据获取数量
        self.price_precision = 4            # 价格精度
        self.quantity_precision = 8         # 数量精度
        
        # 日志和监控
        self.enable_detailed_logging = True  # 启用详细日志
        self.enable_performance_monitoring = True  # 启用性能监控
        
        # 模拟交易设置
        self.is_simulation = False          # 是否为模拟交易
        self.simulation_balance = 10000.0   # 模拟账户余额
        
    def validate(self) -> bool:
        """验证配置参数的有效性"""
        try:
            # 验证MA参数
            if self.fast_period <= 0 or self.fast_period > 100:
                logger.error("快线周期必须在1-100之间")
                return False
            
            if self.slow_period <= 0 or self.slow_period > 200:
                logger.error("慢线周期必须在1-200之间")
                return False
            
            if self.fast_period >= self.slow_period:
                logger.error("快线周期必须小于慢线周期")
                return False
            
            if self.signal_confirmation_periods < 1 or self.signal_confirmation_periods > 10:
                logger.error("信号确认周期必须在1-10之间")
                return False
            
            # 验证自定义交易参数
            if not self.custom_symbol or "/" not in self.custom_symbol:
                logger.error("自定义交易对格式无效")
                return False
            
            if self.custom_leverage <= 0 or self.custom_leverage > 100:
                logger.error("自定义杠杆倍数必须在1-100之间")
                return False
            
            if self.initial_margin <= 0:
                logger.error("初始保证金必须大于0")
                return False
            
            # 验证风险控制参数
            if self.take_profit_percent <= 0:
                logger.error("止盈百分比必须大于0")
                return False
            
            if self.stop_loss_percent <= 0:
                logger.error("止损百分比必须大于0")
                return False
            
            if self.max_total_loss_percent <= 0:
                logger.error("最大总亏损百分比必须大于0")
                return False
            
            if self.max_investment_ratio <= 0 or self.max_investment_ratio > 1:
                logger.error("最大投入资金比例必须在0-1之间")
                return False
            
            # 验证加仓参数
            if self.max_add_count < 0:
                logger.error("最大加仓次数不能为负数")
                return False
            
            if len(self.add_intervals) < self.max_add_count:
                logger.error("加仓间距设置数量不足")
                return False
            
            # 验证信号过滤参数
            if self.min_cross_strength < 0 or self.min_cross_strength > 1:
                logger.error("最小交叉强度必须在0-1之间")
                return False
            
            if self.min_trend_strength < 0 or self.min_trend_strength > 1:
                logger.error("最小趋势强度必须在0-1之间")
                return False
            
            if self.max_distance_ratio < 0 or self.max_distance_ratio > 1:
                logger.error("最大距离比例必须在0-1之间")
                return False
            
            logger.info("✅ 移动平均线策略配置验证通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 移动平均线策略配置验证失败: {e}")
            return False
    
    def get_required_data_count(self) -> int:
        """获取所需的最小数据点数"""
        # MA需要的最小数据点数
        return max(self.slow_period, 20) + 10  # 额外10个点用于交叉检测
    
    def to_dict(self) -> Dict[str, Any]:
        """将配置转换为字典"""
        return {
            # MA参数
            "fast_period": self.fast_period,
            "slow_period": self.slow_period,
            "ma_type": self.ma_type.value,
            "signal_confirmation_periods": self.signal_confirmation_periods,
            
            # 自定义交易参数
            "custom_symbol": self.custom_symbol,
            "custom_leverage": self.custom_leverage,
            "initial_margin": self.initial_margin,
            "take_profit_percent": self.take_profit_percent,
            "stop_loss_percent": self.stop_loss_percent,
            
            # 风险控制
            "max_total_loss_percent": self.max_total_loss_percent,
            "max_investment_ratio": self.max_investment_ratio,
            "max_add_count": self.max_add_count,
            
            # 信号过滤
            "enable_trend_filter": self.enable_trend_filter,
            "min_cross_strength": self.min_cross_strength,
            "min_trend_strength": self.min_trend_strength,
            "enable_distance_filter": self.enable_distance_filter,
            "max_distance_ratio": self.max_distance_ratio,
            
            # 立即开仓
            "immediate_open_enabled": self.immediate_open_enabled,
            "immediate_check_interval": self.immediate_check_interval,
            
            # 交易设置
            "exchange_name": self.exchange_name,
        }
    
    def from_dict(self, config_dict: Dict[str, Any]):
        """从字典加载配置"""
        for key, value in config_dict.items():
            if hasattr(self, key):
                if key == "ma_type":
                    # 特殊处理枚举类型
                    try:
                        setattr(self, key, MAType(value))
                    except ValueError:
                        logger.warning(f"无效的MA类型: {value}")
                else:
                    setattr(self, key, value)
            else:
                logger.warning(f"未知的配置参数: {key}")
    
    def __str__(self) -> str:
        """配置信息的字符串表示"""
        return f"""移动平均线策略配置:
        MA参数: 快线={self.fast_period}, 慢线={self.slow_period}, 类型={self.ma_type.value}
        自定义交易: 交易对={self.custom_symbol}, 杠杆={self.custom_leverage}x, 保证金={self.initial_margin}
        风险控制: 止盈={self.take_profit_percent}%, 止损={self.stop_loss_percent}%
        加仓设置: 最大次数={self.max_add_count}, 最大亏损={self.max_total_loss_percent}%
        立即开仓: {'启用' if self.immediate_open_enabled else '禁用'}
        交易所: {self.exchange_name}"""

class MACalculator:
    """移动平均线计算器 - 核心计算逻辑"""

    def __init__(self, fast_period: int = 5, slow_period: int = 20, ma_type: MAType = MAType.SMA):
        self.fast_period = fast_period
        self.slow_period = slow_period
        self.ma_type = ma_type

    def calculate_ma(self, prices: List[float], min_periods: int = None) -> Optional[MAData]:
        """
        计算移动平均线指标（支持部分数据计算）

        Args:
            prices: 价格列表
            min_periods: 最小计算周期，默认为slow_period

        Returns:
            Optional[MAData]: MA数据（包含可靠性评估）
        """
        # 设置最小计算周期
        if min_periods is None:
            min_periods = max(self.slow_period, 10)  # 至少需要slow_period个数据点

        data_count = len(prices)
        required_count = self.slow_period + 10  # 额外数据用于交叉检测

        # 数据完全不足
        if data_count < min_periods:
            logger.warning(f"❌ 价格数据严重不足，需要至少{min_periods}个，当前{data_count}个")
            return None

        try:
            # 计算可靠性评分
            if data_count >= required_count:
                reliability = 1.0  # 完全可靠
                calculation_period = required_count
                logger.info(f"✅ MA数据充足: {data_count}/{required_count} (可靠性: 100%)")
            else:
                reliability = data_count / required_count  # 部分可靠
                calculation_period = data_count
                logger.warning(f"⚠️ MA数据不足: {data_count}/{required_count} (可靠性: {reliability*100:.1f}%)")

            # 使用实际可用的数据
            calc_prices = prices[-calculation_period:] if calculation_period < data_count else prices

            # 转换为pandas Series进行计算
            price_series = pd.Series(calc_prices)

            # 计算快线和慢线
            fast_ma = self._calculate_moving_average(price_series, self.fast_period)
            slow_ma = self._calculate_moving_average(price_series, self.slow_period)

            # 获取最新值
            fast_ma_value = fast_ma.iloc[-1]
            slow_ma_value = slow_ma.iloc[-1]
            current_price = calc_prices[-1]

            # 分析MA信号
            signal = self._analyze_ma_signal(fast_ma.tolist(), slow_ma.tolist(), calc_prices)

            # 计算交叉强度
            cross_strength = self._calculate_cross_strength(fast_ma.tolist(), slow_ma.tolist())

            # 计算趋势强度
            trend_strength = self._calculate_trend_strength(fast_ma.tolist(), slow_ma.tolist())

            # 计算价格与均线距离比例
            distance_ratio = self._calculate_distance_ratio(current_price, fast_ma_value, slow_ma_value)

            # 记录详细信息
            logger.info(f"📊 MA计算完成: 快线={fast_ma_value:.4f}, 慢线={slow_ma_value:.4f}, 价格={current_price:.4f}")
            logger.info(f"📊 信号: {signal.value}, 交叉强度: {cross_strength:.3f}, 趋势强度: {trend_strength:.3f}")
            logger.info(f"📊 数据统计: 使用{len(calc_prices)}个数据点, 可靠性={reliability:.2f}")

            return MAData(
                fast_ma=round(fast_ma_value, 4),
                slow_ma=round(slow_ma_value, 4),
                current_price=round(current_price, 4),
                signal=signal,
                cross_strength=cross_strength,
                trend_strength=trend_strength,
                distance_ratio=distance_ratio,
                reliability=reliability,
                data_count=len(calc_prices),
                required_count=required_count,
                calculation_period=len(calc_prices)
            )

        except Exception as e:
            logger.error(f"计算MA异常: {e}")
            return None

    def _calculate_moving_average(self, prices: pd.Series, period: int) -> pd.Series:
        """
        计算移动平均线

        Args:
            prices: 价格序列
            period: 周期

        Returns:
            pd.Series: 移动平均线序列
        """
        try:
            if self.ma_type == MAType.SMA:
                # 简单移动平均
                return prices.rolling(window=period, min_periods=1).mean()
            elif self.ma_type == MAType.EMA:
                # 指数移动平均
                return prices.ewm(span=period, adjust=False).mean()
            elif self.ma_type == MAType.WMA:
                # 加权移动平均
                weights = np.arange(1, period + 1)
                return prices.rolling(window=period, min_periods=1).apply(
                    lambda x: np.average(x, weights=weights[:len(x)]) if len(x) > 0 else np.nan,
                    raw=True
                )
            else:
                # 默认使用SMA
                return prices.rolling(window=period, min_periods=1).mean()

        except Exception as e:
            logger.error(f"计算移动平均线异常: {e}")
            return prices.rolling(window=period, min_periods=1).mean()

    def _analyze_ma_signal(self, fast_ma_list: List[float], slow_ma_list: List[float],
                          prices: List[float]) -> MASignal:
        """
        分析MA信号

        Args:
            fast_ma_list: 快线MA历史
            slow_ma_list: 慢线MA历史
            prices: 价格历史

        Returns:
            MASignal: MA信号类型
        """
        try:
            if len(fast_ma_list) < 2 or len(slow_ma_list) < 2:
                return MASignal.NEUTRAL

            current_fast = fast_ma_list[-1]
            current_slow = slow_ma_list[-1]
            prev_fast = fast_ma_list[-2]
            prev_slow = slow_ma_list[-2]
            current_price = prices[-1]

            # 检测金叉死叉
            if prev_fast <= prev_slow and current_fast > current_slow:
                cross_strength = abs(current_fast - current_slow) / current_slow
                if cross_strength > 0.001:  # 最小交叉幅度 0.1%
                    logger.info(f"🟢 MA金叉信号: 快线({current_fast:.4f}) > 慢线({current_slow:.4f})")
                    return MASignal.GOLDEN_CROSS
            elif prev_fast >= prev_slow and current_fast < current_slow:
                cross_strength = abs(current_fast - current_slow) / current_slow
                if cross_strength > 0.001:  # 最小交叉幅度 0.1%
                    logger.info(f"🔴 MA死叉信号: 快线({current_fast:.4f}) < 慢线({current_slow:.4f})")
                    return MASignal.DEATH_CROSS

            # 检测强势趋势
            fast_distance = abs(current_price - current_fast) / current_fast
            slow_distance = abs(current_price - current_slow) / current_slow

            if current_fast > current_slow:
                # 多头趋势
                if fast_distance > 0.03 and current_price > current_fast:  # 价格远离快线3%以上
                    logger.info(f"🚀 强势多头信号: 价格远离均线")
                    return MASignal.STRONG_BULLISH
                else:
                    logger.debug(f"📈 多头趋势: 快线在慢线上方")
                    return MASignal.BULLISH_TREND
            elif current_fast < current_slow:
                # 空头趋势
                if fast_distance > 0.03 and current_price < current_fast:  # 价格远离快线3%以上
                    logger.info(f"💥 强势空头信号: 价格远离均线")
                    return MASignal.STRONG_BEARISH
                else:
                    logger.debug(f"📉 空头趋势: 快线在慢线下方")
                    return MASignal.BEARISH_TREND
            else:
                logger.debug(f"⚪ 中性区域: 快慢线接近")
                return MASignal.NEUTRAL

        except Exception as e:
            logger.error(f"分析MA信号异常: {e}")
            return MASignal.NEUTRAL

    def _calculate_cross_strength(self, fast_ma_list: List[float], slow_ma_list: List[float]) -> float:
        """
        计算MA交叉强度

        Args:
            fast_ma_list: 快线MA历史
            slow_ma_list: 慢线MA历史

        Returns:
            float: 交叉强度 (0.0-1.0)
        """
        try:
            if len(fast_ma_list) < 3 or len(slow_ma_list) < 3:
                return 0.0

            # 计算最近的快慢线差值变化
            current_diff = abs(fast_ma_list[-1] - slow_ma_list[-1])
            prev_diff = abs(fast_ma_list[-2] - slow_ma_list[-2])

            # 交叉强度基于差值变化和相对差值
            relative_diff = current_diff / slow_ma_list[-1] if slow_ma_list[-1] != 0 else 0
            diff_change = abs(current_diff - prev_diff) / slow_ma_list[-1] if slow_ma_list[-1] != 0 else 0

            # 综合计算强度
            strength = min(1.0, (relative_diff + diff_change) * 100)

            return strength

        except Exception as e:
            logger.error(f"计算交叉强度异常: {e}")
            return 0.0

    def _calculate_trend_strength(self, fast_ma_list: List[float], slow_ma_list: List[float]) -> float:
        """
        计算趋势强度

        Args:
            fast_ma_list: 快线MA历史
            slow_ma_list: 慢线MA历史

        Returns:
            float: 趋势强度 (0.0-1.0)
        """
        try:
            if len(fast_ma_list) < 5 or len(slow_ma_list) < 5:
                return 0.0

            # 计算快慢线的趋势方向一致性
            fast_trend = (fast_ma_list[-1] - fast_ma_list[-5]) / fast_ma_list[-5] if fast_ma_list[-5] != 0 else 0
            slow_trend = (slow_ma_list[-1] - slow_ma_list[-5]) / slow_ma_list[-5] if slow_ma_list[-5] != 0 else 0

            # 计算快慢线分离度
            separation = abs(fast_ma_list[-1] - slow_ma_list[-1]) / slow_ma_list[-1] if slow_ma_list[-1] != 0 else 0

            # 趋势强度 = 方向一致性 + 分离度
            direction_consistency = 1.0 if fast_trend * slow_trend > 0 else 0.0
            strength = min(1.0, direction_consistency * 0.7 + separation * 30)

            return strength

        except Exception as e:
            logger.error(f"计算趋势强度异常: {e}")
            return 0.0

    def _calculate_distance_ratio(self, current_price: float, fast_ma: float, slow_ma: float) -> float:
        """
        计算价格与均线距离比例

        Args:
            current_price: 当前价格
            fast_ma: 快线MA值
            slow_ma: 慢线MA值

        Returns:
            float: 距离比例
        """
        try:
            # 计算价格与快线的距离比例
            if fast_ma != 0:
                distance_ratio = abs(current_price - fast_ma) / fast_ma
                return distance_ratio
            else:
                return 0.0

        except Exception as e:
            logger.error(f"计算距离比例异常: {e}")
            return 0.0

    def calculate_ma_signal_direction(self, ma_data: MAData) -> str:
        """
        基于MA数据计算开仓方向

        Args:
            ma_data: MA数据

        Returns:
            str: 开仓方向 ("long", "short", "hold")
        """
        try:
            # 金叉信号 - 做多信号
            if ma_data.signal == MASignal.GOLDEN_CROSS:
                # 结合交叉强度判断
                if ma_data.cross_strength > 0.1:  # 交叉强度足够
                    logger.info("📈 金叉做多信号: 快线上穿慢线")
                    return "long"
                else:
                    logger.info("⚠️ 金叉信号但强度不足")
                    return "hold"

            # 死叉信号 - 做空信号
            elif ma_data.signal == MASignal.DEATH_CROSS:
                # 结合交叉强度判断
                if ma_data.cross_strength > 0.1:  # 交叉强度足够
                    logger.info("📉 死叉做空信号: 快线下穿慢线")
                    return "short"
                else:
                    logger.info("⚠️ 死叉信号但强度不足")
                    return "hold"

            # 强势多头信号
            elif ma_data.signal == MASignal.STRONG_BULLISH:
                if ma_data.trend_strength > 0.3:  # 趋势强度足够
                    logger.info("🚀 强势多头做多信号")
                    return "long"
                else:
                    logger.info("⚠️ 强势多头但趋势强度不足")
                    return "hold"

            # 强势空头信号
            elif ma_data.signal == MASignal.STRONG_BEARISH:
                if ma_data.trend_strength > 0.3:  # 趋势强度足够
                    logger.info("💥 强势空头做空信号")
                    return "short"
                else:
                    logger.info("⚠️ 强势空头但趋势强度不足")
                    return "hold"

            # 其他情况保持观望
            else:
                logger.debug(f"⏸️ 保持观望: 快线={ma_data.fast_ma:.4f}, 慢线={ma_data.slow_ma:.4f}, 信号={ma_data.signal.value}")
                return "hold"

        except Exception as e:
            logger.error(f"计算MA开仓方向异常: {e}")
            return "hold"

    def detect_ma_signal(self, prices: List[float], min_periods: int = None) -> Tuple[bool, str, float]:
        """
        专门检测MA信号

        Args:
            prices: 价格列表
            min_periods: 最小计算周期

        Returns:
            Tuple[bool, str, float]: (是否有信号, 信号类型, 信号强度)
        """
        try:
            if len(prices) < self.slow_period:
                return False, "hold", 0.0

            # 计算MA数据
            ma_data = self.calculate_ma(prices, min_periods)
            if not ma_data:
                return False, "hold", 0.0

            # 计算开仓方向
            direction = self.calculate_ma_signal_direction(ma_data)

            if direction != "hold":
                # 计算信号强度
                if ma_data.signal in [MASignal.GOLDEN_CROSS, MASignal.DEATH_CROSS]:
                    signal_strength = 0.7 + ma_data.cross_strength * 0.3  # 交叉信号基础强度
                elif ma_data.signal in [MASignal.STRONG_BULLISH, MASignal.STRONG_BEARISH]:
                    signal_strength = 0.8 + ma_data.trend_strength * 0.2  # 强势信号高强度
                elif ma_data.signal in [MASignal.BULLISH_TREND, MASignal.BEARISH_TREND]:
                    signal_strength = 0.4 + ma_data.trend_strength * 0.3  # 趋势信号中等强度
                else:
                    signal_strength = 0.3  # 其他信号较低强度

                # 结合可靠性调整强度
                signal_strength *= ma_data.reliability

                return True, direction, signal_strength

            return False, "hold", 0.0

        except Exception as e:
            logger.error(f"检测MA信号异常: {e}")
            return False, "hold", 0.0

    def is_signal_confirmed(self, prices: List[float], signal_type: str,
                           confirmation_periods: int = 2) -> bool:
        """
        确认MA信号的有效性

        Args:
            prices: 价格列表
            signal_type: 信号类型
            confirmation_periods: 确认周期数

        Returns:
            bool: 信号是否确认
        """
        try:
            if len(prices) < confirmation_periods + 1:
                return False

            confirmed_count = 0

            # 检查最近几个周期的信号一致性
            for i in range(confirmation_periods):
                if i == 0:
                    check_prices = prices
                else:
                    check_prices = prices[:-(i)]

                has_signal, detected_type, strength = self.detect_ma_signal(check_prices)

                if has_signal and detected_type == signal_type:
                    confirmed_count += 1

            # 需要至少一半的周期确认信号
            required_confirmations = max(1, confirmation_periods // 2)
            is_confirmed = confirmed_count >= required_confirmations

            if is_confirmed:
                logger.info(f"✅ MA信号确认: {signal_type}, 确认次数: {confirmed_count}/{confirmation_periods}")
            else:
                logger.warning(f"⚠️ MA信号未确认: {signal_type}, 确认次数: {confirmed_count}/{confirmation_periods}")

            return is_confirmed

        except Exception as e:
            logger.error(f"确认MA信号异常: {e}")
            return False

    def get_ma_type_name(self) -> str:
        """获取MA类型名称"""
        type_names = {
            MAType.SMA: "简单移动平均",
            MAType.EMA: "指数移动平均",
            MAType.WMA: "加权移动平均"
        }
        return type_names.get(self.ma_type, "未知类型")

class MAStrategy:
    """移动平均线策略主类"""

    def __init__(self, config: MAConfig, exchange_manager=None):
        self.config = config
        self.exchange_manager = exchange_manager
        self.calculator = MACalculator(
            fast_period=config.fast_period,
            slow_period=config.slow_period,
            ma_type=config.ma_type
        )

        # 策略状态
        self.is_running = False
        self.last_signal_time = None
        self.last_order_time = None
        self.current_position = None
        self.add_position_records: List[AddPositionRecord] = []

        # 性能监控
        self.total_trades = 0
        self.successful_trades = 0
        self.total_pnl = 0.0

        # 异步任务管理
        self.monitoring_task = None
        self.immediate_open_task = None

        logger.info(f"🎯 移动平均线策略初始化完成: {config}")

    async def start(self):
        """启动移动平均线策略"""
        try:
            if self.is_running:
                logger.warning("⚠️ 移动平均线策略已在运行中")
                return

            # 验证配置
            if not self.config.validate():
                logger.error("❌ 移动平均线策略配置验证失败")
                return

            # 验证交易所连接
            if not self.exchange_manager:
                logger.error("❌ 交易所管理器未设置")
                return

            self.is_running = True
            logger.info("🚀 移动平均线策略启动成功")

            # 启动立即开仓功能（如果启用）
            if self.config.immediate_open_enabled:
                await self._start_immediate_open_monitor()

            # 启动持仓监控
            self.monitoring_task = asyncio.create_task(self._start_position_monitor())

        except Exception as e:
            logger.error(f"❌ 移动平均线策略启动失败: {e}")
            self.is_running = False

    async def stop(self):
        """停止移动平均线策略"""
        try:
            self.is_running = False

            # 停止异步任务
            if self.monitoring_task:
                self.monitoring_task.cancel()
                try:
                    await self.monitoring_task
                except asyncio.CancelledError:
                    pass

            if self.immediate_open_task:
                self.immediate_open_task.cancel()
                try:
                    await self.immediate_open_task
                except asyncio.CancelledError:
                    pass

            logger.info("🛑 移动平均线策略已停止")

        except Exception as e:
            logger.error(f"❌ 移动平均线策略停止失败: {e}")

    @monitor_task(TaskType.OPENING_CHECK)
    async def _start_immediate_open_monitor(self):
        """启动立即开仓监控"""
        try:
            logger.info(f"🔍 启动MA立即开仓监控，检测间隔: {self.config.immediate_check_interval}秒")

            async def check_immediate_open():
                while self.is_running and self.config.immediate_open_enabled:
                    try:
                        await self._check_immediate_open_opportunity()
                        await asyncio.sleep(self.config.immediate_check_interval)
                    except Exception as e:
                        logger.error(f"❌ 立即开仓检查异常: {e}")
                        await asyncio.sleep(5)  # 错误时短暂等待

            self.immediate_open_task = asyncio.create_task(check_immediate_open())

        except Exception as e:
            logger.error(f"❌ 启动立即开仓监控失败: {e}")

    @monitor_task(TaskType.OPENING_CHECK)
    async def _check_immediate_open_opportunity(self):
        """检查立即开仓机会"""
        try:
            # 检查是否有持仓
            if await self._has_position():
                logger.debug("已有持仓，跳过开仓检查")
                return

            # 检查冷却时间
            if not self._check_order_cooldown():
                logger.debug("冷却时间未到，跳过开仓检查")
                return

            # 获取当前价格数据
            prices = await self._get_price_data()
            if not prices or len(prices) < self.config.get_required_data_count():
                logger.warning("⚠️ 价格数据不足，无法进行MA分析")
                return

            # 检测MA信号
            has_signal, signal_type, signal_strength = self.calculator.detect_ma_signal(prices)

            if not has_signal:
                logger.debug("未检测到MA信号")
                return

            # 信号确认（如果启用）
            if self.config.signal_confirmation_periods > 1:
                if not self.calculator.is_signal_confirmed(prices, signal_type,
                                                         self.config.signal_confirmation_periods):
                    logger.warning(f"⚠️ MA信号未确认: {signal_type}")
                    return

            # 信号过滤
            if not self._filter_signal(prices, signal_type, signal_strength):
                logger.warning(f"⚠️ MA信号被过滤: {signal_type}")
                return

            logger.info(f"🚀 准备执行MA开仓: 方向={signal_type}, 强度={signal_strength:.3f}")
            await self._execute_immediate_opening(signal_type, signal_strength)

        except Exception as e:
            logger.error(f"❌ 检查立即开仓机会异常: {e}")

    def _filter_signal(self, prices: List[float], signal_type: str, signal_strength: float) -> bool:
        """
        过滤MA信号

        Args:
            prices: 价格列表
            signal_type: 信号类型
            signal_strength: 信号强度

        Returns:
            bool: 信号是否通过过滤
        """
        try:
            # 计算MA数据用于过滤
            ma_data = self.calculator.calculate_ma(prices)
            if not ma_data:
                return False

            # 趋势过滤
            if self.config.enable_trend_filter:
                if ma_data.trend_strength < self.config.min_trend_strength:
                    logger.debug(f"趋势强度不足: {ma_data.trend_strength:.3f} < {self.config.min_trend_strength}")
                    return False

            # 交叉强度过滤
            if ma_data.cross_strength < self.config.min_cross_strength:
                logger.debug(f"交叉强度不足: {ma_data.cross_strength:.3f} < {self.config.min_cross_strength}")
                return False

            # 距离过滤
            if self.config.enable_distance_filter:
                if ma_data.distance_ratio > self.config.max_distance_ratio:
                    logger.debug(f"价格距离过远: {ma_data.distance_ratio:.3f} > {self.config.max_distance_ratio}")
                    return False

            return True

        except Exception as e:
            logger.error(f"过滤MA信号异常: {e}")
            return False

    async def _execute_immediate_opening(self, direction: str, signal_strength: float):
        """执行立即开仓"""
        try:
            logger.info(f"🚀 [MA立即开仓] 执行开仓: 方向={direction}, 强度={signal_strength:.3f}")

            # 获取当前价格
            current_price = await self._get_current_price()
            if not current_price:
                logger.error("❌ 无法获取当前价格")
                return

            # 计算开仓数量
            quantity = self._calculate_position_size(current_price)
            if quantity <= 0:
                logger.error("❌ 计算的开仓数量无效")
                return

            # 计算止损价格
            stop_loss_price = self._calculate_stop_loss_price(current_price, direction)

            # 记录开仓信息
            logger.info(f"📊 [MA开仓详情] 价格: {current_price:.4f}, 数量: {quantity}, 止损: {stop_loss_price:.4f}")
            logger.info(f"📊 [MA信号详情] 方向: {direction}, 强度: {signal_strength:.3f}")
            logger.info(f"📊 [自定义设置] 交易对: {self.config.custom_symbol}, 杠杆: {self.config.custom_leverage}x")
            logger.info(f"📊 [MA类型] {self.calculator.get_ma_type_name()}: 快线{self.config.fast_period}/慢线{self.config.slow_period}")

            # 执行开仓
            order_result = await self._place_market_order(direction, quantity, current_price)
            if order_result:
                logger.info(f"✅ [MA立即开仓] 开仓成功: {direction}仓位，价格: {current_price:.4f}，数量: {quantity}")

                # 设置止损订单
                await self._place_stop_loss_order(direction, quantity, stop_loss_price)

                # 更新策略状态
                self.last_order_time = datetime.now()
                self.last_signal_time = datetime.now()
                self.total_trades += 1

                # 记录信号强度用于后续分析
                if not hasattr(self, 'signal_history'):
                    self.signal_history = []
                self.signal_history.append({
                    'timestamp': datetime.now(),
                    'direction': direction,
                    'signal_strength': signal_strength,
                    'price': current_price,
                    'quantity': quantity,
                    'custom_symbol': self.config.custom_symbol,
                    'custom_leverage': self.config.custom_leverage,
                    'ma_type': self.config.ma_type.value,
                    'fast_period': self.config.fast_period,
                    'slow_period': self.config.slow_period
                })

                # 禁用立即开仓功能（开仓后自动禁用）
                self.config.immediate_open_enabled = False
                logger.info("🔒 [MA立即开仓] 开仓后自动禁用立即开仓功能")

            else:
                logger.error("❌ [MA立即开仓] 开仓失败")

        except Exception as e:
            logger.error(f"❌ 执行立即开仓异常: {e}")

    def _calculate_stop_loss_price(self, entry_price: float, direction: str) -> float:
        """计算止损价格"""
        try:
            stop_loss_ratio = self.config.stop_loss_percent / 100.0

            if direction == "long":
                # 多仓止损价格 = 入场价格 * (1 - 止损比例)
                stop_loss_price = entry_price * (1 - stop_loss_ratio)
            else:
                # 空仓止损价格 = 入场价格 * (1 + 止损比例)
                stop_loss_price = entry_price * (1 + stop_loss_ratio)

            logger.info(f"💡 [止损计算] 入场价: {entry_price:.4f}, 方向: {direction}, 止损价: {stop_loss_price:.4f}")
            return round(stop_loss_price, self.config.price_precision)

        except Exception as e:
            logger.error(f"❌ 计算止损价格异常: {e}")
            return entry_price * 0.95 if direction == "long" else entry_price * 1.05  # 默认5%止损

    async def _place_stop_loss_order(self, direction: str, quantity: float, stop_loss_price: float):
        """设置止损订单"""
        try:
            if not self.exchange_manager:
                logger.error("❌ 交易所管理器未设置")
                return

            # 确定止损订单方向（与开仓方向相反）
            stop_side = "sell" if direction == "long" else "buy"

            logger.info(f"🛡️ [止损订单] 设置止损: 方向={stop_side}, 数量={quantity}, 止损价={stop_loss_price:.4f}")
            logger.info(f"🛡️ [止损订单] 交易对: {self.config.custom_symbol}")

            # 根据交易所类型选择止损订单方式
            exchange_name = self.config.exchange_name.lower()

            if exchange_name == "okx":
                await self._place_okx_algo_stop_loss_order(stop_side, quantity, stop_loss_price)
            elif exchange_name == "gate":
                await self._place_gate_stop_loss_order(stop_side, quantity, stop_loss_price)
            else:
                # 默认使用限价单作为止损
                await self._place_limit_stop_loss_order(stop_side, quantity, stop_loss_price)

        except Exception as e:
            logger.error(f"❌ 设置止损订单异常: {e}")

    async def _place_okx_algo_stop_loss_order(self, side: str, quantity: float, stop_loss_price: float):
        """使用OKX策略委托接口设置止损订单"""
        try:
            if hasattr(self.exchange_manager.exchange, 'place_algo_order'):
                # 使用OKX策略委托接口
                order_result = await self.exchange_manager.exchange.place_algo_order(
                    symbol=self.config.custom_symbol,
                    side=side,
                    order_type="conditional",
                    quantity=quantity,
                    trigger_price=stop_loss_price,
                    order_price=-1,  # 市价执行
                    reduce_only=True
                )

                if order_result:
                    logger.info(f"✅ [OKX策略委托] 止损订单设置成功: {order_result.order_id}")
                else:
                    logger.warning("⚠️ [OKX策略委托] 止损订单设置失败，尝试限价单")
                    await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)
            else:
                logger.warning("⚠️ OKX交易所不支持策略委托，使用限价单")
                await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)

        except Exception as e:
            logger.error(f"❌ OKX策略委托止损失败: {e}")
            await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)

    async def _place_gate_stop_loss_order(self, side: str, quantity: float, stop_loss_price: float):
        """使用Gate.io止损订单"""
        try:
            # Gate.io的止损订单实现
            logger.info(f"🔧 [Gate.io止损] 设置止损订单: {side}, {quantity}, {stop_loss_price}")

            # 暂时使用限价单作为备选
            await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)

        except Exception as e:
            logger.error(f"❌ Gate.io止损订单失败: {e}")
            await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)

    async def _place_limit_stop_loss_order(self, side: str, quantity: float, stop_loss_price: float):
        """使用限价单作为止损（备选方案）"""
        try:
            logger.info(f"📋 [限价止损] 设置限价止损单: {side}, {quantity}, {stop_loss_price}")

            # 这里可以实现限价单止损逻辑
            # 注意：限价单不是真正的止损，需要持续监控价格

        except Exception as e:
            logger.error(f"❌ 限价止损订单失败: {e}")

    async def _start_position_monitor(self):
        """启动持仓监控"""
        try:
            logger.info("👁️ 启动MA持仓监控")

            while self.is_running:
                try:
                    await self._check_position_status()
                    await asyncio.sleep(self.config.position_check_interval)
                except Exception as e:
                    logger.error(f"❌ 持仓监控异常: {e}")
                    await asyncio.sleep(5)

        except Exception as e:
            logger.error(f"❌ 持仓监控启动失败: {e}")

    async def _check_position_status(self):
        """检查持仓状态"""
        try:
            # 这里实现持仓状态检查逻辑
            # 包括止盈止损检查、加仓条件检查等
            pass

        except Exception as e:
            logger.error(f"❌ 检查持仓状态异常: {e}")

    # 辅助方法
    async def _has_position(self) -> bool:
        """检查是否有持仓"""
        try:
            if not self.exchange_manager:
                return False

            # 这里实现持仓检查逻辑
            return False  # 临时返回

        except Exception as e:
            logger.error(f"❌ 检查持仓异常: {e}")
            return False

    def _check_order_cooldown(self) -> bool:
        """检查下单冷却时间"""
        try:
            if not self.last_order_time:
                return True

            elapsed = (datetime.now() - self.last_order_time).total_seconds()
            return elapsed >= self.config.order_cooldown

        except Exception as e:
            logger.error(f"❌ 检查冷却时间异常: {e}")
            return True

    async def _get_price_data(self) -> List[float]:
        """获取价格数据"""
        try:
            if not self.exchange_manager:
                return []

            # 这里实现价格数据获取逻辑
            return []  # 临时返回

        except Exception as e:
            logger.error(f"❌ 获取价格数据异常: {e}")
            return []

    async def _get_current_price(self) -> Optional[float]:
        """获取当前价格"""
        try:
            if not self.exchange_manager:
                return None

            # 这里实现当前价格获取逻辑
            return None  # 临时返回

        except Exception as e:
            logger.error(f"❌ 获取当前价格异常: {e}")
            return None

    def _calculate_position_size(self, price: float) -> float:
        """计算开仓数量"""
        try:
            # 基于初始保证金和自定义杠杆计算开仓数量
            position_value = self.config.initial_margin * self.config.custom_leverage
            quantity = position_value / price

            # 应用精度
            quantity = round(quantity, self.config.quantity_precision)

            logger.info(f"💰 [仓位计算] 价格: {price:.4f}, 保证金: {self.config.initial_margin}, 杠杆: {self.config.custom_leverage}x, 数量: {quantity}")
            return quantity

        except Exception as e:
            logger.error(f"❌ 计算开仓数量异常: {e}")
            return 0.0

    async def _place_market_order(self, direction: str, quantity: float, price: float) -> bool:
        """下市价单"""
        try:
            if not self.exchange_manager:
                logger.error("❌ 交易所管理器未设置")
                return False

            logger.info(f"📈 [MA市价下单] 方向: {direction}, 数量: {quantity}, 参考价格: {price:.4f}")
            logger.info(f"📊 [交易对] 使用自定义交易对: {self.config.custom_symbol}")
            logger.info(f"📊 [杠杆] 使用自定义杠杆: {self.config.custom_leverage}x")
            logger.info(f"📊 [MA设置] {self.calculator.get_ma_type_name()}: {self.config.fast_period}/{self.config.slow_period}")

            # 确定订单方向
            from exchanges.base_exchange import OrderSide
            if direction == "long":
                order_side = OrderSide.BUY
            elif direction == "short":
                order_side = OrderSide.SELL
            else:
                logger.error(f"❌ 未知的开仓方向: {direction}")
                return False

            # 调用交易所API下单
            order_result = await self.exchange_manager.place_market_order(
                symbol=self.config.custom_symbol,
                side=order_side,
                amount=quantity,
                reduce_only=False  # 开仓单，不是平仓单
            )

            if order_result and hasattr(order_result, 'order_id') and order_result.order_id:
                logger.info(f"✅ [MA开仓] 开仓订单已提交: 订单ID {order_result.order_id}")

                # 等待一段时间让订单执行
                await asyncio.sleep(1)

                # 验证开仓是否成功
                positions_after = await self.exchange_manager.get_positions(self.config.custom_symbol)
                if positions_after and any(p.size > 0 for p in positions_after):
                    logger.info(f"✅ [MA开仓] 开仓验证成功：已建立持仓")
                    return True
                else:
                    logger.warning(f"⚠️ [MA开仓] 开仓可能未成功，未检测到持仓")
                    return False
            else:
                logger.error(f"❌ [MA开仓] 开仓订单提交失败")
                return False

        except Exception as e:
            logger.error(f"❌ MA市价下单异常: {e}")
            return False

    def get_strategy_status(self) -> Dict[str, Any]:
        """获取策略状态"""
        return {
            "is_running": self.is_running,
            "config": self.config.to_dict(),
            "total_trades": self.total_trades,
            "successful_trades": self.successful_trades,
            "success_rate": self.successful_trades / self.total_trades if self.total_trades > 0 else 0,
            "total_pnl": self.total_pnl,
            "add_position_count": len(self.add_position_records),
            "last_signal_time": self.last_signal_time.isoformat() if self.last_signal_time else None,
            "last_order_time": self.last_order_time.isoformat() if self.last_order_time else None,
            "custom_symbol": self.config.custom_symbol,
            "custom_leverage": self.config.custom_leverage,
            "ma_type": self.config.ma_type.value,
            "fast_period": self.config.fast_period,
            "slow_period": self.config.slow_period,
        }
