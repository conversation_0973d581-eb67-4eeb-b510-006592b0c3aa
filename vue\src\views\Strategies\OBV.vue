<template>
  <div class="obv-strategy">
    <div class="page-header">
      <h2>成交量平衡指标策略配置</h2>
      <p>配置成交量平衡指标(OBV)技术指标参数和交易策略</p>
    </div>

    <div class="card">
      <h3 class="mb-md">OBV策略开发中...</h3>
      <el-empty description="OBV策略配置页面正在开发中">
        <template #image>
          <el-icon size="60" color="#c0c4cc"><PieChart /></el-icon>
        </template>
      </el-empty>
    </div>
  </div>
</template>

<script setup>
import { PieChart } from '@element-plus/icons-vue'
</script>

<style lang="scss" scoped>
.obv-strategy {
  .page-header {
    margin-bottom: var(--spacing-lg);
    
    h2 {
      margin-bottom: var(--spacing-xs);
      color: var(--text-color-primary);
    }
    
    p {
      color: var(--text-color-secondary);
    }
  }
}
</style>
