<?php
/**
 * 配置管理控制器
 * 
 * @description 处理交易配置、策略参数等设置
 */

require_once 'BaseController.php';

class ConfigController extends BaseController {

    /**
     * 配置管理主页
     */
    public function index() {
        try {
            // 获取当前用户配置
            $userConfig = $this->getUserConfig();
            
            // 合并默认配置
            $config = array_merge($GLOBALS['DEFAULT_USER_CONFIG'], $userConfig);
            
            // 获取支持的配置选项
            $supportedExchanges = $GLOBALS['SUPPORTED_EXCHANGES'];
            $supportedStrategies = $GLOBALS['SUPPORTED_STRATEGIES'];
            $timeframes = $GLOBALS['TIMEFRAMES'];
            $addPositionTypes = $GLOBALS['ADD_POSITION_TYPES'];
            $riskManagement = $GLOBALS['RISK_MANAGEMENT'];
            
            $this->render('config/index', [
                'title' => '交易配置 - ' . SYSTEM_NAME,
                'config' => $config,
                'supported_exchanges' => $supportedExchanges,
                'supported_strategies' => $supportedStrategies,
                'timeframes' => $timeframes,
                'add_position_types' => $addPositionTypes,
                'risk_management' => $riskManagement
            ]);
            
        } catch (Exception $e) {
            $this->error('加载配置失败: ' . $e->getMessage());
        }
    }

    /**
     * 保存配置
     */
    public function save() {
        try {
            $this->validateCSRF();
            
            // 验证基础配置
            $this->validateRequired(['exchange', 'symbol', 'leverage', 'initial_margin', 'timeframe']);
            
            // 获取配置数据
            $config = $this->request;
            
            // 验证交易所配置
            $this->validateExchangeConfig($config);
            
            // 验证交易参数
            $this->validateTradingParams($config);
            
            // 验证策略参数
            $this->validateStrategyParams($config);
            
            // 保存配置到数据库
            $this->saveUserConfig('trading_config', $config);
            
            // 记录操作日志
            $this->logAction('save_config', [
                'exchange' => $config['exchange'],
                'symbol' => $config['symbol'],
                'strategy' => $config['strategy'] ?? 'macd'
            ]);
            
            $this->success('配置保存成功');
            
        } catch (Exception $e) {
            $this->error('保存配置失败: ' . $e->getMessage());
        }
    }

    /**
     * 测试交易所连接
     */
    public function testConnection() {
        try {
            $this->validateCSRF();
            $this->validateRequired(['exchange', 'api_key', 'api_secret']);
            
            $config = [
                'exchange' => $this->request['exchange'],
                'api_key' => $this->request['api_key'],
                'api_secret' => $this->request['api_secret'],
                'passphrase' => $this->request['passphrase'] ?? '',
                'sandbox' => isset($this->request['sandbox']) && $this->request['sandbox'] === 'true'
            ];
            
            // 验证交易所配置
            $this->validateExchangeConfig($config);
            
            // 调用Python API测试连接
            $response = $this->api->testExchangeConnection($config);
            
            if ($response['success']) {
                $this->success('交易所连接测试成功', $response['data']);
            } else {
                $this->error('连接测试失败: ' . ($response['data']['error'] ?? '未知错误'));
            }
            
        } catch (Exception $e) {
            $this->error('连接测试失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取交易对列表
     */
    public function getSymbols() {
        try {
            $exchange = $this->request['exchange'] ?? '';
            
            if (empty($exchange)) {
                $this->error('请指定交易所');
            }
            
            global $SUPPORTED_EXCHANGES;
            if (!isset($SUPPORTED_EXCHANGES[$exchange])) {
                $this->error('不支持的交易所');
            }
            
            // 返回支持的交易对
            $symbols = $SUPPORTED_EXCHANGES[$exchange]['supported_symbols'];
            $this->success('获取交易对成功', $symbols);
            
        } catch (Exception $e) {
            $this->error('获取交易对失败: ' . $e->getMessage());
        }
    }

    /**
     * 重置配置为默认值
     */
    public function reset() {
        try {
            $this->validateCSRF();
            
            // 删除用户配置
            $userId = Session::getUserId() ?? 'default';
            $this->db->delete('user_configs', 'user_id = ?', [$userId]);
            
            // 记录操作日志
            $this->logAction('reset_config');
            
            $this->success('配置已重置为默认值');
            
        } catch (Exception $e) {
            $this->error('重置配置失败: ' . $e->getMessage());
        }
    }

    /**
     * 导出配置
     */
    public function export() {
        try {
            $config = $this->getUserConfig();
            
            // 移除敏感信息
            unset($config['api_key'], $config['api_secret'], $config['passphrase']);
            
            $filename = 'bitv_config_' . date('Y-m-d_H-i-s') . '.json';
            
            header('Content-Type: application/json');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            header('Cache-Control: no-cache, must-revalidate');
            
            echo json_encode($config, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            exit;
            
        } catch (Exception $e) {
            $this->error('导出配置失败: ' . $e->getMessage());
        }
    }

    /**
     * 导入配置
     */
    public function import() {
        try {
            $this->validateCSRF();
            
            if (!isset($_FILES['config_file']) || $_FILES['config_file']['error'] !== UPLOAD_ERR_OK) {
                $this->error('请选择有效的配置文件');
            }
            
            $file = $_FILES['config_file'];
            
            // 验证文件类型
            if ($file['type'] !== 'application/json' && 
                pathinfo($file['name'], PATHINFO_EXTENSION) !== 'json') {
                $this->error('只支持JSON格式的配置文件');
            }
            
            // 读取文件内容
            $content = file_get_contents($file['tmp_name']);
            $config = json_decode($content, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->error('配置文件格式错误: ' . json_last_error_msg());
            }
            
            // 验证配置
            if (isset($config['exchange'])) {
                $this->validateExchangeConfig($config);
            }
            if (isset($config['leverage']) || isset($config['initial_margin'])) {
                $this->validateTradingParams($config);
            }
            
            // 保存配置
            $this->saveUserConfig('trading_config', $config);
            
            // 记录操作日志
            $this->logAction('import_config', ['filename' => $file['name']]);
            
            $this->success('配置导入成功');
            
        } catch (Exception $e) {
            $this->error('导入配置失败: ' . $e->getMessage());
        }
    }

    /**
     * 验证策略参数
     */
    private function validateStrategyParams($config) {
        global $SUPPORTED_STRATEGIES;
        
        // MACD策略参数验证
        if (isset($config['macd_enabled']) && $config['macd_enabled']) {
            if (isset($config['macd_fast'])) {
                $this->validateRange($config['macd_fast'], 5, 50, 'MACD快线周期');
            }
            if (isset($config['macd_slow'])) {
                $this->validateRange($config['macd_slow'], 10, 100, 'MACD慢线周期');
            }
            if (isset($config['macd_signal'])) {
                $this->validateRange($config['macd_signal'], 3, 30, 'MACD信号线周期');
            }
            if (isset($config['min_signal_strength'])) {
                $this->validateRange($config['min_signal_strength'], 0.1, 1.0, '最小信号强度');
            }
        }
        
        // 布林带策略参数验证
        if (isset($config['bollinger_enabled']) && $config['bollinger_enabled']) {
            if (isset($config['bb_period'])) {
                $this->validateRange($config['bb_period'], 10, 50, '布林带周期');
            }
            if (isset($config['bb_std_dev'])) {
                $this->validateRange($config['bb_std_dev'], 1.0, 3.0, '布林带标准差');
            }
        }
        
        // 风险管理参数验证
        if (isset($config['max_add_times'])) {
            $this->validateRange($config['max_add_times'], 1, 10, '最大加仓次数');
        }
        if (isset($config['alert_points'])) {
            $this->validateRange($config['alert_points'], 0.1, 10.0, '预警点数');
        }
    }

    /**
     * 获取策略配置表单
     */
    public function getStrategyForm() {
        try {
            $strategy = $this->request['strategy'] ?? 'macd';
            
            global $SUPPORTED_STRATEGIES;
            if (!isset($SUPPORTED_STRATEGIES[$strategy])) {
                $this->error('不支持的策略');
            }
            
            $strategyConfig = $SUPPORTED_STRATEGIES[$strategy];
            $userConfig = $this->getUserConfig();
            
            $this->render('config/strategy_form', [
                'strategy' => $strategy,
                'strategy_config' => $strategyConfig,
                'user_config' => $userConfig
            ]);
            
        } catch (Exception $e) {
            $this->error('获取策略配置失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取风险管理配置
     */
    public function getRiskConfig() {
        try {
            $userConfig = $this->getUserConfig();
            $riskManagement = $GLOBALS['RISK_MANAGEMENT'];
            
            $this->success('获取风险配置成功', [
                'user_config' => $userConfig,
                'risk_management' => $riskManagement
            ]);
            
        } catch (Exception $e) {
            $this->error('获取风险配置失败: ' . $e->getMessage());
        }
    }

    /**
     * 保存风险管理配置
     */
    public function saveRiskConfig() {
        try {
            $this->validateCSRF();
            
            $riskConfig = [
                'risk_control_enabled' => isset($this->request['risk_control_enabled']),
                'emergency_stop' => isset($this->request['emergency_stop']),
                'max_daily_loss_percent' => floatval($this->request['max_daily_loss_percent'] ?? 50),
                'max_position_ratio' => floatval($this->request['max_position_ratio'] ?? 0.5),
                'liquidation_warning_distance' => floatval($this->request['liquidation_warning_distance'] ?? 0.1)
            ];
            
            // 验证风险参数
            $this->validateRange($riskConfig['max_daily_loss_percent'], 1, 100, '最大日损失百分比');
            $this->validateRange($riskConfig['max_position_ratio'], 0.1, 1.0, '最大持仓比例');
            $this->validateRange($riskConfig['liquidation_warning_distance'], 0.01, 0.5, '强平预警距离');
            
            // 保存配置
            $this->saveUserConfig('risk_config', $riskConfig);
            
            // 记录操作日志
            $this->logAction('save_risk_config', $riskConfig);
            
            $this->success('风险管理配置保存成功');
            
        } catch (Exception $e) {
            $this->error('保存风险配置失败: ' . $e->getMessage());
        }
    }
}
?>
