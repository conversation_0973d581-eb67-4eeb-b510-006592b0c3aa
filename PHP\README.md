# BitV MACD智能加仓交易系统 - PHP前端

## 🎯 系统概述

这是一个基于Python异步后端的现代化Web交易前端，专为MACD智能加仓交易策略设计。系统采用PHP开发，提供完整的交易配置、实时监控、历史分析等功能。

## ✨ 主要特性

### 🔧 核心功能
- **交易配置管理** - 支持OKX和Gate.io交易所配置
- **实时监控仪表板** - 价格监控、持仓状态、风险指标
- **智能交易控制** - 启动/停止交易、紧急控制、手动平仓
- **历史数据分析** - 交易记录、盈亏分析、策略性能
- **多策略支持** - MACD、布林带、RSI等技术指标策略

### 🎨 技术特色
- **响应式设计** - 完美适配桌面、平板、手机
- **实时数据** - WebSocket连接，毫秒级数据更新
- **安全架构** - CSRF保护、会话管理、API密钥加密
- **现代UI** - Bootstrap 5 + Chart.js，极致用户体验

## 🚀 快速开始

### 环境要求
- PHP 7.4+
- MySQL 5.7+
- Web服务器 (Apache/Nginx)
- Python后端交易系统运行中

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd bitV/PHP
```

2. **配置数据库**
```sql
CREATE DATABASE bitv_trading CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

3. **配置系统**
编辑 `config/config.php` 文件：
```php
// 数据库配置
define('DB_HOST', 'localhost');
define('DB_NAME', 'bitv_trading');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');

// Python后端API配置
define('PYTHON_API_HOST', 'localhost');
define('PYTHON_API_PORT', 8000);
define('PYTHON_WS_PORT', 8001);
```

4. **设置Web服务器**
将项目目录设置为Web根目录，确保 `index.php` 为入口文件。

5. **启动系统**
访问 `http://your-domain/` 即可开始使用。

## 📁 目录结构

```
PHP/
├── index.php              # 主入口文件
├── config/                # 配置文件
│   └── config.php         # 系统配置
├── includes/              # 核心类库
│   ├── Database.php       # 数据库操作
│   ├── ApiClient.php      # API客户端
│   ├── Session.php        # 会话管理
│   └── Router.php         # 路由系统
├── controllers/           # 控制器
│   ├── BaseController.php # 基础控制器
│   ├── HomeController.php # 首页控制器
│   ├── ConfigController.php # 配置管理
│   ├── DashboardController.php # 仪表板
│   ├── TradingController.php # 交易控制
│   └── HistoryController.php # 历史分析
├── templates/             # 模板文件
│   ├── layout.php         # 主布局
│   ├── home/              # 首页模板
│   ├── config/            # 配置页面
│   ├── dashboard/         # 仪表板
│   ├── trading/           # 交易控制
│   └── history/           # 历史分析
├── assets/                # 静态资源
│   └── css/
│       └── style.css      # 自定义样式
└── logs/                  # 日志文件
```

## 🔧 功能模块

### 1. 交易配置 (`/config`)
- **交易所设置** - API密钥配置、连接测试
- **交易参数** - 杠杆、保证金、时间周期
- **策略配置** - MACD参数、加仓策略、风险管理
- **配置管理** - 导入/导出、重置默认值

### 2. 实时仪表板 (`/dashboard`)
- **价格监控** - 实时K线图、技术指标
- **持仓状态** - 当前持仓、未实现盈亏
- **风险监控** - 强平距离、风险等级
- **系统状态** - 交易状态、连接状态

### 3. 交易控制 (`/trading`)
- **交易操作** - 启动/停止交易、紧急停止
- **持仓管理** - 手动平仓、加仓控制
- **会话管理** - 活跃会话监控、历史会话
- **实时配置** - 动态调整交易参数

### 4. 历史分析 (`/history`)
- **交易记录** - 完整交易历史、筛选查询
- **盈亏分析** - 日/周/月盈亏趋势
- **策略性能** - 各策略表现对比
- **数据导出** - CSV/Excel格式导出

## 🔐 安全特性

### 数据安全
- **CSRF保护** - 所有表单提交都有CSRF令牌验证
- **会话安全** - 安全的会话管理和超时控制
- **API密钥加密** - 敏感信息加密存储
- **SQL注入防护** - 使用预处理语句

### 访问控制
- **IP验证** - 会话IP绑定防止劫持
- **操作日志** - 完整的用户操作记录
- **错误处理** - 安全的错误信息显示

## 📊 API集成

### Python后端接口
系统通过RESTful API与Python后端通信：

```php
// 获取交易状态
GET /api/trading/status

// 启动交易
POST /api/trading/start

// 获取持仓信息
GET /api/positions

// 获取价格数据
GET /api/price/{symbol}
```

### WebSocket实时数据
```javascript
// 连接WebSocket
const ws = new WebSocket('ws://localhost:8001');

// 订阅实时数据
ws.send(JSON.stringify({
    action: 'subscribe',
    symbol: 'BTC-USDT-SWAP',
    channels: ['price', 'position', 'risk']
}));
```

## 🎨 界面定制

### 主题配置
在 `assets/css/style.css` 中修改CSS变量：
```css
:root {
    --primary-color: #2563eb;
    --success-color: #059669;
    --danger-color: #dc2626;
    --warning-color: #d97706;
}
```

### 响应式断点
- **桌面** - 1200px+
- **平板** - 768px - 1199px
- **手机** - 767px以下

## 🔧 开发指南

### 添加新控制器
1. 在 `controllers/` 目录创建新控制器
2. 继承 `BaseController` 类
3. 在 `index.php` 中添加路由

### 创建新模板
1. 在 `templates/` 目录创建模板文件
2. 使用 `$this->render()` 方法渲染
3. 模板自动使用 `layout.php` 布局

### 数据库操作
```php
// 查询数据
$data = $this->db->fetchAll('SELECT * FROM table WHERE id = ?', [$id]);

// 插入数据
$id = $this->db->insert('table', ['column' => 'value']);

// 更新数据
$this->db->update('table', ['column' => 'value'], 'id = ?', [$id]);
```

## 🐛 故障排除

### 常见问题

1. **无法连接Python后端**
   - 检查 `config.php` 中的API地址配置
   - 确认Python后端服务正在运行
   - 检查防火墙设置

2. **数据库连接失败**
   - 验证数据库配置信息
   - 确认数据库服务运行正常
   - 检查用户权限

3. **页面显示错误**
   - 开启调试模式：`define('DEBUG_MODE', true)`
   - 检查PHP错误日志
   - 验证文件权限

### 日志查看
```bash
# 查看系统日志
tail -f logs/trading.log

# 查看PHP错误日志
tail -f /var/log/php_errors.log
```

## 📈 性能优化

### 缓存策略
- 静态资源CDN加速
- 数据库查询结果缓存
- API响应缓存

### 数据库优化
- 添加适当索引
- 定期清理历史数据
- 优化查询语句

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如有问题或建议，请通过以下方式联系：
- 提交 Issue
- 发送邮件
- 技术支持

---

**BitV Trading Team** - 专业的量化交易解决方案
