"""
BIT交易系统测试模块
提供全面的单元测试和集成测试
"""

import sys
import os
import asyncio
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 配置测试日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('tests/test.log')
    ]
)

# 测试配置
TEST_CONFIG = {
    'api_key': 'test_api_key',
    'api_secret': 'test_api_secret',
    'passphrase': 'test_passphrase',
    'sandbox': True,  # 测试环境使用沙盒
    'symbol': 'BTC-USDT',
    'timeframe': '1m',
    'initial_margin': 100.0,
    'leverage': 10
}

def get_event_loop():
    """获取或创建事件循环"""
    try:
        loop = asyncio.get_event_loop()
        if loop.is_closed():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
    except RuntimeError:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
    return loop

__all__ = ['TEST_CONFIG', 'get_event_loop']
