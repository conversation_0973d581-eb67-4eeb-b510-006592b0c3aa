"""
交易所工厂类
严格遵守交易所完全分离原则
实现异步交易所适配器的统一创建和管理
"""
import asyncio
from typing import Optional, Dict, Any
import logging

from .base_exchange import BaseExchange
from .okx_exchange import OKXExchange
from .gateio_exchange import GateIOExchange

logger = logging.getLogger(__name__)

class ExchangeFactory:
    """
    交易所工厂类
    负责创建和管理不同交易所的异步适配器实例
    """
    
    # 支持的交易所映射
    _EXCHANGE_MAPPING = {
        'okx': OKXExchange,
        'gateio': GateIOExchange
    }
    
    @classmethod
    def get_supported_exchanges(cls) -> list:
        """获取支持的交易所列表"""
        return list(cls._EXCHANGE_MAPPING.keys())
    
    @classmethod
    def is_exchange_supported(cls, exchange_name: str) -> bool:
        """检查交易所是否支持"""
        return exchange_name.lower() in cls._EXCHANGE_MAPPING
    
    @classmethod
    async def create_exchange(cls, 
                            exchange_name: str,
                            api_key: str,
                            api_secret: str,
                            passphrase: Optional[str] = None,
                            sandbox: bool = False,
                            auto_connect: bool = True) -> BaseExchange:
        """
        创建交易所实例
        
        Args:
            exchange_name: 交易所名称 ('okx' 或 'gateio')
            api_key: API密钥
            api_secret: API密钥
            passphrase: API密码短语（OKX需要）
            sandbox: 是否使用沙盒环境
            auto_connect: 是否自动连接
            
        Returns:
            BaseExchange: 交易所实例
            
        Raises:
            ValueError: 不支持的交易所
            Exception: 连接失败
        """
        exchange_name = exchange_name.lower()
        
        if not cls.is_exchange_supported(exchange_name):
            raise ValueError(f"不支持的交易所: {exchange_name}. 支持的交易所: {cls.get_supported_exchanges()}")
        
        # 验证必要参数
        if not api_key or not api_secret:
            raise ValueError("API_KEY和API_SECRET不能为空")
        
        if exchange_name == 'okx' and not passphrase:
            raise ValueError("OKX交易所需要提供PASSPHRASE")
        
        # 创建交易所实例
        exchange_class = cls._EXCHANGE_MAPPING[exchange_name]
        
        try:
            if exchange_name == 'okx':
                exchange = exchange_class(api_key, api_secret, passphrase, sandbox)
            else:  # gateio
                exchange = exchange_class(api_key, api_secret, None, sandbox)
            
            # 自动连接
            if auto_connect:
                success = await exchange.connect()
                if not success:
                    raise Exception(f"{exchange_name}交易所连接失败")
                
                logger.info(f"成功创建并连接到{exchange_name}交易所")
            else:
                logger.info(f"成功创建{exchange_name}交易所实例（未连接）")
            
            return exchange
            
        except Exception as e:
            logger.error(f"创建{exchange_name}交易所实例失败: {e}")
            raise e
    
    @classmethod
    async def create_exchange_from_config(cls, config: Dict[str, Any]) -> BaseExchange:
        """
        从配置字典创建交易所实例
        
        Args:
            config: 配置字典，包含exchange, api_key, api_secret等
            
        Returns:
            BaseExchange: 交易所实例
        """
        required_fields = ['exchange', 'api_key', 'api_secret']
        for field in required_fields:
            if field not in config:
                raise ValueError(f"配置中缺少必要字段: {field}")
        
        return await cls.create_exchange(
            exchange_name=config['exchange'],
            api_key=config['api_key'],
            api_secret=config['api_secret'],
            passphrase=config.get('passphrase'),
            sandbox=config.get('sandbox', True),
            auto_connect=config.get('auto_connect', True)
        )

class ExchangeManager:
    """
    交易所管理器
    负责管理多个交易所实例的生命周期
    """
    
    def __init__(self):
        self._exchanges: Dict[str, BaseExchange] = {}
        self._active_exchange: Optional[BaseExchange] = None
        self._active_exchange_name: Optional[str] = None
    
    async def add_exchange(self, name: str, exchange: BaseExchange) -> None:
        """添加交易所实例"""
        self._exchanges[name] = exchange
        logger.info(f"添加交易所实例: {name}")
    
    async def remove_exchange(self, name: str) -> None:
        """移除交易所实例"""
        if name in self._exchanges:
            exchange = self._exchanges[name]
            if exchange.is_connected:
                await exchange.disconnect()
            del self._exchanges[name]
            
            # 如果移除的是当前活跃交易所，清空活跃状态
            if name == self._active_exchange_name:
                self._active_exchange = None
                self._active_exchange_name = None
            
            logger.info(f"移除交易所实例: {name}")
    
    async def set_active_exchange(self, name: str) -> None:
        """设置活跃交易所"""
        if name not in self._exchanges:
            raise ValueError(f"交易所实例不存在: {name}")
        
        self._active_exchange = self._exchanges[name]
        self._active_exchange_name = name
        logger.info(f"设置活跃交易所: {name}")
    
    def get_active_exchange(self) -> Optional[BaseExchange]:
        """获取当前活跃交易所"""
        return self._active_exchange
    
    def get_active_exchange_name(self) -> Optional[str]:
        """获取当前活跃交易所名称"""
        return self._active_exchange_name
    
    def get_exchange(self, name: str) -> Optional[BaseExchange]:
        """获取指定交易所实例"""
        return self._exchanges.get(name)
    
    def list_exchanges(self) -> list:
        """列出所有交易所实例"""
        return list(self._exchanges.keys())
    
    async def disconnect_all(self) -> None:
        """断开所有交易所连接"""
        for name, exchange in self._exchanges.items():
            if exchange.is_connected:
                await exchange.disconnect()
                logger.info(f"断开交易所连接: {name}")
        
        self._active_exchange = None
        self._active_exchange_name = None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.disconnect_all()

# 全局交易所管理器实例
exchange_manager = ExchangeManager()
