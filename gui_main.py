"""
MACD智能加仓交易系统 - GUI版本 (异步架构)
图形界面启动程序
"""
import sys
import os
import logging
import tkinter as tk
import asyncio
from tkinter import messagebox

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from gui import MainWindow
    from utils.async_gui import run_async_gui, set_async_tk
except ImportError as e:
    print(f"导入GUI模块失败: {e}")
    print("请确保所有依赖已正确安装")
    sys.exit(1)

def setup_logging():
    """设置日志系统"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('trading_gui.log', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def check_dependencies():
    """检查依赖"""
    required_modules = [
        'tkinter', 'asyncio', 'threading', 'queue',
        'pandas', 'numpy', 'aiohttp', 'websockets'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        error_msg = f"缺少以下依赖模块: {', '.join(missing_modules)}\n"
        error_msg += "请运行: pip install -r requirements.txt"
        
        # 尝试显示GUI错误对话框
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("依赖错误", error_msg)
            root.destroy()
        except:
            print(error_msg)
        
        return False
    
    return True

def main():
    """主函数"""
    try:
        print("=== MACD智能加仓交易系统 GUI版本 (异步架构) ===")
        print("正在启动异步图形界面...")

        # 设置日志
        setup_logging()
        logger = logging.getLogger(__name__)

        # 检查依赖
        if not check_dependencies():
            logger.error("依赖检查失败")
            return

        logger.info("异步GUI版本启动")

        # 创建主窗口
        root = tk.Tk()

        # 异步GUI设置函数
        async def setup_gui(async_tk):
            """设置异步GUI"""
            set_async_tk(async_tk)

            # 创建主窗口应用
            app = MainWindow(root, async_tk)

            # 设置窗口关闭事件
            root.protocol("WM_DELETE_WINDOW", lambda: asyncio.create_task(app.on_closing()))

            logger.info("异步GUI设置完成")

        # 运行异步GUI
        run_async_gui(root, setup_gui)

        logger.info("异步GUI版本退出")

    except KeyboardInterrupt:
        print("\n用户中断程序")
    except Exception as e:
        error_msg = f"程序异常: {e}"
        print(error_msg)

        # 尝试显示GUI错误对话框
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("程序错误", error_msg)
            root.destroy()
        except:
            pass

        logging.error(error_msg, exc_info=True)

if __name__ == "__main__":
    main()
