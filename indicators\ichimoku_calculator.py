"""
一目均衡表（Ichimoku）技术指标计算器
实现完整的一目均衡表指标计算和信号分析
"""

import asyncio
import logging
from typing import List, Optional, Tuple, Dict, Any
from dataclasses import dataclass
from enum import Enum
import numpy as np

logger = logging.getLogger(__name__)

class IchimokuSignal(Enum):
    """一目均衡表信号类型"""
    STRONG_BULLISH = "strong_bullish"      # 强烈看涨
    WEAK_BULLISH = "weak_bullish"          # 弱看涨
    NEUTRAL = "neutral"                    # 中性
    WEAK_BEARISH = "weak_bearish"          # 弱看跌
    STRONG_BEARISH = "strong_bearish"      # 强烈看跌

@dataclass
class IchimokuData:
    """一目均衡表数据"""
    tenkan_sen: float          # 转换线（9日）
    kijun_sen: float           # 基准线（26日）
    senkou_span_a: float       # 先行带A（未来26日）
    senkou_span_b: float       # 先行带B（未来26日）
    chikou_span: float         # 滞后线（过去26日）
    current_price: float       # 当前价格
    signal: IchimokuSignal     # 综合信号
    signal_strength: float     # 信号强度 (0.0-1.0)
    cloud_thickness: float     # 云层厚度
    price_vs_cloud: str        # 价格与云层关系
    trend_direction: str       # 趋势方向
    reliability: float = 1.0   # 可靠性评分 (0.0-1.0)

class IchimokuCalculator:
    """一目均衡表计算器"""
    
    def __init__(self, tenkan_period: int = 9, kijun_period: int = 26, 
                 senkou_b_period: int = 52, displacement: int = 26):
        """
        初始化一目均衡表计算器
        
        Args:
            tenkan_period: 转换线周期（默认9）
            kijun_period: 基准线周期（默认26）
            senkou_b_period: 先行带B周期（默认52）
            displacement: 位移周期（默认26）
        """
        self.tenkan_period = tenkan_period
        self.kijun_period = kijun_period
        self.senkou_b_period = senkou_b_period
        self.displacement = displacement
        
        # 历史数据缓存
        self._price_history: List[Dict[str, float]] = []
        self._ichimoku_history: List[IchimokuData] = []
        
        logger.info(f"一目均衡表计算器初始化: 转换线={tenkan_period}, 基准线={kijun_period}, "
                   f"先行带B={senkou_b_period}, 位移={displacement}")
    
    def get_required_data_count(self) -> int:
        """获取所需的最小数据点数"""
        return max(self.senkou_b_period, self.kijun_period) + self.displacement
    
    async def calculate_ichimoku(self, highs: List[float], lows: List[float], 
                                closes: List[float]) -> Optional[IchimokuData]:
        """
        计算一目均衡表指标
        
        Args:
            highs: 最高价列表
            lows: 最低价列表
            closes: 收盘价列表
            
        Returns:
            Optional[IchimokuData]: 一目均衡表数据，数据不足时返回None
        """
        try:
            if len(highs) != len(lows) or len(highs) != len(closes):
                logger.error("高低收价格数据长度不一致")
                return None
            
            required_count = self.get_required_data_count()
            if len(closes) < required_count:
                logger.warning(f"数据不足，需要至少{required_count}个数据点，当前{len(closes)}个")
                return None
            
            # 计算转换线（Tenkan-sen）：(9日最高价+9日最低价)/2
            tenkan_sen = await self._calculate_line(highs, lows, self.tenkan_period)
            
            # 计算基准线（Kijun-sen）：(26日最高价+26日最低价)/2
            kijun_sen = await self._calculate_line(highs, lows, self.kijun_period)
            
            # 计算先行带A（Senkou Span A）：(转换线+基准线)/2，向前位移26日
            senkou_span_a = (tenkan_sen + kijun_sen) / 2
            
            # 计算先行带B（Senkou Span B）：(52日最高价+52日最低价)/2，向前位移26日
            senkou_span_b = await self._calculate_line(highs, lows, self.senkou_b_period)
            
            # 计算滞后线（Chikou Span）：当前收盘价，向后位移26日
            current_price = closes[-1]
            chikou_span = current_price  # 实际应用中这是过去26日的价格位置
            
            # 分析信号
            signal, signal_strength = await self._analyze_ichimoku_signal(
                current_price, tenkan_sen, kijun_sen, senkou_span_a, senkou_span_b, closes
            )
            
            # 计算云层厚度
            cloud_thickness = abs(senkou_span_a - senkou_span_b) / current_price * 100
            
            # 判断价格与云层关系
            price_vs_cloud = self._analyze_price_vs_cloud(current_price, senkou_span_a, senkou_span_b)
            
            # 判断趋势方向
            trend_direction = self._analyze_trend_direction(tenkan_sen, kijun_sen, current_price)
            
            # 计算可靠性
            reliability = self._calculate_reliability(len(closes), required_count, signal_strength)
            
            result = IchimokuData(
                tenkan_sen=round(tenkan_sen, 4),
                kijun_sen=round(kijun_sen, 4),
                senkou_span_a=round(senkou_span_a, 4),
                senkou_span_b=round(senkou_span_b, 4),
                chikou_span=round(chikou_span, 4),
                current_price=round(current_price, 4),
                signal=signal,
                signal_strength=round(signal_strength, 3),
                cloud_thickness=round(cloud_thickness, 3),
                price_vs_cloud=price_vs_cloud,
                trend_direction=trend_direction,
                reliability=round(reliability, 3)
            )
            
            # 更新历史记录
            self._ichimoku_history.append(result)
            if len(self._ichimoku_history) > 100:
                self._ichimoku_history = self._ichimoku_history[-100:]
            
            logger.debug(f"一目均衡表计算完成: 转换线={tenkan_sen:.4f}, 基准线={kijun_sen:.4f}, "
                        f"信号={signal.value}, 强度={signal_strength:.3f}")
            
            return result
            
        except Exception as e:
            logger.error(f"一目均衡表计算异常: {e}")
            return None
    
    async def _calculate_line(self, highs: List[float], lows: List[float], period: int) -> float:
        """计算一目均衡表线条（最高价+最低价）/2"""
        if len(highs) < period or len(lows) < period:
            raise ValueError(f"数据不足，需要至少{period}个数据点")
        
        # 获取最近period个数据点
        recent_highs = highs[-period:]
        recent_lows = lows[-period:]
        
        # 计算期间内的最高价和最低价
        max_high = max(recent_highs)
        min_low = min(recent_lows)
        
        return (max_high + min_low) / 2
    
    async def _analyze_ichimoku_signal(self, current_price: float, tenkan_sen: float, 
                                     kijun_sen: float, senkou_span_a: float, 
                                     senkou_span_b: float, closes: List[float]) -> Tuple[IchimokuSignal, float]:
        """分析一目均衡表信号"""
        try:
            signal_score = 0.0
            max_score = 5.0  # 最大得分
            
            # 1. 价格与转换线关系 (权重: 1.0)
            if current_price > tenkan_sen:
                signal_score += 1.0
            elif current_price < tenkan_sen:
                signal_score -= 1.0
            
            # 2. 转换线与基准线关系 (权重: 1.0)
            if tenkan_sen > kijun_sen:
                signal_score += 1.0
            elif tenkan_sen < kijun_sen:
                signal_score -= 1.0
            
            # 3. 价格与云层关系 (权重: 1.5)
            cloud_top = max(senkou_span_a, senkou_span_b)
            cloud_bottom = min(senkou_span_a, senkou_span_b)
            
            if current_price > cloud_top:
                signal_score += 1.5  # 价格在云层上方，看涨
            elif current_price < cloud_bottom:
                signal_score -= 1.5  # 价格在云层下方，看跌
            # 价格在云层内部，不加分
            
            # 4. 云层颜色（先行带A与先行带B关系）(权重: 1.0)
            if senkou_span_a > senkou_span_b:
                signal_score += 1.0  # 绿云（看涨云）
            elif senkou_span_a < senkou_span_b:
                signal_score -= 1.0  # 红云（看跌云）
            
            # 5. 滞后线确认 (权重: 0.5)
            if len(closes) >= self.displacement:
                past_price = closes[-(self.displacement + 1)]
                if current_price > past_price:
                    signal_score += 0.5
                elif current_price < past_price:
                    signal_score -= 0.5
            
            # 标准化信号强度
            signal_strength = abs(signal_score) / max_score
            signal_strength = min(signal_strength, 1.0)
            
            # 确定信号类型
            if signal_score >= 3.5:
                signal = IchimokuSignal.STRONG_BULLISH
            elif signal_score >= 1.5:
                signal = IchimokuSignal.WEAK_BULLISH
            elif signal_score <= -3.5:
                signal = IchimokuSignal.STRONG_BEARISH
            elif signal_score <= -1.5:
                signal = IchimokuSignal.WEAK_BEARISH
            else:
                signal = IchimokuSignal.NEUTRAL
            
            return signal, signal_strength
            
        except Exception as e:
            logger.error(f"分析一目均衡表信号异常: {e}")
            return IchimokuSignal.NEUTRAL, 0.0
    
    def _analyze_price_vs_cloud(self, price: float, senkou_a: float, senkou_b: float) -> str:
        """分析价格与云层关系"""
        cloud_top = max(senkou_a, senkou_b)
        cloud_bottom = min(senkou_a, senkou_b)
        
        if price > cloud_top:
            return "云层上方"
        elif price < cloud_bottom:
            return "云层下方"
        else:
            return "云层内部"
    
    def _analyze_trend_direction(self, tenkan_sen: float, kijun_sen: float, price: float) -> str:
        """分析趋势方向"""
        if tenkan_sen > kijun_sen and price > tenkan_sen:
            return "强势上涨"
        elif tenkan_sen > kijun_sen:
            return "上涨趋势"
        elif tenkan_sen < kijun_sen and price < tenkan_sen:
            return "强势下跌"
        elif tenkan_sen < kijun_sen:
            return "下跌趋势"
        else:
            return "横盘整理"
    
    def _calculate_reliability(self, data_count: int, required_count: int, signal_strength: float) -> float:
        """计算可靠性评分"""
        # 基于数据充足性的可靠性
        data_reliability = min(data_count / required_count, 1.0)
        
        # 基于信号强度的可靠性
        strength_reliability = signal_strength
        
        # 综合可靠性
        return (data_reliability * 0.3 + strength_reliability * 0.7)
    
    def detect_ichimoku_signal(self, highs: List[float], lows: List[float], 
                              closes: List[float]) -> Tuple[bool, str, float]:
        """
        检测一目均衡表交易信号
        
        Args:
            highs: 最高价列表
            lows: 最低价列表
            closes: 收盘价列表
            
        Returns:
            Tuple[bool, str, float]: (是否有信号, 信号类型, 信号强度)
        """
        try:
            # 使用同步方式调用异步方法
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                ichimoku_data = loop.run_until_complete(
                    self.calculate_ichimoku(highs, lows, closes)
                )
            finally:
                loop.close()
            
            if not ichimoku_data:
                return False, "neutral", 0.0
            
            # 判断是否有明确信号
            if ichimoku_data.signal in [IchimokuSignal.STRONG_BULLISH, IchimokuSignal.WEAK_BULLISH]:
                return True, "long", ichimoku_data.signal_strength
            elif ichimoku_data.signal in [IchimokuSignal.STRONG_BEARISH, IchimokuSignal.WEAK_BEARISH]:
                return True, "short", ichimoku_data.signal_strength
            else:
                return False, "neutral", ichimoku_data.signal_strength
                
        except Exception as e:
            logger.error(f"检测一目均衡表信号异常: {e}")
            return False, "neutral", 0.0
