#!/bin/bash

echo ""
echo "========================================"
echo "   BitV Vue前端启动脚本"
echo "========================================"
echo ""

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ 错误: 未检测到Node.js"
    echo "请先安装Node.js: https://nodejs.org/"
    exit 1
fi

echo "✅ Node.js版本:"
node --version

# 检查是否在正确目录
if [ ! -f "package.json" ]; then
    echo "❌ 错误: 未找到package.json文件"
    echo "请确保在vue目录下运行此脚本"
    exit 1
fi

# 检查Python后端是否运行
echo ""
echo "🔍 检查Python后端连接..."
if curl -s http://localhost:8000/health > /dev/null 2>&1; then
    echo "✅ Python后端连接正常"
else
    echo "⚠️  警告: Python后端未运行或无法连接"
    echo "请确保Python后端服务运行在 http://localhost:8000"
    echo ""
    read -p "是否继续启动前端? (y/n): " continue
    if [ "$continue" != "y" ] && [ "$continue" != "Y" ]; then
        echo "已取消启动"
        exit 1
    fi
fi

# 检查依赖是否安装
if [ ! -d "node_modules" ]; then
    echo ""
    echo "📦 首次运行，正在安装依赖..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败"
        exit 1
    fi
    echo "✅ 依赖安装完成"
fi

# 启动开发服务器
echo ""
echo "🚀 启动Vue开发服务器..."
echo ""
echo "📝 访问地址:"
echo "   - 本地: http://localhost:3000"
echo "   - 网络: http://0.0.0.0:3000"
echo ""
echo "💡 提示:"
echo "   - 按 Ctrl+C 停止服务器"
echo "   - 修改代码会自动热重载"
echo "   - API请求会自动代理到Python后端"
echo ""

npm run dev

echo ""
echo "👋 Vue前端服务已停止"
