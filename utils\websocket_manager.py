#!/usr/bin/env python3
"""
统一WebSocket管理器
解决多个WebSocket实现冲突的问题
"""

import asyncio
import json
import logging
from typing import Dict, List, Any, Optional, Set
from fastapi import WebSocket, WebSocketDisconnect
from datetime import datetime
import weakref

logger = logging.getLogger(__name__)

class WebSocketManager:
    """统一的WebSocket连接管理器"""
    
    def __init__(self):
        # 活跃连接列表
        self.active_connections: List[WebSocket] = []
        
        # 订阅管理 - 连接ID到订阅频道的映射
        self.subscriptions: Dict[str, Set[str]] = {}
        
        # 连接元数据
        self.connection_metadata: Dict[str, Dict[str, Any]] = {}
        
        # 消息队列
        self.message_queue: asyncio.Queue = asyncio.Queue()
        
        # 统计信息
        self.stats = {
            "total_connections": 0,
            "active_connections": 0,
            "messages_sent": 0,
            "messages_received": 0,
            "errors": 0
        }
    
    def get_connection_id(self, websocket: WebSocket) -> str:
        """获取连接的唯一ID"""
        return f"ws_{id(websocket)}"
    
    async def connect(self, websocket: WebSocket, client_info: Optional[Dict] = None) -> str:
        """
        接受新的WebSocket连接
        
        Args:
            websocket: WebSocket连接对象
            client_info: 客户端信息
            
        Returns:
            str: 连接ID
        """
        await websocket.accept()
        
        connection_id = self.get_connection_id(websocket)
        self.active_connections.append(websocket)
        self.subscriptions[connection_id] = set()
        
        # 保存连接元数据
        self.connection_metadata[connection_id] = {
            "connected_at": datetime.now().isoformat(),
            "client_info": client_info or {},
            "last_activity": datetime.now().isoformat()
        }
        
        # 更新统计
        self.stats["total_connections"] += 1
        self.stats["active_connections"] = len(self.active_connections)
        
        logger.info(f"🔌 WebSocket连接已建立: {connection_id}")
        
        # 发送连接确认消息
        await self.send_to_connection(websocket, {
            "type": "connection_established",
            "data": {
                "connection_id": connection_id,
                "message": "WebSocket连接成功",
                "timestamp": datetime.now().isoformat(),
                "available_channels": [
                    "price_update",
                    "position_update", 
                    "risk_update",
                    "trading_update",
                    "log_update",
                    "system_notification"
                ]
            }
        })
        
        return connection_id
    
    async def disconnect(self, websocket: WebSocket):
        """
        断开WebSocket连接
        
        Args:
            websocket: WebSocket连接对象
        """
        connection_id = self.get_connection_id(websocket)
        
        # 移除连接
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        
        # 清理订阅
        if connection_id in self.subscriptions:
            del self.subscriptions[connection_id]
        
        # 清理元数据
        if connection_id in self.connection_metadata:
            del self.connection_metadata[connection_id]
        
        # 更新统计
        self.stats["active_connections"] = len(self.active_connections)
        
        logger.info(f"🔌 WebSocket连接已断开: {connection_id}")
    
    async def send_to_connection(self, websocket: WebSocket, message: Dict[str, Any]):
        """
        向指定连接发送消息
        
        Args:
            websocket: WebSocket连接对象
            message: 要发送的消息
        """
        try:
            await websocket.send_text(json.dumps(message))
            self.stats["messages_sent"] += 1
            
            # 更新最后活动时间
            connection_id = self.get_connection_id(websocket)
            if connection_id in self.connection_metadata:
                self.connection_metadata[connection_id]["last_activity"] = datetime.now().isoformat()
                
        except Exception as e:
            logger.error(f"发送消息失败: {e}")
            self.stats["errors"] += 1
            # 移除失效连接
            await self.disconnect(websocket)
    
    async def broadcast(self, message: Dict[str, Any], channel: Optional[str] = None):
        """
        广播消息到所有连接或指定频道的订阅者
        
        Args:
            message: 要广播的消息
            channel: 可选的频道名称，如果指定则只发送给订阅该频道的连接
        """
        if not self.active_connections:
            return
        
        # 如果指定了频道，只发送给订阅该频道的连接
        if channel:
            target_connections = []
            for connection_id, channels in self.subscriptions.items():
                if channel in channels:
                    # 找到对应的WebSocket连接
                    for ws in self.active_connections:
                        if self.get_connection_id(ws) == connection_id:
                            target_connections.append(ws)
                            break
        else:
            target_connections = self.active_connections.copy()
        
        # 并发发送消息
        if target_connections:
            tasks = [
                self.send_to_connection(ws, message)
                for ws in target_connections
            ]
            await asyncio.gather(*tasks, return_exceptions=True)
    
    async def handle_client_message(self, websocket: WebSocket, message: Dict[str, Any]):
        """
        处理客户端消息
        
        Args:
            websocket: WebSocket连接对象
            message: 客户端消息
        """
        connection_id = self.get_connection_id(websocket)
        action = message.get("action")
        
        self.stats["messages_received"] += 1
        
        try:
            if action == "subscribe":
                # 处理订阅请求
                channels = message.get("channels", [])
                if isinstance(channels, str):
                    channels = [channels]
                
                for channel in channels:
                    self.subscriptions[connection_id].add(channel)
                
                await self.send_to_connection(websocket, {
                    "type": "subscription_confirmed",
                    "data": {
                        "channels": list(self.subscriptions[connection_id]),
                        "timestamp": datetime.now().isoformat()
                    }
                })
                
                logger.info(f"订阅频道: {connection_id} -> {channels}")
            
            elif action == "unsubscribe":
                # 处理取消订阅请求
                channels = message.get("channels", [])
                if isinstance(channels, str):
                    channels = [channels]
                
                for channel in channels:
                    self.subscriptions[connection_id].discard(channel)
                
                await self.send_to_connection(websocket, {
                    "type": "unsubscription_confirmed", 
                    "data": {
                        "channels": list(self.subscriptions[connection_id]),
                        "timestamp": datetime.now().isoformat()
                    }
                })
                
                logger.info(f"取消订阅频道: {connection_id} -> {channels}")
            
            elif action == "get_current_data":
                # 处理数据快照请求
                await self.send_to_connection(websocket, {
                    "type": "data_snapshot_request",
                    "data": {
                        "message": "数据快照请求已收到",
                        "timestamp": datetime.now().isoformat()
                    }
                })
            
            elif action == "ping":
                # 处理心跳请求
                await self.send_to_connection(websocket, {
                    "type": "pong",
                    "data": {
                        "timestamp": datetime.now().isoformat()
                    }
                })
            
            else:
                logger.warning(f"未知操作: {action}")
                await self.send_to_connection(websocket, {
                    "type": "error",
                    "data": {
                        "message": f"未知操作: {action}",
                        "timestamp": datetime.now().isoformat()
                    }
                })
        
        except Exception as e:
            logger.error(f"处理客户端消息失败: {e}")
            await self.send_to_connection(websocket, {
                "type": "error",
                "data": {
                    "message": f"消息处理失败: {str(e)}",
                    "timestamp": datetime.now().isoformat()
                }
            })
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            **self.stats,
            "subscriptions": {
                conn_id: list(channels) 
                for conn_id, channels in self.subscriptions.items()
            },
            "connection_metadata": self.connection_metadata
        }

# 全局WebSocket管理器实例
websocket_manager = WebSocketManager()
