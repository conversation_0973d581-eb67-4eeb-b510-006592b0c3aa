<?php
/**
 * BitV MACD智能加仓交易系统 - 登录页面
 * 实盘交易版本 - 安全登录界面
 */

session_start();

// 加载配置
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/security.php';

// 如果已经登录，重定向到首页
if (isAuthenticated()) {
    redirect('index.php');
}

$error = '';
$success = '';

// 处理登录请求
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // 验证CSRF令牌
        if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
            throw new Exception('安全验证失败，请刷新页面重试');
        }
        
        // 获取并清理输入
        $username = sanitizeInput($_POST['username'] ?? '');
        $password = $_POST['password'] ?? '';
        $rememberMe = isset($_POST['remember_me']);
        
        // 基本验证
        if (empty($username) || empty($password)) {
            throw new Exception('用户名和密码不能为空');
        }
        
        // 频率限制检查
        $clientIP = getClientIP();
        if (!rateLimitCheck('login_' . $clientIP, 5, 300)) { // 5分钟内最多5次尝试
            throw new Exception('登录尝试过于频繁，请稍后再试');
        }
        
        // 尝试登录
        global $userAuth;
        $user = $userAuth->login($username, $password, $rememberMe);
        
        // 记录成功登录
        logUserAction('LOGIN_SUCCESS', [
            'username' => $username,
            'ip' => $clientIP,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ], $user['id']);
        
        // 设置成功消息
        $_SESSION['flash_message'] = '登录成功，欢迎回来！';
        $_SESSION['flash_type'] = 'success';
        
        // 重定向到首页
        redirect('index.php');
        
    } catch (Exception $e) {
        $error = $e->getMessage();
        
        // 记录失败登录
        logUserAction('LOGIN_FAILED', [
            'username' => $username ?? 'unknown',
            'ip' => $clientIP,
            'error' => $error
        ]);
        
        writeLog('WARNING', '登录失败', [
            'username' => $username ?? 'unknown',
            'ip' => $clientIP,
            'error' => $error
        ]);
    }
}

$pageTitle = '用户登录';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="BitV MACD智能加仓交易系统 - 安全登录">
    <meta name="robots" content="noindex, nofollow">
    
    <title><?php echo htmlspecialchars($pageTitle . ' - ' . SYSTEM_NAME); ?></title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/img/favicon.ico">
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
        }
        
        .login-left {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            text-align: center;
        }
        
        .login-right {
            padding: 60px 40px;
        }
        
        .system-logo {
            font-size: 3rem;
            margin-bottom: 20px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-size: 16px;
            font-weight: 600;
            color: white;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
            color: white;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            opacity: 0;
            animation: slideInLeft 0.6s ease forwards;
        }
        
        .feature-item:nth-child(1) { animation-delay: 0.1s; }
        .feature-item:nth-child(2) { animation-delay: 0.2s; }
        .feature-item:nth-child(3) { animation-delay: 0.3s; }
        .feature-item:nth-child(4) { animation-delay: 0.4s; }
        
        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
        
        .feature-icon {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 18px;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .security-info {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
        }
        
        .security-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            font-size: 14px;
            color: #6c757d;
        }
        
        .security-item i {
            margin-right: 10px;
            color: #28a745;
        }
        
        @media (max-width: 768px) {
            .login-left {
                padding: 40px 20px;
            }
            
            .login-right {
                padding: 40px 20px;
            }
            
            .system-logo {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="row g-0">
                <!-- 左侧：系统介绍 -->
                <div class="col-lg-6">
                    <div class="login-left">
                        <div class="system-logo">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h2 class="mb-4">BitV交易系统</h2>
                        <p class="mb-4">专业级MACD智能加仓量化交易平台</p>
                        
                        <div class="features">
                            <div class="feature-item">
                                <div class="feature-icon">
                                    <i class="fas fa-robot"></i>
                                </div>
                                <div>
                                    <strong>智能交易</strong><br>
                                    <small>MACD策略自动化交易</small>
                                </div>
                            </div>
                            
                            <div class="feature-item">
                                <div class="feature-icon">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <div>
                                    <strong>风险管理</strong><br>
                                    <small>实时监控与风险控制</small>
                                </div>
                            </div>
                            
                            <div class="feature-item">
                                <div class="feature-icon">
                                    <i class="fas fa-exchange-alt"></i>
                                </div>
                                <div>
                                    <strong>多交易所</strong><br>
                                    <small>支持OKX、Gate.io等</small>
                                </div>
                            </div>
                            
                            <div class="feature-item">
                                <div class="feature-icon">
                                    <i class="fas fa-chart-bar"></i>
                                </div>
                                <div>
                                    <strong>数据分析</strong><br>
                                    <small>专业级交易数据分析</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <small class="text-white-50">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                实盘交易版本 - 高风险投资
                            </small>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧：登录表单 -->
                <div class="col-lg-6">
                    <div class="login-right">
                        <div class="text-center mb-4">
                            <h3 class="text-dark">欢迎回来</h3>
                            <p class="text-muted">请登录您的账户以继续</p>
                        </div>
                        
                        <?php if ($error): ?>
                        <div class="alert alert-danger" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <?php echo htmlspecialchars($error); ?>
                        </div>
                        <?php endif; ?>
                        
                        <?php if ($success): ?>
                        <div class="alert alert-success" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            <?php echo htmlspecialchars($success); ?>
                        </div>
                        <?php endif; ?>
                        
                        <form method="POST" action="" id="loginForm">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            
                            <div class="mb-3">
                                <label for="username" class="form-label">
                                    <i class="fas fa-user me-2"></i>用户名
                                </label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" 
                                       required autocomplete="username">
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock me-2"></i>密码
                                </label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="password" name="password" 
                                           required autocomplete="current-password">
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="remember_me" name="remember_me">
                                <label class="form-check-label" for="remember_me">
                                    记住我（30天）
                                </label>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-login">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    登录系统
                                </button>
                            </div>
                        </form>
                        
                        <!-- 安全信息 -->
                        <div class="security-info">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-shield-alt me-2"></i>
                                安全保障
                            </h6>
                            
                            <div class="security-item">
                                <i class="fas fa-lock"></i>
                                <span>SSL加密传输</span>
                            </div>
                            
                            <div class="security-item">
                                <i class="fas fa-user-shield"></i>
                                <span>多重身份验证</span>
                            </div>
                            
                            <div class="security-item">
                                <i class="fas fa-history"></i>
                                <span>登录日志记录</span>
                            </div>
                            
                            <div class="security-item">
                                <i class="fas fa-ban"></i>
                                <span>异常登录拦截</span>
                            </div>
                        </div>
                        
                        <div class="text-center mt-4">
                            <small class="text-muted">
                                默认管理员账户：admin / BitV2024!<br>
                                <span class="text-danger">首次登录后请立即修改密码</span>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 密码显示/隐藏切换
            const togglePassword = document.getElementById('togglePassword');
            const passwordInput = document.getElementById('password');
            
            togglePassword.addEventListener('click', function() {
                const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordInput.setAttribute('type', type);
                
                const icon = this.querySelector('i');
                icon.classList.toggle('fa-eye');
                icon.classList.toggle('fa-eye-slash');
            });
            
            // 表单验证
            const loginForm = document.getElementById('loginForm');
            loginForm.addEventListener('submit', function(e) {
                const username = document.getElementById('username').value.trim();
                const password = document.getElementById('password').value;
                
                if (!username || !password) {
                    e.preventDefault();
                    alert('请填写用户名和密码');
                    return;
                }
                
                if (username.length < 3) {
                    e.preventDefault();
                    alert('用户名至少需要3个字符');
                    return;
                }
                
                if (password.length < 6) {
                    e.preventDefault();
                    alert('密码至少需要6个字符');
                    return;
                }
                
                // 显示加载状态
                const submitBtn = this.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>登录中...';
                submitBtn.disabled = true;
                
                // 如果有错误，恢复按钮状态
                setTimeout(() => {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }, 5000);
            });
            
            // 自动聚焦到用户名输入框
            document.getElementById('username').focus();
            
            // 检查Caps Lock
            document.addEventListener('keydown', function(e) {
                if (e.getModifierState && e.getModifierState('CapsLock')) {
                    showCapsLockWarning(true);
                } else {
                    showCapsLockWarning(false);
                }
            });
            
            function showCapsLockWarning(show) {
                let warning = document.getElementById('capsLockWarning');
                
                if (show && !warning) {
                    warning = document.createElement('div');
                    warning.id = 'capsLockWarning';
                    warning.className = 'alert alert-warning mt-2';
                    warning.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>大写锁定已开启';
                    
                    const passwordGroup = document.getElementById('password').parentNode.parentNode;
                    passwordGroup.appendChild(warning);
                } else if (!show && warning) {
                    warning.remove();
                }
            }
        });
    </script>
</body>
</html>
