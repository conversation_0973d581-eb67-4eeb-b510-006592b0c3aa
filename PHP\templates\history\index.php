<div class="row mb-4">
    <div class="col-12">
        <h1 class="h2 mb-3">
            <i class="fas fa-history text-primary me-2"></i>
            交易历史分析
        </h1>
        <p class="text-muted">查看交易记录、分析盈亏表现和策略效果</p>
    </div>
</div>

<!-- 统计概览 -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value text-primary">
                    <?php echo $statistics['basic']['total_trades'] ?? 0; ?>
                </div>
                <div class="metric-label">总交易次数</div>
                <small class="text-muted">历史记录</small>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value <?php echo ($statistics['basic']['total_pnl'] ?? 0) >= 0 ? 'text-success' : 'text-danger'; ?>">
                    <?php echo formatNumber($statistics['basic']['total_pnl'] ?? 0, 2); ?>
                </div>
                <div class="metric-label">总盈亏 (USDT)</div>
                <small class="text-muted">累计收益</small>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value text-info">
                    <?php echo formatNumber($statistics['basic']['win_rate'] ?? 0, 1); ?>%
                </div>
                <div class="metric-label">胜率</div>
                <small class="text-muted">
                    <?php echo $statistics['basic']['profit_trades'] ?? 0; ?> / <?php echo $statistics['basic']['total_trades'] ?? 0; ?>
                </small>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value <?php echo ($statistics['basic']['avg_pnl'] ?? 0) >= 0 ? 'text-success' : 'text-danger'; ?>">
                    <?php echo formatNumber($statistics['basic']['avg_pnl'] ?? 0, 2); ?>
                </div>
                <div class="metric-label">平均盈亏</div>
                <small class="text-muted">每笔交易</small>
            </div>
        </div>
    </div>
</div>

<!-- 筛选和图表 -->
<div class="row mb-4">
    <!-- 筛选器 -->
    <div class="col-lg-3 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-filter me-2"></i>数据筛选
                </h5>
            </div>
            <div class="card-body">
                <form id="filterForm">
                    <div class="mb-3">
                        <label for="exchange" class="form-label">交易所</label>
                        <select class="form-select" id="exchange" name="exchange">
                            <option value="">全部</option>
                            <?php foreach ($supported_exchanges as $key => $exchange): ?>
                                <option value="<?php echo $key; ?>" 
                                    <?php echo ($filters['exchange'] ?? '') === $key ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($exchange['display_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="strategy" class="form-label">策略</label>
                        <select class="form-select" id="strategy" name="strategy">
                            <option value="">全部</option>
                            <?php foreach ($supported_strategies as $key => $strategy): ?>
                                <option value="<?php echo $key; ?>" 
                                    <?php echo ($filters['strategy'] ?? '') === $key ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($strategy['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="start_date" class="form-label">开始日期</label>
                        <input type="date" class="form-control" id="start_date" name="start_date" 
                               value="<?php echo $filters['start_date'] ?? ''; ?>">
                    </div>

                    <div class="mb-3">
                        <label for="end_date" class="form-label">结束日期</label>
                        <input type="date" class="form-control" id="end_date" name="end_date" 
                               value="<?php echo $filters['end_date'] ?? ''; ?>">
                    </div>

                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-primary" onclick="applyFilters()">
                            <i class="fas fa-search me-1"></i>应用筛选
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="resetFilters()">
                            <i class="fas fa-undo me-1"></i>重置
                        </button>
                    </div>
                </form>

                <hr>

                <h6 class="mb-3">数据导出</h6>
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-success btn-sm" onclick="exportData('csv')">
                        <i class="fas fa-file-csv me-1"></i>导出CSV
                    </button>
                    <button class="btn btn-outline-info btn-sm" onclick="exportData('excel')">
                        <i class="fas fa-file-excel me-1"></i>导出Excel
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 盈亏图表 -->
    <div class="col-lg-9 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-area me-2"></i>盈亏趋势分析
                </h5>
                <div class="btn-group btn-group-sm" role="group">
                    <input type="radio" class="btn-check" name="pnlPeriod" id="pnl7d" value="7d">
                    <label class="btn btn-outline-primary" for="pnl7d">7天</label>
                    
                    <input type="radio" class="btn-check" name="pnlPeriod" id="pnl30d" value="30d" checked>
                    <label class="btn btn-outline-primary" for="pnl30d">30天</label>
                    
                    <input type="radio" class="btn-check" name="pnlPeriod" id="pnl90d" value="90d">
                    <label class="btn btn-outline-primary" for="pnl90d">90天</label>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="pnlChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 策略性能和详细数据 -->
<div class="row mb-4">
    <!-- 策略性能 -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-trophy me-2"></i>策略性能排行
                </h5>
            </div>
            <div class="card-body">
                <div id="strategyPerformance">
                    <div class="text-center">
                        <i class="fas fa-spinner fa-spin fa-2x mb-2"></i>
                        <p>加载策略性能数据...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 交易所分布 -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>交易所分布
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="height: 300px;">
                    <canvas id="exchangeChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 交易记录表格 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-table me-2"></i>交易记录
                </h5>
                <div>
                    <span class="text-muted me-3">
                        共 <?php echo $trading_history['total'] ?? 0; ?> 条记录
                    </span>
                    <button class="btn btn-sm btn-outline-primary" onclick="refreshHistory()">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="historyTable">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>交易所</th>
                                <th>交易对</th>
                                <th>策略</th>
                                <th>方向</th>
                                <th>数量</th>
                                <th>价格</th>
                                <th>盈亏</th>
                                <th>手续费</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($trading_history['data'])): ?>
                                <?php foreach ($trading_history['data'] as $trade): ?>
                                <tr>
                                    <td>
                                        <small><?php echo date('m-d H:i', strtotime($trade['timestamp'])); ?></small>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            <?php echo strtoupper($trade['exchange']); ?>
                                        </span>
                                    </td>
                                    <td><?php echo htmlspecialchars($trade['symbol']); ?></td>
                                    <td>
                                        <span class="badge bg-secondary">
                                            <?php echo strtoupper($trade['strategy']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo $trade['side'] === 'buy' ? 'success' : 'danger'; ?>">
                                            <?php echo $trade['side'] === 'buy' ? '买入' : '卖出'; ?>
                                        </span>
                                    </td>
                                    <td><?php echo formatNumber($trade['amount'], 4); ?></td>
                                    <td><?php echo formatNumber($trade['price'], 2); ?></td>
                                    <td>
                                        <span class="<?php echo $trade['pnl'] >= 0 ? 'text-success' : 'text-danger'; ?>">
                                            <?php echo formatNumber($trade['pnl'], 2); ?>
                                        </span>
                                    </td>
                                    <td><?php echo formatNumber($trade['fee'], 4); ?></td>
                                    <td>
                                        <span class="badge bg-<?php 
                                            echo $trade['status'] === 'filled' ? 'success' : 
                                                ($trade['status'] === 'cancelled' ? 'secondary' : 'warning'); 
                                        ?>">
                                            <?php echo $trade['status']; ?>
                                        </span>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="10" class="text-center text-muted">
                                        <i class="fas fa-inbox fa-2x mb-2"></i>
                                        <p>暂无交易记录</p>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <?php if (($trading_history['total_pages'] ?? 0) > 1): ?>
                <nav aria-label="交易记录分页">
                    <ul class="pagination justify-content-center">
                        <?php for ($i = 1; $i <= $trading_history['total_pages']; $i++): ?>
                            <li class="page-item <?php echo $i === ($trading_history['page'] ?? 1) ? 'active' : ''; ?>">
                                <a class="page-link" href="#" onclick="loadPage(<?php echo $i; ?>)"><?php echo $i; ?></a>
                            </li>
                        <?php endfor; ?>
                    </ul>
                </nav>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
// 全局变量
let pnlChart = null;
let exchangeChart = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
    loadPnlAnalysis();
    loadStrategyPerformance();
    loadExchangeDistribution();
    
    // 绑定盈亏周期切换事件
    document.querySelectorAll('input[name="pnlPeriod"]').forEach(radio => {
        radio.addEventListener('change', function() {
            loadPnlAnalysis(this.value);
        });
    });
});

// 初始化图表
function initializeCharts() {
    // 盈亏趋势图表
    const pnlCtx = document.getElementById('pnlChart').getContext('2d');
    pnlChart = new Chart(pnlCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [
                {
                    label: '日盈亏',
                    data: [],
                    borderColor: '#2563eb',
                    backgroundColor: 'rgba(37, 99, 235, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                },
                {
                    label: '累计盈亏',
                    data: [],
                    borderColor: '#059669',
                    backgroundColor: 'transparent',
                    borderWidth: 2,
                    yAxisID: 'y1'
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                }
            },
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: '日盈亏 (USDT)'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: '累计盈亏 (USDT)'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            }
        }
    });

    // 交易所分布图表
    const exchangeCtx = document.getElementById('exchangeChart').getContext('2d');
    exchangeChart = new Chart(exchangeCtx, {
        type: 'doughnut',
        data: {
            labels: [],
            datasets: [{
                data: [],
                backgroundColor: [
                    '#2563eb',
                    '#059669',
                    '#dc2626',
                    '#d97706',
                    '#7c3aed'
                ],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// 加载盈亏分析
function loadPnlAnalysis(period = '30d') {
    fetch(`<?php echo Router::url('history/pnl-analysis'); ?>?period=${period}&group_by=day`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updatePnlChart(data.data);
            }
        })
        .catch(error => console.error('加载盈亏分析失败:', error));
}

// 更新盈亏图表
function updatePnlChart(data) {
    const labels = data.map(item => item.period);
    const dailyPnl = data.map(item => parseFloat(item.total_pnl));
    
    // 计算累计盈亏
    let cumulative = 0;
    const cumulativePnl = dailyPnl.map(pnl => {
        cumulative += pnl;
        return cumulative;
    });
    
    pnlChart.data.labels = labels;
    pnlChart.data.datasets[0].data = dailyPnl;
    pnlChart.data.datasets[1].data = cumulativePnl;
    pnlChart.update();
}

// 加载策略性能
function loadStrategyPerformance() {
    fetch('<?php echo Router::url('history/strategy-performance'); ?>?period=30d')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayStrategyPerformance(data.data);
            }
        })
        .catch(error => console.error('加载策略性能失败:', error));
}

// 显示策略性能
function displayStrategyPerformance(strategies) {
    const container = document.getElementById('strategyPerformance');
    
    if (!strategies || strategies.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted">
                <i class="fas fa-chart-bar fa-2x mb-2"></i>
                <p>暂无策略数据</p>
            </div>
        `;
        return;
    }
    
    let html = '';
    strategies.forEach((strategy, index) => {
        const winRate = parseFloat(strategy.win_rate || 0);
        const totalPnl = parseFloat(strategy.total_pnl || 0);
        
        html += `
            <div class="mb-3 p-3 border rounded">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h6 class="mb-0">${strategy.strategy.toUpperCase()}</h6>
                    <span class="badge bg-${index === 0 ? 'warning' : (index === 1 ? 'secondary' : 'info')}">
                        ${index === 0 ? '🥇' : (index === 1 ? '🥈' : '🥉')}
                    </span>
                </div>
                <div class="row">
                    <div class="col-6">
                        <small class="text-muted">总盈亏</small>
                        <div class="${totalPnl >= 0 ? 'text-success' : 'text-danger'} fw-bold">
                            ${formatNumber(totalPnl, 2)} USDT
                        </div>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">胜率</small>
                        <div class="text-info fw-bold">${formatNumber(winRate, 1)}%</div>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-6">
                        <small class="text-muted">交易次数: ${strategy.total_trades}</small>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">平均盈亏: ${formatNumber(strategy.avg_pnl, 2)}</small>
                    </div>
                </div>
            </div>
        `;
    });
    
    container.innerHTML = html;
}

// 加载交易所分布
function loadExchangeDistribution() {
    const exchangeStats = <?php echo json_encode($statistics['by_exchange'] ?? []); ?>;
    
    if (exchangeStats && exchangeStats.length > 0) {
        const labels = exchangeStats.map(stat => stat.exchange.toUpperCase());
        const data = exchangeStats.map(stat => parseInt(stat.trade_count));
        
        exchangeChart.data.labels = labels;
        exchangeChart.data.datasets[0].data = data;
        exchangeChart.update();
    }
}

// 应用筛选
function applyFilters() {
    const form = document.getElementById('filterForm');
    const formData = new FormData(form);
    const params = new URLSearchParams(formData);
    
    window.location.href = '<?php echo Router::url('history'); ?>?' + params.toString();
}

// 重置筛选
function resetFilters() {
    window.location.href = '<?php echo Router::url('history'); ?>';
}

// 导出数据
function exportData(format) {
    const form = document.getElementById('filterForm');
    const formData = new FormData(form);
    formData.append('format', format);
    
    const params = new URLSearchParams(formData);
    window.open('<?php echo Router::url('history/export'); ?>?' + params.toString(), '_blank');
}

// 刷新历史记录
function refreshHistory() {
    location.reload();
}

// 加载指定页面
function loadPage(page) {
    const url = new URL(window.location);
    url.searchParams.set('page', page);
    window.location.href = url.toString();
}

// 工具函数
function formatNumber(num, decimals = 2) {
    return parseFloat(num).toFixed(decimals);
}
</script>
