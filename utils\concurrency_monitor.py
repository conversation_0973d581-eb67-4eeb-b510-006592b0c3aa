#!/usr/bin/env python3
"""
并发架构监控工具
监控和分析交易系统中的并发问题
"""

import asyncio
import time
import threading
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class TaskType(Enum):
    """任务类型"""
    OPENING_CHECK = "opening_check"
    PRICE_FETCH = "price_fetch"
    ORDER_EXECUTION = "order_execution"
    POSITION_CHECK = "position_check"

@dataclass
class TaskInfo:
    """任务信息"""
    task_id: str
    task_type: TaskType
    start_time: float
    end_time: Optional[float] = None
    status: str = "running"
    thread_id: Optional[int] = None
    coroutine_name: Optional[str] = None
    error: Optional[str] = None

@dataclass
class ConcurrencyStats:
    """并发统计"""
    total_tasks: int = 0
    concurrent_tasks: int = 0
    max_concurrent: int = 0
    avg_duration: float = 0.0
    error_rate: float = 0.0
    race_conditions: int = 0
    lock_contentions: int = 0

class ConcurrencyMonitor:
    """并发监控器"""
    
    def __init__(self):
        self.tasks: Dict[str, TaskInfo] = {}
        self.completed_tasks: List[TaskInfo] = []
        self.stats = ConcurrencyStats()
        self._lock = threading.Lock()
        self._async_lock = None
        self._monitoring = False
        
    async def start_monitoring(self):
        """启动监控"""
        self._monitoring = True
        self._async_lock = asyncio.Lock()
        logger.info("并发监控器已启动")
        
    def stop_monitoring(self):
        """停止监控"""
        self._monitoring = False
        logger.info("并发监控器已停止")
        
    def start_task(self, task_type: TaskType, task_id: Optional[str] = None) -> str:
        """开始任务监控"""
        if not self._monitoring:
            return ""
            
        if task_id is None:
            task_id = f"{task_type.value}_{int(time.time() * 1000000)}"
            
        task_info = TaskInfo(
            task_id=task_id,
            task_type=task_type,
            start_time=time.time(),
            thread_id=threading.get_ident(),
            coroutine_name=self._get_current_coroutine_name()
        )
        
        with self._lock:
            self.tasks[task_id] = task_info
            self.stats.total_tasks += 1
            self.stats.concurrent_tasks += 1
            
            if self.stats.concurrent_tasks > self.stats.max_concurrent:
                self.stats.max_concurrent = self.stats.concurrent_tasks
                
            # 检测竞态条件
            same_type_tasks = [t for t in self.tasks.values() if t.task_type == task_type]
            if len(same_type_tasks) > 1:
                self.stats.race_conditions += 1
                logger.warning(f"检测到竞态条件: {task_type.value} 有 {len(same_type_tasks)} 个并发任务")
                
        return task_id
        
    def end_task(self, task_id: str, error: Optional[str] = None):
        """结束任务监控"""
        if not self._monitoring or task_id not in self.tasks:
            return
            
        with self._lock:
            task_info = self.tasks.pop(task_id)
            task_info.end_time = time.time()
            task_info.status = "error" if error else "completed"
            task_info.error = error
            
            self.completed_tasks.append(task_info)
            self.stats.concurrent_tasks -= 1
            
            # 更新统计
            self._update_stats()
            
    def _update_stats(self):
        """更新统计信息"""
        if not self.completed_tasks:
            return
            
        # 计算平均持续时间
        durations = [
            t.end_time - t.start_time 
            for t in self.completed_tasks 
            if t.end_time is not None
        ]
        if durations:
            self.stats.avg_duration = sum(durations) / len(durations)
            
        # 计算错误率
        error_count = sum(1 for t in self.completed_tasks if t.error is not None)
        self.stats.error_rate = error_count / len(self.completed_tasks) if self.completed_tasks else 0
        
    def _get_current_coroutine_name(self) -> Optional[str]:
        """获取当前协程名称"""
        try:
            import inspect
            frame = inspect.currentframe()
            while frame:
                if 'self' in frame.f_locals and hasattr(frame.f_locals['self'], '__class__'):
                    return f"{frame.f_locals['self'].__class__.__name__}.{frame.f_code.co_name}"
                frame = frame.f_back
            return None
        except:
            return None
            
    def get_current_status(self) -> Dict[str, Any]:
        """获取当前状态"""
        with self._lock:
            return {
                "running_tasks": len(self.tasks),
                "completed_tasks": len(self.completed_tasks),
                "stats": {
                    "total_tasks": self.stats.total_tasks,
                    "concurrent_tasks": self.stats.concurrent_tasks,
                    "max_concurrent": self.stats.max_concurrent,
                    "avg_duration": round(self.stats.avg_duration, 3),
                    "error_rate": round(self.stats.error_rate * 100, 2),
                    "race_conditions": self.stats.race_conditions,
                    "lock_contentions": self.stats.lock_contentions
                },
                "running_task_types": {
                    task_type.value: len([t for t in self.tasks.values() if t.task_type == task_type])
                    for task_type in TaskType
                }
            }
            
    def get_race_condition_report(self) -> List[Dict[str, Any]]:
        """获取竞态条件报告"""
        report = []
        
        # 按任务类型分组
        task_groups = {}
        for task in self.tasks.values():
            if task.task_type not in task_groups:
                task_groups[task.task_type] = []
            task_groups[task.task_type].append(task)
            
        # 检查每个组的并发情况
        for task_type, tasks in task_groups.items():
            if len(tasks) > 1:
                report.append({
                    "task_type": task_type.value,
                    "concurrent_count": len(tasks),
                    "task_ids": [t.task_id for t in tasks],
                    "start_times": [t.start_time for t in tasks],
                    "thread_ids": [t.thread_id for t in tasks],
                    "coroutine_names": [t.coroutine_name for t in tasks]
                })
                
        return report
        
    def generate_performance_report(self) -> str:
        """生成性能报告"""
        status = self.get_current_status()
        race_report = self.get_race_condition_report()
        
        report = []
        report.append("=" * 60)
        report.append("📊 并发架构性能报告")
        report.append("=" * 60)
        
        # 基本统计
        report.append(f"📈 任务统计:")
        report.append(f"   总任务数: {status['stats']['total_tasks']}")
        report.append(f"   当前并发: {status['stats']['concurrent_tasks']}")
        report.append(f"   最大并发: {status['stats']['max_concurrent']}")
        report.append(f"   平均耗时: {status['stats']['avg_duration']}秒")
        report.append(f"   错误率: {status['stats']['error_rate']}%")
        
        # 竞态条件
        report.append(f"\n🚨 并发问题:")
        report.append(f"   竞态条件: {status['stats']['race_conditions']}次")
        report.append(f"   锁争用: {status['stats']['lock_contentions']}次")
        
        # 当前运行任务
        report.append(f"\n🔄 当前运行任务:")
        for task_type, count in status['running_task_types'].items():
            if count > 0:
                report.append(f"   {task_type}: {count}个")
                
        # 竞态条件详情
        if race_report:
            report.append(f"\n⚠️ 竞态条件详情:")
            for race in race_report:
                report.append(f"   {race['task_type']}: {race['concurrent_count']}个并发任务")
                report.append(f"      任务ID: {race['task_ids']}")
                
        # 建议
        report.append(f"\n💡 优化建议:")
        if status['stats']['race_conditions'] > 0:
            report.append("   - 检查异步锁机制是否正确实现")
            report.append("   - 考虑使用队列机制串行化关键操作")
            
        if status['stats']['max_concurrent'] > 5:
            report.append("   - 考虑限制最大并发数量")
            report.append("   - 实施任务优先级机制")
            
        if status['stats']['error_rate'] > 10:
            report.append("   - 检查错误处理机制")
            report.append("   - 增强异常恢复能力")
            
        return "\n".join(report)

# 全局监控器实例
concurrency_monitor = ConcurrencyMonitor()

def monitor_task(task_type: TaskType):
    """任务监控装饰器"""
    def decorator(func):
        if asyncio.iscoroutinefunction(func):
            async def async_wrapper(*args, **kwargs):
                task_id = concurrency_monitor.start_task(task_type)
                try:
                    result = await func(*args, **kwargs)
                    concurrency_monitor.end_task(task_id)
                    return result
                except Exception as e:
                    concurrency_monitor.end_task(task_id, str(e))
                    raise
            return async_wrapper
        else:
            def sync_wrapper(*args, **kwargs):
                task_id = concurrency_monitor.start_task(task_type)
                try:
                    result = func(*args, **kwargs)
                    concurrency_monitor.end_task(task_id)
                    return result
                except Exception as e:
                    concurrency_monitor.end_task(task_id, str(e))
                    raise
            return sync_wrapper
    return decorator
