<div class="row mb-4">
    <div class="col-12">
        <h1 class="h2 mb-3">
            <i class="fas fa-play-circle text-primary me-2"></i>
            交易控制中心
        </h1>
        <p class="text-muted">启动、停止和监控自动交易系统</p>
    </div>
</div>

<!-- 交易状态概览 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card trading-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-robot me-2"></i>交易系统状态
                </h5>
                <button class="btn btn-sm btn-outline-primary" onclick="refreshStatus()">
                    <i class="fas fa-sync-alt"></i> 刷新状态
                </button>
            </div>
            <div class="card-body">
                <div id="tradingStatusDisplay">
                    <div class="text-center">
                        <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
                        <p>正在获取交易状态...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 交易控制面板 -->
<div class="row mb-4">
    <!-- 快速控制 -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-gamepad me-2"></i>快速控制
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-success btn-lg" id="startTradingBtn" onclick="startTrading()">
                        <i class="fas fa-play me-2"></i>启动交易
                    </button>
                    <button class="btn btn-warning btn-lg" id="stopTradingBtn" onclick="stopTrading()" disabled>
                        <i class="fas fa-stop me-2"></i>停止交易
                    </button>
                    <button class="btn btn-danger" id="emergencyStopBtn" onclick="emergencyStop()" disabled>
                        <i class="fas fa-exclamation-triangle me-2"></i>紧急停止
                    </button>
                </div>
                
                <hr>
                
                <h6 class="mb-3">持仓操作</h6>
                <div class="mb-3">
                    <select class="form-select form-select-sm" id="positionSymbol">
                        <option value="">选择交易对</option>
                    </select>
                </div>
                <button class="btn btn-outline-danger btn-sm w-100" onclick="closePosition()">
                    <i class="fas fa-times me-1"></i>手动平仓
                </button>
            </div>
        </div>
    </div>

    <!-- 交易配置 -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cog me-2"></i>交易配置
                </h5>
            </div>
            <div class="card-body">
                <form id="tradingConfigForm">
                    <input type="hidden" name="_token" value="<?php echo htmlspecialchars($csrf_token); ?>">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <!-- 交易所 -->
                            <div class="mb-3">
                                <label for="exchange" class="form-label">交易所</label>
                                <select class="form-select" id="exchange" name="exchange">
                                    <?php foreach ($supported_exchanges as $key => $exchange): ?>
                                        <option value="<?php echo $key; ?>" 
                                            <?php echo ($user_config['exchange'] ?? '') === $key ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($exchange['display_name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <!-- 交易对 -->
                            <div class="mb-3">
                                <label for="symbol" class="form-label">交易对</label>
                                <select class="form-select" id="symbol" name="symbol">
                                    <!-- 动态加载 -->
                                </select>
                            </div>

                            <!-- 杠杆 -->
                            <div class="mb-3">
                                <label for="leverage" class="form-label">杠杆倍数</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="leverage" name="leverage" 
                                           min="1" max="100" value="<?php echo $user_config['leverage'] ?? 10; ?>">
                                    <span class="input-group-text">x</span>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <!-- 初始保证金 -->
                            <div class="mb-3">
                                <label for="initial_margin" class="form-label">初始保证金</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="initial_margin" name="initial_margin" 
                                           min="10" step="0.01" value="<?php echo $user_config['initial_margin'] ?? 100; ?>">
                                    <span class="input-group-text">USDT</span>
                                </div>
                            </div>

                            <!-- 时间周期 -->
                            <div class="mb-3">
                                <label for="timeframe" class="form-label">时间周期</label>
                                <select class="form-select" id="timeframe" name="timeframe">
                                    <option value="1m" <?php echo ($user_config['timeframe'] ?? '') === '1m' ? 'selected' : ''; ?>>1分钟</option>
                                    <option value="5m" <?php echo ($user_config['timeframe'] ?? '') === '5m' ? 'selected' : ''; ?>>5分钟</option>
                                    <option value="15m" <?php echo ($user_config['timeframe'] ?? '') === '15m' ? 'selected' : ''; ?>>15分钟</option>
                                    <option value="30m" <?php echo ($user_config['timeframe'] ?? '30m') === '30m' ? 'selected' : ''; ?>>30分钟</option>
                                    <option value="1h" <?php echo ($user_config['timeframe'] ?? '') === '1h' ? 'selected' : ''; ?>>1小时</option>
                                    <option value="4h" <?php echo ($user_config['timeframe'] ?? '') === '4h' ? 'selected' : ''; ?>>4小时</option>
                                </select>
                            </div>

                            <!-- 策略选择 -->
                            <div class="mb-3">
                                <label for="strategy" class="form-label">交易策略</label>
                                <select class="form-select" id="strategy" name="strategy">
                                    <?php foreach ($supported_strategies as $key => $strategy): ?>
                                        <option value="<?php echo $key; ?>" 
                                            <?php echo ($user_config['strategy'] ?? 'macd') === $key ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($strategy['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- 策略参数 -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="mb-3">MACD策略参数</h6>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="macd_fast" class="form-label">快线周期</label>
                                <input type="number" class="form-control" id="macd_fast" name="macd_fast" 
                                       min="5" max="50" value="<?php echo $user_config['macd_fast'] ?? 12; ?>">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="macd_slow" class="form-label">慢线周期</label>
                                <input type="number" class="form-control" id="macd_slow" name="macd_slow" 
                                       min="10" max="100" value="<?php echo $user_config['macd_slow'] ?? 26; ?>">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="macd_signal" class="form-label">信号线周期</label>
                                <input type="number" class="form-control" id="macd_signal" name="macd_signal" 
                                       min="3" max="30" value="<?php echo $user_config['macd_signal'] ?? 9; ?>">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="min_signal_strength" class="form-label">最小信号强度</label>
                                <input type="number" class="form-control" id="min_signal_strength" name="min_signal_strength" 
                                       min="0.1" max="1.0" step="0.1" value="<?php echo $user_config['min_signal_strength'] ?? 0.3; ?>">
                            </div>
                        </div>
                    </div>

                    <!-- 风险管理 -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="mb-3">风险管理</h6>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="max_add_times" class="form-label">最大加仓次数</label>
                                <input type="number" class="form-control" id="max_add_times" name="max_add_times" 
                                       min="1" max="10" value="<?php echo $user_config['max_add_times'] ?? 3; ?>">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="alert_points" class="form-label">预警点数</label>
                                <input type="number" class="form-control" id="alert_points" name="alert_points" 
                                       min="0.1" max="10.0" step="0.1" value="<?php echo $user_config['alert_points'] ?? 1.5; ?>">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="risk_control_enabled" name="risk_control_enabled" 
                                           <?php echo isset($user_config['risk_control_enabled']) && $user_config['risk_control_enabled'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="risk_control_enabled">
                                        启用风险控制
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-end">
                        <button type="button" class="btn btn-outline-primary me-2" onclick="updateConfig()">
                            <i class="fas fa-save me-1"></i>保存配置
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="resetConfig()">
                            <i class="fas fa-undo me-1"></i>重置
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 活跃交易会话 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>活跃交易会话
                </h5>
                <button class="btn btn-sm btn-outline-primary" onclick="refreshSessions()">
                    <i class="fas fa-sync-alt"></i> 刷新
                </button>
            </div>
            <div class="card-body">
                <div id="activeSessionsDisplay">
                    <div class="text-center text-muted">
                        <i class="fas fa-spinner fa-spin fa-2x mb-2"></i>
                        <p>加载活跃会话...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 全局变量
let currentTradingStatus = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
    updateSymbols();
    refreshStatus();
    refreshSessions();
    
    // 绑定交易所变化事件
    document.getElementById('exchange').addEventListener('change', updateSymbols);
});

// 初始化页面
function initializePage() {
    // 从PHP传递的数据初始化
    const tradingStatus = <?php echo json_encode($trading_status); ?>;
    if (tradingStatus) {
        updateTradingStatusDisplay(tradingStatus);
    }
}

// 更新交易对列表
function updateSymbols() {
    const exchange = document.getElementById('exchange').value;
    const symbolSelect = document.getElementById('symbol');
    const positionSymbolSelect = document.getElementById('positionSymbol');
    
    // 清空现有选项
    symbolSelect.innerHTML = '';
    positionSymbolSelect.innerHTML = '<option value="">选择交易对</option>';
    
    // 获取支持的交易对
    const supportedExchanges = <?php echo json_encode($supported_exchanges); ?>;
    const symbols = supportedExchanges[exchange]?.supported_symbols || {};
    
    // 添加选项
    Object.entries(symbols).forEach(([value, label]) => {
        // 交易配置选择
        const option1 = document.createElement('option');
        option1.value = value;
        option1.textContent = label;
        symbolSelect.appendChild(option1);
        
        // 持仓操作选择
        const option2 = document.createElement('option');
        option2.value = value;
        option2.textContent = label;
        positionSymbolSelect.appendChild(option2);
    });
    
    // 设置默认选中
    const currentSymbol = '<?php echo $user_config['symbol'] ?? ''; ?>';
    if (currentSymbol) {
        symbolSelect.value = currentSymbol;
    }
}

// 启动交易
function startTrading() {
    const btn = document.getElementById('startTradingBtn');
    const originalHtml = btn.innerHTML;
    btn.innerHTML = '<span class="loading-spinner"></span> 启动中...';
    btn.disabled = true;
    
    const formData = new FormData(document.getElementById('tradingConfigForm'));
    
    fetch('<?php echo Router::url('trading/start'); ?>', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', '交易启动成功！');
            refreshStatus();
            refreshSessions();
        } else {
            showAlert('danger', '启动失败: ' + data.message);
        }
    })
    .catch(error => {
        showAlert('danger', '启动失败: 网络错误');
    })
    .finally(() => {
        btn.innerHTML = originalHtml;
        btn.disabled = false;
    });
}

// 停止交易
function stopTrading() {
    if (!confirm('确定要停止交易吗？')) return;
    
    const btn = document.getElementById('stopTradingBtn');
    const originalHtml = btn.innerHTML;
    btn.innerHTML = '<span class="loading-spinner"></span> 停止中...';
    btn.disabled = true;
    
    const formData = new FormData();
    formData.append('_token', '<?php echo $csrf_token; ?>');
    
    fetch('<?php echo Router::url('trading/stop'); ?>', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', '交易已停止');
            refreshStatus();
            refreshSessions();
        } else {
            showAlert('danger', '停止失败: ' + data.message);
        }
    })
    .catch(error => {
        showAlert('danger', '停止失败: 网络错误');
    })
    .finally(() => {
        btn.innerHTML = originalHtml;
        btn.disabled = false;
    });
}

// 紧急停止
function emergencyStop() {
    if (!confirm('确定要执行紧急停止吗？这将立即停止所有交易并平仓！')) return;
    
    const btn = document.getElementById('emergencyStopBtn');
    const originalHtml = btn.innerHTML;
    btn.innerHTML = '<span class="loading-spinner"></span> 紧急停止中...';
    btn.disabled = true;
    
    const formData = new FormData();
    formData.append('_token', '<?php echo $csrf_token; ?>');
    
    fetch('<?php echo Router::url('trading/emergency-stop'); ?>', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('warning', '紧急停止执行成功');
            refreshStatus();
            refreshSessions();
        } else {
            showAlert('danger', '紧急停止失败: ' + data.message);
        }
    })
    .catch(error => {
        showAlert('danger', '紧急停止失败: 网络错误');
    })
    .finally(() => {
        btn.innerHTML = originalHtml;
        btn.disabled = false;
    });
}

// 手动平仓
function closePosition() {
    const symbol = document.getElementById('positionSymbol').value;
    if (!symbol) {
        showAlert('warning', '请选择要平仓的交易对');
        return;
    }
    
    if (!confirm(`确定要平仓 ${symbol} 吗？`)) return;
    
    const formData = new FormData();
    formData.append('_token', '<?php echo $csrf_token; ?>');
    formData.append('symbol', symbol);
    
    fetch('<?php echo Router::url('trading/close-position'); ?>', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', '平仓操作成功');
            refreshStatus();
        } else {
            showAlert('danger', '平仓失败: ' + data.message);
        }
    })
    .catch(error => {
        showAlert('danger', '平仓失败: 网络错误');
    });
}

// 刷新交易状态
function refreshStatus() {
    fetch('<?php echo Router::url('trading/status'); ?>')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                currentTradingStatus = data.data;
                updateTradingStatusDisplay(data.data);
                updateControlButtons(data.data);
            }
        })
        .catch(error => {
            console.error('刷新状态失败:', error);
        });
}

// 更新交易状态显示
function updateTradingStatusDisplay(status) {
    const container = document.getElementById('tradingStatusDisplay');
    
    if (!status) {
        container.innerHTML = `
            <div class="text-center text-muted">
                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                <p>无法获取交易状态</p>
            </div>
        `;
        return;
    }
    
    const isRunning = status.is_running || false;
    const tradingState = status.trading_state || 'idle';
    
    container.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6>系统状态</h6>
                <p class="mb-2">
                    <span class="badge bg-${isRunning ? 'success' : 'secondary'}">
                        ${isRunning ? '运行中' : '已停止'}
                    </span>
                </p>
                <p class="mb-2"><strong>交易状态:</strong> <span class="text-primary">${tradingState}</span></p>
                <p class="mb-0"><strong>最后更新:</strong> ${new Date().toLocaleString()}</p>
            </div>
            <div class="col-md-6">
                ${status.current_session ? `
                    <h6>当前会话</h6>
                    <p class="mb-1"><strong>交易对:</strong> ${status.current_session.symbol}</p>
                    <p class="mb-1"><strong>杠杆:</strong> ${status.current_session.leverage}x</p>
                    <p class="mb-1"><strong>保证金:</strong> ${formatNumber(status.current_session.initial_margin)} USDT</p>
                    <p class="mb-0"><strong>盈亏:</strong> 
                        <span class="${status.current_session.total_pnl >= 0 ? 'text-success' : 'text-danger'}">
                            ${formatNumber(status.current_session.total_pnl)} USDT
                        </span>
                    </p>
                ` : `
                    <div class="text-center text-muted">
                        <i class="fas fa-pause-circle fa-2x mb-2"></i>
                        <p>当前没有活跃的交易会话</p>
                    </div>
                `}
            </div>
        </div>
    `;
}

// 更新控制按钮状态
function updateControlButtons(status) {
    const startBtn = document.getElementById('startTradingBtn');
    const stopBtn = document.getElementById('stopTradingBtn');
    const emergencyBtn = document.getElementById('emergencyStopBtn');
    
    const isRunning = status && status.is_running;
    
    startBtn.disabled = isRunning;
    stopBtn.disabled = !isRunning;
    emergencyBtn.disabled = !isRunning;
}

// 刷新活跃会话
function refreshSessions() {
    fetch('<?php echo Router::url('trading/active-sessions'); ?>')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayActiveSessions(data.data);
            }
        })
        .catch(error => {
            console.error('刷新会话失败:', error);
        });
}

// 显示活跃会话
function displayActiveSessions(sessions) {
    const container = document.getElementById('activeSessionsDisplay');
    
    if (!sessions || sessions.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted">
                <i class="fas fa-inbox fa-2x mb-2"></i>
                <p>当前没有活跃的交易会话</p>
            </div>
        `;
        return;
    }
    
    let html = '<div class="table-responsive"><table class="table table-hover"><thead><tr>';
    html += '<th>会话ID</th><th>交易所</th><th>交易对</th><th>策略</th><th>保证金</th><th>杠杆</th><th>开始时间</th><th>操作</th>';
    html += '</tr></thead><tbody>';
    
    sessions.forEach(session => {
        html += `
            <tr>
                <td><code class="small">${session.session_id.substr(-8)}</code></td>
                <td><span class="badge bg-info">${session.exchange.toUpperCase()}</span></td>
                <td>${session.symbol}</td>
                <td><span class="badge bg-secondary">${session.strategy.toUpperCase()}</span></td>
                <td>${formatNumber(session.initial_margin)} USDT</td>
                <td>${session.leverage}x</td>
                <td><small>${new Date(session.start_time).toLocaleString()}</small></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewSessionDetails('${session.session_id}')">
                        <i class="fas fa-eye"></i>
                    </button>
                </td>
            </tr>
        `;
    });
    
    html += '</tbody></table></div>';
    container.innerHTML = html;
}

// 更新配置
function updateConfig() {
    const formData = new FormData(document.getElementById('tradingConfigForm'));
    
    fetch('<?php echo Router::url('trading/update-config'); ?>', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', '配置更新成功');
        } else {
            showAlert('danger', '配置更新失败: ' + data.message);
        }
    })
    .catch(error => {
        showAlert('danger', '配置更新失败: 网络错误');
    });
}

// 重置配置
function resetConfig() {
    if (confirm('确定要重置配置吗？')) {
        document.getElementById('tradingConfigForm').reset();
        showAlert('info', '配置已重置');
    }
}

// 查看会话详情
function viewSessionDetails(sessionId) {
    // 这里可以打开模态框显示详细信息
    window.open(`<?php echo Router::url('history'); ?>?session=${sessionId}`, '_blank');
}

// 工具函数
function formatNumber(num, decimals = 2) {
    return parseFloat(num).toFixed(decimals);
}

// 定期刷新状态
setInterval(refreshStatus, 10000); // 每10秒刷新一次
</script>
