<template>
  <div class="config-page">
    <div class="page-header">
      <h2>配置设置</h2>
      <p>配置交易所连接、基础参数和系统设置</p>
    </div>

    <el-tabs v-model="activeTab" class="config-tabs">
      <!-- 交易所配置 -->
      <el-tab-pane label="交易所配置" name="exchange">
        <div class="form-container">
          <el-form :model="exchangeConfig" :rules="exchangeRules" ref="exchangeFormRef" label-width="120px">
            <div class="form-section">
              <h3 class="section-title">交易所选择</h3>
              
              <el-form-item label="交易所" prop="exchange">
                <el-select v-model="exchangeConfig.exchange" placeholder="请选择交易所" style="width: 200px">
                  <el-option label="OKX" value="okx" />
                  <el-option label="Gate.io" value="gateio" />
                </el-select>
              </el-form-item>
              
              <el-form-item label="环境模式" prop="sandbox">
                <el-radio-group v-model="exchangeConfig.sandbox">
                  <el-radio :label="false">生产环境</el-radio>
                  <el-radio :label="true">沙盒环境</el-radio>
                </el-radio-group>
              </el-form-item>
            </div>

            <div class="form-section">
              <h3 class="section-title">API配置</h3>
              
              <el-form-item label="API Key" prop="api_key">
                <el-input 
                  v-model="exchangeConfig.api_key" 
                  placeholder="请输入API Key"
                  show-password
                  style="width: 400px"
                />
              </el-form-item>
              
              <el-form-item label="API Secret" prop="api_secret">
                <el-input 
                  v-model="exchangeConfig.api_secret" 
                  placeholder="请输入API Secret"
                  show-password
                  style="width: 400px"
                />
              </el-form-item>
              
              <el-form-item label="Passphrase" prop="passphrase" v-if="exchangeConfig.exchange === 'okx'">
                <el-input 
                  v-model="exchangeConfig.passphrase" 
                  placeholder="请输入Passphrase"
                  show-password
                  style="width: 400px"
                />
              </el-form-item>
            </div>

            <div class="form-section">
              <h3 class="section-title">连接测试</h3>
              
              <el-form-item>
                <el-button 
                  type="primary" 
                  @click="testConnection"
                  :loading="testing"
                >
                  测试连接
                </el-button>
                <el-button @click="saveExchangeConfig" :loading="saving">
                  保存配置
                </el-button>
              </el-form-item>
              
              <el-form-item v-if="connectionResult">
                <el-alert 
                  :title="connectionResult.success ? '连接成功' : '连接失败'"
                  :type="connectionResult.success ? 'success' : 'error'"
                  :description="connectionResult.message"
                  show-icon
                />
              </el-form-item>
            </div>
          </el-form>
        </div>
      </el-tab-pane>

      <!-- 基础配置 -->
      <el-tab-pane label="基础配置" name="basic">
        <div class="form-container">
          <el-form :model="basicConfig" :rules="basicRules" ref="basicFormRef" label-width="120px">
            <div class="form-section">
              <h3 class="section-title">交易参数</h3>
              
              <el-form-item label="默认交易对" prop="default_symbol">
                <el-select v-model="basicConfig.default_symbol" placeholder="请选择交易对" style="width: 200px">
                  <el-option label="BTC/USDT" value="BTC-USDT" />
                  <el-option label="ETH/USDT" value="ETH-USDT" />
                  <el-option label="BNB/USDT" value="BNB-USDT" />
                  <el-option label="SOL/USDT" value="SOL-USDT" />
                </el-select>
              </el-form-item>
              
              <el-form-item label="默认杠杆" prop="default_leverage">
                <el-input-number 
                  v-model="basicConfig.default_leverage" 
                  :min="1" 
                  :max="100" 
                  style="width: 200px"
                />
              </el-form-item>
              
              <el-form-item label="时间周期" prop="timeframe">
                <el-select v-model="basicConfig.timeframe" placeholder="请选择时间周期" style="width: 200px">
                  <el-option label="1分钟" value="1m" />
                  <el-option label="5分钟" value="5m" />
                  <el-option label="15分钟" value="15m" />
                  <el-option label="30分钟" value="30m" />
                  <el-option label="1小时" value="1h" />
                  <el-option label="4小时" value="4h" />
                </el-select>
              </el-form-item>
            </div>

            <div class="form-section">
              <h3 class="section-title">风险控制</h3>
              
              <el-form-item label="最大持仓数" prop="max_positions">
                <el-input-number 
                  v-model="basicConfig.max_positions" 
                  :min="1" 
                  :max="10" 
                  style="width: 200px"
                />
              </el-form-item>
              
              <el-form-item label="止损比例" prop="stop_loss_percent">
                <el-input-number 
                  v-model="basicConfig.stop_loss_percent" 
                  :min="0.1" 
                  :max="50" 
                  :step="0.1"
                  :precision="1"
                  style="width: 200px"
                />
                <span style="margin-left: 8px; color: #909399;">%</span>
              </el-form-item>
              
              <el-form-item label="止盈比例" prop="take_profit_percent">
                <el-input-number 
                  v-model="basicConfig.take_profit_percent" 
                  :min="0.1" 
                  :max="100" 
                  :step="0.1"
                  :precision="1"
                  style="width: 200px"
                />
                <span style="margin-left: 8px; color: #909399;">%</span>
              </el-form-item>
            </div>

            <div class="form-section">
              <h3 class="section-title">系统设置</h3>
              
              <el-form-item label="日志级别" prop="log_level">
                <el-select v-model="basicConfig.log_level" placeholder="请选择日志级别" style="width: 200px">
                  <el-option label="DEBUG" value="DEBUG" />
                  <el-option label="INFO" value="INFO" />
                  <el-option label="WARNING" value="WARNING" />
                  <el-option label="ERROR" value="ERROR" />
                </el-select>
              </el-form-item>
              
              <el-form-item label="自动重启" prop="auto_restart">
                <el-switch v-model="basicConfig.auto_restart" />
              </el-form-item>
              
              <el-form-item label="邮件通知" prop="email_notifications">
                <el-switch v-model="basicConfig.email_notifications" />
              </el-form-item>
            </div>

            <div class="form-section">
              <el-form-item>
                <el-button type="primary" @click="saveBasicConfig" :loading="saving">
                  保存配置
                </el-button>
                <el-button @click="resetBasicConfig">
                  重置
                </el-button>
              </el-form-item>
            </div>
          </el-form>
        </div>
      </el-tab-pane>

      <!-- 高级配置 -->
      <el-tab-pane label="高级配置" name="advanced">
        <div class="form-container">
          <el-form :model="advancedConfig" ref="advancedFormRef" label-width="120px">
            <div class="form-section">
              <h3 class="section-title">性能优化</h3>
              
              <el-form-item label="数据缓存" prop="enable_cache">
                <el-switch v-model="advancedConfig.enable_cache" />
                <span style="margin-left: 8px; color: #909399;">启用数据缓存以提高性能</span>
              </el-form-item>
              
              <el-form-item label="并发请求数" prop="max_concurrent_requests">
                <el-input-number 
                  v-model="advancedConfig.max_concurrent_requests" 
                  :min="1" 
                  :max="20" 
                  style="width: 200px"
                />
              </el-form-item>
              
              <el-form-item label="请求间隔" prop="request_interval">
                <el-input-number 
                  v-model="advancedConfig.request_interval" 
                  :min="100" 
                  :max="5000" 
                  :step="100"
                  style="width: 200px"
                />
                <span style="margin-left: 8px; color: #909399;">毫秒</span>
              </el-form-item>
            </div>

            <div class="form-section">
              <h3 class="section-title">数据库配置</h3>
              
              <el-form-item label="数据保留天数" prop="data_retention_days">
                <el-input-number 
                  v-model="advancedConfig.data_retention_days" 
                  :min="1" 
                  :max="365" 
                  style="width: 200px"
                />
              </el-form-item>
              
              <el-form-item label="自动清理" prop="auto_cleanup">
                <el-switch v-model="advancedConfig.auto_cleanup" />
                <span style="margin-left: 8px; color: #909399;">自动清理过期数据</span>
              </el-form-item>
            </div>

            <div class="form-section">
              <h3 class="section-title">安全设置</h3>
              
              <el-form-item label="API限流" prop="enable_rate_limit">
                <el-switch v-model="advancedConfig.enable_rate_limit" />
              </el-form-item>
              
              <el-form-item label="IP白名单" prop="ip_whitelist">
                <el-input 
                  v-model="advancedConfig.ip_whitelist" 
                  type="textarea" 
                  :rows="3"
                  placeholder="每行一个IP地址，留空表示不限制"
                  style="width: 400px"
                />
              </el-form-item>
            </div>

            <div class="form-section">
              <el-form-item>
                <el-button type="primary" @click="saveAdvancedConfig" :loading="saving">
                  保存配置
                </el-button>
                <el-button @click="resetAdvancedConfig">
                  重置
                </el-button>
              </el-form-item>
            </div>
          </el-form>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import api from '@/utils/api'

// 响应式数据
const activeTab = ref('exchange')
const testing = ref(false)
const saving = ref(false)
const connectionResult = ref(null)

// 表单引用
const exchangeFormRef = ref()
const basicFormRef = ref()
const advancedFormRef = ref()

// 交易所配置
const exchangeConfig = reactive({
  exchange: 'okx',
  sandbox: true,
  api_key: '',
  api_secret: '',
  passphrase: ''
})

// 基础配置
const basicConfig = reactive({
  default_symbol: 'BTC-USDT',
  default_leverage: 10,
  timeframe: '30m',
  max_positions: 3,
  stop_loss_percent: 5.0,
  take_profit_percent: 10.0,
  log_level: 'INFO',
  auto_restart: true,
  email_notifications: false
})

// 高级配置
const advancedConfig = reactive({
  enable_cache: true,
  max_concurrent_requests: 5,
  request_interval: 1000,
  data_retention_days: 30,
  auto_cleanup: true,
  enable_rate_limit: true,
  ip_whitelist: ''
})

// 验证规则
const exchangeRules = {
  exchange: [{ required: true, message: '请选择交易所', trigger: 'change' }],
  api_key: [{ required: true, message: '请输入API Key', trigger: 'blur' }],
  api_secret: [{ required: true, message: '请输入API Secret', trigger: 'blur' }],
  passphrase: [{ required: true, message: '请输入Passphrase', trigger: 'blur' }]
}

const basicRules = {
  default_symbol: [{ required: true, message: '请选择默认交易对', trigger: 'change' }],
  default_leverage: [{ required: true, message: '请输入默认杠杆', trigger: 'blur' }],
  timeframe: [{ required: true, message: '请选择时间周期', trigger: 'change' }]
}

// 方法
const testConnection = async () => {
  if (!exchangeFormRef.value) return
  
  const valid = await exchangeFormRef.value.validate().catch(() => false)
  if (!valid) return
  
  testing.value = true
  connectionResult.value = null
  
  try {
    const response = await api.post('/api/exchange/test-connection', exchangeConfig)
    connectionResult.value = {
      success: response.data.success,
      message: response.data.success ? '连接测试成功' : response.data.error
    }
  } catch (error) {
    connectionResult.value = {
      success: false,
      message: error.message
    }
  } finally {
    testing.value = false
  }
}

const saveExchangeConfig = async () => {
  if (!exchangeFormRef.value) return
  
  const valid = await exchangeFormRef.value.validate().catch(() => false)
  if (!valid) return
  
  saving.value = true
  
  try {
    await api.post('/api/configure-exchange', exchangeConfig)
    ElMessage.success('交易所配置保存成功')
  } catch (error) {
    ElMessage.error(`保存失败: ${error.message}`)
  } finally {
    saving.value = false
  }
}

const saveBasicConfig = async () => {
  if (!basicFormRef.value) return
  
  const valid = await basicFormRef.value.validate().catch(() => false)
  if (!valid) return
  
  saving.value = true
  
  try {
    await api.post('/api/config/basic', basicConfig)
    ElMessage.success('基础配置保存成功')
  } catch (error) {
    ElMessage.error(`保存失败: ${error.message}`)
  } finally {
    saving.value = false
  }
}

const saveAdvancedConfig = async () => {
  saving.value = true
  
  try {
    await api.post('/api/config/advanced', advancedConfig)
    ElMessage.success('高级配置保存成功')
  } catch (error) {
    ElMessage.error(`保存失败: ${error.message}`)
  } finally {
    saving.value = false
  }
}

const resetBasicConfig = () => {
  Object.assign(basicConfig, {
    default_symbol: 'BTC-USDT',
    default_leverage: 10,
    timeframe: '30m',
    max_positions: 3,
    stop_loss_percent: 5.0,
    take_profit_percent: 10.0,
    log_level: 'INFO',
    auto_restart: true,
    email_notifications: false
  })
}

const resetAdvancedConfig = () => {
  Object.assign(advancedConfig, {
    enable_cache: true,
    max_concurrent_requests: 5,
    request_interval: 1000,
    data_retention_days: 30,
    auto_cleanup: true,
    enable_rate_limit: true,
    ip_whitelist: ''
  })
}

const loadConfigs = async () => {
  try {
    const response = await api.get('/api/config')
    if (response.data.success) {
      const config = response.data.data
      if (config.exchange) Object.assign(exchangeConfig, config.exchange)
      if (config.basic) Object.assign(basicConfig, config.basic)
      if (config.advanced) Object.assign(advancedConfig, config.advanced)
    }
  } catch (error) {
    console.error('加载配置失败:', error)
  }
}

// 生命周期
onMounted(() => {
  loadConfigs()
})
</script>

<style lang="scss" scoped>
.config-page {
  .page-header {
    margin-bottom: var(--spacing-lg);
    
    h2 {
      margin-bottom: var(--spacing-xs);
      color: var(--text-color-primary);
    }
    
    p {
      color: var(--text-color-secondary);
    }
  }
  
  .config-tabs {
    .el-tabs__content {
      padding-top: var(--spacing-lg);
    }
  }
}
</style>
