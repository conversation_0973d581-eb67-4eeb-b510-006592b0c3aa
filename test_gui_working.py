"""
测试GUI是否正常工作
"""
import tkinter as tk
from tkinter import messagebox
import sys
import os
import time

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_basic_gui():
    """测试基础GUI"""
    print("🚀 启动基础GUI测试...")
    
    root = tk.Tk()
    root.title("GUI测试")
    root.geometry("400x300")
    
    # 创建标签
    label = tk.Label(root, text="✅ GUI系统正常工作！", font=("Arial", 14))
    label.pack(pady=50)
    
    # 创建按钮
    def show_message():
        messagebox.showinfo("成功", "🎉 GUI测试成功！\n\nTkinter界面正常工作。")
    
    button = tk.Button(root, text="测试消息框", command=show_message)
    button.pack(pady=10)
    
    # 自动关闭
    def auto_close():
        print("✅ GUI测试完成，自动关闭")
        root.quit()
    
    # 5秒后自动关闭
    root.after(5000, auto_close)
    
    print("📝 GUI窗口已创建，将在5秒后自动关闭...")
    root.mainloop()
    print("🔚 GUI测试结束")

def test_imports():
    """测试模块导入"""
    print("📦 测试模块导入...")
    
    try:
        from exchanges import OKXExchange
        print("✅ OKXExchange 导入成功")
    except Exception as e:
        print(f"❌ OKXExchange 导入失败: {e}")
        return False
    
    try:
        from gui import MainWindow
        print("✅ MainWindow 导入成功")
    except Exception as e:
        print(f"❌ MainWindow 导入失败: {e}")
        return False
    
    try:
        from utils.async_gui import run_async_gui, set_async_tk
        print("✅ async_gui 导入成功")
    except Exception as e:
        print(f"❌ async_gui 导入失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("🧪 BitV GUI系统测试")
    print("=" * 50)
    
    # 测试导入
    if not test_imports():
        print("❌ 模块导入测试失败")
        return
    
    print("✅ 所有模块导入成功")
    
    # 测试基础GUI
    try:
        test_basic_gui()
        print("✅ 基础GUI测试成功")
    except Exception as e:
        print(f"❌ 基础GUI测试失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    print("🎉 所有测试通过！GUI系统正常工作。")

if __name__ == "__main__":
    main()
